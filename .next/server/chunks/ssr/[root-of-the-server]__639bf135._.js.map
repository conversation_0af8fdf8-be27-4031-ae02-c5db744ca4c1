{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/context.tsx"], "sourcesContent": ["'use client'\n\nimport type { RefObject } from 'react'\nimport { createContext, useContext } from 'use-context-selector'\nimport type {\n  Callback,\n  ChatConfig,\n  ChatItemInTree,\n  Feedback,\n} from '../types'\nimport type { ThemeBuilder } from '../embedded-chatbot/theme/theme-context'\nimport type {\n  AppConversationData,\n  AppData,\n  AppMeta,\n  ConversationItem,\n  EmbedSource,\n} from '@/models/share'\nimport { noop } from 'lodash-es'\n\nexport type ChatWithHistoryContextValue = {\n  appInfoError?: any\n  appInfoLoading?: boolean\n  appMeta?: AppMeta\n  appData?: AppData\n  appParams?: ChatConfig\n  appChatListDataLoading?: boolean\n  currentConversationId: string\n  currentConversationItem?: ConversationItem\n  appPrevChatTree: ChatItemInTree[]\n  pinnedConversationList: AppConversationData['data']\n  conversationList: AppConversationData['data']\n  newConversationInputs: Record<string, any>\n  newConversationInputsRef: RefObject<Record<string, any>>\n  handleNewConversationInputsChange: (v: Record<string, any>) => void\n  inputsForms: any[]\n  handleNewConversation: () => void\n  handleStartChat: (callback?: any) => void\n  handleChangeConversation: (conversationId: string) => void\n  handlePinConversation: (conversationId: string) => void\n  handleUnpinConversation: (conversationId: string) => void\n  handleDeleteConversation: (conversationId: string, callback: Callback) => void\n  conversationRenaming: boolean\n  handleRenameConversation: (conversationId: string, newName: string, callback: Callback) => void\n  handleNewConversationCompleted: (newConversationId: string) => void\n  chatShouldReloadKey: string\n  isMobile: boolean\n  isInstalledApp: boolean\n  appId?: string\n  handleFeedback: (messageId: string, feedback: Feedback) => void\n  currentChatInstanceRef: RefObject<{ handleStop: () => void }>\n  themeBuilder?: ThemeBuilder\n  sidebarCollapseState?: boolean\n  handleSidebarCollapse: (state: boolean) => void\n  clearChatList?: boolean\n  setClearChatList: (state: boolean) => void\n  isResponding?: boolean\n  setIsResponding: (state: boolean) => void,\n  currentConversationInputs: Record<string, any> | null,\n  setCurrentConversationInputs: (v: Record<string, any>) => void,\n  isFold?: boolean\n  setIsFold?: (bool: boolean) => void\n  embedSource?: EmbedSource\n  refreshRenderKey?: number\n  larkInfo?: any\n  handleClearAllConversations?: (callback: Callback) => void\n  rightSideInfo?: null | any// 右侧面板信息\n  setRightSideInfo?: (info: any) => void // 右侧面板信息\n}\n\nexport const ChatWithHistoryContext = createContext<ChatWithHistoryContextValue>({\n  currentConversationId: '',\n  appPrevChatTree: [],\n  pinnedConversationList: [],\n  conversationList: [],\n  newConversationInputs: {},\n  newConversationInputsRef: { current: {} },\n  handleNewConversationInputsChange: noop,\n  inputsForms: [],\n  handleNewConversation: noop,\n  handleStartChat: noop,\n  handleChangeConversation: noop,\n  handlePinConversation: noop,\n  handleUnpinConversation: noop,\n  handleDeleteConversation: noop,\n  conversationRenaming: false,\n  handleRenameConversation: noop,\n  handleNewConversationCompleted: noop,\n  chatShouldReloadKey: '',\n  isMobile: false,\n  isInstalledApp: false,\n  handleFeedback: noop,\n  currentChatInstanceRef: { current: { handleStop: noop } },\n  sidebarCollapseState: false,\n  handleSidebarCollapse: noop,\n  clearChatList: false,\n  setClearChatList: noop,\n  isResponding: false,\n  setIsResponding: noop,\n  currentConversationInputs: {},\n  setCurrentConversationInputs: noop,\n  isFold: false,\n  setIsFold: noop,\n  embedSource: '',\n  refreshRenderKey: -1,\n  larkInfo: {},\n  handleClearAllConversations: noop,\n  rightSideInfo: null,\n  setRightSideInfo: noop,\n})\nexport const useChatWithHistoryContext = () => useContext(ChatWithHistoryContext)\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;AAsEO,MAAM,yBAAyB,cAA2C;IAC/E,uBAAuB;IACvB,iBAAiB,EAAE;IACnB,wBAAwB,EAAE;IAC1B,kBAAkB,EAAE;IACpB,uBAAuB,CAAC;IACxB,0BAA0B;QAAE,SAAS,CAAC;IAAE;IACxC,mCAAmC;IACnC,aAAa,EAAE;IACf,uBAAuB;IACvB,iBAAiB;IACjB,0BAA0B;IAC1B,uBAAuB;IACvB,yBAAyB;IACzB,0BAA0B;IAC1B,sBAAsB;IACtB,0BAA0B;IAC1B,gCAAgC;IAChC,qBAAqB;IACrB,UAAU;IACV,gBAAgB;IAChB,gBAAgB;IAChB,wBAAwB;QAAE,SAAS;YAAE,YAAY;QAAK;IAAE;IACxD,sBAAsB;IACtB,uBAAuB;IACvB,eAAe;IACf,kBAAkB;IAClB,cAAc;IACd,iBAAiB;IACjB,2BAA2B,CAAC;IAC5B,8BAA8B;IAC9B,QAAQ;IACR,WAAW;IACX,aAAa;IACb,kBAAkB,CAAC;IACnB,UAAU,CAAC;IACX,6BAA6B;IAC7B,eAAe;IACf,kBAAkB;AACpB;AACO,MAAM,4BAA4B,IAAM,WAAW", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/input/index.tsx"], "sourcesContent": ["import type { CSSProperties } from 'react'\nimport React from 'react'\nimport { useTranslation } from 'react-i18next'\nimport { RiCloseCircleFill, RiErrorWarningLine, RiSearchLine } from '@remixicon/react'\nimport { type VariantProps, cva } from 'class-variance-authority'\nimport cn from '@/utils/classnames'\nimport { noop } from 'lodash-es'\n\nexport const inputVariants = cva(\n  '',\n  {\n    variants: {\n      size: {\n        regular: 'px-3 radius-md system-sm-regular',\n        large: 'px-4 radius-lg system-md-regular',\n      },\n    },\n    defaultVariants: {\n      size: 'regular',\n    },\n  },\n)\n\nexport type InputProps = {\n  showLeftIcon?: boolean\n  showClearIcon?: boolean\n  onClear?: () => void\n  disabled?: boolean\n  destructive?: boolean\n  wrapperClassName?: string\n  styleCss?: CSSProperties\n  unit?: string\n} & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> & VariantProps<typeof inputVariants>\n\nconst Input = ({\n  size,\n  disabled,\n  destructive,\n  showLeftIcon,\n  showClearIcon,\n  onClear,\n  wrapperClassName,\n  className,\n  styleCss,\n  value,\n  placeholder,\n  onChange = noop,\n  unit,\n  ...props\n}: InputProps) => {\n  const { t } = useTranslation()\n  return (\n    <div className={cn('relative w-full', wrapperClassName)}>\n      {showLeftIcon && <RiSearchLine className={cn('absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-components-input-text-placeholder')} />}\n      <input\n        style={styleCss}\n        className={cn(\n          'w-full appearance-none border border-transparent bg-components-input-bg-normal py-[7px] text-components-input-text-filled caret-primary-600 outline-none placeholder:text-components-input-text-placeholder hover:border-components-input-border-hover hover:bg-components-input-bg-hover focus:border-components-input-border-active focus:bg-components-input-bg-active focus:shadow-xs',\n          inputVariants({ size }),\n          showLeftIcon && 'pl-[26px]',\n          showLeftIcon && size === 'large' && 'pl-7',\n          showClearIcon && value && 'pr-[26px]',\n          showClearIcon && value && size === 'large' && 'pr-7',\n          destructive && 'pr-[26px]',\n          destructive && size === 'large' && 'pr-7',\n          disabled && 'cursor-not-allowed border-transparent bg-components-input-bg-disabled text-components-input-text-filled-disabled hover:border-transparent hover:bg-components-input-bg-disabled',\n          destructive && 'border-components-input-border-destructive bg-components-input-bg-destructive text-components-input-text-filled hover:border-components-input-border-destructive hover:bg-components-input-bg-destructive focus:border-components-input-border-destructive focus:bg-components-input-bg-destructive',\n          className,\n        )}\n        placeholder={placeholder ?? (showLeftIcon\n          ? (t('common.operation.search') || '')\n          : (t('common.placeholder.input') || ''))}\n        value={value}\n        onChange={onChange}\n        disabled={disabled}\n        {...props}\n      />\n      {showClearIcon && value && !disabled && !destructive && (\n        <div className={cn('group absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer p-[1px]')} onClick={onClear}>\n          <RiCloseCircleFill className='h-3.5 w-3.5 cursor-pointer text-text-quaternary group-hover:text-text-tertiary' />\n        </div>\n      )}\n      {destructive && (\n        <RiErrorWarningLine className='absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 text-text-destructive-secondary' />\n      )}\n      {\n        unit && (\n          <div className='system-sm-regular absolute right-2 top-1/2 -translate-y-1/2 text-text-tertiary'>\n            {unit}\n          </div>\n        )\n      }\n    </div>\n  )\n}\n\nexport default Input\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAIA;;;;;;;;;;;;;;;;;AAIO,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EAC7B,IACA;IACE,UAAU;QACR,MAAM;YACJ,SAAS;YACT,OAAO;QACT;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAcF,MAAM,QAAQ,CAAC,EACb,IAAI,EACJ,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,aAAa,EACb,OAAO,EACP,gBAAgB,EAChB,SAAS,EACT,QAAQ,EACR,KAAK,EACL,WAAW,EACX,WAAW,IAAI,EACf,IAAI,EACJ,GAAG,OACQ;IACX,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,qBACE,qZAAC;QAAI,WAAW,GAAG,mBAAmB;;YACnC,8BAAgB,qZAAC;gBAAa,WAAW,GAAG;;;;;;0BAC7C,qZAAC;gBACC,OAAO;gBACP,WAAW,GACT,6XACA,cAAc;oBAAE;gBAAK,IACrB,gBAAgB,aAChB,gBAAgB,SAAS,WAAW,QACpC,iBAAiB,SAAS,aAC1B,iBAAiB,SAAS,SAAS,WAAW,QAC9C,eAAe,aACf,eAAe,SAAS,WAAW,QACnC,YAAY,mLACZ,eAAe,uSACf;gBAEF,aAAa,eAAe,CAAC,eACxB,EAAE,8BAA8B,KAChC,EAAE,+BAA+B,EAAG;gBACzC,OAAO;gBACP,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;;;;;YAEV,iBAAiB,SAAS,CAAC,YAAY,CAAC,6BACvC,qZAAC;gBAAI,WAAW,GAAG;gBAA2E,SAAS;0BACrG,cAAA,qZAAC;oBAAkB,WAAU;;;;;;;;;;;YAGhC,6BACC,qZAAC;gBAAmB,WAAU;;;;;;YAG9B,sBACE,qZAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/spinner/index.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport React from 'react'\n\ntype Props = {\n  loading?: boolean\n  className?: string\n  children?: React.ReactNode | string\n}\n\nconst Spinner: FC<Props> = ({ loading = false, children, className }) => {\n  return (\n    <div\n      className={`inline-block h-4 w-4 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] text-gray-200 ${loading ? 'motion-reduce:animate-[spin_1.5s_linear_infinite]' : 'hidden'} ${className ?? ''}`}\n      role=\"status\"\n    >\n      <span\n        className=\"!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]\"\n      >Loading...</span>\n      {children}\n    </div>\n  )\n}\n\nexport default Spinner\n"], "names": [], "mappings": ";;;;;AASA,MAAM,UAAqB,CAAC,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE;IAClE,qBACE,qZAAC;QACC,WAAW,CAAC,wIAAwI,EAAE,UAAU,sDAAsD,SAAS,CAAC,EAAE,aAAa,IAAI;QACnP,MAAK;;0BAEL,qZAAC;gBACC,WAAU;0BACX;;;;;;YACA;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/button/index.tsx"], "sourcesContent": ["import type { CSSProperties } from 'react'\nimport React from 'react'\nimport { type VariantProps, cva } from 'class-variance-authority'\nimport Spinner from '../spinner'\nimport classNames from '@/utils/classnames'\n\nconst buttonVariants = cva(\n  'btn disabled:btn-disabled',\n  {\n    variants: {\n      variant: {\n        'primary': 'btn-primary',\n        'warning': 'btn-warning',\n        'secondary': 'btn-secondary',\n        'secondary-accent': 'btn-secondary-accent',\n        'ghost': 'btn-ghost',\n        'ghost-accent': 'btn-ghost-accent',\n        'tertiary': 'btn-tertiary',\n      },\n      size: {\n        small: 'btn-small',\n        medium: 'btn-medium',\n        large: 'btn-large',\n      },\n    },\n    defaultVariants: {\n      variant: 'secondary',\n      size: 'medium',\n    },\n  },\n)\n\nexport type ButtonProps = {\n  destructive?: boolean\n  loading?: boolean\n  styleCss?: CSSProperties\n  spinnerClassName?: string\n} & React.ButtonHTMLAttributes<HTMLButtonElement> & VariantProps<typeof buttonVariants>\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, destructive, loading, styleCss, children, spinnerClassName, ...props }, ref) => {\n    return (\n      <button\n        type='button'\n        className={classNames(\n          buttonVariants({ variant, size, className }),\n          destructive && 'btn-destructive',\n        )}\n        ref={ref}\n        style={styleCss}\n        {...props}\n      >\n        {children}\n        {loading && <Spinner loading={loading} className={classNames('!text-white !h-3 !w-3 !border-2 !ml-1', spinnerClassName)} />}\n      </button>\n    )\n  },\n)\nButton.displayName = 'Button'\n\nexport default Button\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;;;;;;;;;;;AAGA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,6BACA;IACE,UAAU;QACR,SAAS;YACP,WAAW;YACX,WAAW;YACX,aAAa;YACb,oBAAoB;YACpB,SAAS;YACT,gBAAgB;YAChB,YAAY;QACd;QACA,MAAM;YACJ,OAAO;YACP,QAAQ;YACR,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,4WAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG,OAAO,EAAE;IACnG,qBACE,qZAAC;QACC,MAAK;QACL,WAAW,WACT,eAAe;YAAE;YAAS;YAAM;QAAU,IAC1C,eAAe;QAEjB,KAAK;QACL,OAAO;QACN,GAAG,KAAK;;YAER;YACA,yBAAW,qZAAC,8IAAA,CAAA,UAAO;gBAAC,SAAS;gBAAS,WAAW,WAAW,yCAAyC;;;;;;;;;;;;AAG5G;AAEF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/btn-fold.tsx"], "sourcesContent": ["import cls from 'classnames'\nimport Button from '../../button'\nconst IconFold = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" className={className}>\n    <g stroke=\"#1E6AFF\" stroke-width=\"1.4\" fill=\"none\" fill-rule=\"evenodd\">\n      <rect x=\".7\" y=\".7\" width=\"18.6\" height=\"18.6\" rx=\"4\" />\n      <path d=\"M13.5 0L13.5 20\" />\n      <path d=\"M6,7 L9,10 L6,13\" transform=\"matrix(-1 0 0 1 15 0)\" />\n    </g>\n  </svg>\n)\nconst IconUnfold = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" className={className}>\n    <g stroke=\"#1E6AFF\" stroke-width=\"1.4\" fill=\"none\" fill-rule=\"evenodd\">\n      <rect x=\".7\" y=\".7\" width=\"18.6\" height=\"18.6\" rx=\"4\" />\n      <path d=\"M13.5 0L13.5 20\" />\n      <path d=\"M6,7 L9,10 L6,13\" />\n    </g>\n  </svg>\n)\n\nconst SvgClass = 'w-[20px] h-[20px]'\n\ntype Props = {\n  className?: string\n  isFold?: boolean\n  onClick?: () => void\n}\n\nconst BtnFold = ({ className, isFold, onClick }: Props) => {\n  return (\n    <div className={cls(className, 'cursor-pointer')} onClick={onClick}>\n      {isFold\n        ? <Button\n          variant='secondary-accent'\n          className={'h-[40px] rounded-[20px] border-[#356CFF] text-[#434B5B]'}\n        >\n          <IconUnfold className=\"w-[18px] mr-[8px]\" />\n        历史对话\n        </Button>\n        : <IconFold className={SvgClass} />}\n    </div>\n  )\n}\n\nexport default BtnFold\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AACA,MAAM,WAAW,CAAC,EAAE,SAAS,EAAyB,iBACpD,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAE,QAAO;YAAU,gBAAa;YAAM,MAAK;YAAO,aAAU;;8BAC3D,qZAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAO,QAAO;oBAAO,IAAG;;;;;;8BAClD,qZAAC;oBAAK,GAAE;;;;;;8BACR,qZAAC;oBAAK,GAAE;oBAAmB,WAAU;;;;;;;;;;;;;;;;;AAI3C,MAAM,aAAa,CAAC,EAAE,SAAS,EAAyB,iBACtD,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAE,QAAO;YAAU,gBAAa;YAAM,MAAK;YAAO,aAAU;;8BAC3D,qZAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAO,QAAO;oBAAO,IAAG;;;;;;8BAClD,qZAAC;oBAAK,GAAE;;;;;;8BACR,qZAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;;AAKd,MAAM,WAAW;AAQjB,MAAM,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAS;IACpD,qBACE,qZAAC;QAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAG,AAAD,EAAE,WAAW;QAAmB,SAAS;kBACxD,uBACG,qZAAC,6IAAA,CAAA,UAAM;YACP,SAAQ;YACR,WAAW;;8BAEX,qZAAC;oBAAW,WAAU;;;;;;gBAAsB;;;;;;iCAG5C,qZAAC;YAAS,WAAW;;;;;;;;;;;AAG/B;uCAEe", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/base/chat/chat-with-history/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"bg\": \"index-module__guMYKq__bg\",\n  \"errorPng\": \"index-module__guMYKq__errorPng\",\n  \"mobileNavTitle\": \"index-module__guMYKq__mobileNavTitle\",\n  \"navBarBg\": \"index-module__guMYKq__navBarBg\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/sidebar/index.tsx"], "sourcesContent": ["import type { ChangeEvent } from 'react'\nimport {\n  useCallback,\n  useState,\n} from 'react'\nimport { useTranslation } from 'react-i18next'\nimport {\n  RiEditBoxLine,\n  RiExpandRightLine,\n  RiLayoutLeft2Line,\n} from '@remixicon/react'\nimport { throttle } from 'lodash-es'\nimport { useChatWithHistoryContext } from '../context'\nimport Input from '../../../input'\nimport BtnFold from '../btn-fold'\nimport AppIcon from '@/components/base/app-icon'\nimport ActionButton from '@/components/base/action-button'\nimport Button from '@/components/base/button'\nimport List from '@/components/base/chat/chat-with-history/sidebar/list'\nimport MenuDropdown from '@/components/share/text-generation/menu-dropdown'\nimport Confirm from '@/components/base/confirm'\nimport RenameModal from '@/components/base/chat/chat-with-history/sidebar/rename-modal'\nimport LogoSite from '@/components/base/logo/logo-site'\nimport type { ConversationItem } from '@/models/share'\nimport cn from '@/utils/classnames'\nimport styles from '../index.module.css'\nimport Robot from '@/assets/lark-app-robot.gif'\nimport Image from 'next/image'\n\nconst LarkDeleteIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 14 14\" className={className}>\n    <g stroke=\"#A3AFBB\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" fill-rule=\"evenodd\">\n      <path d=\"M8.75,2.04166667 L8.22889873,9.85818568 C8.19387888,10.3834835 7.75757745,10.7916667 7.23111357,10.7916667 L2.10221976,10.7916667 C1.57575588,10.7916667 1.13945446,10.3834835 1.1044346,9.85818568 L0.583333333,2.04166667 L0.583333333,2.04166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M0 2.04166667L9.33333333 2.04166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M3.5 0.29166667L5.83333333 0.29166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M3.5 7.875L3.5 4.95833333\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M5.83333333 7.875L5.83333333 4.95833333\" transform=\"translate(2.3333 1.4583)\" />\n    </g>\n  </svg>\n)\n\ntype Props = {\n  isPanel?: boolean\n}\n\nconst Sidebar = ({ isPanel }: Props) => {\n  const { t } = useTranslation()\n  const {\n    appData,\n    handleNewConversation,\n    pinnedConversationList,\n    conversationList,\n    currentConversationId,\n    handleChangeConversation,\n    handlePinConversation,\n    handleUnpinConversation,\n    conversationRenaming,\n    handleRenameConversation,\n    handleDeleteConversation,\n    sidebarCollapseState,\n    handleSidebarCollapse,\n    isMobile,\n    isResponding,\n    embedSource,\n    isFold,\n    setIsFold,\n    handleClearAllConversations\n  } = useChatWithHistoryContext()\n  const isSidebarCollapsed = sidebarCollapseState\n\n  const [showConfirm, setShowConfirm] = useState<ConversationItem | null>(null)\n  const [showClearAll, setShowClearAll] = useState<boolean | null>(null)\n  const [showRename, setShowRename] = useState<ConversationItem | null>(null)\n  const [keyword, setKeyword] = useState<string>('')\n  const isEmbedMobile = isMobile && embedSource\n\n  const handleOperate = useCallback((type: string, item: ConversationItem) => {\n    if (type === 'pin')\n      handlePinConversation(item.id)\n\n    if (type === 'unpin')\n      handleUnpinConversation(item.id)\n\n    if (type === 'delete')\n      setShowConfirm(item)\n\n    if (type === 'rename')\n      setShowRename(item)\n  }, [handlePinConversation, handleUnpinConversation])\n  const handleCancelConfirm = useCallback(() => {\n    setShowConfirm(null)\n  }, [])\n  const handleDelete = useCallback(() => {\n    if (showConfirm)\n      handleDeleteConversation(showConfirm.id, { onSuccess: handleCancelConfirm })\n  }, [showConfirm, handleDeleteConversation, handleCancelConfirm])\n  const handleCancelRename = useCallback(() => {\n    setShowRename(null)\n  }, [])\n  const handleRename = useCallback((newName: string) => {\n    if (showRename)\n      handleRenameConversation(showRename.id, newName, { onSuccess: handleCancelRename })\n  }, [showRename, handleRenameConversation, handleCancelRename])\n\n  const handleSearch = throttle(({ target }: ChangeEvent<HTMLInputElement>) => {\n    setKeyword(target?.value)\n  }, 100)\n\n  const handleClearAll = () => {\n    setShowClearAll(true)\n  }\n\n  const handleClearAllConfirm = () => {\n    handleClearAllConversations?.({ onSuccess: () => setShowClearAll(false) })\n  }\n\n  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}\n  const aiRobotGifUrl = chatPageConfigData?.aiRobotGifUrl || Robot // AI机器人动画\n\n  return (\n    <div className={cn(\n      'flex w-full grow flex-col',\n      isPanel && 'rounded-xl border-[0.5px] border-components-panel-border-subtle bg-components-panel-bg shadow-lg',\n      isEmbedMobile && styles.bg,\n      isEmbedMobile && 'bg-[#f5f6f8] w-[74vw] !border-none'\n    )}>\n      <div className={cn(\n        'flex shrink-0 items-center gap-3 p-3 pr-2',\n      )}>\n        <div className='shrink-0'>\n          {\n            embedSource ? (\n              !isMobile && <div className=\"flex items-center\">\n                <Input\n                  className='h-[40px] rounded-[8px] border-[1px] border-solid border-[#DFE4E8]'\n                  value={keyword}\n                  placeholder=\"搜索对话...\"\n                  showLeftIcon\n                  showClearIcon\n                  onChange={handleSearch}\n                  onClear={() => setKeyword('')}\n                />\n                <BtnFold className=\"ml-[11px]\" isFold={isFold} onClick={() => setIsFold?.(!isFold)} />\n              </div>\n            ) : (\n              <>\n                <AppIcon\n                  size='large'\n                  iconType={appData?.site.icon_type}\n                  icon={appData?.site.icon}\n                  background={appData?.site.icon_background}\n                  imageUrl={appData?.site.icon_url}\n                />\n                <div className={cn('system-md-semibold grow truncate text-text-secondary')}>{appData?.site.title}</div>\n                {!isMobile && isSidebarCollapsed && (\n                  <ActionButton size='l' onClick={() => handleSidebarCollapse(false)}>\n                    <RiExpandRightLine className='h-[18px] w-[18px]' />\n                  </ActionButton>\n                )}\n                {!isMobile && !isSidebarCollapsed && (\n                  <ActionButton size='l' onClick={() => handleSidebarCollapse(true)}>\n                    <RiLayoutLeft2Line className='h-[18px] w-[18px]' />\n                  </ActionButton>\n                )}\n              </>\n            )\n          }\n        </div>\n      </div>\n      {\n        !embedSource && (\n          <div className='shrink-0 px-3 py-4'>\n            <Button variant='secondary-accent' disabled={isResponding} className='w-full justify-center' onClick={handleNewConversation}>\n              <RiEditBoxLine className='mr-1 h-4 w-4' />\n              {t('share.chat.newChat')}\n            </Button>\n          </div>\n        )\n      }\n      {embedSource && isMobile && <div className=\"mb-[30px]\">\n        <Image src={aiRobotGifUrl} width={100} height={100} className=\"w-[100px] height-[100px] mt-[64px] mx-auto mb-[6px]\" alt=\"\" />\n        <p className=\"text-center text-[18px] text-[#242933] mb-[20px] font-semibold\">Hi～我是{appData?.site.title}</p>\n        <div className=\"px-[12px]\">\n          <Input\n            className='h-[40px] rounded-[8px] border-[1px] border-solid bg-[#fff] hover:bg-[#fff]'\n            value={keyword}\n            placeholder=\"搜索对话...\"\n            showLeftIcon\n            showClearIcon\n            onChange={handleSearch}\n            onClear={() => setKeyword('')}\n          />\n        </div>\n      </div>}\n      <div className=\"flex items-center justify-between px-[20px] mb-[10px]\">\n        <p className=\"text-[16px] text-[#242933] font-medium\">对话记录</p>\n        <button onClick={handleClearAll}><LarkDeleteIcon className=\"w-[20px] h-[20px]\" /></button>\n      </div>\n      <div className='h-0 grow space-y-2 overflow-y-auto px-3 pt-4'>\n        {/* pinned list */}\n        {!!pinnedConversationList.length && (\n          <div className='mb-4'>\n            <List\n              embedSource={embedSource}\n              isMobile={isMobile}\n              isPin\n              title={t('share.chat.pinnedTitle') || ''}\n              list={pinnedConversationList.filter(item => item.name.includes(keyword))}\n              onChangeConversation={handleChangeConversation}\n              onOperate={handleOperate}\n              currentConversationId={currentConversationId}\n            />\n          </div>\n        )}\n        {!!conversationList.length && (\n          <List\n            embedSource={embedSource}\n            isMobile={isMobile}\n            title={(pinnedConversationList.length && t('share.chat.unpinnedTitle')) || ''}\n            list={conversationList.filter(item => item.name.includes(keyword))}\n            onChangeConversation={handleChangeConversation}\n            onOperate={handleOperate}\n            currentConversationId={currentConversationId}\n          />\n        )}\n      </div>\n      <div className='flex shrink-0 items-center justify-between p-3'>\n        <MenuDropdown placement='top-start' data={appData?.site} />\n        {/* powered by */}\n        <div className='shrink-0'>\n          {!appData?.custom_config?.remove_webapp_brand && (\n            <div className={cn(\n              'flex shrink-0 items-center gap-1.5 px-2',\n            )}>\n              <div className='system-2xs-medium-uppercase text-text-tertiary'>{t('share.chat.poweredBy')}</div>\n              {appData?.custom_config?.replace_webapp_logo && (\n                <img src={appData?.custom_config?.replace_webapp_logo} alt='logo' className='block h-5 w-auto' />\n              )}\n              {!appData?.custom_config?.replace_webapp_logo && (\n                <LogoSite className='!h-5' />\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n      {!!showConfirm && (\n        <Confirm\n          title={embedSource && isMobile ? ' 确定要删除吗？' : t('share.chat.deleteConversation.title')}\n          content={embedSource && isMobile ? \"删除后无法撤销\" : t('share.chat.deleteConversation.content') || ''}\n          isShow\n          isEmbedMobile={Boolean(embedSource && isMobile)}\n          onCancel={handleCancelConfirm}\n          onConfirm={handleDelete}\n          footerRender={embedSource && isMobile ? (() => <div className='flex w-full border-t-[1px] border-t-[#EEF0F2] mt-[23px]'>\n            <p className=\"py-[15px] text-[#A3AFBB] flex-1 text-center border-r-[1px] border-r-[#EEF0F2] text-[16px]\" onClick={handleCancelConfirm}>取消</p>\n            <p className=\"py-[15px] text-[#FF4C4C] flex-1 text-center text-[16px]\" onClick={handleDelete}>删除</p>\n          </div>) : undefined}\n        />\n      )}\n      {showClearAll && (\n        <Confirm\n          title={'确定要删除所有对话记录吗？'}\n          content={\"删除后无法撤销\"}\n          isShow\n          isEmbedMobile={Boolean(embedSource && isMobile)}\n          onCancel={() => setShowClearAll(false)}\n          onConfirm={handleClearAllConfirm}\n          footerRender={embedSource && isMobile ? (() => <div className='flex w-full border-t-[1px] border-t-[#EEF0F2] mt-[23px]'>\n            <p className=\"py-[15px] text-[#A3AFBB] flex-1 text-center border-r-[1px] border-r-[#EEF0F2] text-[16px]\" onClick={() => setShowClearAll(false)}>取消</p>\n            <p className=\"py-[15px] text-[#FF4C4C] flex-1 text-center text-[16px]\" onClick={handleClearAllConfirm}>删除</p>\n          </div>) : undefined}\n        />\n      )}\n      {showRename && (\n        <RenameModal\n          isEmbedMobile={Boolean(embedSource && isMobile)}\n          isShow\n          onClose={handleCancelRename}\n          saveLoading={conversationRenaming}\n          name={showRename?.name || ''}\n          onSave={handleRename}\n        />\n      )}\n    </div>\n  )\n}\n\nexport default Sidebar\n"], "names": [], "mappings": ";;;;AACA;;;;;;;;;;;;;;;;AAWA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA;;;;;;AAEA;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAyB,iBAC1D,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAE,QAAO;YAAU,kBAAe;YAAQ,mBAAgB;YAAQ,MAAK;YAAO,aAAU;;8BACvF,qZAAC;oBAAK,GAAE;oBAAsP,WAAU;;;;;;8BACxQ,qZAAC;oBAAK,GAAE;oBAAsC,WAAU;;;;;;8BACxD,qZAAC;oBAAK,GAAE;oBAAwC,WAAU;;;;;;8BAC1D,qZAAC;oBAAK,GAAE;oBAA4B,WAAU;;;;;;8BAC9C,qZAAC;oBAAK,GAAE;oBAA0C,WAAU;;;;;;;;;;;;;;;;;AASlE,MAAM,UAAU,CAAC,EAAE,OAAO,EAAS;IACjC,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,EACJ,OAAO,EACP,qBAAqB,EACrB,sBAAsB,EACtB,gBAAgB,EAChB,qBAAqB,EACrB,wBAAwB,EACxB,qBAAqB,EACrB,uBAAuB,EACvB,oBAAoB,EACpB,wBAAwB,EACxB,wBAAwB,EACxB,oBAAoB,EACpB,qBAAqB,EACrB,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,MAAM,EACN,SAAS,EACT,2BAA2B,EAC5B,GAAG,CAAA,GAAA,wKAAA,CAAA,4BAAyB,AAAD;IAC5B,MAAM,qBAAqB;IAE3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAA2B;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAkB;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAA2B;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,gBAAgB,YAAY;IAElC,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAC/C,IAAI,SAAS,OACX,sBAAsB,KAAK,EAAE;QAE/B,IAAI,SAAS,SACX,wBAAwB,KAAK,EAAE;QAEjC,IAAI,SAAS,UACX,eAAe;QAEjB,IAAI,SAAS,UACX,cAAc;IAClB,GAAG;QAAC;QAAuB;KAAwB;IACnD,MAAM,sBAAsB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACtC,eAAe;IACjB,GAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,aACF,yBAAyB,YAAY,EAAE,EAAE;YAAE,WAAW;QAAoB;IAC9E,GAAG;QAAC;QAAa;QAA0B;KAAoB;IAC/D,MAAM,qBAAqB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACrC,cAAc;IAChB,GAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI,YACF,yBAAyB,WAAW,EAAE,EAAE,SAAS;YAAE,WAAW;QAAmB;IACrF,GAAG;QAAC;QAAY;QAA0B;KAAmB;IAE7D,MAAM,eAAe,SAAS,CAAC,EAAE,MAAM,EAAiC;QACtE,WAAW,QAAQ;IACrB,GAAG;IAEH,MAAM,iBAAiB;QACrB,gBAAgB;IAClB;IAEA,MAAM,wBAAwB;QAC5B,8BAA8B;YAAE,WAAW,IAAM,gBAAgB;QAAO;IAC1E;IAEA,MAAM,qBAAqB,SAAS,MAAM,iBAAiB,KAAK,KAAK,CAAC,SAAS,MAAM,kBAAkB,CAAC;IACxG,MAAM,gBAAgB,oBAAoB,iBAAiB,MAAM,UAAU;;IAE3E,qBACE,qZAAC;QAAI,WAAW,GACd,6BACA,WAAW,oGACX,iBAAiB,iLAAA,CAAA,UAAM,CAAC,EAAE,EAC1B,iBAAiB;;0BAEjB,qZAAC;gBAAI,WAAW,GACd;0BAEA,cAAA,qZAAC;oBAAI,WAAU;8BAEX,cACE,CAAC,0BAAY,qZAAC;wBAAI,WAAU;;0CAC1B,qZAAC,4IAAA,CAAA,UAAK;gCACJ,WAAU;gCACV,OAAO;gCACP,aAAY;gCACZ,YAAY;gCACZ,aAAa;gCACb,UAAU;gCACV,SAAS,IAAM,WAAW;;;;;;0CAE5B,qZAAC,4KAAA,CAAA,UAAO;gCAAC,WAAU;gCAAY,QAAQ;gCAAQ,SAAS,IAAM,YAAY,CAAC;;;;;;;;;;;6CAG7E;;0CACE,qZAAC;gCACC,MAAK;gCACL,UAAU,SAAS,KAAK;gCACxB,MAAM,SAAS,KAAK;gCACpB,YAAY,SAAS,KAAK;gCAC1B,UAAU,SAAS,KAAK;;;;;;0CAE1B,qZAAC;gCAAI,WAAW,GAAG;0CAA0D,SAAS,KAAK;;;;;;4BAC1F,CAAC,YAAY,oCACZ,qZAAC;gCAAa,MAAK;gCAAI,SAAS,IAAM,sBAAsB;0CAC1D,cAAA,qZAAC;oCAAkB,WAAU;;;;;;;;;;;4BAGhC,CAAC,YAAY,CAAC,oCACb,qZAAC;gCAAa,MAAK;gCAAI,SAAS,IAAM,sBAAsB;0CAC1D,cAAA,qZAAC;oCAAkB,WAAU;;;;;;;;;;;;;;;;;;;;;;;YASzC,CAAC,6BACC,qZAAC;gBAAI,WAAU;0BACb,cAAA,qZAAC;oBAAO,SAAQ;oBAAmB,UAAU;oBAAc,WAAU;oBAAwB,SAAS;;sCACpG,qZAAC;4BAAc,WAAU;;;;;;wBACxB,EAAE;;;;;;;;;;;;YAKV,eAAe,0BAAY,qZAAC;gBAAI,WAAU;;kCACzC,qZAAC,oSAAA,CAAA,UAAK;wBAAC,KAAK;wBAAe,OAAO;wBAAK,QAAQ;wBAAK,WAAU;wBAAsD,KAAI;;;;;;kCACxH,qZAAC;wBAAE,WAAU;;4BAAiE;4BAAM,SAAS,KAAK;;;;;;;kCAClG,qZAAC;wBAAI,WAAU;kCACb,cAAA,qZAAC,4IAAA,CAAA,UAAK;4BACJ,WAAU;4BACV,OAAO;4BACP,aAAY;4BACZ,YAAY;4BACZ,aAAa;4BACb,UAAU;4BACV,SAAS,IAAM,WAAW;;;;;;;;;;;;;;;;;0BAIhC,qZAAC;gBAAI,WAAU;;kCACb,qZAAC;wBAAE,WAAU;kCAAyC;;;;;;kCACtD,qZAAC;wBAAO,SAAS;kCAAgB,cAAA,qZAAC;4BAAe,WAAU;;;;;;;;;;;;;;;;;0BAE7D,qZAAC;gBAAI,WAAU;;oBAEZ,CAAC,CAAC,uBAAuB,MAAM,kBAC9B,qZAAC;wBAAI,WAAU;kCACb,cAAA,qZAAC;4BACC,aAAa;4BACb,UAAU;4BACV,KAAK;4BACL,OAAO,EAAE,6BAA6B;4BACtC,MAAM,uBAAuB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC;4BAC/D,sBAAsB;4BACtB,WAAW;4BACX,uBAAuB;;;;;;;;;;;oBAI5B,CAAC,CAAC,iBAAiB,MAAM,kBACxB,qZAAC;wBACC,aAAa;wBACb,UAAU;wBACV,OAAO,AAAC,uBAAuB,MAAM,IAAI,EAAE,+BAAgC;wBAC3E,MAAM,iBAAiB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC;wBACzD,sBAAsB;wBACtB,WAAW;wBACX,uBAAuB;;;;;;;;;;;;0BAI7B,qZAAC;gBAAI,WAAU;;kCACb,qZAAC;wBAAa,WAAU;wBAAY,MAAM,SAAS;;;;;;kCAEnD,qZAAC;wBAAI,WAAU;kCACZ,CAAC,SAAS,eAAe,qCACxB,qZAAC;4BAAI,WAAW,GACd;;8CAEA,qZAAC;oCAAI,WAAU;8CAAkD,EAAE;;;;;;gCAClE,SAAS,eAAe,qCACvB,qZAAC;oCAAI,KAAK,SAAS,eAAe;oCAAqB,KAAI;oCAAO,WAAU;;;;;;gCAE7E,CAAC,SAAS,eAAe,qCACxB,qZAAC;oCAAS,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAM7B,CAAC,CAAC,6BACD,qZAAC;gBACC,OAAO,eAAe,WAAW,aAAa,EAAE;gBAChD,SAAS,eAAe,WAAW,YAAY,EAAE,4CAA4C;gBAC7F,MAAM;gBACN,eAAe,QAAQ,eAAe;gBACtC,UAAU;gBACV,WAAW;gBACX,cAAc,eAAe,WAAY,kBAAM,qZAAC;wBAAI,WAAU;;0CAC5D,qZAAC;gCAAE,WAAU;gCAA4F,SAAS;0CAAqB;;;;;;0CACvI,qZAAC;gCAAE,WAAU;gCAA0D,SAAS;0CAAc;;;;;;;;;;;iCACtF;;;;;;YAGb,8BACC,qZAAC;gBACC,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,eAAe,QAAQ,eAAe;gBACtC,UAAU,IAAM,gBAAgB;gBAChC,WAAW;gBACX,cAAc,eAAe,WAAY,kBAAM,qZAAC;wBAAI,WAAU;;0CAC5D,qZAAC;gCAAE,WAAU;gCAA4F,SAAS,IAAM,gBAAgB;0CAAQ;;;;;;0CAChJ,qZAAC;gCAAE,WAAU;gCAA0D,SAAS;0CAAuB;;;;;;;;;;;iCAC/F;;;;;;YAGb,4BACC,qZAAC;gBACC,eAAe,QAAQ,eAAe;gBACtC,MAAM;gBACN,SAAS;gBACT,aAAa;gBACb,MAAM,YAAY,QAAQ;gBAC1B,QAAQ;;;;;;;;;;;;AAKlB;uCAEe", "debugId": null}}, {"offset": {"line": 1091, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/header/operation.tsx"], "sourcesContent": ["'use client'\nimport type { FC } from 'react'\nimport React, { useState } from 'react'\nimport type { Placement } from '@floating-ui/react'\nimport {\n  RiArrowDownSLine,\n} from '@remixicon/react'\nimport { useTranslation } from 'react-i18next'\nimport { PortalToFollowElem, PortalToFollowElemContent, PortalToFollowElemTrigger } from '@/components/base/portal-to-follow-elem'\nimport cn from '@/utils/classnames'\n\ntype Props = {\n  title: string\n  isPinned: boolean\n  isShowRenameConversation?: boolean\n  onRenameConversation?: () => void\n  isShowDelete: boolean\n  togglePin: () => void\n  onDelete: () => void\n  placement?: Placement\n}\n\nconst Operation: FC<Props> = ({\n  title,\n  isPinned,\n  togglePin,\n  isShowRenameConversation,\n  onRenameConversation,\n  isShowDelete,\n  onDelete,\n  placement = 'bottom-start',\n}) => {\n  const { t } = useTranslation()\n  const [open, setOpen] = useState(false)\n\n  return (\n    <PortalToFollowElem\n      open={open}\n      onOpenChange={setOpen}\n      placement={placement}\n      offset={4}\n    >\n      <PortalToFollowElemTrigger\n        onClick={() => setOpen(v => !v)}\n      >\n        <div className={cn('flex cursor-pointer items-center rounded-lg p-1.5 pl-2 text-text-secondary hover:bg-state-base-hover', open && 'bg-state-base-hover')}>\n          <div className='system-md-semibold'>{title}</div>\n          <RiArrowDownSLine className='h-4 w-4 ' />\n        </div>\n      </PortalToFollowElemTrigger>\n      <PortalToFollowElemContent className=\"z-50\">\n        <div\n          className={'min-w-[120px] rounded-xl border-[0.5px] border-components-panel-border bg-components-panel-bg-blur p-1 shadow-lg backdrop-blur-sm'}\n        >\n          <div className={cn('system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-base-hover')} onClick={togglePin}>\n            <span className='grow'>{isPinned ? t('explore.sidebar.action.unpin') : t('explore.sidebar.action.pin')}</span>\n          </div>\n          {isShowRenameConversation && (\n            <div className={cn('system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-base-hover')} onClick={onRenameConversation}>\n              <span className='grow'>{t('explore.sidebar.action.rename')}</span>\n            </div>\n          )}\n          {isShowDelete && (\n            <div className={cn('system-md-regular group flex cursor-pointer items-center space-x-1 rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-destructive-hover hover:text-text-destructive')} onClick={onDelete} >\n              <span className='grow'>{t('explore.sidebar.action.delete')}</span>\n            </div>\n          )}\n        </div>\n      </PortalToFollowElemContent>\n    </PortalToFollowElem>\n  )\n}\nexport default React.memo(Operation)\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;;;;;;;;AAFA;;;;;;;AAsBA,MAAM,YAAuB,CAAC,EAC5B,KAAK,EACL,QAAQ,EACR,SAAS,EACT,wBAAwB,EACxB,oBAAoB,EACpB,YAAY,EACZ,QAAQ,EACR,YAAY,cAAc,EAC3B;IACC,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,qBACE,qZAAC;QACC,MAAM;QACN,cAAc;QACd,WAAW;QACX,QAAQ;;0BAER,qZAAC;gBACC,SAAS,IAAM,QAAQ,CAAA,IAAK,CAAC;0BAE7B,cAAA,qZAAC;oBAAI,WAAW,GAAG,wGAAwG,QAAQ;;sCACjI,qZAAC;4BAA<PERSON>,WAAU;sCAAsB;;;;;;sCACrC,qZAAC;4BAAiB,WAAU;;;;;;;;;;;;;;;;;0BAGhC,qZAAC;gBAA0B,WAAU;0BACnC,cAAA,qZAAC;oBACC,WAAW;;sCAEX,qZAAC;4BAAI,WAAW,GAAG;4BAAsI,SAAS;sCAChK,cAAA,qZAAC;gCAAK,WAAU;0CAAQ,WAAW,EAAE,kCAAkC,EAAE;;;;;;;;;;;wBAE1E,0CACC,qZAAC;4BAAI,WAAW,GAAG;4BAAsI,SAAS;sCAChK,cAAA,qZAAC;gCAAK,WAAU;0CAAQ,EAAE;;;;;;;;;;;wBAG7B,8BACC,qZAAC;4BAAI,WAAW,GAAG;4BAA+K,SAAS;sCACzM,cAAA,qZAAC;gCAAK,WAAU;0CAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;qDACe,4WAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 1241, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/header/index.tsx"], "sourcesContent": ["import { useCallback, useState } from 'react'\nimport {\n  RiEditBoxLine,\n  RiLayoutRight2Line,\n  RiResetLeftLine,\n} from '@remixicon/react'\nimport { useTranslation } from 'react-i18next'\nimport {\n  useChatWithHistoryContext,\n} from '../context'\nimport Operation from './operation'\nimport ActionButton, { ActionButtonState } from '@/components/base/action-button'\nimport AppIcon from '@/components/base/app-icon'\nimport Tooltip from '@/components/base/tooltip'\nimport ViewFormDropdown from '@/components/base/chat/chat-with-history/inputs-form/view-form-dropdown'\nimport Confirm from '@/components/base/confirm'\nimport RenameModal from '@/components/base/chat/chat-with-history/sidebar/rename-modal'\nimport type { ConversationItem } from '@/models/share'\nimport cn from '@/utils/classnames'\n\nconst Header = () => {\n  const {\n    appData,\n    currentConversationId,\n    currentConversationItem,\n    inputsForms,\n    pinnedConversationList,\n    handlePinConversation,\n    handleUnpinConversation,\n    conversationRenaming,\n    handleRenameConversation,\n    handleDeleteConversation,\n    handleNewConversation,\n    sidebarCollapseState,\n    handleSidebarCollapse,\n    isResponding,\n  } = useChatWithHistoryContext()\n  const { t } = useTranslation()\n  const isSidebarCollapsed = sidebarCollapseState\n\n  const isPin = pinnedConversationList.some(item => item.id === currentConversationId)\n\n  const [showConfirm, setShowConfirm] = useState<ConversationItem | null>(null)\n  const [showRename, setShowRename] = useState<ConversationItem | null>(null)\n  const handleOperate = useCallback((type: string) => {\n    if (type === 'pin')\n      handlePinConversation(currentConversationId)\n\n    if (type === 'unpin')\n      handleUnpinConversation(currentConversationId)\n\n    if (type === 'delete')\n      setShowConfirm(currentConversationItem as any)\n\n    if (type === 'rename')\n      setShowRename(currentConversationItem as any)\n  }, [currentConversationId, currentConversationItem, handlePinConversation, handleUnpinConversation])\n  const handleCancelConfirm = useCallback(() => {\n    setShowConfirm(null)\n  }, [])\n  const handleDelete = useCallback(() => {\n    if (showConfirm)\n      handleDeleteConversation(showConfirm.id, { onSuccess: handleCancelConfirm })\n  }, [showConfirm, handleDeleteConversation, handleCancelConfirm])\n  const handleCancelRename = useCallback(() => {\n    setShowRename(null)\n  }, [])\n  const handleRename = useCallback((newName: string) => {\n    if (showRename)\n      handleRenameConversation(showRename.id, newName, { onSuccess: handleCancelRename })\n  }, [showRename, handleRenameConversation, handleCancelRename])\n\n  return (\n    <>\n      <div className='flex h-14 shrink-0 items-center justify-between p-3'>\n        <div className={cn('flex items-center gap-1 transition-all duration-200 ease-in-out', !isSidebarCollapsed && 'user-select-none opacity-0')}>\n          <ActionButton className={cn(!isSidebarCollapsed && 'cursor-default')} size='l' onClick={() => handleSidebarCollapse(false)}>\n            <RiLayoutRight2Line className='h-[18px] w-[18px]' />\n          </ActionButton>\n          <div className='mr-1 shrink-0'>\n            <AppIcon\n              size='large'\n              iconType={appData?.site.icon_type}\n              icon={appData?.site.icon}\n              background={appData?.site.icon_background}\n              imageUrl={appData?.site.icon_url}\n            />\n          </div>\n          {!currentConversationId && (\n            <div className={cn('system-md-semibold grow truncate text-text-secondary')}>{appData?.site.title}</div>\n          )}\n          {currentConversationId && currentConversationItem && isSidebarCollapsed && (\n            <>\n              <div className='p-1 text-divider-deep'>/</div>\n              <Operation\n                title={currentConversationItem?.name || ''}\n                isPinned={!!isPin}\n                togglePin={() => handleOperate(isPin ? 'unpin' : 'pin')}\n                isShowDelete\n                isShowRenameConversation\n                onRenameConversation={() => handleOperate('rename')}\n                onDelete={() => handleOperate('delete')}\n              />\n            </>\n          )}\n          <div className='flex items-center px-1'>\n            <div className='h-[14px] w-px bg-divider-regular'></div>\n          </div>\n          {isSidebarCollapsed && (\n            <Tooltip\n              disabled={!!currentConversationId}\n              popupContent={t('share.chat.newChatTip')}\n            >\n              <div>\n                <ActionButton\n                  size='l'\n                  state={(!currentConversationId || isResponding) ? ActionButtonState.Disabled : ActionButtonState.Default}\n                  disabled={!currentConversationId || isResponding}\n                  onClick={handleNewConversation}\n                >\n                  <RiEditBoxLine className='h-[18px] w-[18px]' />\n                </ActionButton>\n              </div>\n            </Tooltip>\n          )}\n        </div>\n        <div className='flex items-center gap-1'>\n          {currentConversationId && (\n            <Tooltip\n              popupContent={t('share.chat.resetChat')}\n            >\n              <ActionButton size='l' onClick={handleNewConversation}>\n                <RiResetLeftLine className='h-[18px] w-[18px]' />\n              </ActionButton>\n            </Tooltip>\n          )}\n          {currentConversationId && inputsForms.length > 0 && (\n            <ViewFormDropdown />\n          )}\n        </div>\n      </div>\n      {!!showConfirm && (\n        <Confirm\n          title={t('share.chat.deleteConversation.title')}\n          content={t('share.chat.deleteConversation.content') || ''}\n          isShow\n          onCancel={handleCancelConfirm}\n          onConfirm={handleDelete}\n        />\n      )}\n      {showRename && (\n        <RenameModal\n          isShow\n          onClose={handleCancelRename}\n          saveLoading={conversationRenaming}\n          name={showRename?.name || ''}\n          onSave={handleRename}\n        />\n      )}\n    </>\n  )\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAAA;;;;;;;;;;;AAOA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,MAAM,SAAS;IACb,MAAM,EACJ,OAAO,EACP,qBAAqB,EACrB,uBAAuB,EACvB,WAAW,EACX,sBAAsB,EACtB,qBAAqB,EACrB,uBAAuB,EACvB,oBAAoB,EACpB,wBAAwB,EACxB,wBAAwB,EACxB,qBAAqB,EACrB,oBAAoB,EACpB,qBAAqB,EACrB,YAAY,EACb,GAAG,CAAA,GAAA,wKAAA,CAAA,4BAAyB,AAAD;IAC5B,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,qBAAqB;IAE3B,MAAM,QAAQ,uBAAuB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAE9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAA2B;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAA2B;IACtE,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,IAAI,SAAS,OACX,sBAAsB;QAExB,IAAI,SAAS,SACX,wBAAwB;QAE1B,IAAI,SAAS,UACX,eAAe;QAEjB,IAAI,SAAS,UACX,cAAc;IAClB,GAAG;QAAC;QAAuB;QAAyB;QAAuB;KAAwB;IACnG,MAAM,sBAAsB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACtC,eAAe;IACjB,GAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,aACF,yBAAyB,YAAY,EAAE,EAAE;YAAE,WAAW;QAAoB;IAC9E,GAAG;QAAC;QAAa;QAA0B;KAAoB;IAC/D,MAAM,qBAAqB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACrC,cAAc;IAChB,GAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI,YACF,yBAAyB,WAAW,EAAE,EAAE,SAAS;YAAE,WAAW;QAAmB;IACrF,GAAG;QAAC;QAAY;QAA0B;KAAmB;IAE7D,qBACE;;0BACE,qZAAC;gBAAI,WAAU;;kCACb,qZAAC;wBAAI,WAAW,GAAG,mEAAmE,CAAC,sBAAsB;;0CAC3G,qZAAC;gCAAa,WAAW,GAAG,CAAC,sBAAsB;gCAAmB,MAAK;gCAAI,SAAS,IAAM,sBAAsB;0CAClH,cAAA,qZAAC;oCAAmB,WAAU;;;;;;;;;;;0CAEhC,qZAAC;gCAAI,WAAU;0CACb,cAAA,qZAAC;oCACC,MAAK;oCACL,UAAU,SAAS,KAAK;oCACxB,MAAM,SAAS,KAAK;oCACpB,YAAY,SAAS,KAAK;oCAC1B,UAAU,SAAS,KAAK;;;;;;;;;;;4BAG3B,CAAC,uCACA,qZAAC;gCAAI,WAAW,GAAG;0CAA0D,SAAS,KAAK;;;;;;4BAE5F,yBAAyB,2BAA2B,oCACnD;;kDACE,qZAAC;wCAAI,WAAU;kDAAwB;;;;;;kDACvC,qZAAC,oLAAA,CAAA,UAAS;wCACR,OAAO,yBAAyB,QAAQ;wCACxC,UAAU,CAAC,CAAC;wCACZ,WAAW,IAAM,cAAc,QAAQ,UAAU;wCACjD,YAAY;wCACZ,wBAAwB;wCACxB,sBAAsB,IAAM,cAAc;wCAC1C,UAAU,IAAM,cAAc;;;;;;;;0CAIpC,qZAAC;gCAAI,WAAU;0CACb,cAAA,qZAAC;oCAAI,WAAU;;;;;;;;;;;4BAEhB,oCACC,qZAAC;gCACC,UAAU,CAAC,CAAC;gCACZ,cAAc,EAAE;0CAEhB,cAAA,qZAAC;8CACC,cAAA,qZAAC;wCACC,MAAK;wCACL,OAAO,AAAC,CAAC,yBAAyB,eAAgB,kBAAkB,QAAQ,GAAG,kBAAkB,OAAO;wCACxG,UAAU,CAAC,yBAAyB;wCACpC,SAAS;kDAET,cAAA,qZAAC;4CAAc,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnC,qZAAC;wBAAI,WAAU;;4BACZ,uCACC,qZAAC;gCACC,cAAc,EAAE;0CAEhB,cAAA,qZAAC;oCAAa,MAAK;oCAAI,SAAS;8CAC9B,cAAA,qZAAC;wCAAgB,WAAU;;;;;;;;;;;;;;;;4BAIhC,yBAAyB,YAAY,MAAM,GAAG,mBAC7C,qZAAC;;;;;;;;;;;;;;;;;YAIN,CAAC,CAAC,6BACD,qZAAC;gBACC,OAAO,EAAE;gBACT,SAAS,EAAE,4CAA4C;gBACvD,MAAM;gBACN,UAAU;gBACV,WAAW;;;;;;YAGd,4BACC,qZAAC;gBACC,MAAM;gBACN,SAAS;gBACT,aAAa;gBACb,MAAM,YAAY,QAAQ;gBAC1B,QAAQ;;;;;;;;AAKlB;uCAEe", "debugId": null}}, {"offset": {"line": 1548, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/header/mobile-operation-dropdown.tsx"], "sourcesContent": ["import { useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport {\n  RiMoreFill,\n} from '@remixicon/react'\nimport { PortalToFollowElem, PortalToFollowElemContent, PortalToFollowElemTrigger } from '@/components/base/portal-to-follow-elem'\nimport ActionButton, { ActionButtonState } from '@/components/base/action-button'\n\ntype Props = {\n  handleResetChat: () => void\n  handleViewChatSettings: () => void\n}\n\nconst MobileOperationDropdown = ({\n  handleResetChat,\n  handleViewChatSettings,\n}: Props) => {\n  const { t } = useTranslation()\n  const [open, setOpen] = useState(false)\n\n  return (\n    <PortalToFollowElem\n      open={open}\n      onOpenChange={setOpen}\n      placement='bottom-end'\n      offset={{\n        mainAxis: 4,\n        crossAxis: -4,\n      }}\n    >\n      <PortalToFollowElemTrigger\n        onClick={() => setOpen(v => !v)}\n      >\n        <ActionButton size='l' state={open ? ActionButtonState.Hover : ActionButtonState.Default}>\n          <RiMoreFill className='h-[18px] w-[18px]' />\n        </ActionButton>\n      </PortalToFollowElemTrigger>\n      <PortalToFollowElemContent className=\"z-40\">\n        <div\n          className={'min-w-[160px] rounded-xl border-[0.5px] border-components-panel-border bg-components-panel-bg-blur p-1 shadow-lg backdrop-blur-sm'}\n        >\n          <div className='system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-base-hover' onClick={handleResetChat}>\n            <span className='grow'>{t('share.chat.resetChat')}</span>\n          </div>\n          <div className='system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-base-hover' onClick={handleViewChatSettings}>\n            <span className='grow'>{t('share.chat.viewChatSettings')}</span>\n          </div>\n        </div>\n      </PortalToFollowElemContent>\n    </PortalToFollowElem>\n\n  )\n}\n\nexport default MobileOperationDropdown\n"], "names": [], "mappings": ";;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAM,0BAA0B,CAAC,EAC/B,eAAe,EACf,sBAAsB,EAChB;IACN,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,qBACE,qZAAC;QACC,MAAM;QACN,cAAc;QACd,WAAU;QACV,QAAQ;YACN,UAAU;YACV,WAAW,CAAC;QACd;;0BAEA,qZAAC;gBACC,SAAS,IAAM,QAAQ,CAAA,IAAK,CAAC;0BAE7B,cAAA,qZAAC;oBAAa,MAAK;oBAAI,OAAO,OAAO,kBAAkB,KAAK,GAAG,kBAAkB,OAAO;8BACtF,cAAA,qZAAC;wBAAW,WAAU;;;;;;;;;;;;;;;;0BAG1B,qZAAC;gBAA0B,WAAU;0BACnC,cAAA,qZAAC;oBACC,WAAW;;sCAEX,qZAAC;4BAAI,WAAU;4BAAoI,SAAS;sCAC1J,cAAA,qZAAC;gCAAK,WAAU;0CAAQ,EAAE;;;;;;;;;;;sCAE5B,qZAAC;4BAAI,WAAU;4BAAoI,SAAS;sCAC1J,cAAA,qZAAC;gCAAK,WAAU;0CAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;uCAEe", "debugId": null}}, {"offset": {"line": 1675, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/header-in-mobile.tsx"], "sourcesContent": ["import { useCallback, useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport {\n  RiMenuLine,\n} from '@remixicon/react'\nimport { useChatWithHistoryContext } from './context'\nimport Operation from './header/operation'\nimport Sidebar from './sidebar'\nimport MobileOperationDropdown from './header/mobile-operation-dropdown'\nimport styles from './index.module.css'\nimport AppIcon from '@/components/base/app-icon'\nimport ActionButton from '@/components/base/action-button'\nimport { Message3Fill } from '@/components/base/icons/src/public/other'\nimport InputsFormContent from '@/components/base/chat/chat-with-history/inputs-form/content'\nimport Confirm from '@/components/base/confirm'\nimport RenameModal from '@/components/base/chat/chat-with-history/sidebar/rename-modal'\nimport type { ConversationItem } from '@/models/share'\n\nconst LarkNewChatIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" className={className}>\n    <path d=\"M10,13.3333333 L10,10 M10,10 L10,6.66666667 M10,10 L6.66666667,10 M10,10 L13.3333333,10 M10.0001111,20 C8.18333333,20 6.47971111,19.5154444 5.01138889,18.6686667 C4.86633333,18.585 4.79371111,18.5431111 4.72547778,18.5243333 C4.66197778,18.5067778 4.60527778,18.5007778 4.53954444,18.5053333 C4.46947778,18.5101111 4.397,18.5342222 4.25287778,18.5823333 L1.68674444,19.4376667 L1.68472222,19.4385556 C1.14324444,19.6191111 0.872,19.7095556 0.691733333,19.6452222 C0.534666667,19.5892222 0.410877778,19.4652222 0.354866667,19.3082222 C0.290633333,19.128 0.380755556,18.8576667 0.560988889,18.317 L0.562066667,18.3136667 L1.41631111,15.7508889 L1.41834444,15.7455556 C1.46595556,15.6026667 1.49006667,15.5303333 1.49483333,15.4606667 C1.49933333,15.3948889 1.49334444,15.3377778 1.47578889,15.2743333 C1.45717778,15.207 1.41608889,15.1356667 1.3346,14.9944444 L1.33132222,14.9887778 C0.484488889,13.5204444 0,11.8167778 0,10 C0,4.47715556 4.47715556,0 10,0 C15.5228889,0 20,4.47715556 20,10 C20,15.5228889 15.523,20 10.0001111,20 Z\" transform=\"translate(2 2)\" stroke=\"#434B5B\" strokeWidth=\"2\" fill=\"none\" fillRule=\"evenodd\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n  </svg>\n)\n\nconst HeaderInMobile = () => {\n  const {\n    appData,\n    currentConversationId,\n    currentConversationItem,\n    pinnedConversationList,\n    handleNewConversation,\n    handlePinConversation,\n    handleUnpinConversation,\n    handleDeleteConversation,\n    handleRenameConversation,\n    conversationRenaming,\n    embedSource,\n  } = useChatWithHistoryContext()\n  const { t } = useTranslation()\n  const isPin = pinnedConversationList.some(item => item.id === currentConversationId)\n  const [showConfirm, setShowConfirm] = useState<ConversationItem | null>(null)\n  const [showRename, setShowRename] = useState<ConversationItem | null>(null)\n  const handleOperate = useCallback((type: string) => {\n    if (type === 'pin')\n      handlePinConversation(currentConversationId)\n\n    if (type === 'unpin')\n      handleUnpinConversation(currentConversationId)\n\n    if (type === 'delete')\n      setShowConfirm(currentConversationItem as any)\n\n    if (type === 'rename')\n      setShowRename(currentConversationItem as any)\n  }, [currentConversationId, currentConversationItem, handlePinConversation, handleUnpinConversation])\n  const handleCancelConfirm = useCallback(() => {\n    setShowConfirm(null)\n  }, [])\n  const handleDelete = useCallback(() => {\n    if (showConfirm)\n      handleDeleteConversation(showConfirm.id, { onSuccess: handleCancelConfirm })\n  }, [showConfirm, handleDeleteConversation, handleCancelConfirm])\n  const handleCancelRename = useCallback(() => {\n    setShowRename(null)\n  }, [])\n  const handleRename = useCallback((newName: string) => {\n    if (showRename)\n      handleRenameConversation(showRename.id, newName, { onSuccess: handleCancelRename })\n  }, [showRename, handleRenameConversation, handleCancelRename])\n  const [showSidebar, setShowSidebar] = useState(false)\n  const [showChatSettings, setShowChatSettings] = useState(false)\n\n  return (\n    <>\n      <div className={`flex shrink-0 items-center gap-1 bg-mask-top2bottom-gray-50-to-transparent px-2 py-3 ${embedSource && 'h-[48px] border-none pt-[8px]'}`}>\n        <ActionButton size='l' className='shrink-0' onClick={() => setShowSidebar(true)}>\n          <RiMenuLine className='h-[18px] w-[18px]' />\n        </ActionButton>\n        <div className='flex grow items-center justify-center'>\n          {!currentConversationId && (\n            <>\n              {!embedSource && (\n                <AppIcon\n                  className='mr-2'\n                  size='tiny'\n                  icon={appData?.site.icon}\n                  iconType={appData?.site.icon_type}\n                  imageUrl={appData?.site.icon_url}\n                  background={appData?.site.icon_background}\n                />\n              )}\n              <div className={`system-md-semibold truncate text-text-secondary ${embedSource && 'text-xl !font-medium'} ${embedSource && styles.mobileNavTitle}`}>\n                {appData?.site.title}\n              </div>\n            </>\n          )}\n          {currentConversationId && (\n            <Operation\n              title={currentConversationItem?.name || ''}\n              isPinned={!!isPin}\n              togglePin={() => handleOperate(isPin ? 'unpin' : 'pin')}\n              isShowDelete\n              isShowRenameConversation\n              onRenameConversation={() => handleOperate('rename')}\n              onDelete={() => handleOperate('delete')}\n            />\n          )}\n        </div>\n        {embedSource\n          ? (\n            <div\n              className='flex h-8 w-8 shrink-0 items-center justify-center rounded-lg'\n              onClick={handleNewConversation}\n            >\n              <LarkNewChatIcon className=\"w-[22px]\" />\n            </div>\n          ) : (\n            <MobileOperationDropdown\n            handleResetChat={handleNewConversation}\n            handleViewChatSettings={() => setShowChatSettings(true)}\n          />\n        )}\n      </div>\n      {showSidebar && (\n        <div className='fixed inset-0 z-50 flex bg-background-overlay p-1'\n          style={{ backgroundColor: embedSource ? 'rgba(0,0,0,.7)' : '' }}\n          onClick={() => setShowSidebar(false)}\n        >\n          <div className='flex h-full w-[calc(100vw_-_40px)] rounded-xl bg-components-panel-bg shadow-lg backdrop-blur-sm' onClick={e => e.stopPropagation()}>\n            <Sidebar />\n          </div>\n        </div>\n      )}\n      {showChatSettings && (\n        <div className='fixed inset-0 z-50 flex justify-end bg-background-overlay p-1'\n          onClick={() => setShowChatSettings(false)}\n        >\n          <div className='flex h-full w-[calc(100vw_-_40px)] flex-col rounded-xl bg-components-panel-bg shadow-lg backdrop-blur-sm' onClick={e => e.stopPropagation()}>\n            <div className='flex items-center gap-3 rounded-t-2xl border-b border-divider-subtle px-4 py-3'>\n              <Message3Fill className='h-6 w-6 shrink-0' />\n              <div className='system-xl-semibold grow text-text-secondary'>{t('share.chat.chatSettingsTitle')}</div>\n            </div>\n            <div className='p-4'>\n              <InputsFormContent />\n            </div>\n          </div>\n        </div>\n      )}\n      {!!showConfirm && (\n        <Confirm\n          title={t('share.chat.deleteConversation.title')}\n          content={t('share.chat.deleteConversation.content') || ''}\n          isShow\n          onCancel={handleCancelConfirm}\n          onConfirm={handleDelete}\n        />\n      )}\n      {showRename && (\n        <RenameModal\n          isShow\n          onClose={handleCancelRename}\n          saveLoading={conversationRenaming}\n          name={showRename?.name || ''}\n          onSave={handleRename}\n        />\n      )}\n    </>\n  )\n}\n\nexport default HeaderInMobile\n"], "names": [], "mappings": ";;;;AAAA;;;;;;;;;;;AAKA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,MAAM,kBAAkB,CAAC,EAAE,SAAS,EAAyB,iBAC3D,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAK,GAAE;YAAwgC,WAAU;YAAiB,QAAO;YAAU,aAAY;YAAI,MAAK;YAAO,UAAS;YAAU,eAAc;YAAQ,gBAAe;;;;;;;;;;;AAIppC,MAAM,iBAAiB;IACrB,MAAM,EACJ,OAAO,EACP,qBAAqB,EACrB,uBAAuB,EACvB,sBAAsB,EACtB,qBAAqB,EACrB,qBAAqB,EACrB,uBAAuB,EACvB,wBAAwB,EACxB,wBAAwB,EACxB,oBAAoB,EACpB,WAAW,EACZ,GAAG,CAAA,GAAA,wKAAA,CAAA,4BAAyB,AAAD;IAC5B,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,QAAQ,uBAAuB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAA2B;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAA2B;IACtE,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,IAAI,SAAS,OACX,sBAAsB;QAExB,IAAI,SAAS,SACX,wBAAwB;QAE1B,IAAI,SAAS,UACX,eAAe;QAEjB,IAAI,SAAS,UACX,cAAc;IAClB,GAAG;QAAC;QAAuB;QAAyB;QAAuB;KAAwB;IACnG,MAAM,sBAAsB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACtC,eAAe;IACjB,GAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,aACF,yBAAyB,YAAY,EAAE,EAAE;YAAE,WAAW;QAAoB;IAC9E,GAAG;QAAC;QAAa;QAA0B;KAAoB;IAC/D,MAAM,qBAAqB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACrC,cAAc;IAChB,GAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI,YACF,yBAAyB,WAAW,EAAE,EAAE,SAAS;YAAE,WAAW;QAAmB;IACrF,GAAG;QAAC;QAAY;QAA0B;KAAmB;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE;;0BACE,qZAAC;gBAAI,WAAW,CAAC,qFAAqF,EAAE,eAAe,iCAAiC;;kCACtJ,qZAAC;wBAAa,MAAK;wBAAI,WAAU;wBAAW,SAAS,IAAM,eAAe;kCACxE,cAAA,qZAAC;4BAAW,WAAU;;;;;;;;;;;kCAExB,qZAAC;wBAAI,WAAU;;4BACZ,CAAC,uCACA;;oCACG,CAAC,6BACA,qZAAC;wCACC,WAAU;wCACV,MAAK;wCACL,MAAM,SAAS,KAAK;wCACpB,UAAU,SAAS,KAAK;wCACxB,UAAU,SAAS,KAAK;wCACxB,YAAY,SAAS,KAAK;;;;;;kDAG9B,qZAAC;wCAAI,WAAW,CAAC,gDAAgD,EAAE,eAAe,uBAAuB,CAAC,EAAE,eAAe,iLAAA,CAAA,UAAM,CAAC,cAAc,EAAE;kDAC/I,SAAS,KAAK;;;;;;;;4BAIpB,uCACC,qZAAC,oLAAA,CAAA,UAAS;gCACR,OAAO,yBAAyB,QAAQ;gCACxC,UAAU,CAAC,CAAC;gCACZ,WAAW,IAAM,cAAc,QAAQ,UAAU;gCACjD,YAAY;gCACZ,wBAAwB;gCACxB,sBAAsB,IAAM,cAAc;gCAC1C,UAAU,IAAM,cAAc;;;;;;;;;;;;oBAInC,4BAEG,qZAAC;wBACC,WAAU;wBACV,SAAS;kCAET,cAAA,qZAAC;4BAAgB,WAAU;;;;;;;;;;6CAG7B,qZAAC,0MAAA,CAAA,UAAuB;wBACxB,iBAAiB;wBACjB,wBAAwB,IAAM,oBAAoB;;;;;;;;;;;;YAIvD,6BACC,qZAAC;gBAAI,WAAU;gBACb,OAAO;oBAAE,iBAAiB,cAAc,mBAAmB;gBAAG;gBAC9D,SAAS,IAAM,eAAe;0BAE9B,cAAA,qZAAC;oBAAI,WAAU;oBAAkG,SAAS,CAAA,IAAK,EAAE,eAAe;8BAC9I,cAAA,qZAAC,iLAAA,CAAA,UAAO;;;;;;;;;;;;;;;YAIb,kCACC,qZAAC;gBAAI,WAAU;gBACb,SAAS,IAAM,oBAAoB;0BAEnC,cAAA,qZAAC;oBAAI,WAAU;oBAA2G,SAAS,CAAA,IAAK,EAAE,eAAe;;sCACvJ,qZAAC;4BAAI,WAAU;;8CACb,qZAAC;oCAAa,WAAU;;;;;;8CACxB,qZAAC;oCAAI,WAAU;8CAA+C,EAAE;;;;;;;;;;;;sCAElE,qZAAC;4BAAI,WAAU;sCACb,cAAA,qZAAC;;;;;;;;;;;;;;;;;;;;;YAKR,CAAC,CAAC,6BACD,qZAAC;gBACC,OAAO,EAAE;gBACT,SAAS,EAAE,4CAA4C;gBACvD,MAAM;gBACN,UAAU;gBACV,WAAW;;;;;;YAGd,4BACC,qZAAC;gBACC,MAAM;gBACN,SAAS;gBACT,aAAa;gBACb,MAAM,YAAY,QAAQ;gBAC1B,QAAQ;;;;;;;;AAKlB;uCAEe", "debugId": null}}, {"offset": {"line": 2011, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/embedded-chatbot/theme/utils.ts"], "sourcesContent": ["export function hexToRGBA(hex: string, opacity: number): string {\n  hex = hex.replace('#', '')\n\n  const r = Number.parseInt(hex.slice(0, 2), 16)\n  const g = Number.parseInt(hex.slice(2, 4), 16)\n  const b = Number.parseInt(hex.slice(4, 6), 16)\n\n  // Returning an RGB color object\n  return `rgba(${r},${g},${b},${opacity.toString()})`\n}\n\n/**\n * Since strings cannot be directly assigned to the 'style' attribute in JSX,\n * this method transforms the string into an object representation of the styles.\n */\nexport function CssTransform(cssString: string): object {\n  if (cssString.length === 0)\n    return {}\n\n  const style: object = {}\n  const propertyValuePairs = cssString.split(';')\n  for (const pair of propertyValuePairs) {\n    if (pair.trim().length > 0) {\n      const [property, value] = pair.split(':')\n      Object.assign(style, { [property.trim()]: value.trim() })\n    }\n  }\n  return style\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,UAAU,GAAW,EAAE,OAAe;IACpD,MAAM,IAAI,OAAO,CAAC,KAAK;IAEvB,MAAM,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI;IAC3C,MAAM,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI;IAC3C,MAAM,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI;IAE3C,gCAAgC;IAChC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,QAAQ,QAAQ,GAAG,CAAC,CAAC;AACrD;AAMO,SAAS,aAAa,SAAiB;IAC5C,IAAI,UAAU,MAAM,KAAK,GACvB,OAAO,CAAC;IAEV,MAAM,QAAgB,CAAC;IACvB,MAAM,qBAAqB,UAAU,KAAK,CAAC;IAC3C,KAAK,MAAM,QAAQ,mBAAoB;QACrC,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG;YAC1B,MAAM,CAAC,UAAU,MAAM,GAAG,KAAK,KAAK,CAAC;YACrC,OAAO,MAAM,CAAC,OAAO;gBAAE,CAAC,SAAS,IAAI,GAAG,EAAE,MAAM,IAAI;YAAG;QACzD;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2048, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/line/arrows/ArrowNarrowLeft.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './ArrowNarrowLeft.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'ArrowNarrowLeft'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;;;;;;;;;AAIA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,qZAAC;QAAU,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,gKAAA,CAAA,UAAI;;;;;;AAE9C,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 2085, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/line/arrows/ArrowUpRight.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './ArrowUpRight.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'ArrowUpRight'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;;;;;;;;;AAIA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,qZAAC;QAAU,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,6JAAA,CAAA,UAAI;;;;;;AAE9C,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 2122, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/line/arrows/ChevronDownDouble.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './ChevronDownDouble.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'ChevronDownDouble'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;;;;;;;;;AAIA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,qZAAC;QAAU,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,kKAAA,CAAA,UAAI;;;;;;AAE9C,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 2159, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/line/arrows/ChevronRight.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './ChevronRight.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'ChevronRight'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;;;;;;;;;AAIA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,qZAAC;QAAU,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,6JAAA,CAAA,UAAI;;;;;;AAE9C,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 2196, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/line/arrows/ChevronSelectorVertical.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './ChevronSelectorVertical.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'ChevronSelectorVertical'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;;;;;;;;;AAIA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,qZAAC;QAAU,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,wKAAA,CAAA,UAAI;;;;;;AAE9C,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 2233, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/line/arrows/RefreshCcw01.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './RefreshCcw01.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'RefreshCcw01'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;;;;;;;;;AAIA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,qZAAC;QAAU,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,6JAAA,CAAA,UAAI;;;;;;AAE9C,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 2270, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/line/arrows/RefreshCw05.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './RefreshCw05.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'RefreshCw05'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;;;;;;;;;AAIA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,qZAAC;QAAU,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,4JAAA,CAAA,UAAI;;;;;;AAE9C,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 2307, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/line/arrows/ReverseLeft.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './ReverseLeft.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'ReverseLeft'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;;;;;;;;;AAIA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,qZAAC;QAAU,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,4JAAA,CAAA,UAAI;;;;;;AAE9C,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 2339, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/line/arrows/index.ts"], "sourcesContent": ["export { default as ArrowNarrowLeft } from './ArrowNarrowLeft'\nexport { default as ArrowUpRight } from './ArrowUpRight'\nexport { default as ChevronDownDouble } from './ChevronDownDouble'\nexport { default as ChevronRight } from './ChevronRight'\nexport { default as ChevronSelectorVertical } from './ChevronSelectorVertical'\nexport { default as RefreshCcw01 } from './RefreshCcw01'\nexport { default as RefreshCw05 } from './RefreshCw05'\nexport { default as ReverseLeft } from './ReverseLeft'\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2388, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/content-switch.tsx"], "sourcesContent": ["import { ChevronRight } from '../../icons/src/vender/line/arrows'\n\nexport default function ContentSwitch({\n  count,\n  currentIndex,\n  prevDisabled,\n  nextDisabled,\n  switchSibling,\n}: {\n  count?: number\n  currentIndex?: number\n  prevDisabled: boolean\n  nextDisabled: boolean\n  switchSibling: (direction: 'prev' | 'next') => void\n}) {\n  return (\n    count && count > 1 && currentIndex !== undefined && (\n      <div className=\"flex items-center justify-center pt-3.5 text-sm\">\n        <button\n          className={`${prevDisabled ? 'opacity-30' : 'opacity-100'}`}\n          disabled={prevDisabled}\n          onClick={() => !prevDisabled && switchSibling('prev')}\n        >\n          <ChevronRight className=\"h-[14px] w-[14px] rotate-180 text-text-primary\" />\n        </button>\n        <span className=\"px-2 text-xs text-text-primary\">\n          {currentIndex + 1} / {count}\n        </span>\n        <button\n          className={`${nextDisabled ? 'opacity-30' : 'opacity-100'}`}\n          disabled={nextDisabled}\n          onClick={() => !nextDisabled && switchSibling('next')}\n        >\n          <ChevronRight className=\"h-[14px] w-[14px] text-text-primary\" />\n        </button>\n      </div>\n    )\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEe,SAAS,cAAc,EACpC,KAAK,EACL,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,aAAa,EAOd;IACC,OACE,SAAS,QAAQ,KAAK,iBAAiB,2BACrC,qZAAC;QAAI,WAAU;;0BACb,qZAAC;gBACC,WAAW,GAAG,eAAe,eAAe,eAAe;gBAC3D,UAAU;gBACV,SAAS,IAAM,CAAC,gBAAgB,cAAc;0BAE9C,cAAA,qZAAC,iOAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;0BAE1B,qZAAC;gBAAK,WAAU;;oBACb,eAAe;oBAAE;oBAAI;;;;;;;0BAExB,qZAAC;gBACC,WAAW,GAAG,eAAe,eAAe,eAAe;gBAC3D,UAAU;gBACV,SAAS,IAAM,CAAC,gBAAgB,cAAc;0BAE9C,cAAA,qZAAC,iOAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKlC", "debugId": null}}, {"offset": {"line": 2457, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/action-button/index.tsx"], "sourcesContent": ["import type { CSSProperties } from 'react'\nimport React from 'react'\nimport { type VariantProps, cva } from 'class-variance-authority'\nimport classNames from '@/utils/classnames'\n\nenum ActionButtonState {\n  Destructive = 'destructive',\n  Active = 'active',\n  Disabled = 'disabled',\n  Default = '',\n  Hover = 'hover',\n}\n\nconst actionButtonVariants = cva(\n  'action-btn',\n  {\n    variants: {\n      size: {\n        xs: 'action-btn-xs',\n        m: 'action-btn-m',\n        l: 'action-btn-l',\n        xl: 'action-btn-xl',\n      },\n    },\n    defaultVariants: {\n      size: 'm',\n    },\n  },\n)\n\nexport type ActionButtonProps = {\n  size?: 'xs' | 's' | 'm' | 'l' | 'xl'\n  state?: ActionButtonState\n  styleCss?: CSSProperties\n} & React.ButtonHTMLAttributes<HTMLButtonElement> & VariantProps<typeof actionButtonVariants>\n\nfunction getActionButtonState(state: ActionButtonState) {\n  switch (state) {\n    case ActionButtonState.Destructive:\n      return 'action-btn-destructive'\n    case ActionButtonState.Active:\n      return 'action-btn-active'\n    case ActionButtonState.Disabled:\n      return 'action-btn-disabled'\n    case ActionButtonState.Hover:\n      return 'action-btn-hover'\n    default:\n      return ''\n  }\n}\n\nconst ActionButton = React.forwardRef<HTMLButtonElement, ActionButtonProps>(\n  ({ className, size, state = ActionButtonState.Default, styleCss, children, ...props }, ref) => {\n    return (\n      <button\n        type='button'\n        className={classNames(\n          actionButtonVariants({ className, size }),\n          getActionButtonState(state),\n        )}\n        ref={ref}\n        style={styleCss}\n        {...props}\n      >\n        {children}\n      </button>\n    )\n  },\n)\nActionButton.displayName = 'ActionButton'\n\nexport default ActionButton\nexport { ActionButton, ActionButtonState, actionButtonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;;;;;;;;;;AAGA,IAAA,AAAK,2CAAA;;;;;;WAAA;EAAA;AAQL,MAAM,uBAAuB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EAC7B,cACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,GAAG;YACH,GAAG;YACH,IAAI;QACN;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AASF,SAAS,qBAAqB,KAAwB;IACpD,OAAQ;QACN;YACE,OAAO;QACT;YACE,OAAO;QACT;YACE,OAAO;QACT;YACE,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,6BAAe,4WAAA,CAAA,UAAK,CAAC,UAAU,CACnC,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,UAAiC,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACrF,qBACE,qZAAC;QACC,MAAK;QACL,WAAW,WACT,qBAAqB;YAAE;YAAW;QAAK,IACvC,qBAAqB;QAEvB,KAAK;QACL,OAAO;QACN,GAAG,KAAK;kBAER;;;;;;AAGP;AAEF,aAAa,WAAW,GAAG;uCAEZ", "debugId": null}}, {"offset": {"line": 2536, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/toast/index.tsx"], "sourcesContent": ["'use client'\nimport type { ReactNode } from 'react'\nimport React, { useEffect, useState } from 'react'\nimport { createRoot } from 'react-dom/client'\nimport {\n  Ri<PERSON>lertFill,\n  RiCheckboxCircleFill,\n  RiCloseLine,\n  RiErrorWarningFill,\n  RiInformation2Fill,\n} from '@remixicon/react'\nimport { createContext, useContext } from 'use-context-selector'\nimport ActionButton from '@/components/base/action-button'\nimport classNames from '@/utils/classnames'\nimport { noop } from 'lodash-es'\n\nexport type IToastProps = {\n  type?: 'success' | 'error' | 'warning' | 'info'\n  size?: 'md' | 'sm'\n  duration?: number\n  message: string\n  children?: ReactNode\n  onClose?: () => void\n  className?: string\n  customComponent?: ReactNode\n}\ntype IToastContext = {\n  notify: (props: IToastProps) => void\n  close: () => void\n}\n\nexport const ToastContext = createContext<IToastContext>({} as IToastContext)\nexport const useToastContext = () => useContext(ToastContext)\nconst Toast = ({\n  type = 'info',\n  size = 'md',\n  message,\n  children,\n  className,\n  customComponent,\n}: IToastProps) => {\n  const { close } = useToastContext()\n  // sometimes message is react node array. Not handle it.\n  if (typeof message !== 'string')\n    return null\n\n  return <div className={classNames(\n    className,\n    'fixed w-[360px] rounded-xl my-4 mx-8 flex-grow z-[9999] overflow-hidden',\n    size === 'md' ? 'p-3' : 'p-2',\n    'border border-components-panel-border-subtle bg-components-panel-bg-blur shadow-sm',\n    'top-0',\n    'right-0',\n  )}>\n    <div className={`absolute inset-0 -z-10 opacity-40 ${\n      (type === 'success' && 'bg-toast-success-bg')\n      || (type === 'warning' && 'bg-toast-warning-bg')\n      || (type === 'error' && 'bg-toast-error-bg')\n      || (type === 'info' && 'bg-toast-info-bg')\n    }`}\n    />\n    <div className={`flex ${size === 'md' ? 'gap-1' : 'gap-0.5'}`}>\n      <div className={`flex items-center justify-center ${size === 'md' ? 'p-0.5' : 'p-1'}`}>\n        {type === 'success' && <RiCheckboxCircleFill className={`${size === 'md' ? 'h-5 w-5' : 'h-4 w-4'} text-text-success`} aria-hidden=\"true\" />}\n        {type === 'error' && <RiErrorWarningFill className={`${size === 'md' ? 'h-5 w-5' : 'h-4 w-4'} text-text-destructive`} aria-hidden=\"true\" />}\n        {type === 'warning' && <RiAlertFill className={`${size === 'md' ? 'h-5 w-5' : 'h-4 w-4'} text-text-warning-secondary`} aria-hidden=\"true\" />}\n        {type === 'info' && <RiInformation2Fill className={`${size === 'md' ? 'h-5 w-5' : 'h-4 w-4'} text-text-accent`} aria-hidden=\"true\" />}\n      </div>\n      <div className={`flex py-1 ${size === 'md' ? 'px-1' : 'px-0.5'} grow flex-col items-start gap-1`}>\n        <div className='flex items-center gap-1'>\n          <div className='system-sm-semibold text-text-primary [word-break:break-word]'>{message}</div>\n          {customComponent}\n        </div>\n        {children && <div className='system-xs-regular text-text-secondary'>\n          {children}\n        </div>\n        }\n      </div>\n      {close\n        && (<ActionButton className='z-[1000]' onClick={close}>\n          <RiCloseLine className='h-4 w-4 shrink-0 text-text-tertiary' />\n        </ActionButton>)\n      }\n    </div>\n  </div>\n}\n\nexport const ToastProvider = ({\n  children,\n}: {\n  children: ReactNode\n}) => {\n  const placeholder: IToastProps = {\n    type: 'info',\n    message: 'Toast message',\n    duration: 6000,\n  }\n  const [params, setParams] = React.useState<IToastProps>(placeholder)\n  const defaultDuring = (params.type === 'success' || params.type === 'info') ? 3000 : 6000\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    if (mounted) {\n      setTimeout(() => {\n        setMounted(false)\n      }, params.duration || defaultDuring)\n    }\n  }, [defaultDuring, mounted, params.duration])\n\n  return <ToastContext.Provider value={{\n    notify: (props) => {\n      setMounted(true)\n      setParams(props)\n    },\n    close: () => setMounted(false),\n  }}>\n    {mounted && <Toast {...params} />}\n    {children}\n  </ToastContext.Provider>\n}\n\nToast.notify = ({\n  type,\n  size = 'md',\n  message,\n  duration,\n  className,\n  customComponent,\n  onClose,\n}: Pick<IToastProps, 'type' | 'size' | 'message' | 'duration' | 'className' | 'customComponent' | 'onClose'>) => {\n  const defaultDuring = (type === 'success' || type === 'info') ? 3000 : 6000\n  if (typeof window === 'object') {\n    const holder = document.createElement('div')\n    const root = createRoot(holder)\n\n    root.render(\n      <ToastContext.Provider value={{\n        notify: noop,\n        close: () => {\n          if (holder) {\n            root.unmount()\n            holder.remove()\n          }\n          onClose?.()\n        },\n      }}>\n        <Toast type={type} size={size} message={message} duration={duration} className={className} customComponent={customComponent} />\n      </ToastContext.Provider>,\n    )\n    document.body.appendChild(holder)\n    setTimeout(() => {\n      if (holder) {\n        root.unmount()\n        holder.remove()\n      }\n      onClose?.()\n    }, duration || defaultDuring)\n  }\n}\n\nexport default Toast\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;;;;;;;;;AA+BO,MAAM,eAAe,cAA6B,CAAC;AACnD,MAAM,kBAAkB,IAAM,WAAW;AAChD,MAAM,QAAQ,CAAC,EACb,OAAO,MAAM,EACb,OAAO,IAAI,EACX,OAAO,EACP,QAAQ,EACR,SAAS,EACT,eAAe,EACH;IACZ,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,wDAAwD;IACxD,IAAI,OAAO,YAAY,UACrB,OAAO;IAET,qBAAO,qZAAC;QAAI,WAAW,WACrB,WACA,2EACA,SAAS,OAAO,QAAQ,OACxB,sFACA,SACA;;0BAEA,qZAAC;gBAAI,WAAW,CAAC,kCAAkC,EACjD,AAAC,SAAS,aAAa,yBACnB,SAAS,aAAa,yBACtB,SAAS,WAAW,uBACpB,SAAS,UAAU,oBACvB;;;;;;0BAEF,qZAAC;gBAAI,WAAW,CAAC,KAAK,EAAE,SAAS,OAAO,UAAU,WAAW;;kCAC3D,qZAAC;wBAAI,WAAW,CAAC,iCAAiC,EAAE,SAAS,OAAO,UAAU,OAAO;;4BAClF,SAAS,2BAAa,qZAAC;gCAAqB,WAAW,GAAG,SAAS,OAAO,YAAY,UAAU,kBAAkB,CAAC;gCAAE,eAAY;;;;;;4BACjI,SAAS,yBAAW,qZAAC;gCAAmB,WAAW,GAAG,SAAS,OAAO,YAAY,UAAU,sBAAsB,CAAC;gCAAE,eAAY;;;;;;4BACjI,SAAS,2BAAa,qZAAC;gCAAY,WAAW,GAAG,SAAS,OAAO,YAAY,UAAU,4BAA4B,CAAC;gCAAE,eAAY;;;;;;4BAClI,SAAS,wBAAU,qZAAC;gCAAmB,WAAW,GAAG,SAAS,OAAO,YAAY,UAAU,iBAAiB,CAAC;gCAAE,eAAY;;;;;;;;;;;;kCAE9H,qZAAC;wBAAI,WAAW,CAAC,UAAU,EAAE,SAAS,OAAO,SAAS,SAAS,gCAAgC,CAAC;;0CAC9F,qZAAC;gCAAI,WAAU;;kDACb,qZAAC;wCAAI,WAAU;kDAAgE;;;;;;oCAC9E;;;;;;;4BAEF,0BAAY,qZAAC;gCAAI,WAAU;0CACzB;;;;;;;;;;;;oBAIJ,uBACK,qZAAC;wBAAa,WAAU;wBAAW,SAAS;kCAC9C,cAAA,qZAAC;4BAAY,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKjC;AAEO,MAAM,gBAAgB,CAAC,EAC5B,QAAQ,EAGT;IACC,MAAM,cAA2B;QAC/B,MAAM;QACN,SAAS;QACT,UAAU;IACZ;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,4WAAA,CAAA,UAAK,CAAC,QAAQ,CAAc;IACxD,MAAM,gBAAgB,AAAC,OAAO,IAAI,KAAK,aAAa,OAAO,IAAI,KAAK,SAAU,OAAO;IACrF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,WAAW;gBACT,WAAW;YACb,GAAG,OAAO,QAAQ,IAAI;QACxB;IACF,GAAG;QAAC;QAAe;QAAS,OAAO,QAAQ;KAAC;IAE5C,qBAAO,qZAAC,aAAa,QAAQ;QAAC,OAAO;YACnC,QAAQ,CAAC;gBACP,WAAW;gBACX,UAAU;YACZ;YACA,OAAO,IAAM,WAAW;QAC1B;;YACG,yBAAW,qZAAC;gBAAO,GAAG,MAAM;;;;;;YAC5B;;;;;;;AAEL;AAEA,MAAM,MAAM,GAAG,CAAC,EACd,IAAI,EACJ,OAAO,IAAI,EACX,OAAO,EACP,QAAQ,EACR,SAAS,EACT,eAAe,EACf,OAAO,EACmG;IAC1G,MAAM,gBAAgB,AAAC,SAAS,aAAa,SAAS,SAAU,OAAO;IACvE,uCAAgC;;IA0BhC;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 2759, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/context.tsx"], "sourcesContent": ["'use client'\n\nimport type { ReactNode } from 'react'\nimport { createContext, useContext } from 'use-context-selector'\nimport type { ChatProps } from './index'\n\nexport type ChatContextValue = Pick<ChatProps, 'config'\n  | 'isResponding'\n  | 'chatList'\n  | 'showPromptLog'\n  | 'questionIcon'\n  | 'answerIcon'\n  | 'onSend'\n  | 'onRegenerate'\n  | 'onAnnotationEdited'\n  | 'onAnnotationAdded'\n  | 'onAnnotationRemoved'\n  | 'onFeedback'\n>\n\nconst ChatContext = createContext<ChatContextValue>({\n  chatList: [],\n})\n\ntype ChatContextProviderProps = {\n  children: ReactNode\n} & ChatContextValue\n\nexport const ChatContextProvider = ({\n  children,\n  config,\n  isResponding,\n  chatList,\n  showPromptLog,\n  questionIcon,\n  answerIcon,\n  onSend,\n  onRegenerate,\n  onAnnotationEdited,\n  onAnnotationAdded,\n  onAnnotationRemoved,\n  onFeedback,\n}: ChatContextProviderProps) => {\n  return (\n    <ChatContext.Provider value={{\n      config,\n      isResponding,\n      chatList: chatList || [],\n      showPromptLog,\n      questionIcon,\n      answerIcon,\n      onSend,\n      onRegenerate,\n      onAnnotationEdited,\n      onAnnotationAdded,\n      onAnnotationRemoved,\n      onFeedback,\n    }}>\n      {children}\n    </ChatContext.Provider>\n  )\n}\n\nexport const useChatContext = () => useContext(ChatContext)\n\nexport default ChatContext\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;AAoBA,MAAM,cAAc,cAAgC;IAClD,UAAU,EAAE;AACd;AAMO,MAAM,sBAAsB,CAAC,EAClC,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,YAAY,EACZ,UAAU,EACV,MAAM,EACN,YAAY,EACZ,kBAAkB,EAClB,iBAAiB,EACjB,mBAAmB,EACnB,UAAU,EACe;IACzB,qBACE,qZAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA,UAAU,YAAY,EAAE;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,MAAM,iBAAiB,IAAM,WAAW;uCAEhC", "debugId": null}}, {"offset": {"line": 2807, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/question.tsx"], "sourcesContent": ["import type {\n  FC,\n  ReactNode,\n} from 'react'\nimport {\n  memo,\n  useCallback,\n  useState,\n} from 'react'\nimport type { ChatItem } from '../types'\nimport type { Theme } from '../embedded-chatbot/theme/theme-context'\nimport { CssTransform } from '../embedded-chatbot/theme/utils'\nimport ContentSwitch from './content-switch'\nimport { useChatWithHistoryContext } from '../chat-with-history/context'\nimport { User } from '@/components/base/icons/src/public/avatar'\nimport { Markdown } from '@/components/base/markdown'\nimport { FileList } from '@/components/base/file-uploader'\nimport ActionButton from '../../action-button'\nimport { RiClipboardLine, RiEditLine } from '@remixicon/react'\nimport Toast from '../../toast'\nimport copy from 'copy-to-clipboard'\nimport { useTranslation } from 'react-i18next'\nimport cn from '@/utils/classnames'\nimport Textarea from 'react-textarea-autosize'\nimport Button from '../../button'\nimport { useChatContext } from './context'\nimport type { EmbedSource } from '@/models/share'\n\ntype QuestionProps = {\n  item: ChatItem\n  questionIcon?: ReactNode\n  theme: Theme | null | undefined\n  switchSibling?: (siblingMessageId: string) => void\n  avatar?: string\n  embedSource?: EmbedSource\n  userName?: string\n}\n\nconst backgroundColor = [['#7d67ff', '#99c2ff'], ['#5099ff', '#c9d8ff'], ['#7d67ff', '#f0e7ff'], ['#5099ff', '#e6e2ff'], ['#a3b9ff', '#99c2ff']]\n\nconst Question: FC<QuestionProps> = ({\n  item,\n  questionIcon,\n  theme,\n  switchSibling,\n  avatar,\n  embedSource,\n  userName,\n}) => {\n  const { t } = useTranslation()\n\n  const {\n    content,\n    message_files,\n  } = item\n  const { appData } = useChatWithHistoryContext()\n  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}\n  const questionBgGradientFrom = chatPageConfigData?.questionBgGradientFrom || '#BFB3FF'\n  const questionBgGradientTo = chatPageConfigData?.questionBgGradientTo || '#ACCEFF'\n\n  // 随机获取背景色\n  const randomIndex = localStorage.getItem('avatarBg') ?? Math.floor(Math.random() * 5)\n  if (localStorage.getItem('avatarBg') === null)\n    localStorage.setItem('avatarBg', String(randomIndex))\n\n  const avatarBgColor = backgroundColor[randomIndex as any]\n\n  const {\n    onRegenerate,\n  } = useChatContext()\n\n  const [isEditing, setIsEditing] = useState(false)\n  const [editedContent, setEditedContent] = useState(content)\n\n  const handleEdit = useCallback(() => {\n    setIsEditing(true)\n    setEditedContent(content)\n  }, [content])\n\n  const handleResend = useCallback(() => {\n    setIsEditing(false)\n    onRegenerate?.(item, { message: editedContent, files: message_files })\n  }, [editedContent, message_files, item, onRegenerate])\n\n  const handleCancelEditing = useCallback(() => {\n    setIsEditing(false)\n    setEditedContent(content)\n  }, [content])\n\n  const handleSwitchSibling = useCallback((direction: 'prev' | 'next') => {\n    if (direction === 'prev')\n      item.prevSibling && switchSibling?.(item.prevSibling)\n    else\n      item.nextSibling && switchSibling?.(item.nextSibling)\n  }, [switchSibling, item.prevSibling, item.nextSibling])\n\n  return (\n    <div className='mb-2 flex justify-end pl-14 last:mb-0'>\n      <div className={cn('group relative mr-4 flex max-w-full items-start', isEditing && 'flex-1')}>\n        <div className={cn('mr-2 gap-1', isEditing ? 'hidden' : 'flex')}>\n          <div className=\"\n            absolutegap-0.5 hidden rounded-[10px] border-[0.5px] border-components-actionbar-border\n            bg-components-actionbar-bg p-0.5 shadow-md backdrop-blur-sm group-hover:flex\n          \">\n            <ActionButton onClick={() => {\n              copy(content)\n              Toast.notify({ type: 'success', message: t('common.actionMsg.copySuccessfully') })\n            }}>\n              <RiClipboardLine className='h-4 w-4' />\n            </ActionButton>\n            <ActionButton onClick={handleEdit}>\n              <RiEditLine className='h-4 w-4' />\n            </ActionButton>\n          </div>\n        </div>\n        <div\n          className={`w-full rounded-2xl bg-[#D1E9FF]/50 px-4 py-3 text-sm text-gray-900 ${embedSource && 'rounded-[12px] rounded-br-[0px]'}`}\n          style={{\n            ...(theme?.chatBubbleColorStyle\n              ? CssTransform(theme.chatBubbleColorStyle)\n              : {}),\n            backgroundImage: embedSource\n              ? `linear-gradient(270deg, ${questionBgGradientFrom} 0%, ${questionBgGradientTo} 100%)`\n              : '',\n          }}\n        >\n          {\n            !!message_files?.length && (\n              <FileList\n                className='mb-2'\n                files={message_files}\n                showDeleteAction={false}\n                showDownloadAction={true}\n              />\n            )\n          }\n          { !isEditing\n            ? <Markdown content={content} />\n            : <div className=\"\n                flex flex-col gap-2 rounded-xl\n                border border-components-chat-input-border bg-components-panel-bg-blur p-[9px] shadow-md\n              \">\n              <div className=\"max-h-[158px] overflow-y-auto overflow-x-hidden\">\n                <Textarea\n                  className={cn(\n                    'body-lg-regular w-full p-1 leading-6 text-text-tertiary outline-none',\n                  )}\n                  autoFocus\n                  minRows={1}\n                  value={editedContent}\n                  onChange={e => setEditedContent(e.target.value)}\n                />\n              </div>\n              <div className=\"flex justify-end gap-2\">\n                <Button variant='ghost' onClick={handleCancelEditing}>{t('common.operation.cancel')}</Button>\n                <Button variant='primary' onClick={handleResend}>{t('common.chat.resend')}</Button>\n              </div>\n            </div> }\n          { !isEditing && <ContentSwitch\n            count={item.siblingCount}\n            currentIndex={item.siblingIndex}\n            prevDisabled={!item.prevSibling}\n            nextDisabled={!item.nextSibling}\n            switchSibling={handleSwitchSibling}\n          />}\n        </div>\n        <div className='mt-1 h-[18px]' />\n      </div>\n      <div className='h-10 w-10 shrink-0'>\n        {\n          questionIcon || (\n            <div className='h-full w-full rounded-full border-[0.5px] border-black/5'>\n              <div className=\"w-full h-full rounded-full border-[0.5px] border-black/5\">\n                {avatar\n                  ? (\n                    <img src={avatar} className=\"w-full h-full rounded-full\" />\n                  )\n                  : (embedSource && userName && userName.length >= 2)\n                    ? (\n                      <div\n                        className={'flex items-center justify-center w-full h-full rounded-full text-[14px] text-white'}\n                        style={{ backgroundImage: `linear-gradient(135deg, ${avatarBgColor[0]},${avatarBgColor[1]})` }}\n                      >\n                        {userName.slice(-2)}\n                      </div>\n                    )\n                    : (\n                      <User className=\"w-full h-full\" />\n                    )}\n              </div>\n            </div>\n          )\n        }\n      </div>\n    </div>\n  )\n}\n\nexport default memo(Question)\n"], "names": [], "mappings": ";;;;AAIA;AAOA;AACA;AACA;;;;;;;;;;;;;;;;AAIA;;;;;;AAEA;;;;;;;;;;;;;;;;;;;;;AAKA;AACA;;;;;;;;;;;;;;;;;;AAaA,MAAM,kBAAkB;IAAC;QAAC;QAAW;KAAU;IAAE;QAAC;QAAW;KAAU;IAAE;QAAC;QAAW;KAAU;IAAE;QAAC;QAAW;KAAU;IAAE;QAAC;QAAW;KAAU;CAAC;AAEhJ,MAAM,WAA8B,CAAC,EACnC,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,aAAa,EACb,MAAM,EACN,WAAW,EACX,QAAQ,EACT;IACC,MAAM,EAAE,CAAC,EAAE,GAAG;IAEd,MAAM,EACJ,OAAO,EACP,aAAa,EACd,GAAG;IACJ,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,4BAAyB,AAAD;IAC5C,MAAM,qBAAqB,SAAS,MAAM,iBAAiB,KAAK,KAAK,CAAC,SAAS,MAAM,kBAAkB,CAAC;IACxG,MAAM,yBAAyB,oBAAoB,0BAA0B;IAC7E,MAAM,uBAAuB,oBAAoB,wBAAwB;IAEzE,UAAU;IACV,MAAM,cAAc,aAAa,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;IACnF,IAAI,aAAa,OAAO,CAAC,gBAAgB,MACvC,aAAa,OAAO,CAAC,YAAY,OAAO;IAE1C,MAAM,gBAAgB,eAAe,CAAC,YAAmB;IAEzD,MAAM,EACJ,YAAY,EACb,GAAG,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,aAAa,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,aAAa;QACb,iBAAiB;IACnB,GAAG;QAAC;KAAQ;IAEZ,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,aAAa;QACb,eAAe,MAAM;YAAE,SAAS;YAAe,OAAO;QAAc;IACtE,GAAG;QAAC;QAAe;QAAe;QAAM;KAAa;IAErD,MAAM,sBAAsB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACtC,aAAa;QACb,iBAAiB;IACnB,GAAG;QAAC;KAAQ;IAEZ,MAAM,sBAAsB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,IAAI,cAAc,QAChB,KAAK,WAAW,IAAI,gBAAgB,KAAK,WAAW;aAEpD,KAAK,WAAW,IAAI,gBAAgB,KAAK,WAAW;IACxD,GAAG;QAAC;QAAe,KAAK,WAAW;QAAE,KAAK,WAAW;KAAC;IAEtD,qBACE,qZAAC;QAAI,WAAU;;0BACb,qZAAC;gBAAI,WAAW,GAAG,mDAAmD,aAAa;;kCACjF,qZAAC;wBAAI,WAAW,GAAG,cAAc,YAAY,WAAW;kCACtD,cAAA,qZAAC;4BAAI,WAAU;;8CAIb,qZAAC,uJAAA,CAAA,UAAY;oCAAC,SAAS;wCACrB,KAAK;wCACL,4IAAA,CAAA,UAAK,CAAC,MAAM,CAAC;4CAAE,MAAM;4CAAW,SAAS,EAAE;wCAAqC;oCAClF;8CACE,cAAA,qZAAC;wCAAgB,WAAU;;;;;;;;;;;8CAE7B,qZAAC,uJAAA,CAAA,UAAY;oCAAC,SAAS;8CACrB,cAAA,qZAAC;wCAAW,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAI5B,qZAAC;wBACC,WAAW,CAAC,mEAAmE,EAAE,eAAe,mCAAmC;wBACnI,OAAO;4BACL,GAAI,OAAO,uBACP,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE,MAAM,oBAAoB,IACvC,CAAC,CAAC;4BACN,iBAAiB,cACb,CAAC,wBAAwB,EAAE,uBAAuB,KAAK,EAAE,qBAAqB,MAAM,CAAC,GACrF;wBACN;;4BAGE,CAAC,CAAC,eAAe,wBACf,qZAAC;gCACC,WAAU;gCACV,OAAO;gCACP,kBAAkB;gCAClB,oBAAoB;;;;;;4BAIxB,CAAC,0BACC,qZAAC;gCAAS,SAAS;;;;;qDACnB,qZAAC;gCAAI,WAAU;;kDAIf,qZAAC;wCAAI,WAAU;kDACb,cAAA,qZAAC;4CACC,WAAW,GACT;4CAEF,SAAS;4CACT,SAAS;4CACT,OAAO;4CACP,UAAU,CAAA,IAAK,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;kDAGlD,qZAAC;wCAAI,WAAU;;0DACb,qZAAC,6IAAA,CAAA,UAAM;gDAAC,SAAQ;gDAAQ,SAAS;0DAAsB,EAAE;;;;;;0DACzD,qZAAC,6IAAA,CAAA,UAAM;gDAAC,SAAQ;gDAAU,SAAS;0DAAe,EAAE;;;;;;;;;;;;;;;;;;4BAGxD,CAAC,2BAAa,qZAAC,+JAAA,CAAA,UAAa;gCAC5B,OAAO,KAAK,YAAY;gCACxB,cAAc,KAAK,YAAY;gCAC/B,cAAc,CAAC,KAAK,WAAW;gCAC/B,cAAc,CAAC,KAAK,WAAW;gCAC/B,eAAe;;;;;;;;;;;;kCAGnB,qZAAC;wBAAI,WAAU;;;;;;;;;;;;0BAEjB,qZAAC;gBAAI,WAAU;0BAEX,8BACE,qZAAC;oBAAI,WAAU;8BACb,cAAA,qZAAC;wBAAI,WAAU;kCACZ,uBAEG,qZAAC;4BAAI,KAAK;4BAAQ,WAAU;;;;;mCAE5B,AAAC,eAAe,YAAY,SAAS,MAAM,IAAI,kBAE7C,qZAAC;4BACC,WAAW;4BACX,OAAO;gCAAE,iBAAiB,CAAC,wBAAwB,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;4BAAC;sCAE5F,SAAS,KAAK,CAAC,CAAC;;;;;iDAInB,qZAAC;4BAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;qDAEe,CAAA,GAAA,4WAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3166, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/u-const.tsx"], "sourcesContent": ["/** 展示相似问题的场景 */\nexport const SHOW_SUGGESTION_INTENT = ['自由对话', '查制度', '联网检索']\n\n/** 自由对话 */\nexport const INTENT_FREE_TALK = '自由对话'\n\n/** 查制度 */\nexport const INTENT_INSTITUTION = '查制度'\n\n/** 文档分析 */\nexport const INTENT_DOCUMENT_ANALYSIS = '文档分析'\n\n/** 包含日程关键字 */\nexport const INTENT_INCLUDES_SCHEDULE = '日程'\n\n/** 包含会议关键字 */\nexport const INTENT_INCLUDES_MEETING = '会议'\n\n/** 订会议 */\nexport const INTENT_MEETING = '订会议'\n\n/** PPT生成(内测) */\nexport const INTENT_PPT = 'PPT生成(内测)'\n\n/** 中建 */\nexport const LOCATION_ZJ = '中建'\n\n/** 会议默认地点 */\nexport const NEED_CREATE_MEETING = 'need_create_meeting'\n\n/** HAI地产大模型 */\nexport const LLM_QWQ = 'qwq'\n"], "names": [], "mappings": "AAAA,cAAc;;;;;;;;;;;;;AACP,MAAM,yBAAyB;IAAC;IAAQ;IAAO;CAAO;AAGtD,MAAM,mBAAmB;AAGzB,MAAM,qBAAqB;AAG3B,MAAM,2BAA2B;AAGjC,MAAM,2BAA2B;AAGjC,MAAM,0BAA0B;AAGhC,MAAM,iBAAiB;AAGvB,MAAM,aAAa;AAGnB,MAAM,cAAc;AAGpB,MAAM,sBAAsB;AAG5B,MAAM,UAAU", "debugId": null}}, {"offset": {"line": 3200, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/tooltip/index.tsx"], "sourcesContent": ["'use client'\nimport type { FC } from 'react'\nimport React, { useEffect, useRef, useState } from 'react'\nimport { useBoolean } from 'ahooks'\nimport type { OffsetOptions, Placement } from '@floating-ui/react'\nimport { RiQuestionLine } from '@remixicon/react'\nimport cn from '@/utils/classnames'\nimport { PortalToFollowElem, PortalToFollowElemContent, PortalToFollowElemTrigger } from '@/components/base/portal-to-follow-elem'\nexport type TooltipProps = {\n  position?: Placement\n  triggerMethod?: 'hover' | 'click'\n  triggerClassName?: string\n  triggerTestId?: string\n  disabled?: boolean\n  popupContent?: React.ReactNode\n  children?: React.ReactNode\n  popupClassName?: string\n  noDecoration?: boolean\n  offset?: OffsetOptions\n  needsDelay?: boolean\n  asChild?: boolean\n}\n\nconst Tooltip: FC<TooltipProps> = ({\n  position = 'top',\n  triggerMethod = 'hover',\n  triggerClassName,\n  triggerTestId,\n  disabled = false,\n  popupContent,\n  children,\n  popupClassName,\n  noDecoration,\n  offset,\n  asChild = true,\n  needsDelay = false,\n}) => {\n  const [open, setOpen] = useState(false)\n  const [isHoverPopup, {\n    setTrue: setHoverPopup,\n    setFalse: setNotHoverPopup,\n  }] = useBoolean(false)\n\n  const isHoverPopupRef = useRef(isHoverPopup)\n  useEffect(() => {\n    isHoverPopupRef.current = isHoverPopup\n  }, [isHoverPopup])\n\n  const [isHoverTrigger, {\n    setTrue: setHoverTrigger,\n    setFalse: setNotHoverTrigger,\n  }] = useBoolean(false)\n\n  const isHoverTriggerRef = useRef(isHoverTrigger)\n  useEffect(() => {\n    isHoverTriggerRef.current = isHoverTrigger\n  }, [isHoverTrigger])\n\n  const handleLeave = (isTrigger: boolean) => {\n    if (isTrigger)\n      setNotHoverTrigger()\n\n    else\n      setNotHoverPopup()\n\n    // give time to move to the popup\n    if (needsDelay) {\n      setTimeout(() => {\n        if (!isHoverPopupRef.current && !isHoverTriggerRef.current)\n          setOpen(false)\n      }, 500)\n    }\n    else {\n      setOpen(false)\n    }\n  }\n\n  return (\n    <PortalToFollowElem\n      open={disabled ? false : open}\n      onOpenChange={setOpen}\n      placement={position}\n      offset={offset ?? 8}\n    >\n      <PortalToFollowElemTrigger\n        onClick={() => triggerMethod === 'click' && setOpen(v => !v)}\n        onMouseEnter={() => {\n          if (triggerMethod === 'hover') {\n            setHoverTrigger()\n            setOpen(true)\n          }\n        }}\n        onMouseLeave={() => triggerMethod === 'hover' && handleLeave(true)}\n        asChild={asChild}\n      >\n        {children || <div data-testid={triggerTestId} className={triggerClassName || 'h-3.5 w-3.5 shrink-0 p-[1px]'}><RiQuestionLine className='h-full w-full text-text-quaternary hover:text-text-tertiary' /></div>}\n      </PortalToFollowElemTrigger>\n      <PortalToFollowElemContent\n        className=\"z-[9999]\"\n      >\n        {popupContent && (<div\n          className={cn(\n            !noDecoration && 'system-xs-regular relative break-words rounded-md bg-components-panel-bg px-3 py-2 text-text-tertiary shadow-lg',\n            popupClassName,\n          )}\n          onMouseEnter={() => triggerMethod === 'hover' && setHoverPopup()}\n          onMouseLeave={() => triggerMethod === 'hover' && handleLeave(false)}\n        >\n          {popupContent}\n        </div>)}\n      </PortalToFollowElemContent>\n    </PortalToFollowElem>\n  )\n}\n\nexport default React.memo(Tooltip)\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;;;;;;;;AAFA;;;;;;;AAuBA,MAAM,UAA4B,CAAC,EACjC,WAAW,KAAK,EAChB,gBAAgB,OAAO,EACvB,gBAAgB,EAChB,aAAa,EACb,WAAW,KAAK,EAChB,YAAY,EACZ,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,MAAM,EACN,UAAU,IAAI,EACd,aAAa,KAAK,EACnB;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,cAAc,EACnB,SAAS,aAAa,EACtB,UAAU,gBAAgB,EAC3B,CAAC,GAAG,WAAW;IAEhB,MAAM,kBAAkB,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB,OAAO,GAAG;IAC5B,GAAG;QAAC;KAAa;IAEjB,MAAM,CAAC,gBAAgB,EACrB,SAAS,eAAe,EACxB,UAAU,kBAAkB,EAC7B,CAAC,GAAG,WAAW;IAEhB,MAAM,oBAAoB,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;IACjC,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB,OAAO,GAAG;IAC9B,GAAG;QAAC;KAAe;IAEnB,MAAM,cAAc,CAAC;QACnB,IAAI,WACF;aAGA;QAEF,iCAAiC;QACjC,IAAI,YAAY;YACd,WAAW;gBACT,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,kBAAkB,OAAO,EACxD,QAAQ;YACZ,GAAG;QACL,OACK;YACH,QAAQ;QACV;IACF;IAEA,qBACE,qZAAC;QACC,MAAM,WAAW,QAAQ;QACzB,cAAc;QACd,WAAW;QACX,QAAQ,UAAU;;0BAElB,qZAAC;gBACC,SAAS,IAAM,kBAAkB,WAAW,QAAQ,CAAA,IAAK,CAAC;gBAC1D,cAAc;oBACZ,IAAI,kBAAkB,SAAS;wBAC7B;wBACA,QAAQ;oBACV;gBACF;gBACA,cAAc,IAAM,kBAAkB,WAAW,YAAY;gBAC7D,SAAS;0BAER,0BAAY,qZAAC;oBAAI,eAAa;oBAAe,WAAW,oBAAoB;8BAAgC,cAAA,qZAAC;wBAAe,WAAU;;;;;;;;;;;;;;;;0BAEzI,qZAAC;gBACC,WAAU;0BAET,8BAAiB,qZAAC;oBACjB,WAAW,GACT,CAAC,gBAAgB,mHACjB;oBAEF,cAAc,IAAM,kBAAkB,WAAW;oBACjD,cAAc,IAAM,kBAAkB,WAAW,YAAY;8BAE5D;;;;;;;;;;;;;;;;;AAKX;qDAEe,4WAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 3327, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/answer/operation.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport {\n  memo,\n  useMemo,\n  useState,\n} from 'react'\nimport { useTranslation } from 'react-i18next'\nimport {\n  RiClipboardLine,\n  RiResetLeftLine,\n  RiThumbDownLine,\n  RiThumbUpLine,\n} from '@remixicon/react'\nimport type { ChatItem } from '../../types'\nimport { useChatContext } from '../context'\nimport copy from 'copy-to-clipboard'\nimport Toast from '@/components/base/toast'\nimport AnnotationCtrlButton from '@/components/base/features/new-feature-panel/annotation-reply/annotation-ctrl-button'\nimport EditReplyModal from '@/components/app/annotation/edit-annotation-modal'\nimport Log from '@/components/base/chat/chat/log'\nimport ActionButton, { ActionButtonState } from '@/components/base/action-button'\nimport NewAudioButton from '@/components/base/new-audio-button'\nimport cn from '@/utils/classnames'\nimport type { EmbedSource } from '@/models/share'\nimport Tooltip from '../../../tooltip'\n\ntype OperationProps = {\n  item: ChatItem\n  question: string\n  index: number\n  showPromptLog?: boolean\n  maxSize: number\n  contentWidth: number\n  hasWorkflowProcess: boolean\n  noChatInput?: boolean\n  embedSource?: EmbedSource\n  isMobile?: boolean\n  className?: string\n}\nconst Operation: FC<OperationProps> = ({\n  item,\n  question,\n  index,\n  showPromptLog,\n  maxSize,\n  contentWidth,\n  hasWorkflowProcess,\n  noChatInput,\n  embedSource,\n  isMobile,\n  className,\n}) => {\n  const { t } = useTranslation()\n  const {\n    config,\n    onAnnotationAdded,\n    onAnnotationEdited,\n    onAnnotationRemoved,\n    onFeedback,\n    onRegenerate,\n  } = useChatContext()\n  const [isShowReplyModal, setIsShowReplyModal] = useState(false)\n  const {\n    id,\n    isOpeningStatement,\n    content: messageContent,\n    annotation,\n    feedback,\n    adminFeedback,\n    agent_thoughts,\n    isPrompt,\n  } = item\n\n  const hasAnnotation = !!annotation?.id\n  const isEmbedMobile = embedSource && isMobile\n  const [localFeedback, setLocalFeedback] = useState(config?.supportAnnotation ? adminFeedback : feedback)\n\n  const content = useMemo(() => {\n    if (agent_thoughts?.length)\n      return agent_thoughts.reduce((acc, cur) => acc + cur.thought, '')\n\n    return messageContent\n  }, [agent_thoughts, messageContent])\n\n  const handleFeedback = async (rating: 'like' | 'dislike' | null) => {\n    if (!config?.supportFeedback || !onFeedback)\n      return\n\n    await onFeedback?.(id, { rating })\n    setLocalFeedback({ rating })\n  }\n\n  const operationWidth = useMemo(() => {\n    let width = 0\n    if (!isOpeningStatement)\n      width += 26\n    if (!isOpeningStatement && showPromptLog)\n      width += 28 + 8\n    if (!isOpeningStatement && config?.text_to_speech?.enabled)\n      width += 26\n    if (!isOpeningStatement && config?.supportAnnotation && config?.annotation_reply?.enabled)\n      width += 26\n    if (config?.supportFeedback && !localFeedback?.rating && onFeedback && !isOpeningStatement)\n      width += 60 + 8\n    if (config?.supportFeedback && localFeedback?.rating && onFeedback && !isOpeningStatement)\n      width += 28 + 8\n    return width\n  }, [isOpeningStatement, showPromptLog, config?.text_to_speech?.enabled, config?.supportAnnotation, config?.annotation_reply?.enabled, config?.supportFeedback, localFeedback?.rating, onFeedback])\n\n  const positionRight = useMemo(() => operationWidth < maxSize, [operationWidth, maxSize])\n\n  // if (embedSource) {\n  //   return <div className='mt-[17px] flex items-center justify-end gap-[6px]'>\n  //     {\n  //       !isOpeningStatement && config?.text_to_speech?.enabled && (\n  //         <AudioBtn\n  //           id={id}\n  //           value={content}\n  //           noCache={false}\n  //           voice={config?.text_to_speech?.voice}\n  //           embedSource={embedSource}\n  //         />\n  //       )\n  //     }\n\n  //     {!isOpeningStatement && (\n  //       <CopyBtn\n  //         embedSource={embedSource}\n  //         isPlain={true}\n  //         value={content}\n  //       />\n  //     )}\n\n  //     <div className=\"h-[18px] w-[1px] bg-[#DFE4E8]\"></div>\n\n  //      {\n  //       !isOpeningStatement && <div className=\"flex\">\n  //         {\n  //           config?.supportFeedback && onFeedback && !isOpeningStatement && (\n  //             <div className='flex gap-[6px] items-center ml-1 shrink-0'>\n  //               <Tooltip\n  //                 popupContent={t('appDebug.operation.agree')}\n  //               >\n  //                 <div\n  //                   className='flex justify-center items-center w-6 h-6 rounded-md cursor-pointer hover:bg-black/5 hover:text-gray-800'\n  //                   onClick={() => handleFeedback(localFeedback?.rating === 'like' ? null : 'like')}\n  //                 >\n  //                   <Image src={localFeedback?.rating === 'like' ? LarkLikeActive : LarkLike} alt=\"\" />\n  //                 </div>\n  //               </Tooltip>\n  //               <Tooltip\n  //                 popupContent={t('appDebug.operation.disagree')}\n  //               >\n  //                 <div\n  //                   className='flex justify-center items-center w-6 h-6 rounded-md cursor-pointer hover:bg-black/5 hover:text-gray-800'\n  //                   onClick={() => handleFeedback(localFeedback?.rating === 'dislike' ? null : 'dislike')}\n  //                 >\n  //                   <Image src={localFeedback?.rating === 'dislike' ? LarkDislikeActive : LarkDislike} alt=\"\" />\n  //                 </div>\n  //               </Tooltip>\n  //             </div>\n  //           )\n  //         }\n  //        </div>\n  //      }\n  //   </div>\n  // }\n\n  return (\n    <>\n      <div\n        className={cn(\n          'absolute flex justify-end gap-1',\n          hasWorkflowProcess && '-bottom-4 right-2',\n          !positionRight && '-bottom-4 right-2',\n          !hasWorkflowProcess && positionRight && '!top-[9px]',\n          embedSource && 'relative items-center',\n          className,\n        )}\n        style={(!hasWorkflowProcess && positionRight && !embedSource) ? { left: contentWidth + 8 } : {}}\n      >\n        {showPromptLog && !isOpeningStatement && !isPrompt && (\n          <div className={cn(embedSource ? 'block' : 'hidden group-hover:block')}>\n            <Log logItem={item} />\n          </div>\n        )}\n        {!isOpeningStatement && (\n          <div className={cn(embedSource ? 'flex' : 'ml-1 hidden items-center gap-0.5 rounded-[10px] border-[0.5px] border-components-actionbar-border bg-components-actionbar-bg p-0.5 shadow-md backdrop-blur-sm group-hover:flex')}>\n            {(config?.text_to_speech?.enabled && !isPrompt) && (\n              <NewAudioButton\n                id={id}\n                value={content}\n                voice={config?.text_to_speech?.voice}\n              />\n            )}\n            <Tooltip\n              popupContent={'复制'}\n            >\n              <ActionButton onClick={() => {\n                copy(content)\n                Toast.notify({ type: 'success', message: t('common.actionMsg.copySuccessfully') })\n              }}>\n                <RiClipboardLine className='h-4 w-4' />\n              </ActionButton>\n            </Tooltip>\n\n            {!noChatInput && !isPrompt && (\n              <Tooltip\n                popupContent={'重新生成'}\n              >\n                <ActionButton onClick={() => onRegenerate?.(item)}>\n                  <RiResetLeftLine className='h-4 w-4' />\n                </ActionButton>\n              </Tooltip>\n            )}\n            {(config?.supportAnnotation && config.annotation_reply?.enabled) && (\n              <AnnotationCtrlButton\n                appId={config?.appId || ''}\n                messageId={id}\n                cached={!!annotation?.id}\n                query={question}\n                answer={content}\n                onAdded={(id, authorName) => onAnnotationAdded?.(id, authorName, question, content, index)}\n                onEdit={() => setIsShowReplyModal(true)}\n              />\n            )}\n          </div>\n        )}\n        {embedSource && !isPrompt && <div className=\"h-[16px] w-[1px] bg-[#DFE4E8]\"></div>}\n        {!isOpeningStatement && config?.supportFeedback && !localFeedback?.rating && onFeedback && !isPrompt && (\n          <div className={cn(embedSource ? 'flex' : 'ml-1 hidden items-center gap-0.5 rounded-[10px] border-[0.5px] border-components-actionbar-border bg-components-actionbar-bg p-0.5 shadow-md backdrop-blur-sm group-hover:flex')}>\n            {!localFeedback?.rating && (\n              <>\n                <Tooltip\n                  popupContent={'赞同'}\n                >\n                  <ActionButton onClick={() => handleFeedback('like')}>\n                    <RiThumbUpLine className='h-4 w-4' />\n                  </ActionButton>\n                </Tooltip>\n                <Tooltip\n                  popupContent={'反对'}\n                >\n                  <ActionButton onClick={() => handleFeedback('dislike')}>\n                    <RiThumbDownLine className='h-4 w-4' />\n                  </ActionButton>\n                </Tooltip>\n              </>\n            )}\n          </div>\n        )}\n        {!isOpeningStatement && config?.supportFeedback && localFeedback?.rating && onFeedback && !isPrompt && (\n          <div className={cn(embedSource ? '' : 'ml-1 flex items-center gap-0.5 rounded-[10px] border-[0.5px] border-components-actionbar-border bg-components-actionbar-bg p-0.5 shadow-md backdrop-blur-sm')}>\n            {localFeedback?.rating === 'like' && (\n              <Tooltip\n                popupContent={'取消赞同'}\n              >\n                <ActionButton state={ActionButtonState.Active} onClick={() => handleFeedback(null)}>\n                  <RiThumbUpLine className='h-4 w-4' />\n                </ActionButton>\n              </Tooltip>\n            )}\n            {localFeedback?.rating === 'dislike' && (\n              <Tooltip\n              popupContent={'取消反对'}\n            >\n              <ActionButton state={ActionButtonState.Destructive} onClick={() => handleFeedback(null)}>\n                <RiThumbDownLine className='h-4 w-4' />\n              </ActionButton>\n            </Tooltip>\n            )}\n          </div>\n        )}\n      </div>\n      <EditReplyModal\n        isShow={isShowReplyModal}\n        onHide={() => setIsShowReplyModal(false)}\n        query={question}\n        answer={content}\n        onEdited={(editedQuery, editedAnswer) => onAnnotationEdited?.(editedQuery, editedAnswer, index)}\n        onAdded={(annotationId, authorName, editedQuery, editedAnswer) => onAnnotationAdded?.(annotationId, authorName, editedQuery, editedAnswer, index)}\n        appId={config?.appId || ''}\n        messageId={id}\n        annotationId={annotation?.id || ''}\n        createdAt={annotation?.created_at}\n        onRemove={() => onAnnotationRemoved?.(index)}\n      />\n    </>\n  )\n}\n\nexport default memo(Operation)\n"], "names": [], "mappings": ";;;;AACA;;;;;;;;;;;AAaA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA;;;;;;;;;;;;;;;AAeA,MAAM,YAAgC,CAAC,EACrC,IAAI,EACJ,QAAQ,EACR,KAAK,EACL,aAAa,EACb,OAAO,EACP,YAAY,EACZ,kBAAkB,EAClB,WAAW,EACX,WAAW,EACX,QAAQ,EACR,SAAS,EACV;IACC,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,EACJ,MAAM,EACN,iBAAiB,EACjB,kBAAkB,EAClB,mBAAmB,EACnB,UAAU,EACV,YAAY,EACb,GAAG,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD;IACjB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EACJ,EAAE,EACF,kBAAkB,EAClB,SAAS,cAAc,EACvB,UAAU,EACV,QAAQ,EACR,aAAa,EACb,cAAc,EACd,QAAQ,EACT,GAAG;IAEJ,MAAM,gBAAgB,CAAC,CAAC,YAAY;IACpC,MAAM,gBAAgB,eAAe;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,oBAAoB,gBAAgB;IAE/F,MAAM,UAAU,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE;QACtB,IAAI,gBAAgB,QAClB,OAAO,eAAe,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,OAAO,EAAE;QAEhE,OAAO;IACT,GAAG;QAAC;QAAgB;KAAe;IAEnC,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,QAAQ,mBAAmB,CAAC,YAC/B;QAEF,MAAM,aAAa,IAAI;YAAE;QAAO;QAChC,iBAAiB;YAAE;QAAO;IAC5B;IAEA,MAAM,iBAAiB,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,IAAI,QAAQ;QACZ,IAAI,CAAC,oBACH,SAAS;QACX,IAAI,CAAC,sBAAsB,eACzB,SAAS,KAAK;QAChB,IAAI,CAAC,sBAAsB,QAAQ,gBAAgB,SACjD,SAAS;QACX,IAAI,CAAC,sBAAsB,QAAQ,qBAAqB,QAAQ,kBAAkB,SAChF,SAAS;QACX,IAAI,QAAQ,mBAAmB,CAAC,eAAe,UAAU,cAAc,CAAC,oBACtE,SAAS,KAAK;QAChB,IAAI,QAAQ,mBAAmB,eAAe,UAAU,cAAc,CAAC,oBACrE,SAAS,KAAK;QAChB,OAAO;IACT,GAAG;QAAC;QAAoB;QAAe,QAAQ,gBAAgB;QAAS,QAAQ;QAAmB,QAAQ,kBAAkB;QAAS,QAAQ;QAAiB,eAAe;QAAQ;KAAW;IAEjM,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE,IAAM,iBAAiB,SAAS;QAAC;QAAgB;KAAQ;IAEvF,qBAAqB;IACrB,+EAA+E;IAC/E,QAAQ;IACR,oEAAoE;IACpE,oBAAoB;IACpB,oBAAoB;IACpB,4BAA4B;IAC5B,4BAA4B;IAC5B,kDAAkD;IAClD,sCAAsC;IACtC,aAAa;IACb,UAAU;IACV,QAAQ;IAER,gCAAgC;IAChC,iBAAiB;IACjB,oCAAoC;IACpC,yBAAyB;IACzB,0BAA0B;IAC1B,WAAW;IACX,SAAS;IAET,4DAA4D;IAE5D,SAAS;IACT,sDAAsD;IACtD,YAAY;IACZ,8EAA8E;IAC9E,0EAA0E;IAC1E,yBAAyB;IACzB,+DAA+D;IAC/D,kBAAkB;IAClB,uBAAuB;IACvB,wIAAwI;IACxI,qGAAqG;IACrG,oBAAoB;IACpB,wGAAwG;IACxG,yBAAyB;IACzB,2BAA2B;IAC3B,yBAAyB;IACzB,kEAAkE;IAClE,kBAAkB;IAClB,uBAAuB;IACvB,wIAAwI;IACxI,2GAA2G;IAC3G,oBAAoB;IACpB,iHAAiH;IACjH,yBAAyB;IACzB,2BAA2B;IAC3B,qBAAqB;IACrB,cAAc;IACd,YAAY;IACZ,gBAAgB;IAChB,SAAS;IACT,WAAW;IACX,IAAI;IAEJ,qBACE;;0BACE,qZAAC;gBACC,WAAW,GACT,mCACA,sBAAsB,qBACtB,CAAC,iBAAiB,qBAClB,CAAC,sBAAsB,iBAAiB,cACxC,eAAe,yBACf;gBAEF,OAAO,AAAC,CAAC,sBAAsB,iBAAiB,CAAC,cAAe;oBAAE,MAAM,eAAe;gBAAE,IAAI,CAAC;;oBAE7F,iBAAiB,CAAC,sBAAsB,CAAC,0BACxC,qZAAC;wBAAI,WAAW,GAAG,cAAc,UAAU;kCACzC,cAAA,qZAAC;4BAAI,SAAS;;;;;;;;;;;oBAGjB,CAAC,oCACA,qZAAC;wBAAI,WAAW,GAAG,cAAc,SAAS;;4BACtC,QAAQ,gBAAgB,WAAW,CAAC,0BACpC,qZAAC;gCACC,IAAI;gCACJ,OAAO;gCACP,OAAO,QAAQ,gBAAgB;;;;;;0CAGnC,qZAAC,8IAAA,CAAA,UAAO;gCACN,cAAc;0CAEd,cAAA,qZAAC;oCAAa,SAAS;wCACrB,KAAK;wCACL,MAAM,MAAM,CAAC;4CAAE,MAAM;4CAAW,SAAS,EAAE;wCAAqC;oCAClF;8CACE,cAAA,qZAAC;wCAAgB,WAAU;;;;;;;;;;;;;;;;4BAI9B,CAAC,eAAe,CAAC,0BAChB,qZAAC,8IAAA,CAAA,UAAO;gCACN,cAAc;0CAEd,cAAA,qZAAC;oCAAa,SAAS,IAAM,eAAe;8CAC1C,cAAA,qZAAC;wCAAgB,WAAU;;;;;;;;;;;;;;;;4BAI/B,QAAQ,qBAAqB,OAAO,gBAAgB,EAAE,yBACtD,qZAAC;gCACC,OAAO,QAAQ,SAAS;gCACxB,WAAW;gCACX,QAAQ,CAAC,CAAC,YAAY;gCACtB,OAAO;gCACP,QAAQ;gCACR,SAAS,CAAC,IAAI,aAAe,oBAAoB,IAAI,YAAY,UAAU,SAAS;gCACpF,QAAQ,IAAM,oBAAoB;;;;;;;;;;;;oBAKzC,eAAe,CAAC,0BAAY,qZAAC;wBAAI,WAAU;;;;;;oBAC3C,CAAC,sBAAsB,QAAQ,mBAAmB,CAAC,eAAe,UAAU,cAAc,CAAC,0BAC1F,qZAAC;wBAAI,WAAW,GAAG,cAAc,SAAS;kCACvC,CAAC,eAAe,wBACf;;8CACE,qZAAC,8IAAA,CAAA,UAAO;oCACN,cAAc;8CAEd,cAAA,qZAAC;wCAAa,SAAS,IAAM,eAAe;kDAC1C,cAAA,qZAAC;4CAAc,WAAU;;;;;;;;;;;;;;;;8CAG7B,qZAAC,8IAAA,CAAA,UAAO;oCACN,cAAc;8CAEd,cAAA,qZAAC;wCAAa,SAAS,IAAM,eAAe;kDAC1C,cAAA,qZAAC;4CAAgB,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAOtC,CAAC,sBAAsB,QAAQ,mBAAmB,eAAe,UAAU,cAAc,CAAC,0BACzF,qZAAC;wBAAI,WAAW,GAAG,cAAc,KAAK;;4BACnC,eAAe,WAAW,wBACzB,qZAAC,8IAAA,CAAA,UAAO;gCACN,cAAc;0CAEd,cAAA,qZAAC;oCAAa,OAAO,kBAAkB,MAAM;oCAAE,SAAS,IAAM,eAAe;8CAC3E,cAAA,qZAAC;wCAAc,WAAU;;;;;;;;;;;;;;;;4BAI9B,eAAe,WAAW,2BACzB,qZAAC,8IAAA,CAAA,UAAO;gCACR,cAAc;0CAEd,cAAA,qZAAC;oCAAa,OAAO,kBAAkB,WAAW;oCAAE,SAAS,IAAM,eAAe;8CAChF,cAAA,qZAAC;wCAAgB,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,qZAAC;gBACC,QAAQ;gBACR,QAAQ,IAAM,oBAAoB;gBAClC,OAAO;gBACP,QAAQ;gBACR,UAAU,CAAC,aAAa,eAAiB,qBAAqB,aAAa,cAAc;gBACzF,SAAS,CAAC,cAAc,YAAY,aAAa,eAAiB,oBAAoB,cAAc,YAAY,aAAa,cAAc;gBAC3I,OAAO,QAAQ,SAAS;gBACxB,WAAW;gBACX,cAAc,YAAY,MAAM;gBAChC,WAAW,YAAY;gBACvB,UAAU,IAAM,sBAAsB;;;;;;;;AAI9C;qDAEe,CAAA,GAAA,4WAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3745, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/answer/agent-content.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport { memo } from 'react'\nimport type {\n  ChatItem,\n} from '../../types'\nimport { Markdown } from '@/components/base/markdown'\nimport Thought from '@/components/base/chat/chat/thought'\nimport { FileList } from '@/components/base/file-uploader'\nimport { getProcessedFilesFromResponse } from '@/components/base/file-uploader/utils'\n\ntype AgentContentProps = {\n  item: ChatItem\n  responding?: boolean\n  content?: string\n}\nconst AgentContent: FC<AgentContentProps> = ({\n  item,\n  responding,\n  content,\n}) => {\n  const {\n    annotation,\n    agent_thoughts,\n  } = item\n\n  if (annotation?.logAnnotation)\n    return <Markdown content={annotation?.logAnnotation.content || ''} />\n\n  return (\n    <div>\n      {content ? <Markdown content={content} /> : agent_thoughts?.map((thought, index) => (\n        <div key={index} className='px-2 py-1'>\n          {thought.thought && (\n            <Markdown content={thought.thought} />\n          )}\n          {/* {item.tool} */}\n          {/* perhaps not use tool */}\n          {!!thought.tool && (\n            <Thought\n              thought={thought}\n              isFinished={!!thought.observation || !responding}\n            />\n          )}\n\n          {\n            !!thought.message_files?.length && (\n              <FileList\n                files={getProcessedFilesFromResponse(thought.message_files.map((item: any) => ({ ...item, related_id: item.id })))}\n                showDeleteAction={false}\n                showDownloadAction={true}\n                canPreview={true}\n              />\n            )\n          }\n        </div>\n      ))}\n    </div>\n  )\n}\n\nexport default memo(AgentContent)\n"], "names": [], "mappings": ";;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,MAAM,eAAsC,CAAC,EAC3C,IAAI,EACJ,UAAU,EACV,OAAO,EACR;IACC,MAAM,EACJ,UAAU,EACV,cAAc,EACf,GAAG;IAEJ,IAAI,YAAY,eACd,qBAAO,qZAAC;QAAS,SAAS,YAAY,cAAc,WAAW;;;;;;IAEjE,qBACE,qZAAC;kBACE,wBAAU,qZAAC;YAAS,SAAS;;;;;mBAAc,gBAAgB,IAAI,CAAC,SAAS,sBACxE,qZAAC;gBAAgB,WAAU;;oBACxB,QAAQ,OAAO,kBACd,qZAAC;wBAAS,SAAS,QAAQ,OAAO;;;;;;oBAInC,CAAC,CAAC,QAAQ,IAAI,kBACb,qZAAC;wBACC,SAAS;wBACT,YAAY,CAAC,CAAC,QAAQ,WAAW,IAAI,CAAC;;;;;;oBAKxC,CAAC,CAAC,QAAQ,aAAa,EAAE,wBACvB,qZAAC;wBACC,OAAO,8BAA8B,QAAQ,aAAa,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;gCAAE,GAAG,IAAI;gCAAE,YAAY,KAAK,EAAE;4BAAC,CAAC;wBAC/G,kBAAkB;wBAClB,oBAAoB;wBACpB,YAAY;;;;;;;eAnBV;;;;;;;;;;AA2BlB;qDAEe,CAAA,GAAA,4WAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3842, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/answer/basic-content.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport { memo } from 'react'\nimport type { ChatItem } from '../../types'\nimport { Markdown } from '@/components/base/markdown'\nimport cn from '@/utils/classnames'\n\ntype BasicContentProps = {\n  item: ChatItem\n}\nconst BasicContent: FC<BasicContentProps> = ({\n  item,\n}) => {\n  const {\n    annotation,\n    content,\n  } = item\n\n  if (annotation?.logAnnotation)\n    return <Markdown content={annotation?.logAnnotation.content || ''} />\n\n  return (\n    <Markdown\n      className={cn(\n        item.isError && '!text-[#F04438]',\n      )}\n      content={content}\n    />\n  )\n}\n\nexport default memo(BasicContent)\n"], "names": [], "mappings": ";;;;AACA;;;;;;;;;;;;;;;AAQA,MAAM,eAAsC,CAAC,EAC3C,IAAI,EACL;IACC,MAAM,EACJ,UAAU,EACV,OAAO,EACR,GAAG;IAEJ,IAAI,YAAY,eACd,qBAAO,qZAAC;QAAS,SAAS,YAAY,cAAc,WAAW;;;;;;IAEjE,qBACE,qZAAC;QACC,WAAW,GACT,KAAK,OAAO,IAAI;QAElB,SAAS;;;;;;AAGf;qDAEe,CAAA,GAAA,4WAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3886, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/answer/suggested-questions.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport { memo } from 'react'\nimport type { ChatItem } from '../../types'\nimport { useChatContext } from '../context'\n\ntype SuggestedQuestionsProps = {\n  item: ChatItem\n}\nconst SuggestedQuestions: FC<SuggestedQuestionsProps> = ({\n  item,\n}) => {\n  const { onSend } = useChatContext()\n\n  const {\n    isOpeningStatement,\n    suggestedQuestions,\n  } = item\n\n  if (!isOpeningStatement || !suggestedQuestions?.length)\n    return null\n\n  return (\n    <div className='flex flex-wrap'>\n      {suggestedQuestions.filter(q => !!q && q.trim()).map((question, index) => (\n        <div\n          key={index}\n          className='system-sm-medium mr-1 mt-1 inline-flex max-w-full shrink-0 cursor-pointer flex-wrap rounded-lg border-[0.5px] border-components-button-secondary-border bg-components-button-secondary-bg px-3.5 py-2 text-components-button-secondary-accent-text shadow-xs last:mr-0 hover:border-components-button-secondary-border-hover hover:bg-components-button-secondary-bg-hover'\n          onClick={() => onSend?.(question)}\n        >\n          {question}\n        </div>),\n      )}\n    </div>\n  )\n}\n\nexport default memo(SuggestedQuestions)\n"], "names": [], "mappings": ";;;;AACA;AAEA;;;;AAKA,MAAM,qBAAkD,CAAC,EACvD,IAAI,EACL;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD;IAEhC,MAAM,EACJ,kBAAkB,EAClB,kBAAkB,EACnB,GAAG;IAEJ,IAAI,CAAC,sBAAsB,CAAC,oBAAoB,QAC9C,OAAO;IAET,qBACE,qZAAC;QAAI,WAAU;kBACZ,mBAAmB,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,KAAK,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC,UAAU,sBAC9D,qZAAC;gBAEC,WAAU;gBACV,SAAS,IAAM,SAAS;0BAEvB;eAJI;;;;;;;;;;AASf;qDAEe,CAAA,GAAA,4WAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3923, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/answer/more.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport { memo } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport type { ChatItem } from '../../types'\nimport { formatNumber } from '@/utils/format'\n\ntype MoreProps = {\n  more: ChatItem['more']\n}\nconst More: FC<MoreProps> = ({\n  more,\n}) => {\n  const { t } = useTranslation()\n\n  return (\n    <div className='system-xs-regular mt-1 flex items-center text-text-quaternary opacity-0 group-hover:opacity-100'>\n      {\n        more && (\n          <>\n            <div\n              className='mr-2 max-w-[33.3%] shrink-0 truncate'\n              title={`${t('appLog.detail.timeConsuming')} ${more.latency}${t('appLog.detail.second')}`}\n            >\n              {`${t('appLog.detail.timeConsuming')} ${more.latency}${t('appLog.detail.second')}`}\n            </div>\n            <div\n              className='max-w-[33.3%] shrink-0 truncate'\n              title={`${t('appLog.detail.tokenCost')} ${formatNumber(more.tokens)}`}\n            >\n              {`${t('appLog.detail.tokenCost')} ${formatNumber(more.tokens)}`}\n            </div>\n            <div className='mx-2 shrink-0'>·</div>\n            <div\n              className='max-w-[33.3%] shrink-0 truncate'\n              title={more.time}\n            >\n              {more.time}\n            </div>\n          </>\n        )\n      }\n    </div>\n  )\n}\n\nexport default memo(More)\n"], "names": [], "mappings": ";;;;AACA;;;;;;;;;;;;;;;AAQA,MAAM,OAAsB,CAAC,EAC3B,IAAI,EACL;IACC,MAAM,EAAE,CAAC,EAAE,GAAG;IAEd,qBACE,qZAAC;QAAI,WAAU;kBAEX,sBACE;;8BACE,qZAAC;oBACC,WAAU;oBACV,OAAO,GAAG,EAAE,+BAA+B,CAAC,EAAE,KAAK,OAAO,GAAG,EAAE,yBAAyB;8BAEvF,GAAG,EAAE,+BAA+B,CAAC,EAAE,KAAK,OAAO,GAAG,EAAE,yBAAyB;;;;;;8BAEpF,qZAAC;oBACC,WAAU;oBACV,OAAO,GAAG,EAAE,2BAA2B,CAAC,EAAE,aAAa,KAAK,MAAM,GAAG;8BAEpE,GAAG,EAAE,2BAA2B,CAAC,EAAE,aAAa,KAAK,MAAM,GAAG;;;;;;8BAEjE,qZAAC;oBAAI,WAAU;8BAAgB;;;;;;8BAC/B,qZAAC;oBACC,WAAU;oBACV,OAAO,KAAK,IAAI;8BAEf,KAAK,IAAI;;;;;;;;;;;;;AAOxB;qDAEe,CAAA,GAAA,4WAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3998, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/answer/workflow-process.tsx"], "sourcesContent": ["import {\n  useEffect,\n  useState,\n} from 'react'\nimport {\n  Ri<PERSON>rrowRightSLine,\n  RiErrorWarningFill,\n  RiLoader2Line,\n} from '@remixicon/react'\nimport { useTranslation } from 'react-i18next'\nimport type { ChatItem, WorkflowProcess } from '../../types'\nimport TracingPanel from '@/components/workflow/run/tracing-panel'\nimport cn from '@/utils/classnames'\nimport { CheckCircle } from '@/components/base/icons/src/vender/solid/general'\nimport { WorkflowRunningStatus } from '@/components/workflow/types'\n\ntype WorkflowProcessProps = {\n  data: WorkflowProcess\n  item?: ChatItem\n  expand?: boolean\n  hideInfo?: boolean\n  hideProcessDetail?: boolean\n  readonly?: boolean\n}\nconst WorkflowProcessItem = ({\n  data,\n  expand = false,\n  hideInfo = false,\n  hideProcessDetail = false,\n  readonly = false,\n}: WorkflowProcessProps) => {\n  const { t } = useTranslation()\n  const [collapse, setCollapse] = useState(!expand)\n  const running = data.status === WorkflowRunningStatus.Running\n  const succeeded = data.status === WorkflowRunningStatus.Succeeded\n  const failed = data.status === WorkflowRunningStatus.Failed || data.status === WorkflowRunningStatus.Stopped\n\n  useEffect(() => {\n    setCollapse(!expand)\n  }, [expand])\n\n  return (\n    <div\n      className={cn(\n        '-mx-1 rounded-xl px-2.5',\n        collapse ? 'border-l-[0.25px] border-components-panel-border py-[7px]' : 'border-[0.5px] border-components-panel-border-subtle px-1 pb-1 pt-[7px]',\n        running && !collapse && 'bg-background-section-burn',\n        succeeded && !collapse && 'bg-state-success-hover',\n        failed && !collapse && 'bg-state-destructive-hover',\n        collapse && 'bg-workflow-process-bg',\n      )}\n    >\n      <div\n        className={cn('flex cursor-pointer items-center', !collapse && 'px-1.5', readonly && 'cursor-default')}\n        onClick={() => !readonly && setCollapse(!collapse)}\n      >\n        {\n          running && (\n            <RiLoader2Line className='mr-1 h-3.5 w-3.5 shrink-0 animate-spin text-text-tertiary' />\n          )\n        }\n        {\n          succeeded && (\n            <CheckCircle className='mr-1 h-3.5 w-3.5 shrink-0 text-text-success' />\n          )\n        }\n        {\n          failed && (\n            <RiErrorWarningFill className='mr-1 h-3.5 w-3.5 shrink-0 text-text-destructive' />\n          )\n        }\n        <div className={cn('system-xs-medium text-text-secondary', !collapse && 'grow')}>\n          {t('workflow.common.workflowProcess')}\n        </div>\n        {!readonly && <RiArrowRightSLine className={cn('ml-1 h-4 w-4 text-text-tertiary', !collapse && 'rotate-90')} />}\n      </div>\n      {\n        !collapse && !readonly && (\n          <div className='mt-1.5'>\n            {\n              <TracingPanel\n                list={data.tracing}\n                hideNodeInfo={hideInfo}\n                hideNodeProcessDetail={hideProcessDetail}\n              />\n            }\n          </div>\n        )\n      }\n    </div>\n  )\n}\n\nexport default WorkflowProcessItem\n"], "names": [], "mappings": ";;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,MAAM,sBAAsB,CAAC,EAC3B,IAAI,EACJ,SAAS,KAAK,EACd,WAAW,KAAK,EAChB,oBAAoB,KAAK,EACzB,WAAW,KAAK,EACK;IACrB,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC1C,MAAM,UAAU,KAAK,MAAM,KAAK,sBAAsB,OAAO;IAC7D,MAAM,YAAY,KAAK,MAAM,KAAK,sBAAsB,SAAS;IACjE,MAAM,SAAS,KAAK,MAAM,KAAK,sBAAsB,MAAM,IAAI,KAAK,MAAM,KAAK,sBAAsB,OAAO;IAE5G,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY,CAAC;IACf,GAAG;QAAC;KAAO;IAEX,qBACE,qZAAC;QACC,WAAW,GACT,2BACA,WAAW,8DAA8D,2EACzE,WAAW,CAAC,YAAY,8BACxB,aAAa,CAAC,YAAY,0BAC1B,UAAU,CAAC,YAAY,8BACvB,YAAY;;0BAGd,qZAAC;gBACC,WAAW,GAAG,oCAAoC,CAAC,YAAY,UAAU,YAAY;gBACrF,SAAS,IAAM,CAAC,YAAY,YAAY,CAAC;;oBAGvC,yBACE,qZAAC;wBAAc,WAAU;;;;;;oBAI3B,2BACE,qZAAC;wBAAY,WAAU;;;;;;oBAIzB,wBACE,qZAAC;wBAAmB,WAAU;;;;;;kCAGlC,qZAAC;wBAAI,WAAW,GAAG,wCAAwC,CAAC,YAAY;kCACrE,EAAE;;;;;;oBAEJ,CAAC,0BAAY,qZAAC;wBAAkB,WAAW,GAAG,mCAAmC,CAAC,YAAY;;;;;;;;;;;;YAG/F,CAAC,YAAY,CAAC,0BACZ,qZAAC;gBAAI,WAAU;0BAEX,cAAA,qZAAC;oBACC,MAAM,KAAK,OAAO;oBAClB,cAAc;oBACd,uBAAuB;;;;;;;;;;;;;;;;;AAQvC;uCAEe", "debugId": null}}, {"offset": {"line": 4131, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/answer/schedule-card.tsx"], "sourcesContent": ["import { useState } from 'react'\nimport Button from '../../../button'\nimport { useChatWithHistoryContext } from '../../chat-with-history/context'\nimport { INTENT_INCLUDES_MEETING, INTENT_INCLUDES_SCHEDULE } from '../u-const'\n\ntype Employee = {\n  guid: string\n  username: string\n  fullname: string\n  department: string\n  keyword: string\n}\n\ntype ScheduleCardProps = {\n  data: {\n    summary: string\n    description: string\n    start_time: string\n    end_time: string\n    event: string\n    meeting_location?: string\n    employees: { keyword: string; employees: Employee[] }[]\n  }\n  isLast?: boolean\n  isEmbedMobile?: boolean\n  onCancel?: () => void\n  onConfirm?: (data: any) => void\n}\n\nconst List = ({ data, isEmbedMobile, scheduleTitleColor, hideBtn, onSelected }: { data: any; isEmbedMobile?: boolean;scheduleTitleColor?: string; hideBtn: boolean; onSelected: (data: any) => void }) => {\n  const [employee, setEmployee] = useState<Employee>()\n  const handleConfirm = (item: Employee) => {\n    if (employee)\n      return\n\n    setEmployee(item)\n    onSelected(item)\n  }\n\n  return (\n    <div className={`p-[24px] hover:bg-[#F8F8F8] ${isEmbedMobile && 'px-[13px] pt-[16px] pb-[0px] last:!pb-[16px]'}`}>\n      <p className={'mb-[16px]'} style={{ color: scheduleTitleColor }}>为你找到以下可能是[{data.keyword}]的人员</p>\n      {data.employees?.map((item: Employee, index: number) => (\n        <div className='flex items-center mb-[20px] last:mb-[0px]' key={index}>\n          {!isEmbedMobile && <p className='font-semibold shrink-0'>[{item.fullname}]</p>}\n          <p className={`mx-[16px] flex-1 ${isEmbedMobile && 'ml-[0px]'}`}>{isEmbedMobile && <span className=\"font-semibold mr-[4px]\">{`[${item.fullname}]`}</span>}{item.department}</p>\n          {!hideBtn && (\n            <Button\n              className={\n                `!border-[#6B4EFF] text-[#6B4EFF] rounded-[4px] px-[12px] h-[24px] text-[12px] \n                ${employee?.guid === item.guid && '!border-[#DFE4E8] text-[#242933]'}\n                ${employee && (employee?.guid !== item.guid) && 'cursor-not-allowed'}\n                `\n              }\n              onClick={() => handleConfirm(item)}\n            >\n              {employee?.guid === item.guid ? '已确认' : '确认'}\n            </Button>\n          )}\n        </div>\n      ))}\n    </div>\n  )\n}\n\nconst ScheduleCard = ({ data, isEmbedMobile, isLast, onConfirm, onCancel }: ScheduleCardProps) => {\n  const scheduleOrMeeting = localStorage.getItem('scheduleOrMeeting')\n  const [selected, setSelected] = useState<Employee[]>([])\n  const [isCancel, setIsCancel] = useState(false)\n  const allowSubmit = !!selected.length\n  const { appData } = useChatWithHistoryContext()\n  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}\n  const scheduleBg = chatPageConfigData?.scheduleBg || '#ECECFF'\n  const scheduleTitleColor = chatPageConfigData?.scheduleTitleColor || '#6550CE'\n\n  const handleItemSelect = (item: Employee) => {\n    selected.push(item)\n    setSelected([...selected])\n  }\n\n  const handleCreate = () => {\n    const confirmData = scheduleOrMeeting === 'schedule'\n      ? {\n        summary: data.summary,\n        description: data.description,\n        start_time: data.start_time,\n        end_time: data.end_time,\n        event: 'create_calendar_submit',\n        attendees_id: selected.map(item => item.guid).join(','),\n        attendees_name: selected.map(item => item.fullname).join(','),\n      }\n      : scheduleOrMeeting === 'meeting'\n        ? {\n          title: data.summary,\n          meeting_start: data.start_time,\n          meeting_end: data.end_time,\n          create_meeting_submit: 'create_meeting_success',\n          attendees_ids: selected.map(item => item.guid).join(','),\n          attendees_names: selected.map(item => item.fullname).join(','),\n          meeting_location: data?.meeting_location,\n        }\n        : {}\n    onConfirm?.({ ...confirmData })\n  }\n\n  const handleCancel = () => {\n    setIsCancel(true)\n    onCancel?.()\n  }\n\n  const typeTit = scheduleOrMeeting === 'schedule' ? INTENT_INCLUDES_SCHEDULE : scheduleOrMeeting === 'meeting' ? INTENT_INCLUDES_MEETING : ''\n  return (\n    <div className=\"w-full bg-[#fff] rounded-[8px] overflow-auto\">\n      <div\n        className={`text-[18px] font-medium py-[16px] px-[24px] ${isEmbedMobile && '!text-[14px] !p-[12px]'}`}\n        style={{ background: scheduleBg, color: scheduleTitleColor }}\n      >{`请确认${typeTit}参与人员`}</div>\n      {\n        data.employees?.map((item: { keyword: string; employees: Employee[] }, index: number) => (\n          <List data={item} isEmbedMobile={isEmbedMobile} scheduleTitleColor={scheduleTitleColor} hideBtn={!isLast || isCancel} key={index} onSelected={handleItemSelect} />\n        ))\n      }\n      {(isLast && !isCancel) && (\n        <div className=\"flex justify-end gap-[8px] px-[24px] items-center h-[64px] border-t-[1px] border-t-[#EEF0F2]\">\n          <Button className='rounded-[4px] px-[18px] h-[32px]' onClick={handleCancel}>\n            取消\n          </Button>\n          <Button\n            className={`rounded-[4px] px-[18px] h-[32px] ${!allowSubmit ? 'bg-[#c0c0c0] cursor-not-allowed hover:!bg-[#c0c0c0]' : 'text-[#fff]'}`}\n            // variant=\"primary\"\n            style={{ background: allowSubmit ? 'linear-gradient(270deg, #7D67FF 0%, #5099FF 100%)' : '' }}\n            onClick={() => allowSubmit && handleCreate()}\n          >\n            创建\n          </Button>\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default ScheduleCard\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AA0BA,MAAM,OAAO,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,kBAAkB,EAAE,OAAO,EAAE,UAAU,EAAyH;IACnM,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD;IACvC,MAAM,gBAAgB,CAAC;QACrB,IAAI,UACF;QAEF,YAAY;QACZ,WAAW;IACb;IAEA,qBACE,qZAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,iBAAiB,gDAAgD;;0BAC9G,qZAAC;gBAAE,WAAW;gBAAa,OAAO;oBAAE,OAAO;gBAAmB;;oBAAG;oBAAW,KAAK,OAAO;oBAAC;;;;;;;YACxF,KAAK,SAAS,EAAE,IAAI,CAAC,MAAgB,sBACpC,qZAAC;oBAAI,WAAU;;wBACZ,CAAC,+BAAiB,qZAAC;4BAAE,WAAU;;gCAAyB;gCAAE,KAAK,QAAQ;gCAAC;;;;;;;sCACzE,qZAAC;4BAAE,WAAW,CAAC,iBAAiB,EAAE,iBAAiB,YAAY;;gCAAG,+BAAiB,qZAAC;oCAAK,WAAU;8CAA0B,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC;;;;;;gCAAU,KAAK,UAAU;;;;;;;wBACzK,CAAC,yBACA,qZAAC,6IAAA,CAAA,UAAM;4BACL,WACE,CAAC;gBACD,EAAE,UAAU,SAAS,KAAK,IAAI,IAAI,mCAAmC;gBACrE,EAAE,YAAa,UAAU,SAAS,KAAK,IAAI,IAAK,qBAAqB;gBACrE,CAAC;4BAEH,SAAS,IAAM,cAAc;sCAE5B,UAAU,SAAS,KAAK,IAAI,GAAG,QAAQ;;;;;;;mBAbkB;;;;;;;;;;;AAoBxE;AAEA,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAqB;IAC3F,MAAM,oBAAoB,aAAa,OAAO,CAAC;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,cAAc,CAAC,CAAC,SAAS,MAAM;IACrC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,4BAAyB,AAAD;IAC5C,MAAM,qBAAqB,SAAS,MAAM,iBAAiB,KAAK,KAAK,CAAC,SAAS,MAAM,kBAAkB,CAAC;IACxG,MAAM,aAAa,oBAAoB,cAAc;IACrD,MAAM,qBAAqB,oBAAoB,sBAAsB;IAErE,MAAM,mBAAmB,CAAC;QACxB,SAAS,IAAI,CAAC;QACd,YAAY;eAAI;SAAS;IAC3B;IAEA,MAAM,eAAe;QACnB,MAAM,cAAc,sBAAsB,aACtC;YACA,SAAS,KAAK,OAAO;YACrB,aAAa,KAAK,WAAW;YAC7B,YAAY,KAAK,UAAU;YAC3B,UAAU,KAAK,QAAQ;YACvB,OAAO;YACP,cAAc,SAAS,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,IAAI,CAAC;YACnD,gBAAgB,SAAS,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,IAAI,CAAC;QAC3D,IACE,sBAAsB,YACpB;YACA,OAAO,KAAK,OAAO;YACnB,eAAe,KAAK,UAAU;YAC9B,aAAa,KAAK,QAAQ;YAC1B,uBAAuB;YACvB,eAAe,SAAS,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,IAAI,CAAC;YACpD,iBAAiB,SAAS,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,IAAI,CAAC;YAC1D,kBAAkB,MAAM;QAC1B,IACE,CAAC;QACP,YAAY;YAAE,GAAG,WAAW;QAAC;IAC/B;IAEA,MAAM,eAAe;QACnB,YAAY;QACZ;IACF;IAEA,MAAM,UAAU,sBAAsB,aAAa,wJAAA,CAAA,2BAAwB,GAAG,sBAAsB,YAAY,wJAAA,CAAA,0BAAuB,GAAG;IAC1I,qBACE,qZAAC;QAAI,WAAU;;0BACb,qZAAC;gBACC,WAAW,CAAC,4CAA4C,EAAE,iBAAiB,0BAA0B;gBACrG,OAAO;oBAAE,YAAY;oBAAY,OAAO;gBAAmB;0BAC3D,CAAC,GAAG,EAAE,QAAQ,IAAI,CAAC;;;;;;YAEnB,KAAK,SAAS,EAAE,IAAI,CAAC,MAAkD,sBACrE,qZAAC;oBAAK,MAAM;oBAAM,eAAe;oBAAe,oBAAoB;oBAAoB,SAAS,CAAC,UAAU;oBAAsB,YAAY;mBAAnB;;;;;YAG7H,UAAU,CAAC,0BACX,qZAAC;gBAAI,WAAU;;kCACb,qZAAC,6IAAA,CAAA,UAAM;wBAAC,WAAU;wBAAmC,SAAS;kCAAc;;;;;;kCAG5E,qZAAC,6IAAA,CAAA,UAAM;wBACL,WAAW,CAAC,iCAAiC,EAAE,CAAC,cAAc,wDAAwD,eAAe;wBACrI,oBAAoB;wBACpB,OAAO;4BAAE,YAAY,cAAc,sDAAsD;wBAAG;wBAC5F,SAAS,IAAM,eAAe;kCAC/B;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 4339, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/base/chat/chat/answer/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"lark-loader\": \"index-module__4KFtIW__lark-loader\",\n  \"rotate\": \"index-module__4KFtIW__rotate\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 4349, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/right-sidebar/index.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport cn from 'classnames'\nimport { type ReactNode, useEffect, useRef, useState } from 'react'\nimport IconRight from '@/assets/right.svg'\nimport IconWebSearch from '@/assets/web-search.svg'\nimport IconArrowRight from '@/assets/arrow-right.svg'\nimport IconClose from '@/assets/close.svg'\nimport { useChatWithHistoryContext } from '../context'\nimport positions from 'positions'\n\nexport const RightSidebarTrigger = ({ process, status, references, children, isMobile }: { process: string, status: string, references: Record<string, any>[], children: ReactNode, isMobile?: boolean }) => {\n  const { rightSideInfo, setRightSideInfo } = useChatWithHistoryContext()\n  const [popCss, setPopCss] = useState<null | Record<string, any>>(null)\n  const timer = useRef<number>(-1)\n  const popIndex = useRef(-1)\n  const isEnter = useRef(false)\n\n  const handleMouseOver = (e: MouseEvent) => {\n    const target = e.target as HTMLElement\n\n    if(target.dataset?.reference && isEnter.current) {\n      clearTimeout(timer.current)\n      const css = positions(document, 'top left', target, 'bottom left')\n      popIndex.current = target.dataset?.reference ? (Number(target.dataset.reference) - 1) : -1\n      setPopCss(css)\n    }\n  }\n\n  const handleMouseLeave = (e: MouseEvent) => {\n    const target = e.target as HTMLElement\n\n    if(target.dataset?.reference) {\n      timer.current = window.setTimeout(() => {\n        setPopCss(null)\n        popIndex.current = -1\n      }, 50)\n    }\n  }\n\n  const handleClick = (e) => {\n    const target = e.target as HTMLElement\n\n    if(target.dataset?.reference) {\n      e.preventDefault()\n\n      const index = Number(target.dataset?.reference) - 1\n      const temp = references[index]\n\n      if(temp) {\n        const link = document.createElement('a')\n        link.href = temp.url\n        link.target = '_blank'\n        link.click()\n      }\n    }\n  }\n\n  useEffect(() => {\n    if(isMobile) {\n      document.documentElement.addEventListener('touchstart', handleClick, { passive: false })\n    }\n    else {\n      document.documentElement.addEventListener('mouseover', handleMouseOver)\n      document.documentElement.addEventListener('mouseout', handleMouseLeave)\n    }\n\n    return () => {\n      document.documentElement.removeEventListener('touchstart', handleClick)\n      document.documentElement.removeEventListener('mouseover', handleMouseOver)\n      document.documentElement.removeEventListener('mouseout', handleMouseLeave)\n    }\n  }, [isMobile])\n\n  const handleToggle = () => {\n    setRightSideInfo?.(rightSideInfo === references ? null : references)\n  }\n\n  const handlePopMouseEnter = () => {\n    if (timer.current) {\n      clearTimeout(timer.current)\n      timer.current = -1\n    }\n  }\n\n  const handlePopMouseLeave = () => {\n    setPopCss(null)\n  }\n\n  const renderReferencePop = ({ style }: { style: any }) => {\n    const temp = references[popIndex.current]\n\n    if(!temp) return null\n\n    return (\n      <a className='z-[999] block w-[300px] rounded-[8px] bg-white p-[10px] shadow-lg' href={temp?.url} target='_blank' style={style} onMouseEnter={handlePopMouseEnter} onMouseLeave={handlePopMouseLeave}>\n        <h2 className={styles['line-clamp-2']}>{temp?.title}</h2>\n        <div className='mt-[10px] flex justify-between text-[12px]'>\n          <span>{temp?.site_name}</span>\n          <Image\n            className=\"ml-[4px] mr-[2px]\"\n            src={IconArrowRight}\n            alt=\"\"\n            width={16}\n            height={16}\n          ></Image>\n        </div>\n      </a>\n    )\n  }\n\n  return (\n    <div className='mb-[20px]' onMouseEnter={e => isEnter.current = true} onMouseLeave={() => isEnter.current = false}>\n      {popCss && renderReferencePop({ style: { position: 'fixed', top: `${Math.floor(popCss?.top)}px`, left: `${popCss?.left}px` } })}\n      <div className=\"mb-[10px] flex items-center\">\n        {status === 'start' ? <>\n          <div className=\"flex items-center text-[#70757f]\">\n            <Image\n              className=\"mr-[2px]\"\n              src={IconWebSearch}\n              alt=\"\"\n              width={18}\n              height={18}\n            ></Image>\n          </div>\n          <span className=\"ml-[4px] text-[#959696]\">{process || '正在搜索...'}</span>\n        </> : <div className='flex cursor-pointer items-center rounded-[4px] py-[2px] pl-[2px] pr-[10px] hover:bg-[#edeeee]' onClick={handleToggle}>\n            <Image\n              className=\"mr-[2px]\"\n              src={IconWebSearch}\n              alt=\"\"\n              width={18}\n              height={18}\n            ></Image>\n            <span className=\"ml-[4px] text-[#959696]\">{process}</span>\n            <Image\n              className=\"ml-[4px]\"\n              src={IconRight}\n              alt=\"\"\n              width={16}\n              height={16}\n            ></Image>\n          </div>}\n      </div>\n      {children}\n    </div>\n  )\n}\n\nconst RightSidebar = ({ visible, isMobile }: { visible: boolean, isMobile: boolean }) => {\n  const { rightSideInfo, setRightSideInfo } = useChatWithHistoryContext()\n\n  const handleClick = () => {\n    setRightSideInfo?.(null)\n  }\n\n  // if(!visible) return null\n\n  const renderContent = () => {\n    return (\n      <div className={cn('flex h-full w-[400px] flex-col bg-[#fafafd]', isMobile && '!w-full rounded-t-[16px]')}>\n      <div className={cn('flex items-center justify-between border-b border-[#e8e8e8] px-[16px] py-[20px] text-[16px]', isMobile && 'border-none !py-[10px]')}>\n        网页搜索\n        <Image\n          className=\"cursor-pointer\"\n          src={IconClose}\n          alt=\"\"\n          width={24}\n          height={24}\n          onClick={handleClick}\n        />\n      </div>\n      <ul className=\"flex-1 overflow-auto p-[8px]\">\n        {rightSideInfo?.map((item: any) => (\n          <li className=\"mb-[10px] cursor-pointer rounded-[10px] px-[12px] py-[8px] hover:bg-[#f5f5f5]\">\n            <a className='flex' href={item.url} target=\"_blank\" rel=\"noreferrer\">\n              {item?.cover_image && (\n                <img\n                  className='mr-[6px] h-[100px] w-[100px] rounded-[8px] object-cover'\n                  src={item?.cover_image?.url}\n                  alt=\"图片\"\n                />\n              )}\n              <div className='flex flex-col justify-between'>\n                <h2\n                  className={cn(\n                    'line-clamp-2 text-[16px] leading-[24px]',\n                  )}\n                >\n                {item.title}\n                </h2>\n                <p\n                  className={cn(\n                    'line-clamp-2 text-[14px] leading-[22px] text-[#70757f]',\n                  )}\n                >\n                  {item.summary}\n                </p>\n                <div>\n                  <span className='text-[14px] text-[#70757f]'>{item.site_name}</span>\n                </div>\n              </div>\n            </a>\n          </li>\n        ))}\n      </ul>\n    </div>\n    )\n  }\n\n  return (\n    isMobile ? <div className={cn('absolute left-[0px] top-[0px] z-[999] flex h-full w-full items-end bg-[#0009] transition-opacity duration-100 ease-linear', !visible && 'pointer-events-none opacity-0 delay-200')}>\n      <div className={cn('w-full transition-all duration-300 ease-linear', visible ? 'h-[90%] opacity-100' : 'h-[0px] opacity-0')}>\n        {renderContent()}\n      </div>\n    </div>\n    : <div className={cn('z-10 border-l border-[#e8e8e8] transition-all duration-300 ease-out', visible ? 'w-[400px] opacity-100' : 'w-[0px] opacity-0')}>\n      {/* <KnowledgeGraph/> */}\n      {renderContent()}\n    </div>\n  )\n}\n\nexport default RightSidebar\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AAKA;;;;;;;;;;;;;;;;AAGO,MAAM,sBAAsB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAmH;IACtM,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,4BAAyB,AAAD;IACpE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAA8B;IACjE,MAAM,QAAQ,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAU,CAAC;IAC9B,MAAM,WAAW,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE,CAAC;IACzB,MAAM,UAAU,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS,EAAE,MAAM;QAEvB,IAAG,OAAO,OAAO,EAAE,aAAa,QAAQ,OAAO,EAAE;YAC/C,aAAa,MAAM,OAAO;YAC1B,MAAM,MAAM,UAAU,UAAU,YAAY,QAAQ;YACpD,SAAS,OAAO,GAAG,OAAO,OAAO,EAAE,YAAa,OAAO,OAAO,OAAO,CAAC,SAAS,IAAI,IAAK,CAAC;YACzF,UAAU;QACZ;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS,EAAE,MAAM;QAEvB,IAAG,OAAO,OAAO,EAAE,WAAW;YAC5B,MAAM,OAAO,GAAG,OAAO,UAAU,CAAC;gBAChC,UAAU;gBACV,SAAS,OAAO,GAAG,CAAC;YACtB,GAAG;QACL;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,SAAS,EAAE,MAAM;QAEvB,IAAG,OAAO,OAAO,EAAE,WAAW;YAC5B,EAAE,cAAc;YAEhB,MAAM,QAAQ,OAAO,OAAO,OAAO,EAAE,aAAa;YAClD,MAAM,OAAO,UAAU,CAAC,MAAM;YAE9B,IAAG,MAAM;gBACP,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG,KAAK,GAAG;gBACpB,KAAK,MAAM,GAAG;gBACd,KAAK,KAAK;YACZ;QACF;IACF;IAEA,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAG,UAAU;YACX,SAAS,eAAe,CAAC,gBAAgB,CAAC,cAAc,aAAa;gBAAE,SAAS;YAAM;QACxF,OACK;YACH,SAAS,eAAe,CAAC,gBAAgB,CAAC,aAAa;YACvD,SAAS,eAAe,CAAC,gBAAgB,CAAC,YAAY;QACxD;QAEA,OAAO;YACL,SAAS,eAAe,CAAC,mBAAmB,CAAC,cAAc;YAC3D,SAAS,eAAe,CAAC,mBAAmB,CAAC,aAAa;YAC1D,SAAS,eAAe,CAAC,mBAAmB,CAAC,YAAY;QAC3D;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,eAAe;QACnB,mBAAmB,kBAAkB,aAAa,OAAO;IAC3D;IAEA,MAAM,sBAAsB;QAC1B,IAAI,MAAM,OAAO,EAAE;YACjB,aAAa,MAAM,OAAO;YAC1B,MAAM,OAAO,GAAG,CAAC;QACnB;IACF;IAEA,MAAM,sBAAsB;QAC1B,UAAU;IACZ;IAEA,MAAM,qBAAqB,CAAC,EAAE,KAAK,EAAkB;QACnD,MAAM,OAAO,UAAU,CAAC,SAAS,OAAO,CAAC;QAEzC,IAAG,CAAC,MAAM,OAAO;QAEjB,qBACE,qZAAC;YAAE,WAAU;YAAoE,MAAM,MAAM;YAAK,QAAO;YAAS,OAAO;YAAO,cAAc;YAAqB,cAAc;;8BAC/K,qZAAC;oBAAG,WAAW,MAAM,CAAC,eAAe;8BAAG,MAAM;;;;;;8BAC9C,qZAAC;oBAAI,WAAU;;sCACb,qZAAC;sCAAM,MAAM;;;;;;sCACb,qZAAC,oSAAA,CAAA,UAAK;4BACJ,WAAU;4BACV,KAAK;4BACL,KAAI;4BACJ,OAAO;4BACP,QAAQ;;;;;;;;;;;;;;;;;;IAKlB;IAEA,qBACE,qZAAC;QAAI,WAAU;QAAY,cAAc,CAAA,IAAK,QAAQ,OAAO,GAAG;QAAM,cAAc,IAAM,QAAQ,OAAO,GAAG;;YACzG,UAAU,mBAAmB;gBAAE,OAAO;oBAAE,UAAU;oBAAS,KAAK,GAAG,KAAK,KAAK,CAAC,QAAQ,KAAK,EAAE,CAAC;oBAAE,MAAM,GAAG,QAAQ,KAAK,EAAE,CAAC;gBAAC;YAAE;0BAC7H,qZAAC;gBAAI,WAAU;0BACZ,WAAW,wBAAU;;sCACpB,qZAAC;4BAAI,WAAU;sCACb,cAAA,qZAAC,oSAAA,CAAA,UAAK;gCACJ,WAAU;gCACV,KAAK;gCACL,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;;;;;;sCAGZ,qZAAC;4BAAK,WAAU;sCAA2B,WAAW;;;;;;;iDAClD,qZAAC;oBAAI,WAAU;oBAAgG,SAAS;;sCAC1H,qZAAC,oSAAA,CAAA,UAAK;4BACJ,WAAU;4BACV,KAAK;4BACL,KAAI;4BACJ,OAAO;4BACP,QAAQ;;;;;;sCAEV,qZAAC;4BAAK,WAAU;sCAA2B;;;;;;sCAC3C,qZAAC,oSAAA,CAAA,UAAK;4BACJ,WAAU;4BACV,KAAK;4BACL,KAAI;4BACJ,OAAO;4BACP,QAAQ;;;;;;;;;;;;;;;;;YAIf;;;;;;;AAGP;AAEA,MAAM,eAAe,CAAC,EAAE,OAAO,EAAE,QAAQ,EAA2C;IAClF,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,4BAAyB,AAAD;IAEpE,MAAM,cAAc;QAClB,mBAAmB;IACrB;IAEA,2BAA2B;IAE3B,MAAM,gBAAgB;QACpB,qBACE,qZAAC;YAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE,+CAA+C,YAAY;;8BAC9E,qZAAC;oBAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE,+FAA+F,YAAY;;wBAA2B;sCAEvJ,qZAAC,oSAAA,CAAA,UAAK;4BACJ,WAAU;4BACV,KAAK;4BACL,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,SAAS;;;;;;;;;;;;8BAGb,qZAAC;oBAAG,WAAU;8BACX,eAAe,IAAI,CAAC,qBACnB,qZAAC;4BAAG,WAAU;sCACZ,cAAA,qZAAC;gCAAE,WAAU;gCAAO,MAAM,KAAK,GAAG;gCAAE,QAAO;gCAAS,KAAI;;oCACrD,MAAM,6BACL,qZAAC;wCACC,WAAU;wCACV,KAAK,MAAM,aAAa;wCACxB,KAAI;;;;;;kDAGR,qZAAC;wCAAI,WAAU;;0DACb,qZAAC;gDACC,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EACV;0DAGH,KAAK,KAAK;;;;;;0DAEX,qZAAC;gDACC,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EACV;0DAGD,KAAK,OAAO;;;;;;0DAEf,qZAAC;0DACC,cAAA,qZAAC;oDAAK,WAAU;8DAA8B,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS5E;IAEA,OACE,yBAAW,qZAAC;QAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE,6HAA6H,CAAC,WAAW;kBACrK,cAAA,qZAAC;YAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE,kDAAkD,UAAU,wBAAwB;sBACpG;;;;;;;;;;6BAGH,qZAAC;QAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE,uEAAuE,UAAU,0BAA0B;kBAE7H;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 4752, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/answer/annotation-content.tsx"], "sourcesContent": ["import { RightSidebarTrigger } from '../../chat-with-history/right-sidebar'\nimport BasicContent from './basic-content'\n\ntype Props = {\n    list: any[],\n    isMobile?: boolean\n}\n\nconst AnnotationContent = ({ list, isMobile }: Props) => {\n    return (\n        <div>\n            {list.map((item) => {\n               const temp = item?.execution_metadata?.agent_log?.[0]\n\n               return <RightSidebarTrigger process={temp.process} status={temp.status} isMobile={isMobile} references={temp.references}>\n                    <BasicContent item={{\n                        content: temp?.content || '',\n                    }} />\n                </RightSidebarTrigger>\n            })}\n        </div>\n    )\n}\n\nexport default AnnotationContent\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,oBAAoB,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAS;IAChD,qBACI,qZAAC;kBACI,KAAK,GAAG,CAAC,CAAC;YACR,MAAM,OAAO,MAAM,oBAAoB,WAAW,CAAC,EAAE;YAErD,qBAAO,qZAAC,0LAAA,CAAA,sBAAmB;gBAAC,SAAS,KAAK,OAAO;gBAAE,QAAQ,KAAK,MAAM;gBAAE,UAAU;gBAAU,YAAY,KAAK,UAAU;0BAClH,cAAA,qZAAC,wKAAA,CAAA,UAAY;oBAAC,MAAM;wBAChB,SAAS,MAAM,WAAW;oBAC9B;;;;;;;;;;;QAER;;;;;;AAGZ;uCAEe", "debugId": null}}, {"offset": {"line": 4798, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/constants.ts"], "sourcesContent": ["export const CONVERSATION_ID_INFO = 'conversationIdInfo'\nexport const UUID_NIL = '00000000-0000-0000-0000-000000000000'\n\n// 卡片展示类型\nexport const DISPLAY_TYPE = {\n    MCP: 'MCP',\n}\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,uBAAuB;AAC7B,MAAM,WAAW;AAGjB,MAAM,eAAe;IACxB,KAAK;AACT", "debugId": null}}, {"offset": {"line": 4814, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/answer/index.tsx"], "sourcesContent": ["import type {\n  FC,\n  ReactNode,\n} from 'react'\nimport { use<PERSON>allback, useEffect, useRef, useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport type {\n  ChatConfig,\n  ChatItem,\n} from '../../types'\nimport Button from '../../../button'\nimport { INTENT_INCLUDES_SCHEDULE, NEED_CREATE_MEETING } from '../u-const'\nimport Operation from './operation'\nimport AgentContent from './agent-content'\nimport BasicContent from './basic-content'\nimport SuggestedQuestions from './suggested-questions'\nimport More from './more'\nimport WorkflowProcessItem from './workflow-process'\nimport ScheduleCard from './schedule-card'\nimport styles from './index.module.css'\nimport Spinner from '@/components/base/spinner'\nimport LoadingAnim from '@/components/base/chat/chat/loading-anim'\nimport Citation from '@/components/base/chat/chat/citation'\nimport { EditTitle } from '@/components/app/annotation/edit-annotation-modal/edit-item'\nimport type { AppData, EmbedSource } from '@/models/share'\nimport AnswerIcon from '@/components/base/answer-icon'\nimport cn from '@/utils/classnames'\nimport { FileList } from '@/components/base/file-uploader'\nimport ContentSwitch from '../content-switch'\nimport { downloadFile } from '@/utils/index'\nimport { fetchAiPptExportApi } from '@/service/share'\nimport AnnotationContent from './annotation-content'\nimport { DISPLAY_TYPE } from '../../constants'\n\ntype SvgProps = {\n  className?: string\n}\nconst IconPptBtnLeft = ({ className }: SvgProps) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\" fill=\"currentColor\" className={className} viewBox=\"0 0 1024 1024\">\n    <path d=\"M40.96 168.96c0-25.6 20.48-40.96 40.96-40.96h855.04c25.6 0 40.96 20.48 40.96 40.96 0 25.6-20.48 40.96-40.96 40.96H87.04c-25.6 5.12-46.08-15.36-46.08-40.96\"></path>\n    <path d=\"M128 168.96c0-25.6 20.48-40.96 40.96-40.96h680.96c25.6 0 40.96 20.48 40.96 40.96v552.96c0 25.6-20.48 40.96-40.96 40.96H168.96c-25.6 0-40.96-20.48-40.96-40.96zm87.04 46.08v471.04h599.04V215.04z\"></path>\n    <path d=\"M440.32 312.32c15.36-15.36 46.08-15.36 61.44 0l107.52 107.52c15.36 15.36 15.36 46.08 0 61.44L501.76 588.8c-15.36 15.36-46.08 15.36-61.44 0s-15.36-46.08 0-61.44l76.8-76.8-76.8-76.8c-15.36-20.48-15.36-46.08 0-61.44m40.96 384c15.36-15.36 46.08-15.36 61.44 0l168.96 168.96c15.36 15.36 15.36 46.08 0 61.44s-46.08 15.36-61.44 0L512 783.36 373.76 921.6c-15.36 15.36-46.08 15.36-61.44 0s-15.36-46.08 0-61.44z\"></path>\n  </svg>\n\n)\nconst IconpPptbtnRight = ({ className }: SvgProps) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\" fill=\"currentColor\" className={className} viewBox=\"0 0 1024 1024\">\n    <path d=\"M353.849 798.151a42.667 42.667 0 0 1 0-60.302L579.698 512l-225.85-225.849a42.667 42.667 0 1 1 60.303-60.302l256 256a42.667 42.667 0 0 1 0 60.302l-256 256a42.667 42.667 0 0 1-60.302 0\"></path>\n  </svg>\n)\n\ntype AnswerProps = {\n  item: ChatItem\n  ppt: any\n  question: string\n  index: number\n  config?: ChatConfig\n  answerIcon?: ReactNode\n  responding?: boolean\n  showPromptLog?: boolean\n  chatAnswerContainerInner?: string\n  hideProcessDetail?: boolean\n  appData?: AppData\n  noChatInput?: boolean\n  switchSibling?: (siblingMessageId: string) => void\n  isLast?: boolean\n  embedSource?: EmbedSource\n  isMobile?: boolean\n  hideOperator?: boolean\n  onNewTopic?: () => void\n  onOneKeyGenerationPpt?: () => void\n  onOneKeyGenerationMeeting?: (data: any) => void\n  onEditClick?: () => void\n  onCreateSchedule?: (data: any) => void\n  updateIntent?: (data: any) => void\n  onSelectIntent?: (data: { question: string; intent: string }) => void\n  activeIntent?: string\n  generationPptLoading?: boolean\n}\n\nconst Answer: FC<AnswerProps> = ({\n  item,\n  ppt,\n  question,\n  index,\n  config,\n  answerIcon,\n  responding,\n  showPromptLog,\n  chatAnswerContainerInner,\n  hideProcessDetail,\n  appData,\n  noChatInput,\n  switchSibling,\n  onNewTopic,\n  onOneKeyGenerationPpt,\n  onOneKeyGenerationMeeting,\n  onEditClick,\n  isLast,\n  embedSource,\n  isMobile,\n  onCreateSchedule,\n  hideOperator,\n  updateIntent,\n  onSelectIntent,\n  activeIntent,\n  generationPptLoading,\n}) => {\n  const { t } = useTranslation()\n  const {\n    content,\n    citation,\n    agent_thoughts,\n    more,\n    annotation,\n    workflowProcess,\n    allFiles,\n    message_files,\n    intent,\n    llm, // 大模型\n    frontTip, // 前置提示\n    behindTip, // 后置提示\n    isPrompt, // 是否提示词（不展示工具栏）\n    intents = [], // 意图识别选项\n    meetingJsonData = {}, // 日程同步创建会议\n    displayType,\n  } = item\n  // 显示工作流\n  const { show_workflow_steps } = appData?.site ?? {}\n  const { id: pptId, taskId, thumbnail } = ppt\n  const mcpData = workflowProcess?.tracing?.filter(item => item.execution_metadata) || []\n\n  const hasAgentThoughts = !!agent_thoughts?.length\n  const isEmbedMobile = embedSource && isMobile\n\n  const [containerWidth, setContainerWidth] = useState(0)\n  const [contentWidth, setContentWidth] = useState(0)\n  const containerRef = useRef<HTMLDivElement>(null)\n  const contentRef = useRef<HTMLDivElement>(null)\n  const scheduleData = useRef<any>()\n  const [downLoading, setDownLoading] = useState(false)\n  const [downPdfLoading, setDownPdfLoading] = useState(false)\n\n  try {\n    const data = JSON.parse(content)\n    if (data.employees)\n      scheduleData.current = data\n  }\n  catch (err) {\n    // console.log(err)\n  }\n  const getContainerWidth = () => {\n    if (containerRef.current)\n      setContainerWidth(containerRef.current?.clientWidth + 16)\n  }\n  useEffect(() => {\n    getContainerWidth()\n  }, [])\n\n  const getContentWidth = () => {\n    if (contentRef.current)\n      setContentWidth(contentRef.current?.clientWidth)\n  }\n\n  const handleScheduleCancel = () => {\n    updateIntent?.('')\n  }\n\n  useEffect(() => {\n    if (!responding)\n      getContentWidth()\n  }, [responding])\n\n  const handlePptDowmload = async (ppt: any, fileType: string) => {\n    fileType === 'ppt' ? setDownLoading(true) : setDownPdfLoading(true)\n    const exportParms = {\n      id: ppt?.id,\n      format: fileType,\n      edit: 'true',\n      files_to_zip: 'true',\n    }\n    const res: any = await fetchAiPptExportApi(exportParms)\n    const dowmloadUrl = res?.data?.[0]\n    dowmloadUrl && downloadFile(dowmloadUrl)\n    fileType === 'ppt' ? setDownLoading(false) : setDownPdfLoading(false)\n  }\n\n  // Recalculate contentWidth when content changes (e.g., SVG preview/source toggle)\n  useEffect(() => {\n    if (!containerRef.current)\n      return\n    const resizeObserver = new ResizeObserver(() => {\n      getContentWidth()\n    })\n    resizeObserver.observe(containerRef.current)\n    return () => {\n      resizeObserver.disconnect()\n    }\n  }, [])\n\n  const NewTopicFooter = ({ intent }: { intent: string; onClick?: () => void }) => {\n    return (\n      <div className={`mt-[8px] flex items-center justify-center rounded-[8px] bg-[#EFF0F5] py-[5px] text-[12px] leading-[22px] text-[#5C6574]  ${isMobile && 'px-[20px]'}`}>\n        <p>已进入<span className='text-[#3B67FF]'>[{intent}]</span>场景，可点击对话框上方按钮切换至其他场景。</p>\n      </div>\n    )\n  }\n  // 是否显示一键生成PPT按钮\n  const hideOneKeyGenerationPptBtn = (content.length > 300 && (content.includes('3.') || content.includes('三、'))) || content.includes('```markdown')\n  const PptGenerationFooter = ({ intent, btnTxt, onClick }: { intent: string; btnTxt?: string; onClick?: () => void }) => {\n    return (\n      (intent.includes('PPT') && hideOneKeyGenerationPptBtn)\n        ? (\n          <div className=\"mt-[8px] flex\">\n            <Button className='h-[38px] cursor-pointer rounded-[10px] border-[#3B67FF] text-[14px] text-[#3B67FF] shadow-none' onClick={onClick}>\n              {\n                generationPptLoading\n                  ? <> PPT生成中 <Spinner loading={generationPptLoading} className='!ml-1 !h-3 !w-3 !border-2 !text-[#3B67FF]' /></>\n                  : <><IconPptBtnLeft className='mr-[4px] h-[20px] w-[20px]'/>{btnTxt || '一键生成PPT'}<IconpPptbtnRight className='ml-[4px] h-[20px] w-[20px]' /></>\n              }\n\n            </Button>\n          </div>\n        )\n        : null\n    )\n  }\n  const MeetingGenerationFooter = ({ intent, btnTxt, onClick }: { intent: string; btnTxt?: string; onClick?: () => void }) => {\n    return (\n      intent.includes(INTENT_INCLUDES_SCHEDULE) && <div className=\"mt-[8px] flex\">\n        <Button className='h-[38px] cursor-pointer rounded-[10px] border-[#3B67FF] text-[14px] text-[#3B67FF] shadow-none' onClick={onClick}>\n          {btnTxt || '同步预定腾讯会议'}<IconpPptbtnRight className='ml-[4px] h-[20px] w-[20px]' />\n        </Button>\n      </div>\n\n    )\n  }\n  const PptReslutShow = ({ thumbnail, onClick, onEditClick, onPdfClick }: { thumbnail: string; onClick?: (data: any) => void; onEditClick?: () => void; onPdfClick?: (data: any) => void }) => {\n    return (\n      thumbnail\n        ? (\n          <div className=\"mt-[8px] flex w-[61%] flex-col\">\n            {thumbnail && <span style={{\n              objectFit: 'contain',\n              aspectRatio: '16 / 9',\n              width: '100%',\n              cursor: 'pointer',\n            }} onClick={onEditClick}>\n              <img src={thumbnail} alt=\"ppt\" className=\"h-[100%] w-[100%] rounded-[4px]\"/>\n            </span>}\n            <div className=\"mt-[8px] flex\">\n              <Button loading={downLoading} className='h-[38px] cursor-pointer rounded-[10px] bg-[#1e86ff]  text-[14px] text-[#ffffff] shadow-none hover:bg-[#1e86ff]' onClick={onClick}>\n                下载PPT\n              </Button>\n              <Button loading={downPdfLoading} className='ml-[8px] h-[38px] cursor-pointer rounded-[10px] bg-[#1e86ff]  text-[14px] text-[#ffffff] shadow-none hover:bg-[#1e86ff]' onClick={onPdfClick}>\n                下载PDF\n              </Button>\n              <Button className='ml-[8px] h-[38px] cursor-pointer rounded-[10px] text-[14px] shadow-none' onClick={onEditClick}>\n                编辑PPT\n              </Button>\n            </div>\n          </div>\n        )\n        : null\n    )\n  }\n\n  const renderOperations = (className?: string) => (\n    <Operation\n      className={className}\n      embedSource={embedSource}\n      isMobile={isMobile}\n      hasWorkflowProcess={!!workflowProcess}\n      maxSize={containerWidth - contentWidth - 4}\n      contentWidth={contentWidth}\n      item={item}\n      question={question}\n      index={index}\n      showPromptLog={showPromptLog}\n      noChatInput={noChatInput}\n    />\n  )\n\n  const handleSwitchSibling = useCallback((direction: 'prev' | 'next') => {\n    if (direction === 'prev')\n      item.prevSibling && switchSibling?.(item.prevSibling)\n    else\n      item.nextSibling && switchSibling?.(item.nextSibling)\n  }, [switchSibling, item.prevSibling, item.nextSibling])\n\n  return (\n    <div>\n      {frontTip && <div className='mx-auto mb-[30px] mt-[6px] w-fit min-w-[100px] rounded-[4px] bg-[#DFE4E8] px-[20px] py-[6px] text-center text-[12px] text-[#434B5B]'>{frontTip}</div>}\n      <div className='mb-2 flex last:mb-0'>\n        <div className='relative h-10 w-10 shrink-0'>\n          {answerIcon || <AnswerIcon />}\n          {responding && (\n            <div className='absolute left-[-3px] top-[-3px] flex h-4 w-4 items-center rounded-full border-[0.5px] border-divider-subtle bg-background-section-burn pl-[6px] shadow-xs'>\n              <LoadingAnim type='avatar' />\n            </div>\n          )}\n        </div>\n        <div className='chat-answer-container group ml-4 w-0 grow pb-4' ref={containerRef}>\n          <div className={cn('group relative pr-10', chatAnswerContainerInner, isEmbedMobile && 'pr-[0px]')}>\n            <div\n              ref={contentRef}\n              className={cn('body-lg-regular relative inline-block max-w-full rounded-2xl bg-chat-bubble-bg px-4 py-3 text-text-primary', workflowProcess && 'w-full')}\n            >\n              {\n                (!responding && !embedSource) && renderOperations()\n              }\n              {/** Render the normal steps */}\n              {\n                workflowProcess && !hideProcessDetail && show_workflow_steps && (\n                  <WorkflowProcessItem\n                    data={workflowProcess}\n                    item={item}\n                    hideProcessDetail={hideProcessDetail}\n                  />\n                )\n              }\n              {/** Hide workflow steps by it's settings in siteInfo */}\n              {\n                workflowProcess && hideProcessDetail && show_workflow_steps && appData && (\n                  <WorkflowProcessItem\n                    data={workflowProcess}\n                    item={item}\n                    hideProcessDetail={hideProcessDetail}\n                    readonly={!appData.site.show_workflow_steps}\n                  />\n                )\n              }\n              {\n                responding && !content && !hasAgentThoughts && !mcpData.length && (\n                  <div className='flex h-5 w-6 items-center justify-center'>\n                    {embedSource ? <span className={styles['lark-loader']}></span> : <LoadingAnim type='text' />}\n                  </div>\n                )\n              }\n              {scheduleData.current ? (\n                <ScheduleCard isEmbedMobile={Boolean(embedSource && isMobile)} isLast={isLast} data={scheduleData.current} onConfirm={onCreateSchedule} onCancel={handleScheduleCancel} />\n              ) : displayType === DISPLAY_TYPE.MCP\n              ? <AnnotationContent isMobile={isMobile} list={mcpData}/>\n              : (\n                <>\n\n                  {\n                    content && !hasAgentThoughts && (\n                      <BasicContent item={item} />\n                    )\n                  }\n                  {\n                    (hasAgentThoughts) && (\n                      <AgentContent\n                        item={item}\n                        responding={responding}\n                        content={content}\n                      />\n                    )\n                  }\n                  {\n                    !!allFiles?.length && !embedSource && (\n                      <FileList\n                        className='my-1'\n                        files={allFiles}\n                        showDeleteAction={false}\n                        showDownloadAction\n                        canPreview\n                      />\n                    )\n                  }\n                  {\n                    !!message_files?.length && (\n                      <FileList\n                        className='my-1'\n                        files={message_files}\n                        showDeleteAction={false}\n                        showDownloadAction\n                        canPreview\n                      />\n                    )\n                  }\n                </>\n               )}\n              {\n                annotation?.id && annotation.authorName && (\n                  <EditTitle\n                    className='mt-1'\n                    title={t('appAnnotation.editBy', { author: annotation.authorName })}\n                  />\n                )\n              }\n              <SuggestedQuestions item={item} />\n              {\n                !!citation?.length && !responding && (\n                  <Citation data={citation} showHitInfo={config?.supportCitationHitInfo} />\n                )\n              }\n              {\n                item.siblingCount && item.siblingCount > 1 && item.siblingIndex !== undefined && (\n                  <ContentSwitch\n                    count={item.siblingCount}\n                    currentIndex={item.siblingIndex}\n                    prevDisabled={!item.prevSibling}\n                    nextDisabled={!item.nextSibling}\n                    switchSibling={handleSwitchSibling}\n                  />\n                )\n              }\n              {!responding && activeIntent && isLast && <NewTopicFooter intent={activeIntent} onClick={onNewTopic} />}\n              {!responding && activeIntent && isLast && !thumbnail && <PptGenerationFooter intent={activeIntent} onClick={onOneKeyGenerationPpt} />}\n              {!responding && isLast && activeIntent && meetingJsonData?.event === NEED_CREATE_MEETING && <MeetingGenerationFooter intent={activeIntent} onClick={() => { onOneKeyGenerationMeeting?.(meetingJsonData) } } />}\n              {thumbnail && <PptReslutShow\n                thumbnail={thumbnail}\n                onClick={() => { handlePptDowmload(ppt, 'ppt') }}\n                onPdfClick={() => { handlePptDowmload(ppt, 'pdf') }}\n                onEditClick={onEditClick} />}\n              {\n                // (!responding && embedSource && !hideOperator && !isPrompt && !intents.length) && renderOperations('mb-[10px]')\n                (!responding && embedSource) && renderOperations('mb-[10px]')\n              }\n            </div>\n            {!!intents?.length && <ul className=\"mb-[20px] mt-[10px] flex gap-x-[10px] pr-14\">\n              {intents.map((intent, index) => (\n                <li className={`flex h-[32px] cursor-pointer items-center rounded-[16px] border bg-[#fff] px-[10px] text-[14px] ${intent === activeIntent ? 'bg-gradient-to-r from-[#5099FF] to-[#7D67FF] text-[#ffffff]' : ''}` } key={index}\n                    onClick={() => { onSelectIntent?.({ question, intent }) }}>{intent}</li>\n              ))}\n            </ul>}\n            {!intents?.length && <More more={more} />}\n          </div>\n        </div>\n      </div>\n      {behindTip && <div className='mx-auto mb-[30px] mt-[10px] w-fit min-w-[100px] rounded-[4px] bg-[#DFE4E8] px-[20px] py-[6px] text-center text-[12px] text-[#434B5B]'>{behindTip}</div>}\n    </div>\n  )\n}\n\nexport default Answer\n\n// export default memo(Answer, (prevProps, nextProps) =>\n//   prevProps.responding === false && nextProps.responding === false && prevProps.activeIntent === nextProps.activeIntent,\n// )\n"], "names": [], "mappings": ";;;;AAIA;;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA;;;;;;;;;;;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAY,iBAC7C,qZAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,MAAK;QAAe,WAAW;QAAW,SAAQ;;0BAC/G,qZAAC;gBAAK,GAAE;;;;;;0BACR,qZAAC;gBAAK,GAAE;;;;;;0BACR,qZAAC;gBAAK,GAAE;;;;;;;;;;;;AAIZ,MAAM,mBAAmB,CAAC,EAAE,SAAS,EAAY,iBAC/C,qZAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,MAAK;QAAe,WAAW;QAAW,SAAQ;kBAC/G,cAAA,qZAAC;YAAK,GAAE;;;;;;;;;;;AAiCZ,MAAM,SAA0B,CAAC,EAC/B,IAAI,EACJ,GAAG,EACH,QAAQ,EACR,KAAK,EACL,MAAM,EACN,UAAU,EACV,UAAU,EACV,aAAa,EACb,wBAAwB,EACxB,iBAAiB,EACjB,OAAO,EACP,WAAW,EACX,aAAa,EACb,UAAU,EACV,qBAAqB,EACrB,yBAAyB,EACzB,WAAW,EACX,MAAM,EACN,WAAW,EACX,QAAQ,EACR,gBAAgB,EAChB,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,YAAY,EACZ,oBAAoB,EACrB;IACC,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,EACJ,OAAO,EACP,QAAQ,EACR,cAAc,EACd,IAAI,EACJ,UAAU,EACV,eAAe,EACf,QAAQ,EACR,aAAa,EACb,MAAM,EACN,GAAG,EACH,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,UAAU,EAAE,EACZ,kBAAkB,CAAC,CAAC,EACpB,WAAW,EACZ,GAAG;IACJ,QAAQ;IACR,MAAM,EAAE,mBAAmB,EAAE,GAAG,SAAS,QAAQ,CAAC;IAClD,MAAM,EAAE,IAAI,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;IACzC,MAAM,UAAU,iBAAiB,SAAS,OAAO,CAAA,OAAQ,KAAK,kBAAkB,KAAK,EAAE;IAEvF,MAAM,mBAAmB,CAAC,CAAC,gBAAgB;IAC3C,MAAM,gBAAgB,eAAe;IAErC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,aAAa,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD;IAC1B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,IAAI;QACF,MAAM,OAAO,KAAK,KAAK,CAAC;QACxB,IAAI,KAAK,SAAS,EAChB,aAAa,OAAO,GAAG;IAC3B,EACA,OAAO,KAAK;IACV,mBAAmB;IACrB;IACA,MAAM,oBAAoB;QACxB,IAAI,aAAa,OAAO,EACtB,kBAAkB,aAAa,OAAO,EAAE,cAAc;IAC1D;IACA,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI,WAAW,OAAO,EACpB,gBAAgB,WAAW,OAAO,EAAE;IACxC;IAEA,MAAM,uBAAuB;QAC3B,eAAe;IACjB;IAEA,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,YACH;IACJ,GAAG;QAAC;KAAW;IAEf,MAAM,oBAAoB,OAAO,KAAU;QACzC,aAAa,QAAQ,eAAe,QAAQ,kBAAkB;QAC9D,MAAM,cAAc;YAClB,IAAI,KAAK;YACT,QAAQ;YACR,MAAM;YACN,cAAc;QAChB;QACA,MAAM,MAAW,MAAM,oBAAoB;QAC3C,MAAM,cAAc,KAAK,MAAM,CAAC,EAAE;QAClC,eAAe,aAAa;QAC5B,aAAa,QAAQ,eAAe,SAAS,kBAAkB;IACjE;IAEA,kFAAkF;IAClF,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,OAAO,EACvB;QACF,MAAM,iBAAiB,IAAI,eAAe;YACxC;QACF;QACA,eAAe,OAAO,CAAC,aAAa,OAAO;QAC3C,OAAO;YACL,eAAe,UAAU;QAC3B;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC,EAAE,MAAM,EAA4C;QAC1E,qBACE,qZAAC;YAAI,WAAW,CAAC,yHAAyH,EAAE,YAAY,aAAa;sBACnK,cAAA,qZAAC;;oBAAE;kCAAG,qZAAC;wBAAK,WAAU;;4BAAiB;4BAAE;4BAAO;;;;;;;oBAAQ;;;;;;;;;;;;IAG9D;IACA,gBAAgB;IAChB,MAAM,6BAA6B,AAAC,QAAQ,MAAM,GAAG,OAAO,CAAC,QAAQ,QAAQ,CAAC,SAAS,QAAQ,QAAQ,CAAC,KAAK,KAAM,QAAQ,QAAQ,CAAC;IACpI,MAAM,sBAAsB,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAA6D;QACjH,OACE,AAAC,OAAO,QAAQ,CAAC,UAAU,2CAEvB,qZAAC;YAAI,WAAU;sBACb,cAAA,qZAAC,6IAAA,CAAA,UAAM;gBAAC,WAAU;gBAAiG,SAAS;0BAExH,qCACI;;wBAAE;sCAAQ,qZAAC;4BAAQ,SAAS;4BAAsB,WAAU;;;;;;;iDAC5D;;sCAAE,qZAAC;4BAAe,WAAU;;;;;;wBAA+B,UAAU;sCAAU,qZAAC;4BAAiB,WAAU;;;;;;;;;;;;;;;;;mBAMrH;IAER;IACA,MAAM,0BAA0B,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAA6D;QACrH,OACE,OAAO,QAAQ,CAAC,wJAAA,CAAA,2BAAwB,mBAAK,qZAAC;YAAI,WAAU;sBAC1D,cAAA,qZAAC,6IAAA,CAAA,UAAM;gBAAC,WAAU;gBAAiG,SAAS;;oBACzH,UAAU;kCAAW,qZAAC;wBAAiB,WAAU;;;;;;;;;;;;;;;;;IAK1D;IACA,MAAM,gBAAgB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAoH;QACtL,OACE,0BAEI,qZAAC;YAAI,WAAU;;gBACZ,2BAAa,qZAAC;oBAAK,OAAO;wBACzB,WAAW;wBACX,aAAa;wBACb,OAAO;wBACP,QAAQ;oBACV;oBAAG,SAAS;8BACV,cAAA,qZAAC;wBAAI,KAAK;wBAAW,KAAI;wBAAM,WAAU;;;;;;;;;;;8BAE3C,qZAAC;oBAAI,WAAU;;sCACb,qZAAC,6IAAA,CAAA,UAAM;4BAAC,SAAS;4BAAa,WAAU;4BAAiH,SAAS;sCAAS;;;;;;sCAG3K,qZAAC,6IAAA,CAAA,UAAM;4BAAC,SAAS;4BAAgB,WAAU;4BAA0H,SAAS;sCAAY;;;;;;sCAG1L,qZAAC,6IAAA,CAAA,UAAM;4BAAC,WAAU;4BAA0E,SAAS;sCAAa;;;;;;;;;;;;;;;;;mBAMtH;IAER;IAEA,MAAM,mBAAmB,CAAC,0BACxB,qZAAC,iKAAA,CAAA,UAAS;YACR,WAAW;YACX,aAAa;YACb,UAAU;YACV,oBAAoB,CAAC,CAAC;YACtB,SAAS,iBAAiB,eAAe;YACzC,cAAc;YACd,MAAM;YACN,UAAU;YACV,OAAO;YACP,eAAe;YACf,aAAa;;;;;;IAIjB,MAAM,sBAAsB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,IAAI,cAAc,QAChB,KAAK,WAAW,IAAI,gBAAgB,KAAK,WAAW;aAEpD,KAAK,WAAW,IAAI,gBAAgB,KAAK,WAAW;IACxD,GAAG;QAAC;QAAe,KAAK,WAAW;QAAE,KAAK,WAAW;KAAC;IAEtD,qBACE,qZAAC;;YACE,0BAAY,qZAAC;gBAAI,WAAU;0BAAuI;;;;;;0BACnK,qZAAC;gBAAI,WAAU;;kCACb,qZAAC;wBAAI,WAAU;;4BACZ,4BAAc,qZAAC;;;;;4BACf,4BACC,qZAAC;gCAAI,WAAU;0CACb,cAAA,qZAAC;oCAAY,MAAK;;;;;;;;;;;;;;;;;kCAIxB,qZAAC;wBAAI,WAAU;wBAAiD,KAAK;kCACnE,cAAA,qZAAC;4BAAI,WAAW,GAAG,wBAAwB,0BAA0B,iBAAiB;;8CACpF,qZAAC;oCACC,KAAK;oCACL,WAAW,GAAG,8GAA8G,mBAAmB;;wCAG5I,CAAC,cAAc,CAAC,eAAgB;wCAIjC,mBAAmB,CAAC,qBAAqB,qCACvC,qZAAC,2KAAA,CAAA,UAAmB;4CAClB,MAAM;4CACN,MAAM;4CACN,mBAAmB;;;;;;wCAMvB,mBAAmB,qBAAqB,uBAAuB,yBAC7D,qZAAC,2KAAA,CAAA,UAAmB;4CAClB,MAAM;4CACN,MAAM;4CACN,mBAAmB;4CACnB,UAAU,CAAC,QAAQ,IAAI,CAAC,mBAAmB;;;;;;wCAK/C,cAAc,CAAC,WAAW,CAAC,oBAAoB,CAAC,QAAQ,MAAM,kBAC5D,qZAAC;4CAAI,WAAU;sDACZ,4BAAc,qZAAC;gDAAK,WAAW,wKAAA,CAAA,UAAM,CAAC,cAAc;;;;;qEAAY,qZAAC;gDAAY,MAAK;;;;;;;;;;;wCAIxF,aAAa,OAAO,iBACnB,qZAAC,wKAAA,CAAA,UAAY;4CAAC,eAAe,QAAQ,eAAe;4CAAW,QAAQ;4CAAQ,MAAM,aAAa,OAAO;4CAAE,WAAW;4CAAkB,UAAU;;;;;mDAChJ,gBAAgB,8IAAA,CAAA,eAAY,CAAC,GAAG,iBAClC,qZAAC,6KAAA,CAAA,UAAiB;4CAAC,UAAU;4CAAU,MAAM;;;;;iEAE7C;;gDAGI,WAAW,CAAC,kCACV,qZAAC,wKAAA,CAAA,UAAY;oDAAC,MAAM;;;;;;gDAIrB,kCACC,qZAAC,wKAAA,CAAA,UAAY;oDACX,MAAM;oDACN,YAAY;oDACZ,SAAS;;;;;;gDAKb,CAAC,CAAC,UAAU,UAAU,CAAC,6BACrB,qZAAC;oDACC,WAAU;oDACV,OAAO;oDACP,kBAAkB;oDAClB,kBAAkB;oDAClB,UAAU;;;;;;gDAKd,CAAC,CAAC,eAAe,wBACf,qZAAC;oDACC,WAAU;oDACV,OAAO;oDACP,kBAAkB;oDAClB,kBAAkB;oDAClB,UAAU;;;;;;;;wCAOlB,YAAY,MAAM,WAAW,UAAU,kBACrC,qZAAC;4CACC,WAAU;4CACV,OAAO,EAAE,wBAAwB;gDAAE,QAAQ,WAAW,UAAU;4CAAC;;;;;;sDAIvE,qZAAC,8KAAA,CAAA,UAAkB;4CAAC,MAAM;;;;;;wCAExB,CAAC,CAAC,UAAU,UAAU,CAAC,4BACrB,qZAAC;4CAAS,MAAM;4CAAU,aAAa,QAAQ;;;;;;wCAIjD,KAAK,YAAY,IAAI,KAAK,YAAY,GAAG,KAAK,KAAK,YAAY,KAAK,2BAClE,qZAAC,+JAAA,CAAA,UAAa;4CACZ,OAAO,KAAK,YAAY;4CACxB,cAAc,KAAK,YAAY;4CAC/B,cAAc,CAAC,KAAK,WAAW;4CAC/B,cAAc,CAAC,KAAK,WAAW;4CAC/B,eAAe;;;;;;wCAIpB,CAAC,cAAc,gBAAgB,wBAAU,qZAAC;4CAAe,QAAQ;4CAAc,SAAS;;;;;;wCACxF,CAAC,cAAc,gBAAgB,UAAU,CAAC,2BAAa,qZAAC;4CAAoB,QAAQ;4CAAc,SAAS;;;;;;wCAC3G,CAAC,cAAc,UAAU,gBAAgB,iBAAiB,UAAU,wJAAA,CAAA,sBAAmB,kBAAI,qZAAC;4CAAwB,QAAQ;4CAAc,SAAS;gDAAQ,4BAA4B;4CAAiB;;;;;;wCACxM,2BAAa,qZAAC;4CACb,WAAW;4CACX,SAAS;gDAAQ,kBAAkB,KAAK;4CAAO;4CAC/C,YAAY;gDAAQ,kBAAkB,KAAK;4CAAO;4CAClD,aAAa;;;;;;wCAEb,iHAAiH;wCAChH,CAAC,cAAc,eAAgB,iBAAiB;;;;;;;gCAGpD,CAAC,CAAC,SAAS,wBAAU,qZAAC;oCAAG,WAAU;8CACjC,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,qZAAC;4CAAG,WAAW,CAAC,gGAAgG,EAAE,WAAW,eAAe,gEAAgE,IAAI;4CAC5M,SAAS;gDAAQ,iBAAiB;oDAAE;oDAAU;gDAAO;4CAAG;sDAAI;2CADwJ;;;;;;;;;;gCAI3N,CAAC,SAAS,wBAAU,qZAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;YAItC,2BAAa,qZAAC;gBAAI,WAAU;0BAAwI;;;;;;;;;;;;AAG3K;uCAEe,OAEf,wDAAwD;CACxD,2HAA2H;CAC3H,IAAI", "debugId": null}}, {"offset": {"line": 5532, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/check-input-forms-hooks.ts"], "sourcesContent": ["import { useCallback } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport type { InputForm } from './type'\nimport { useToastContext } from '@/components/base/toast'\nimport { InputVarType } from '@/components/workflow/types'\nimport { TransferMethod } from '@/types/app'\n\nexport const useCheckInputsForms = () => {\n  const { t } = useTranslation()\n  const { notify } = useToastContext()\n\n  const checkInputsForm = useCallback((inputs: Record<string, any>, inputsForm: InputForm[]) => {\n    let hasEmptyInput = ''\n    let fileIsUploading = false\n    const requiredVars = inputsForm.filter(({ required }) => required)\n\n    if (requiredVars?.length) {\n      requiredVars.forEach(({ variable, label, type }) => {\n        if (hasEmptyInput)\n          return\n\n        if (fileIsUploading)\n          return\n\n        if (!inputs[variable])\n          hasEmptyInput = label as string\n\n        if ((type === InputVarType.singleFile || type === InputVarType.multiFiles) && inputs[variable]) {\n          const files = inputs[variable]\n          if (Array.isArray(files))\n            fileIsUploading = files.find(item => item.transferMethod === TransferMethod.local_file && !item.uploadedId)\n          else\n            fileIsUploading = files.transferMethod === TransferMethod.local_file && !files.uploadedId\n        }\n      })\n    }\n\n    if (hasEmptyInput) {\n      notify({ type: 'error', message: t('appDebug.errorMessage.valueOfVarRequired', { key: hasEmptyInput }) })\n      return false\n    }\n\n    if (fileIsUploading) {\n      notify({ type: 'info', message: t('appDebug.errorMessage.waitForFileUpload') })\n      return\n    }\n\n    return true\n  }, [notify, t])\n\n  return {\n    checkInputsForm,\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,MAAM,sBAAsB;IACjC,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,kBAAkB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA6B;QAChE,IAAI,gBAAgB;QACpB,IAAI,kBAAkB;QACtB,MAAM,eAAe,WAAW,MAAM,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAK;QAEzD,IAAI,cAAc,QAAQ;YACxB,aAAa,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;gBAC7C,IAAI,eACF;gBAEF,IAAI,iBACF;gBAEF,IAAI,CAAC,MAAM,CAAC,SAAS,EACnB,gBAAgB;gBAElB,IAAI,CAAC,SAAS,aAAa,UAAU,IAAI,SAAS,aAAa,UAAU,KAAK,MAAM,CAAC,SAAS,EAAE;oBAC9F,MAAM,QAAQ,MAAM,CAAC,SAAS;oBAC9B,IAAI,MAAM,OAAO,CAAC,QAChB,kBAAkB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,cAAc,KAAK,eAAe,UAAU,IAAI,CAAC,KAAK,UAAU;yBAE1G,kBAAkB,MAAM,cAAc,KAAK,eAAe,UAAU,IAAI,CAAC,MAAM,UAAU;gBAC7F;YACF;QACF;QAEA,IAAI,eAAe;YACjB,OAAO;gBAAE,MAAM;gBAAS,SAAS,EAAE,4CAA4C;oBAAE,KAAK;gBAAc;YAAG;YACvG,OAAO;QACT;QAEA,IAAI,iBAAiB;YACnB,OAAO;gBAAE,MAAM;gBAAQ,SAAS,EAAE;YAA2C;YAC7E;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAQ;KAAE;IAEd,OAAO;QACL;IACF;AACF", "debugId": null}}, {"offset": {"line": 5611, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/chat-input-area/hooks.ts"], "sourcesContent": ["import {\n  useCallback,\n  useRef,\n  useState,\n} from 'react'\n\nexport const useTextAreaHeight = (embedSource) => {\n  const wrapperRef = useRef<HTMLDivElement>(null)\n  const textareaRef = useRef<HTMLTextAreaElement | undefined>(undefined)\n  const textValueRef = useRef<HTMLDivElement>(null)\n  const holdSpaceRef = useRef<HTMLDivElement>(null)\n  const [isMultipleLine, setIsMultipleLine] = useState(false)\n\n  const handleComputeHeight = useCallback(() => {\n    const textareaElement = textareaRef.current\n\n    if (wrapperRef.current && textareaElement && textValueRef.current && holdSpaceRef.current) {\n      const { width: wrapperWidth } = wrapperRef.current.getBoundingClientRect()\n      const { height: textareaHeight } = textareaElement.getBoundingClientRect()\n      const { width: textValueWidth } = textValueRef.current.getBoundingClientRect()\n      const { width: holdSpaceWidth } = holdSpaceRef.current.getBoundingClientRect()\n\n      if (textareaHeight > 32) {\n        setIsMultipleLine(true)\n      }\n      else {\n        if(embedSource) {\n          setIsMultipleLine(false)\n        }\n        else {\n          if (textValueWidth + holdSpaceWidth >= wrapperWidth)\n            setIsMultipleLine(true)\n          else\n            setIsMultipleLine(false)\n        }\n      }\n    }\n  }, [embedSource])\n\n  const handleTextareaResize = useCallback(() => {\n    handleComputeHeight()\n  }, [handleComputeHeight])\n\n  return {\n    wrapperRef,\n    textareaRef,\n    textValueRef,\n    holdSpaceRef,\n    handleTextareaResize,\n    isMultipleLine,\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAMO,MAAM,oBAAoB,CAAC;IAChC,MAAM,aAAa,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,cAAc,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAmC;IAC5D,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,sBAAsB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACtC,MAAM,kBAAkB,YAAY,OAAO;QAE3C,IAAI,WAAW,OAAO,IAAI,mBAAmB,aAAa,OAAO,IAAI,aAAa,OAAO,EAAE;YACzF,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,WAAW,OAAO,CAAC,qBAAqB;YACxE,MAAM,EAAE,QAAQ,cAAc,EAAE,GAAG,gBAAgB,qBAAqB;YACxE,MAAM,EAAE,OAAO,cAAc,EAAE,GAAG,aAAa,OAAO,CAAC,qBAAqB;YAC5E,MAAM,EAAE,OAAO,cAAc,EAAE,GAAG,aAAa,OAAO,CAAC,qBAAqB;YAE5E,IAAI,iBAAiB,IAAI;gBACvB,kBAAkB;YACpB,OACK;gBACH,IAAG,aAAa;oBACd,kBAAkB;gBACpB,OACK;oBACH,IAAI,iBAAiB,kBAAkB,cACrC,kBAAkB;yBAElB,kBAAkB;gBACtB;YACF;QACF;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,uBAAuB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACvC;IACF,GAAG;QAAC;KAAoB;IAExB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 5663, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/chat-input-area/operation.tsx"], "sourcesContent": ["import { memo } from 'react'\nimport {\n  RiMicLine,\n  RiSendPlane2Fill,\n} from '@remixicon/react'\nimport type {\n  EnableType,\n} from '../../types'\nimport type { Theme } from '../../embedded-chatbot/theme/theme-context'\nimport { useChatWithHistoryContext } from '../../chat-with-history/context'\nimport { INTENT_DOCUMENT_ANALYSIS, INTENT_FREE_TALK } from '@/components/base/chat/chat/u-const'\nimport Button from '@/components/base/button'\nimport ActionButton from '@/components/base/action-button'\nimport FileUploaderInChatInput from '@/components/base/file-uploader/file-uploader-in-chat-input'\nimport type { FileUpload } from '@/components/base/features/types'\nimport cn from '@/utils/classnames'\nimport type { EmbedSource } from '@/models/share'\nimport { EMBED_SOURCE_TYPE } from '@/config'\n\ntype OperationProps = {\n  fileConfig?: FileUpload\n  speechToTextConfig?: EnableType\n  onShowVoiceInput?: () => void\n  onSend: () => void\n  theme?: Theme | null\n  intent?: string\n  embedSource?: EmbedSource\n  isMobile?: boolean\n}\nconst Operation = (\n  {\n    ref,\n    fileConfig,\n    speechToTextConfig,\n    onShowVoiceInput,\n    onSend,\n    theme,\n    intent,\n    embedSource,\n    isMobile,\n  }: OperationProps & {\n    ref: React.RefObject<HTMLDivElement>;\n  },\n) => {\n  const { appData } = useChatWithHistoryContext()\n  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}\n  const btnBgGradientFrom = chatPageConfigData?.btnBgGradientFrom || '#5099FF'\n  const btnBgGradientTo = chatPageConfigData?.btnBgGradientTo || '#7D67FF'\n\n  /**\n   * intent为INTENT_DOCUMENT_ANALYSIS，embedSource为lark(中海)，则显示文件上传按钮；\n   * intent为INTENT_FREE_TALK，embedSource为wework(中建)，则显示文件上传按钮；\n   */\n  function shouldShowUpload(embedSource: string | undefined, intent: string | undefined): boolean {\n    if (!embedSource || !intent)\n      return false // 如果 embedSource 或 intent 为空，直接返回 false\n\n    const validCombinations: { [key: string]: string[] } = {\n      [EMBED_SOURCE_TYPE.coli688]: [INTENT_DOCUMENT_ANALYSIS],\n      [EMBED_SOURCE_TYPE.FS]: [INTENT_DOCUMENT_ANALYSIS],\n      [EMBED_SOURCE_TYPE.ZJT]: [INTENT_FREE_TALK],\n      [EMBED_SOURCE_TYPE.ZJ4A]: [INTENT_FREE_TALK],\n    }\n\n    // 检查是否存在有效的组合\n    return validCombinations[embedSource]?.includes(intent) ?? false\n  }\n  const isShowUpload = shouldShowUpload(embedSource, intent)\n\n  return (\n    <div\n      className={cn(\n        'flex shrink-0 items-center justify-end',\n      )}\n    >\n      <div\n        className='flex items-center pl-1'\n        ref={ref}\n      >\n        <div className='flex items-center space-x-1'>\n          {\n            embedSource\n              ? (isShowUpload && fileConfig?.enabled && <FileUploaderInChatInput fileConfig={fileConfig}/>)\n              : (fileConfig?.enabled && <FileUploaderInChatInput fileConfig={fileConfig}/>)\n          }\n          {\n            speechToTextConfig?.enabled && (\n              <ActionButton\n                size='l'\n                onClick={onShowVoiceInput}\n              >\n                <RiMicLine className='h-5 w-5' />\n              </ActionButton>\n            )\n          }\n        </div>\n        <Button\n          className={`ml-3 w-8 px-0 ${embedSource && isMobile && 'rounded-full'}`}\n          variant='primary'\n          onClick={onSend}\n          style={\n            theme\n              ? {\n                backgroundColor: theme.primaryColor,\n                background: `linear-gradient(to right, ${btnBgGradientFrom}, ${btnBgGradientTo})`,\n              }\n              : {}\n          }\n        >\n          <RiSendPlane2Fill className='h-4 w-4' />\n        </Button>\n      </div>\n    </div>\n  )\n}\nOperation.displayName = 'Operation'\n\nexport default memo(Operation)\n"], "names": [], "mappings": ";;;;AAAA;;;;;;AASA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,MAAM,YAAY,CAChB,EACE,GAAG,EACH,UAAU,EACV,kBAAkB,EAClB,gBAAgB,EAChB,MAAM,EACN,KAAK,EACL,MAAM,EACN,WAAW,EACX,QAAQ,EAGT;IAED,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,4BAAyB,AAAD;IAC5C,MAAM,qBAAqB,SAAS,MAAM,iBAAiB,KAAK,KAAK,CAAC,SAAS,MAAM,kBAAkB,CAAC;IACxG,MAAM,oBAAoB,oBAAoB,qBAAqB;IACnE,MAAM,kBAAkB,oBAAoB,mBAAmB;IAE/D;;;GAGC,GACD,SAAS,iBAAiB,WAA+B,EAAE,MAA0B;QACnF,IAAI,CAAC,eAAe,CAAC,QACnB,OAAO,MAAM,wCAAwC;;QAEvD,MAAM,oBAAiD;YACrD,CAAC,kBAAkB,OAAO,CAAC,EAAE;gBAAC;aAAyB;YACvD,CAAC,kBAAkB,EAAE,CAAC,EAAE;gBAAC;aAAyB;YAClD,CAAC,kBAAkB,GAAG,CAAC,EAAE;gBAAC;aAAiB;YAC3C,CAAC,kBAAkB,IAAI,CAAC,EAAE;gBAAC;aAAiB;QAC9C;QAEA,cAAc;QACd,OAAO,iBAAiB,CAAC,YAAY,EAAE,SAAS,WAAW;IAC7D;IACA,MAAM,eAAe,iBAAiB,aAAa;IAEnD,qBACE,qZAAC;QACC,WAAW,GACT;kBAGF,cAAA,qZAAC;YACC,WAAU;YACV,KAAK;;8BAEL,qZAAC;oBAAI,WAAU;;wBAEX,cACK,gBAAgB,YAAY,yBAAW,qZAAC;4BAAwB,YAAY;;;;;mCAC5E,YAAY,yBAAW,qZAAC;4BAAwB,YAAY;;;;;;wBAGjE,oBAAoB,yBAClB,qZAAC;4BACC,MAAK;4BACL,SAAS;sCAET,cAAA,qZAAC;gCAAU,WAAU;;;;;;;;;;;;;;;;;8BAK7B,qZAAC;oBACC,WAAW,CAAC,cAAc,EAAE,eAAe,YAAY,gBAAgB;oBACvE,SAAQ;oBACR,SAAS;oBACT,OACE,QACI;wBACA,iBAAiB,MAAM,YAAY;wBACnC,YAAY,CAAC,0BAA0B,EAAE,kBAAkB,EAAE,EAAE,gBAAgB,CAAC,CAAC;oBACnF,IACE,CAAC;8BAGP,cAAA,qZAAC;wBAAiB,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKtC;AACA,UAAU,WAAW,GAAG;qDAET,CAAA,GAAA,4WAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 5826, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/chat-input-area/mobile-pop-input.tsx"], "sourcesContent": ["import cn from 'classnames'\nimport IconMinify from '@/assets/minify.svg'\nimport Image from 'next/image'\nimport { type ReactElement, useEffect, useState } from 'react'\n\ntype Props = {\n    visible: boolean\n    children: ReactElement\n    onClose: () => void\n}\n\nconst MobilePopInput = ({ visible, children, onClose }: Props) => {\n    const [isPop, setIsPop] = useState(false)\n\n    useEffect(() => {\n        setTimeout(() => {\n            setIsPop(visible)\n        }, 100)\n    }, [visible])\n\n    if (!visible && !isPop)\n        return null\n\n    return (\n        <div className={cn('fixed left-[0px] top-[0px] z-[999] flex h-full w-full items-end bg-[#0009] transition-opacity duration-100 ease-linear')}>\n            <div className={cn('relative flex h-[50%] w-full flex-col rounded-t-[8px] bg-white transition-all duration-100 ease-linear', (!isPop || (isPop && !visible)) ? 'translate-y-[300px] opacity-0' : 'opacity-1')}>\n                <div className='flex justify-end p-[10px]'>\n                    <Image className='right-[10px] top-[10px] cursor-pointer' src={IconMinify} width='20' height=\"20\" alt=\"\" onClick={onClose}/>\n                </div>\n               <div className='flex-1 overflow-auto'>\n                    {children}\n               </div>\n            </div>\n        </div>\n    )\n}\n\nexport default MobilePopInput\n"], "names": [], "mappings": ";;;;AAAA;;;;;;AAEA;AACA;;;;;;AAQA,MAAM,iBAAiB,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAS;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACN,WAAW;YACP,SAAS;QACb,GAAG;IACP,GAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,WAAW,CAAC,OACb,OAAO;IAEX,qBACI,qZAAC;QAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE;kBACf,cAAA,qZAAC;YAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE,0GAA0G,AAAC,CAAC,SAAU,SAAS,CAAC,UAAY,kCAAkC;;8BAC7L,qZAAC;oBAAI,WAAU;8BACX,cAAA,qZAAC,oSAAA,CAAA,UAAK;wBAAC,WAAU;wBAAyC,KAAK;wBAAY,OAAM;wBAAK,QAAO;wBAAK,KAAI;wBAAG,SAAS;;;;;;;;;;;8BAEvH,qZAAC;oBAAI,WAAU;8BACT;;;;;;;;;;;;;;;;;AAKrB;uCAEe", "debugId": null}}, {"offset": {"line": 5904, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/chat-input-area/index.tsx"], "sourcesContent": ["import {\n  useCallback,\n  useEffect,\n  useRef,\n  useState,\n} from 'react'\nimport Textarea from 'react-textarea-autosize'\nimport { useTranslation } from 'react-i18next'\nimport Recorder from 'js-audio-recorder'\nimport type {\n  EnableType,\n  OnSend,\n} from '../../types'\nimport type { Theme } from '../../embedded-chatbot/theme/theme-context'\nimport type { InputForm } from '../type'\nimport { useCheckInputsForms } from '../check-input-forms-hooks'\nimport { useTextAreaHeight } from './hooks'\nimport Operation from './operation'\nimport cn from '@/utils/classnames'\nimport { FileListInChatInput } from '@/components/base/file-uploader/file-uploader-in-chat-input/file-list'\nimport { useFile } from '@/components/base/file-uploader/hooks'\nimport {\n  FileContextProvider,\n  useFileStore,\n} from '@/components/base/file-uploader/store'\nimport VoiceInput from '@/components/base/voice-input'\nimport { useToastContext } from '@/components/base/toast'\nimport FeatureBar from '@/components/base/features/new-feature-panel/feature-bar'\nimport type { FileUpload } from '@/components/base/features/types'\nimport { TransferMethod } from '@/types/app'\nimport type { EmbedSource } from '@/models/share'\nimport IconZoom from '@/assets/zoom.svg'\nimport Image from 'next/image'\nimport MobilePopInput from './mobile-pop-input'\n\ntype ChatInputAreaProps = {\n  showFeatureBar?: boolean\n  showFileUpload?: boolean\n  featureBarDisabled?: boolean\n  onFeatureBarClick?: (state: boolean) => void\n  visionConfig?: FileUpload\n  speechToTextConfig?: EnableType\n  onSend?: OnSend\n  inputs?: Record<string, any>\n  inputsForm?: InputForm[]\n  theme?: Theme | null\n  isResponding?: boolean\n  disabled?: boolean\n  embedSource?: EmbedSource\n  isMobile?: boolean\n  intent?: string\n  footer?: React.ReactNode\n}\nconst ChatInputArea = ({\n  showFeatureBar,\n  showFileUpload,\n  featureBarDisabled,\n  onFeatureBarClick,\n  visionConfig,\n  speechToTextConfig = { enabled: true },\n  onSend,\n  inputs = {},\n  inputsForm = [],\n  theme,\n  isResponding,\n  disabled,\n  embedSource,\n  isMobile,\n  intent,\n  footer,\n}: ChatInputAreaProps) => {\n  const { t } = useTranslation()\n  const { notify } = useToastContext()\n  const {\n    wrapperRef,\n    textareaRef,\n    textValueRef,\n    holdSpaceRef,\n    handleTextareaResize,\n    isMultipleLine,\n  } = useTextAreaHeight(embedSource)\n  const [query, setQuery] = useState('')\n  const [showVoiceInput, setShowVoiceInput] = useState(false)\n  const filesStore = useFileStore()\n  const {\n    handleDragFileEnter,\n    handleDragFileLeave,\n    handleDragFileOver,\n    handleDropFile,\n    handleClipboardPasteFile,\n    isDragActive,\n  } = useFile(visionConfig!)\n  const { checkInputsForm } = useCheckInputsForms()\n  const historyRef = useRef([''])\n  const [currentIndex, setCurrentIndex] = useState(-1)\n  const isComposingRef = useRef(false)\n  const [popInputVisible, setPopInputVisible] = useState(false)\n\n  const handleSend = () => {\n    if (isResponding) {\n      notify({ type: 'info', message: t('appDebug.errorMessage.waitForResponse') })\n      return\n    }\n\n    if (onSend) {\n      const { files, setFiles } = filesStore.getState()\n      if (files.find(item => item.transferMethod === TransferMethod.local_file && !item.uploadedId)) {\n        notify({ type: 'info', message: t('appDebug.errorMessage.waitForFileUpload') })\n        return\n      }\n      if (!query || !query.trim()) {\n        notify({ type: 'info', message: t('appAnnotation.errorMessage.queryRequired') })\n        return\n      }\n      if (checkInputsForm(inputs, inputsForm)) {\n        onSend(query, files)\n        setQuery('')\n        setFiles([])\n        isMobile && textareaRef.current?.blur()\n      }\n    }\n  }\n  const handleCompositionStart = () => {\n    // e: React.CompositionEvent<HTMLTextAreaElement>\n    isComposingRef.current = true\n  }\n  const handleCompositionEnd = () => {\n    // safari or some browsers will trigger compositionend before keydown.\n    // delay 50ms for safari.\n    setTimeout(() => {\n      isComposingRef.current = false\n    }, 50)\n  }\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\n    if (e.key === 'Enter' && !e.shiftKey && !e.nativeEvent.isComposing) {\n      // if isComposing, exit\n      if (isComposingRef.current) return\n      e.preventDefault()\n      setQuery(query.replace(/\\n$/, ''))\n      historyRef.current.push(query)\n      setCurrentIndex(historyRef.current.length)\n      handleSend()\n    }\n    else if (e.key === 'ArrowUp' && !e.shiftKey && !e.nativeEvent.isComposing && e.metaKey) {\n      // When the cmd + up key is pressed, output the previous element\n      if (currentIndex > 0) {\n        setCurrentIndex(currentIndex - 1)\n        setQuery(historyRef.current[currentIndex - 1])\n      }\n    }\n    else if (e.key === 'ArrowDown' && !e.shiftKey && !e.nativeEvent.isComposing && e.metaKey) {\n      // When the cmd + down key is pressed, output the next element\n      if (currentIndex < historyRef.current.length - 1) {\n        setCurrentIndex(currentIndex + 1)\n        setQuery(historyRef.current[currentIndex + 1])\n      }\n      else if (currentIndex === historyRef.current.length - 1) {\n        // If it is the last element, clear the input box\n        setCurrentIndex(historyRef.current.length)\n        setQuery('')\n      }\n    }\n  }\n\n  const handleShowVoiceInput = useCallback(() => {\n    (Recorder as any).getPermission().then(() => {\n      setShowVoiceInput(true)\n    }, () => {\n      notify({ type: 'error', message: t('common.voiceInput.notAllow') })\n    })\n  }, [t, notify])\n\n  useEffect(() => {\n    // 发送后监听输入框高度变化\n    if(!query)\n      handleTextareaResize()\n  }, [query])\n\n  const operation = (\n    <Operation\n      ref={holdSpaceRef}\n      fileConfig={visionConfig}\n      speechToTextConfig={speechToTextConfig}\n      onShowVoiceInput={handleShowVoiceInput}\n      onSend={handleSend}\n      theme={theme}\n      intent={intent}\n      embedSource={embedSource}\n      isMobile={isMobile}\n    />\n  )\n\n  const renderInput = (props = {}) => (\n    <Textarea\n      // ref={ref => textareaRef.current = ref as any}\n      className={cn(\n        'body-lg-regular w-full resize-none bg-transparent p-1 leading-6 text-text-tertiary outline-none',\n      )}\n      placeholder={t('common.chat.inputPlaceholder') || ''}\n      autoFocus={!isMobile}\n      minRows={1}\n      spellCheck=\"false\"\n      inputMode=\"text\"\n      onResize={handleTextareaResize}\n      value={query}\n      onChange={(e) => {\n        setQuery(e.target.value)\n        setTimeout(handleTextareaResize, 0)\n      }}\n      onCompositionStart={handleCompositionStart}\n      onCompositionEnd={handleCompositionEnd}\n      onPaste={handleClipboardPasteFile}\n      onDragEnter={handleDragFileEnter}\n      onDragLeave={handleDragFileLeave}\n      onDragOver={handleDragFileOver}\n      onDrop={handleDropFile}\n      {...props}\n    />\n  )\n\n  return (\n    <>\n      <div\n        className={cn(\n          'relative z-10 rounded-xl border border-components-chat-input-border bg-components-panel-bg-blur pb-[9px] shadow-md',\n          isMobile && 'flex flex-1',\n          !!embedSource && !isMobile && 'border-[2px] !border-[#1E86FF]',\n          isDragActive && 'border border-dashed border-components-option-card-option-selected-border',\n          disabled && 'pointer-events-none border-components-panel-border opacity-50 shadow-none',\n          showVoiceInput && 'pb-0',\n          embedSource && isMobile && 'border-none bg-transparent px-[12px] pb-[0] shadow-none',\n        )}\n      >\n        {isMobile && footer}\n        <div className={'relative flex-1'}>\n          <div className={`relative flex max-h-[158px] flex-1 flex-col overflow-y-auto overflow-x-hidden px-[9px] py-[8px] ${!embedSource && 'max-h-[158px]'} ${!!embedSource && !isMobile && 'box-content min-h-[48px]'} ${embedSource && isMobile && 'ml-[8px] rounded-[24px] bg-white'}`} style={{ scrollbarWidth: 'none' }}>\n            <FileListInChatInput fileConfig={visionConfig!} />\n            <div\n              ref={wrapperRef}\n              className={`flex items-center justify-between ${embedSource && isMobile && isMultipleLine && 'pr-[20px]'}`}\n            >\n              <div className='relative flex w-full grow items-center'>\n                <div\n                  ref={textValueRef}\n                  className='body-lg-regular pointer-events-none invisible absolute h-auto w-auto whitespace-pre p-1 leading-6'\n                >\n                  {query}\n                </div>\n                {renderInput({ onKeyDown: handleKeyDown, ref: ref => textareaRef.current = ref as any, maxRows: 5 })}\n              </div>\n              {\n                embedSource ? (!isMultipleLine && isMobile && operation) : (!isMultipleLine && operation)\n              }\n            </div>\n            {\n              showVoiceInput && (\n                <VoiceInput\n                  isEmbedSourceMobile={Boolean(embedSource && isMobile)}\n                  border={false}\n                  onCancel={() => setShowVoiceInput(false)}\n                  onConverted={text => setQuery(text)}\n                />\n              )\n            }\n          </div>\n          {embedSource && isMobile && isMultipleLine && (<Image className='absolute right-[10px] top-[10px] cursor-pointer' src={IconZoom} width='20' height=\"20\" alt=\"\" onClick={() => setPopInputVisible(true)}/>)}\n        </div>\n\n        {!showVoiceInput && <>\n          {\n          (isMultipleLine || (!!embedSource && !isMobile)) && (\n            <div className='px-[4px]'>{operation}</div>\n          )\n        }\n        {!isMobile && footer}\n        </>}\n      </div>\n      {showFeatureBar && <FeatureBar showFileUpload={showFileUpload} disabled={featureBarDisabled} onFeatureBarClick={onFeatureBarClick} />}\n\n      <MobilePopInput visible={popInputVisible} onClose={() => setPopInputVisible(false)}>\n        {renderInput({ style: { height: '100% !important' } })}\n      </MobilePopInput>\n    </>\n  )\n}\n\nconst ChatInputAreaWrapper = (props: ChatInputAreaProps) => {\n  return (\n    <FileContextProvider>\n      <ChatInputArea {...props} />\n    </FileContextProvider>\n  )\n}\n\nexport default ChatInputAreaWrapper\n"], "names": [], "mappings": ";;;;AAAA;;;;;;;;;;;;;;;;AAeA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA;AACA;;;;;;;;;;;;;;;;;;;;AAoBA,MAAM,gBAAgB,CAAC,EACrB,cAAc,EACd,cAAc,EACd,kBAAkB,EAClB,iBAAiB,EACjB,YAAY,EACZ,qBAAqB;IAAE,SAAS;AAAK,CAAC,EACtC,MAAM,EACN,SAAS,CAAC,CAAC,EACX,aAAa,EAAE,EACf,KAAK,EACL,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,MAAM,EACN,MAAM,EACa;IACnB,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,EACJ,UAAU,EACV,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,oBAAoB,EACpB,cAAc,EACf,GAAG,CAAA,GAAA,2KAAA,CAAA,oBAAiB,AAAD,EAAE;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,aAAa;IACnB,MAAM,EACJ,mBAAmB,EACnB,mBAAmB,EACnB,kBAAkB,EAClB,cAAc,EACd,wBAAwB,EACxB,YAAY,EACb,GAAG,QAAQ;IACZ,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;IAC9C,MAAM,aAAa,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;QAAC;KAAG;IAC9B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,iBAAiB,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,aAAa;QACjB,IAAI,cAAc;YAChB,OAAO;gBAAE,MAAM;gBAAQ,SAAS,EAAE;YAAyC;YAC3E;QACF;QAEA,IAAI,QAAQ;YACV,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,WAAW,QAAQ;YAC/C,IAAI,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,cAAc,KAAK,eAAe,UAAU,IAAI,CAAC,KAAK,UAAU,GAAG;gBAC7F,OAAO;oBAAE,MAAM;oBAAQ,SAAS,EAAE;gBAA2C;gBAC7E;YACF;YACA,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;gBAC3B,OAAO;oBAAE,MAAM;oBAAQ,SAAS,EAAE;gBAA4C;gBAC9E;YACF;YACA,IAAI,gBAAgB,QAAQ,aAAa;gBACvC,OAAO,OAAO;gBACd,SAAS;gBACT,SAAS,EAAE;gBACX,YAAY,YAAY,OAAO,EAAE;YACnC;QACF;IACF;IACA,MAAM,yBAAyB;QAC7B,iDAAiD;QACjD,eAAe,OAAO,GAAG;IAC3B;IACA,MAAM,uBAAuB;QAC3B,sEAAsE;QACtE,yBAAyB;QACzB,WAAW;YACT,eAAe,OAAO,GAAG;QAC3B,GAAG;IACL;IACA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,IAAI,CAAC,EAAE,WAAW,CAAC,WAAW,EAAE;YAClE,uBAAuB;YACvB,IAAI,eAAe,OAAO,EAAE;YAC5B,EAAE,cAAc;YAChB,SAAS,MAAM,OAAO,CAAC,OAAO;YAC9B,WAAW,OAAO,CAAC,IAAI,CAAC;YACxB,gBAAgB,WAAW,OAAO,CAAC,MAAM;YACzC;QACF,OACK,IAAI,EAAE,GAAG,KAAK,aAAa,CAAC,EAAE,QAAQ,IAAI,CAAC,EAAE,WAAW,CAAC,WAAW,IAAI,EAAE,OAAO,EAAE;YACtF,gEAAgE;YAChE,IAAI,eAAe,GAAG;gBACpB,gBAAgB,eAAe;gBAC/B,SAAS,WAAW,OAAO,CAAC,eAAe,EAAE;YAC/C;QACF,OACK,IAAI,EAAE,GAAG,KAAK,eAAe,CAAC,EAAE,QAAQ,IAAI,CAAC,EAAE,WAAW,CAAC,WAAW,IAAI,EAAE,OAAO,EAAE;YACxF,8DAA8D;YAC9D,IAAI,eAAe,WAAW,OAAO,CAAC,MAAM,GAAG,GAAG;gBAChD,gBAAgB,eAAe;gBAC/B,SAAS,WAAW,OAAO,CAAC,eAAe,EAAE;YAC/C,OACK,IAAI,iBAAiB,WAAW,OAAO,CAAC,MAAM,GAAG,GAAG;gBACvD,iDAAiD;gBACjD,gBAAgB,WAAW,OAAO,CAAC,MAAM;gBACzC,SAAS;YACX;QACF;IACF;IAEA,MAAM,uBAAuB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACtC,SAAiB,aAAa,GAAG,IAAI,CAAC;YACrC,kBAAkB;QACpB,GAAG;YACD,OAAO;gBAAE,MAAM;gBAAS,SAAS,EAAE;YAA8B;QACnE;IACF,GAAG;QAAC;QAAG;KAAO;IAEd,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;QACf,IAAG,CAAC,OACF;IACJ,GAAG;QAAC;KAAM;IAEV,MAAM,0BACJ,qZAAC,gLAAA,CAAA,UAAS;QACR,KAAK;QACL,YAAY;QACZ,oBAAoB;QACpB,kBAAkB;QAClB,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;;;;;;IAId,MAAM,cAAc,CAAC,QAAQ,CAAC,CAAC,iBAC7B,qZAAC;YACC,gDAAgD;YAChD,WAAW,GACT;YAEF,aAAa,EAAE,mCAAmC;YAClD,WAAW,CAAC;YACZ,SAAS;YACT,YAAW;YACX,WAAU;YACV,UAAU;YACV,OAAO;YACP,UAAU,CAAC;gBACT,SAAS,EAAE,MAAM,CAAC,KAAK;gBACvB,WAAW,sBAAsB;YACnC;YACA,oBAAoB;YACpB,kBAAkB;YAClB,SAAS;YACT,aAAa;YACb,aAAa;YACb,YAAY;YACZ,QAAQ;YACP,GAAG,KAAK;;;;;;IAIb,qBACE;;0BACE,qZAAC;gBACC,WAAW,GACT,sHACA,YAAY,eACZ,CAAC,CAAC,eAAe,CAAC,YAAY,kCAC9B,gBAAgB,6EAChB,YAAY,6EACZ,kBAAkB,QAClB,eAAe,YAAY;;oBAG5B,YAAY;kCACb,qZAAC;wBAAI,WAAW;;0CACd,qZAAC;gCAAI,WAAW,CAAC,gGAAgG,EAAE,CAAC,eAAe,gBAAgB,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,YAAY,2BAA2B,CAAC,EAAE,eAAe,YAAY,oCAAoC;gCAAE,OAAO;oCAAE,gBAAgB;gCAAO;;kDACjT,qZAAC;wCAAoB,YAAY;;;;;;kDACjC,qZAAC;wCACC,KAAK;wCACL,WAAW,CAAC,kCAAkC,EAAE,eAAe,YAAY,kBAAkB,aAAa;;0DAE1G,qZAAC;gDAAI,WAAU;;kEACb,qZAAC;wDACC,KAAK;wDACL,WAAU;kEAET;;;;;;oDAEF,YAAY;wDAAE,WAAW;wDAAe,KAAK,CAAA,MAAO,YAAY,OAAO,GAAG;wDAAY,SAAS;oDAAE;;;;;;;4CAGlG,cAAe,CAAC,kBAAkB,YAAY,YAAc,CAAC,kBAAkB;;;;;;;oCAIjF,gCACE,qZAAC;wCACC,qBAAqB,QAAQ,eAAe;wCAC5C,QAAQ;wCACR,UAAU,IAAM,kBAAkB;wCAClC,aAAa,CAAA,OAAQ,SAAS;;;;;;;;;;;;4BAKrC,eAAe,YAAY,gCAAmB,qZAAC,oSAAA,CAAA,UAAK;gCAAC,WAAU;gCAAkD,KAAK;gCAAU,OAAM;gCAAK,QAAO;gCAAK,KAAI;gCAAG,SAAS,IAAM,mBAAmB;;;;;;;;;;;;oBAGlM,CAAC,gCAAkB;;4BAElB,CAAC,kBAAmB,CAAC,CAAC,eAAe,CAAC,QAAS,mBAC7C,qZAAC;gCAAI,WAAU;0CAAY;;;;;;4BAG9B,CAAC,YAAY;;;;;;;;;YAGf,gCAAkB,qZAAC;gBAAW,gBAAgB;gBAAgB,UAAU;gBAAoB,mBAAmB;;;;;;0BAEhH,qZAAC,6LAAA,CAAA,UAAc;gBAAC,SAAS;gBAAiB,SAAS,IAAM,mBAAmB;0BACzE,YAAY;oBAAE,OAAO;wBAAE,QAAQ;oBAAkB;gBAAE;;;;;;;;AAI5D;AAEA,MAAM,uBAAuB,CAAC;IAC5B,qBACE,qZAAC;kBACC,cAAA,qZAAC;YAAe,GAAG,KAAK;;;;;;;;;;;AAG9B;uCAEe", "debugId": null}}, {"offset": {"line": 6297, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/try-to-ask.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport { memo } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport type { OnSend } from '../types'\nimport Button from '@/components/base/button'\nimport Divider from '@/components/base/divider'\nimport cn from '@/utils/classnames'\nimport type { EmbedSource } from '@/models/share'\n\nconst LarkLeftIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" className={className}>\n    <g fill=\"#6550CE\" fill-rule=\"nonzero\" opacity=\".5\">\n      <path d=\"M5.31485975,3.23575343 L5.79250053,4.56304549 C6.32371828,6.03615532 7.48359419,7.19599314 8.95664688,7.72719185 L10.2839389,8.20483262 C10.4037224,8.24798371 10.4037224,8.41761685 10.2839389,8.46077555 L8.95664688,8.93841633 C7.48353705,9.46963408 6.32369924,10.62951 5.79250053,12.1025627 L5.31485975,13.4298547 C5.27170866,13.5496382 5.10207553,13.5496382 5.05891682,13.4298547 L4.58127605,12.1025627 C4.05005829,10.6294529 2.89018238,9.46961504 1.41712969,8.93841633 L0.0898376249,8.46077555 C-0.029945875,8.41762446 -0.029945875,8.24799133 0.0898376249,8.20483262 L1.41712969,7.72719185 C2.89023952,7.19597409 4.05007733,6.03609818 4.58127605,4.56304549 L5.05891682,3.23575343 C5.10206791,3.11522712 5.27170105,3.11522712 5.31485975,3.23575343 Z\" transform=\"translate(3.3333 1.6667)\" />\n      <path d=\"M11.2898453,0.046313709 L11.5323797,0.718130565 C11.8017121,1.46361815 12.3887162,2.05136505 13.1349466,2.32069746 L13.8067635,2.56323186 C13.8677704,2.58555211 13.8677704,2.67111148 13.8067635,2.69268702 L13.1349466,2.93522142 C12.389459,3.20455383 11.8017121,3.79155793 11.5323797,4.53778832 L11.2898453,5.20960517 C11.2675251,5.27061208 11.1819657,5.27061208 11.1603902,5.20960517 L10.9178558,4.53778832 C10.6485233,3.79230073 10.0615192,3.20455383 9.31528886,2.93522142 L8.64347201,2.69268702 C8.5824651,2.67036677 8.5824651,2.5848074 8.64347201,2.56323186 L9.31528886,2.32069746 C10.0607764,2.05136505 10.6485233,1.46436095 10.9178558,0.718130565 L11.1603902,0.046313709 C11.1819657,-0.015437903 11.2682698,-0.015437903 11.2898453,0.046313709 L11.2898453,0.046313709 Z\" transform=\"translate(3.3333 1.6667)\" />\n      <path d=\"M11.2898453,11.45762 L11.5323797,12.1294369 C11.8017121,12.8749245 12.3887162,13.4626714 13.1349466,13.7320038 L13.8067635,13.9745382 C13.8677704,13.9968584 13.8677704,14.0824178 13.8067635,14.1039933 L13.1349466,14.3465277 C12.389459,14.6158601 11.8017121,15.2028642 11.5323797,15.9490946 L11.2898453,16.6209115 C11.2675251,16.6819184 11.1819657,16.6819184 11.1603902,16.6209115 L10.9178558,15.9490946 C10.6485233,15.203607 10.0615192,14.6158601 9.31528886,14.3465277 L8.64347201,14.1039933 C8.5824651,14.0816731 8.5824651,13.9961137 8.64347201,13.9745382 L9.31528886,13.7320038 C10.0607764,13.4626714 10.6485233,12.8756673 10.9178558,12.1294369 L11.1603902,11.45762 C11.1819657,11.3966131 11.2682698,11.3966131 11.2898453,11.45762 Z\" transform=\"translate(3.3333 1.6667)\" />\n    </g>\n  </svg>\n)\n\nconst LarkRightIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" className={className}>\n    <g fill=\"#6550CE\" fill-rule=\"nonzero\" opacity=\".5\">\n      <path d=\"M5.31485975,3.23575343 L5.79250053,4.56304549 C6.32371828,6.03615532 7.48359419,7.19599314 8.95664688,7.72719185 L10.2839389,8.20483262 C10.4037224,8.24798371 10.4037224,8.41761685 10.2839389,8.46077555 L8.95664688,8.93841633 C7.48353705,9.46963408 6.32369924,10.62951 5.79250053,12.1025627 L5.31485975,13.4298547 C5.27170866,13.5496382 5.10207553,13.5496382 5.05891682,13.4298547 L4.58127605,12.1025627 C4.05005829,10.6294529 2.89018238,9.46961504 1.41712969,8.93841633 L0.0898376249,8.46077555 C-0.029945875,8.41762446 -0.029945875,8.24799133 0.0898376249,8.20483262 L1.41712969,7.72719185 C2.89023952,7.19597409 4.05007733,6.03609818 4.58127605,4.56304549 L5.05891682,3.23575343 C5.10206791,3.11522712 5.27170105,3.11522712 5.31485975,3.23575343 Z\" transform=\"matrix(-1 0 0 1 16.6667 1.6667)\" />\n      <path d=\"M11.2898453,0.046313709 L11.5323797,0.718130565 C11.8017121,1.46361815 12.3887162,2.05136505 13.1349466,2.32069746 L13.8067635,2.56323186 C13.8677704,2.58555211 13.8677704,2.67111148 13.8067635,2.69268702 L13.1349466,2.93522142 C12.389459,3.20455383 11.8017121,3.79155793 11.5323797,4.53778832 L11.2898453,5.20960517 C11.2675251,5.27061208 11.1819657,5.27061208 11.1603902,5.20960517 L10.9178558,4.53778832 C10.6485233,3.79230073 10.0615192,3.20455383 9.31528886,2.93522142 L8.64347201,2.69268702 C8.5824651,2.67036677 8.5824651,2.5848074 8.64347201,2.56323186 L9.31528886,2.32069746 C10.0607764,2.05136505 10.6485233,1.46436095 10.9178558,0.718130565 L11.1603902,0.046313709 C11.1819657,-0.015437903 11.2682698,-0.015437903 11.2898453,0.046313709 L11.2898453,0.046313709 Z\" transform=\"matrix(-1 0 0 1 16.6667 1.6667)\" />\n      <path d=\"M11.2898453,11.45762 L11.5323797,12.1294369 C11.8017121,12.8749245 12.3887162,13.4626714 13.1349466,13.7320038 L13.8067635,13.9745382 C13.8677704,13.9968584 13.8677704,14.0824178 13.8067635,14.1039933 L13.1349466,14.3465277 C12.389459,14.6158601 11.8017121,15.2028642 11.5323797,15.9490946 L11.2898453,16.6209115 C11.2675251,16.6819184 11.1819657,16.6819184 11.1603902,16.6209115 L10.9178558,15.9490946 C10.6485233,15.203607 10.0615192,14.6158601 9.31528886,14.3465277 L8.64347201,14.1039933 C8.5824651,14.0816731 8.5824651,13.9961137 8.64347201,13.9745382 L9.31528886,13.7320038 C10.0607764,13.4626714 10.6485233,12.8756673 10.9178558,12.1294369 L11.1603902,11.45762 C11.1819657,11.3966131 11.2682698,11.3966131 11.2898453,11.45762 Z\" transform=\"matrix(-1 0 0 1 16.6667 1.6667)\" />\n    </g>\n  </svg>\n\n)\n\ntype TryToAskProps = {\n  isMobile?: boolean\n  embedSource?: EmbedSource\n  suggestedQuestions: string[]\n  onSend: OnSend\n}\nconst TryToAsk: FC<TryToAskProps> = ({\n  isMobile,\n  embedSource,\n  suggestedQuestions,\n  onSend,\n}) => {\n  const { t } = useTranslation()\n  const isEmbedMobile = embedSource && isMobile\n\n  return (\n    <div className={embedSource ? 'mb-[80px]' : 'mb-2 py-2'}>\n      <div className={cn(`mb-2.5 flex items-center justify-between gap-2 ${embedSource && !isMobile && 'px-14'}`, isMobile && 'justify-end')}>\n        {embedSource ? (\n          <div\n            className='h-[1px] grow'\n            style={{\n              background: 'linear-gradient(to left, #ACA1E3, #FAFAFD)',\n            }}\n          />\n        ) : (\n          <Divider bgStyle='gradient' className='h-px grow rotate-180' />\n        )}\n        <div className='system-xs-medium-uppercase flex shrink-0 items-center text-text-tertiary'>\n          {embedSource && <LarkLeftIcon className=\"h-[20px] w-[20px]\" />}\n          <span className={`${embedSource && '!text-[#6550CE]'}`}>{embedSource ? '可以试着这样问噢' : t('appDebug.feature.suggestedQuestionsAfterAnswer.tryToAsk')}</span>\n          {embedSource && <LarkRightIcon className=\"h-[20px] w-[20px]\" />}\n        </div>\n        {embedSource ? (\n          <div\n            className='h-[1px] grow'\n            style={{\n              background: 'linear-gradient(to right, #ACA1E3, #FAFAFD)',\n            }}\n          />\n        ) : (\n          <Divider bgStyle='gradient' className='h-px grow' />\n        )}\n      </div>\n      <div className={cn('flex flex-wrap justify-center', isMobile && 'justify-end', embedSource && isMobile && 'block')}>\n        {\n          suggestedQuestions.map((suggestQuestion, index) => (\n            <Button\n              size={embedSource ? 'medium' : 'small'}\n              key={index}\n              variant='secondary-accent'\n              className={`mb-1 mr-1 last:mr-0 ${!isEmbedMobile && 'last:mr-0'} ${embedSource && 'rounded-[16px] text-center text-[12px] text-[#434B5B]'} ${isEmbedMobile && 'block w-fit justify-center'}`}\n              onClick={() => onSend(suggestQuestion)}\n            >\n              {suggestQuestion}\n            </Button>\n          ))\n        }\n      </div>\n    </div>\n  )\n}\n\nexport default memo(TryToAsk)\n"], "names": [], "mappings": ";;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,MAAM,eAAe,CAAC,EAAE,SAAS,EAAyB,iBACxD,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAE,MAAK;YAAU,aAAU;YAAU,SAAQ;;8BAC5C,qZAAC;oBAAK,GAAE;oBAAivB,WAAU;;;;;;8BACnwB,qZAAC;oBAAK,GAAE;oBAAwwB,WAAU;;;;;;8BAC1xB,qZAAC;oBAAK,GAAE;oBAAiuB,WAAU;;;;;;;;;;;;;;;;;AAKzvB,MAAM,gBAAgB,CAAC,EAAE,SAAS,EAAyB,iBACzD,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAE,MAAK;YAAU,aAAU;YAAU,SAAQ;;8BAC5C,qZAAC;oBAAK,GAAE;oBAAivB,WAAU;;;;;;8BACnwB,qZAAC;oBAAK,GAAE;oBAAwwB,WAAU;;;;;;8BAC1xB,qZAAC;oBAAK,GAAE;oBAAiuB,WAAU;;;;;;;;;;;;;;;;;AAYzvB,MAAM,WAA8B,CAAC,EACnC,QAAQ,EACR,WAAW,EACX,kBAAkB,EAClB,MAAM,EACP;IACC,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,gBAAgB,eAAe;IAErC,qBACE,qZAAC;QAAI,WAAW,cAAc,cAAc;;0BAC1C,qZAAC;gBAAI,WAAW,GAAG,CAAC,+CAA+C,EAAE,eAAe,CAAC,YAAY,SAAS,EAAE,YAAY;;oBACrH,4BACC,qZAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY;wBACd;;;;;6CAGF,qZAAC;wBAAQ,SAAQ;wBAAW,WAAU;;;;;;kCAExC,qZAAC;wBAAI,WAAU;;4BACZ,6BAAe,qZAAC;gCAAa,WAAU;;;;;;0CACxC,qZAAC;gCAAK,WAAW,GAAG,eAAe,mBAAmB;0CAAG,cAAc,aAAa,EAAE;;;;;;4BACrF,6BAAe,qZAAC;gCAAc,WAAU;;;;;;;;;;;;oBAE1C,4BACC,qZAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY;wBACd;;;;;6CAGF,qZAAC;wBAAQ,SAAQ;wBAAW,WAAU;;;;;;;;;;;;0BAG1C,qZAAC;gBAAI,WAAW,GAAG,iCAAiC,YAAY,eAAe,eAAe,YAAY;0BAEtG,mBAAmB,GAAG,CAAC,CAAC,iBAAiB,sBACvC,qZAAC;wBACC,MAAM,cAAc,WAAW;wBAE/B,SAAQ;wBACR,WAAW,CAAC,oBAAoB,EAAE,CAAC,iBAAiB,YAAY,CAAC,EAAE,eAAe,wDAAwD,CAAC,EAAE,iBAAiB,8BAA8B;wBAC5L,SAAS,IAAM,OAAO;kCAErB;uBALI;;;;;;;;;;;;;;;;AAYnB;qDAEe,CAAA,GAAA,4WAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 6528, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/portal-to-follow-elem/index.tsx"], "sourcesContent": ["'use client'\nimport React from 'react'\nimport {\n  FloatingPortal,\n  autoUpdate,\n  flip,\n  offset,\n  shift,\n  size,\n  useDismiss,\n  useFloating,\n  useFocus,\n  useHover,\n  useInteractions,\n  useMergeRefs,\n  useRole,\n} from '@floating-ui/react'\n\nimport type { OffsetOptions, Placement } from '@floating-ui/react'\nimport cn from '@/utils/classnames'\nexport type PortalToFollowElemOptions = {\n  /*\n  * top, bottom, left, right\n  * start, end. Default is middle\n  * combine: top-start, top-end\n  */\n  placement?: Placement\n  open?: boolean\n  offset?: number | OffsetOptions\n  onOpenChange?: (open: boolean) => void\n  triggerPopupSameWidth?: boolean\n}\n\nexport function usePortalToFollowElem({\n  placement = 'bottom',\n  open,\n  offset: offsetValue = 0,\n  onOpenChange: setControlledOpen,\n  triggerPopupSameWidth,\n}: PortalToFollowElemOptions = {}) {\n  const setOpen = setControlledOpen\n\n  const data = useFloating({\n    placement,\n    open,\n    onOpenChange: setOpen,\n    whileElementsMounted: autoUpdate,\n    middleware: [\n      offset(offsetValue),\n      flip({\n        crossAxis: placement.includes('-'),\n        fallbackAxisSideDirection: 'start',\n        padding: 5,\n      }),\n      shift({ padding: 5 }),\n      size({\n        apply({ rects, elements }) {\n          if (triggerPopupSameWidth)\n            elements.floating.style.width = `${rects.reference.width}px`\n        },\n      }),\n    ],\n  })\n\n  const context = data.context\n\n  const hover = useHover(context, {\n    move: false,\n    enabled: open == null,\n  })\n  const focus = useFocus(context, {\n    enabled: open == null,\n  })\n  const dismiss = useDismiss(context)\n  const role = useRole(context, { role: 'tooltip' })\n\n  const interactions = useInteractions([hover, focus, dismiss, role])\n\n  return React.useMemo(\n    () => ({\n      open,\n      setOpen,\n      ...interactions,\n      ...data,\n    }),\n    [open, setOpen, interactions, data],\n  )\n}\n\ntype ContextType = ReturnType<typeof usePortalToFollowElem> | null\n\nconst PortalToFollowElemContext = React.createContext<ContextType>(null)\n\nexport function usePortalToFollowElemContext() {\n  const context = React.useContext(PortalToFollowElemContext)\n\n  if (context == null)\n    throw new Error('PortalToFollowElem components must be wrapped in <PortalToFollowElem />')\n\n  return context\n}\n\nexport function PortalToFollowElem({\n  children,\n  ...options\n}: { children: React.ReactNode } & PortalToFollowElemOptions) {\n  // This can accept any props as options, e.g. `placement`,\n  // or other positioning options.\n  const tooltip = usePortalToFollowElem(options)\n  return (\n    <PortalToFollowElemContext.Provider value={tooltip}>\n      {children}\n    </PortalToFollowElemContext.Provider>\n  )\n}\n\nexport const PortalToFollowElemTrigger = (\n  {\n    ref: propRef,\n    children,\n    asChild = false,\n    ...props\n  }: React.HTMLProps<HTMLElement> & { ref?: React.RefObject<HTMLElement>, asChild?: boolean },\n) => {\n  const context = usePortalToFollowElemContext()\n  const childrenRef = (children as any).props?.ref\n  const ref = useMergeRefs([context.refs.setReference, propRef, childrenRef])\n\n  // `asChild` allows the user to pass any element as the anchor\n  if (asChild && React.isValidElement(children)) {\n    return React.cloneElement(\n      children,\n      context.getReferenceProps({\n        ref,\n        ...props,\n        ...children.props,\n        'data-state': context.open ? 'open' : 'closed',\n      }),\n    )\n  }\n\n  return (\n    <div\n      ref={ref}\n      className={cn('inline-block', props.className)}\n      // The user can style the trigger based on the state\n      data-state={context.open ? 'open' : 'closed'}\n      {...context.getReferenceProps(props)}\n    >\n      {children}\n    </div>\n  )\n}\nPortalToFollowElemTrigger.displayName = 'PortalToFollowElemTrigger'\n\nexport const PortalToFollowElemContent = (\n  {\n    ref: propRef,\n    style,\n    ...props\n  }: React.HTMLProps<HTMLDivElement> & {\n    ref?: React.RefObject<HTMLDivElement>;\n  },\n) => {\n  const context = usePortalToFollowElemContext()\n  const ref = useMergeRefs([context.refs.setFloating, propRef])\n\n  if (!context.open)\n    return null\n\n  const body = document.body\n\n  return (\n    <FloatingPortal root={body}>\n      <div\n        ref={ref}\n        style={{\n          ...context.floatingStyles,\n          ...style,\n        }}\n        {...context.getFloatingProps(props)}\n      />\n    </FloatingPortal>\n  )\n}\n\nPortalToFollowElemContent.displayName = 'PortalToFollowElemContent'\n"], "names": [], "mappings": ";;;;;;;;AACA;;;;;;;;;;;AADA;;;;;AAiCO,SAAS,sBAAsB,EACpC,YAAY,QAAQ,EACpB,IAAI,EACJ,QAAQ,cAAc,CAAC,EACvB,cAAc,iBAAiB,EAC/B,qBAAqB,EACK,GAAG,CAAC,CAAC;IAC/B,MAAM,UAAU;IAEhB,MAAM,OAAO,YAAY;QACvB;QACA;QACA,cAAc;QACd,sBAAsB;QACtB,YAAY;YACV,OAAO;YACP,KAAK;gBACH,WAAW,UAAU,QAAQ,CAAC;gBAC9B,2BAA2B;gBAC3B,SAAS;YACX;YACA,MAAM;gBAAE,SAAS;YAAE;YACnB,KAAK;gBACH,OAAM,EAAE,KAAK,EAAE,QAAQ,EAAE;oBACvB,IAAI,uBACF,SAAS,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,MAAM,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChE;YACF;SACD;IACH;IAEA,MAAM,UAAU,KAAK,OAAO;IAE5B,MAAM,QAAQ,SAAS,SAAS;QAC9B,MAAM;QACN,SAAS,QAAQ;IACnB;IACA,MAAM,QAAQ,SAAS,SAAS;QAC9B,SAAS,QAAQ;IACnB;IACA,MAAM,UAAU,WAAW;IAC3B,MAAM,OAAO,QAAQ,SAAS;QAAE,MAAM;IAAU;IAEhD,MAAM,eAAe,gBAAgB;QAAC;QAAO;QAAO;QAAS;KAAK;IAElE,OAAO,4WAAA,CAAA,UAAK,CAAC,OAAO,CAClB,IAAM,CAAC;YACL;YACA;YACA,GAAG,YAAY;YACf,GAAG,IAAI;QACT,CAAC,GACD;QAAC;QAAM;QAAS;QAAc;KAAK;AAEvC;AAIA,MAAM,0CAA4B,4WAAA,CAAA,UAAK,CAAC,aAAa,CAAc;AAE5D,SAAS;IACd,MAAM,UAAU,4WAAA,CAAA,UAAK,CAAC,UAAU,CAAC;IAEjC,IAAI,WAAW,MACb,MAAM,IAAI,MAAM;IAElB,OAAO;AACT;AAEO,SAAS,mBAAmB,EACjC,QAAQ,EACR,GAAG,SACuD;IAC1D,0DAA0D;IAC1D,gCAAgC;IAChC,MAAM,UAAU,sBAAsB;IACtC,qBACE,qZAAC,0BAA0B,QAAQ;QAAC,OAAO;kBACxC;;;;;;AAGP;AAEO,MAAM,4BAA4B,CACvC,EACE,KAAK,OAAO,EACZ,QAAQ,EACR,UAAU,KAAK,EACf,GAAG,OACsF;IAE3F,MAAM,UAAU;IAChB,MAAM,cAAc,AAAC,SAAiB,KAAK,EAAE;IAC7C,MAAM,MAAM,aAAa;QAAC,QAAQ,IAAI,CAAC,YAAY;QAAE;QAAS;KAAY;IAE1E,8DAA8D;IAC9D,IAAI,yBAAW,4WAAA,CAAA,UAAK,CAAC,cAAc,CAAC,WAAW;QAC7C,qBAAO,4WAAA,CAAA,UAAK,CAAC,YAAY,CACvB,UACA,QAAQ,iBAAiB,CAAC;YACxB;YACA,GAAG,KAAK;YACR,GAAG,SAAS,KAAK;YACjB,cAAc,QAAQ,IAAI,GAAG,SAAS;QACxC;IAEJ;IAEA,qBACE,qZAAC;QACC,KAAK;QACL,WAAW,GAAG,gBAAgB,MAAM,SAAS;QAC7C,oDAAoD;QACpD,cAAY,QAAQ,IAAI,GAAG,SAAS;QACnC,GAAG,QAAQ,iBAAiB,CAAC,MAAM;kBAEnC;;;;;;AAGP;AACA,0BAA0B,WAAW,GAAG;AAEjC,MAAM,4BAA4B,CACvC,EACE,KAAK,OAAO,EACZ,KAAK,EACL,GAAG,OAGJ;IAED,MAAM,UAAU;IAChB,MAAM,MAAM,aAAa;QAAC,QAAQ,IAAI,CAAC,WAAW;QAAE;KAAQ;IAE5D,IAAI,CAAC,QAAQ,IAAI,EACf,OAAO;IAET,MAAM,OAAO,SAAS,IAAI;IAE1B,qBACE,qZAAC;QAAe,MAAM;kBACpB,cAAA,qZAAC;YACC,KAAK;YACL,OAAO;gBACL,GAAG,QAAQ,cAAc;gBACzB,GAAG,KAAK;YACV;YACC,GAAG,QAAQ,gBAAgB,CAAC,MAAM;;;;;;;;;;;AAI3C;AAEA,0BAA0B,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 6691, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/toolbar/sdks/index.tsx"], "sourcesContent": ["/*\n * 第三方嵌入 SDK 类\n */\n\n// 图标水平方向间隔\nconst HORIZONTAL_SPACE = 10\n// 图标垂直方向间隔\nconst VERTICAL_SPACE = 10\n// 弹窗默认宽度\nconst POPUP_DEFAULT_WIDTH = 0.8\n// 弹窗默认高度\nconst POPUP_DEFAULT_HEIGHT = 0.9\n// 弹窗最小宽度\nconst POPUP_MIN_WIDTH = 800\n// 弹窗最小高度\nconst POPUP_MIN_HEIGHT = 400\n// 形变角度\nconst SKEW_DGE = 20\n\nexport default class AiPopup {\n  private visible: boolean\n  private bubbleId: string\n  private popupId: string | null\n  private isDrag: boolean\n  private userCode: string\n\n  constructor() {\n    this.visible = false\n    this.isDrag = true\n    this.popupId = null\n    this.bubbleId = `bubble_${new Date().getTime()}`\n    this.userCode = ''\n  }\n\n  // 创建悬浮气泡按钮元素\n  createButton(options = {\n    userCode: '',\n  }) {\n    let dragStartTime = 0 // 拖拽开始时间\n    let dragEndTime = 0 // 拖拽结束时间\n    let dragStartOffset = { x: 0, y: 0 } // 拖拽偏移量\n    let dragEndOffset = { x: 0, y: 0 } // 拖拽偏移量\n    this.userCode = options.userCode\n\n    const bubble = document.createElement('div')\n    bubble.id = this.bubbleId\n    bubble.style.position = 'absolute'\n    // bubble.style.left = `calc(100vw - 48px - ${HORIZONTAL_SPACE}px)`;\n    bubble.style.left = 'calc(100% - 58px)'\n    // bubble.style.top = `calc(100vh - 48px - ${VERTICAL_SPACE}px)`;\n    bubble.style.top = '100px'\n    bubble.style.zIndex = '10086'\n    bubble.style.display = 'none'\n    bubble.style.alignContent = 'center'\n    bubble.style.justifyContent = 'center'\n    bubble.style.width = '48px'\n    bubble.style.height = '48px'\n    bubble.style.color = 'white'\n    bubble.style.fontSize = '16px'\n    bubble.style.fontWeight = 'bold'\n    bubble.style.border = 'none'\n    bubble.style.backgroundColor = '#0081CC'\n    bubble.style.borderRadius = '50%'\n    bubble.style.cursor = 'move'\n\n    const button = document.createElement('button')\n    button.textContent = 'AI'\n    button.style.color = 'white'\n    button.style.fontSize = '16px'\n    button.style.fontWeight = 'bold'\n    button.style.border = 'none'\n    button.style.backgroundColor = 'transparent'\n    button.style.borderRadius = '50%'\n    button.style.cursor = 'pointer'\n    button.style.width = '48px'\n    // 添加点击事件处理\n    button.addEventListener('click', (e) => {\n      if (dragEndTime - dragStartTime > 200)\n        return\n      if (\n        Math.abs(dragStartOffset.x - dragEndOffset.x) > 20\n        || Math.abs(dragStartOffset.y - dragEndOffset.y) > 20\n      ) return\n\n      e.preventDefault()\n      e.stopPropagation()\n      this.switchPopups()\n    })\n\n    bubble.onmousedown = (e) => {\n      dragStartTime = Date.now()\n      dragStartOffset = { x: e.clientX, y: e.clientY }\n\n      // 展开状态下禁止拖拽\n      if (!this.isDrag)\n        return\n\n      button.style.cursor = 'move'\n\n      const offsetX = e.clientX - bubble.offsetLeft\n      const offsetY = e.clientY - bubble.offsetTop\n\n      const drag = (event: MouseEvent) => {\n        bubble.style.left = `${event.clientX - offsetX}px`\n        bubble.style.top = `${event.clientY - offsetY}px`\n      }\n\n      const stopDrag = (e: MouseEvent) => {\n        dragEndTime = Date.now()\n        dragEndOffset = { x: e.clientX, y: e.clientY }\n        document.removeEventListener('mousemove', drag)\n        document.removeEventListener('mouseup', stopDrag)\n        button.style.cursor = 'pointer'\n        this.absorbedToSide()\n      }\n\n      document.addEventListener('mousemove', drag)\n      document.addEventListener('mouseup', stopDrag)\n    }\n\n    this.createPopup()\n    // this.addHeaderDrag()\n    // this.addResize()\n\n    // bubble.appendChild(button)\n    // document.body.appendChild(bubble)\n  }\n\n  // 开关弹窗\n  switchPopups() {\n    // const bubble = document.querySelector(`#${this.bubbleId}`) as HTMLDivElement\n    if (this.popupId) {\n      const popup = document.querySelector(`#${this.popupId}`) as HTMLDivElement\n      // const clientHeight = document.documentElement.clientHeight\n      // const bound = bubble.getBoundingClientRect()\n      // const isRightSide = bubble.dataset.side === 'right'\n      if (this.visible) {\n        this.visible = false\n        this.isDrag = true\n        // bubble.style.cursor = 'move'\n        popup.style.transform = 'scale(1)'\n        popup.getBoundingClientRect()\n\n        popup.style.transition = 'all .3s ease-in-out'\n        popup.style.transform = 'scale(0)'\n        popup.style.top = '5%'\n        popup.style.left = '10%'\n      }\n      else {\n        this.visible = true\n        this.isDrag = false\n        // bubble.style.cursor = 'default'\n\n        // popup.style.marginTop = `-${popup.offsetHeight / 2}px`\n        // popup.style.marginLeft = `-${popup.offsetWidth / 2}px`\n        // popup.style.top = bubble.style.top\n        // popup.style.left = bubble.style.left\n        popup.style.top = '5%'\n        popup.style.left = '10%'\n        popup.style.transform = 'scale(0)'\n\n        popup.getBoundingClientRect()\n\n        popup.style.transition = 'all .3s ease-in-out'\n        // popup.style.transform = 'scale(1) translate(-50%, -50%) skew(0deg,0deg)'\n        popup.style.transform = 'scale(1)'\n\n        popup.style.top = '5%'\n        popup.style.left = '10%'\n\n        // 设置弹窗上、左距离\n        // const height = popup.offsetHeight\n        // if (bound.top + height < clientHeight)\n        //   popup.style.top = `${bound.top}px`\n        // else if (bound.top + height > clientHeight)\n        //   popup.style.top = `${clientHeight - height - VERTICAL_SPACE}px`\n\n        // if (isRightSide) {\n        //   const width = popup.offsetWidth\n        //   popup.style.left = `calc(100vw - ${width}px - 80px)`\n        // }\n        // else {\n        //   popup.style.left = '80px'\n        // }\n      }\n\n      // popup.style.display = this.visible ? 'block' : 'none'\n    }\n    else {\n      this.isDrag = false\n      // bubble.style.cursor = 'default'\n    }\n  }\n\n  // 创建弹窗\n  createPopup() {\n    this.popupId = `popup_${new Date().getTime()}`\n    // 创建弹出页面元素\n    const popup = document.createElement('div')\n    popup.id = this.popupId\n    popup.style.position = 'fixed'\n    popup.style.top = '100vh'\n    popup.style.left = '5%'\n    popup.style.zIndex = '10000'\n    popup.style.width = `calc(${POPUP_DEFAULT_WIDTH} * 100vw)`\n    popup.style.height = `calc(${POPUP_DEFAULT_HEIGHT} * 100vh)`\n    popup.style.backgroundColor = 'white'\n    popup.style.border = '1px solid #DFE4E8'\n    popup.style.borderRadius = '10px'\n    popup.style.boxShadow = '0 0 20px 0 #434b5b26'\n    // popup.style.transform = `scale(0) skew(${SKEW_DGE}deg,${SKEW_DGE}deg)`\n    popup.style.transformOrigin = 'center center'\n\n    const iframe = document.createElement('iframe')\n    iframe.style.width = '100%'\n    iframe.style.height = '100%'\n    iframe.style.border = 'none'\n    iframe.style.borderRadius = '10px'\n    let origin = 'https://news-craft-test.coli688.com'\n    if (process.env.NEXT_PUBLIC_DEPLOY_ENV === 'PRODUCTION')\n      origin = 'https://news-craft.coli688.com'\n    iframe.src = encodeURI(`${origin}/#/creation/index?mini=1&code=${this.userCode}`)\n    popup.appendChild(iframe)\n\n    // 添加关闭按钮\n    const closeButton = document.createElement('button')\n    closeButton.style.position = 'absolute'\n    closeButton.style.width = '18px'\n    closeButton.style.height = '18px'\n    closeButton.style.right = '24px'\n    closeButton.style.top = '24px'\n    closeButton.style.border = 'none'\n    closeButton.style.backgroundColor = 'transparent'\n    closeButton.style.color = '#A3AFBB'\n    closeButton.style.cursor = 'pointer'\n    closeButton.style.zIndex = '10'\n    closeButton.innerHTML = '&#x2715;'\n\n    closeButton.addEventListener('click', () => {\n      this.switchPopups()\n    })\n\n    popup.appendChild(closeButton)\n\n    // 添加其他内容到弹出页面\n    document.body.appendChild(popup)\n\n    // 关闭动画结束重置样式\n    popup.addEventListener('transitionend', () => {\n      // popup.style.display = this.visible ? 'block' : 'none'\n      // 弹窗动画结束时移除动画效果\n      popup.style.transition = 'unset'\n      // if (!this.visible) {\n      //   popup.style.top = '100vh'\n      //   popup.style.left = '100vw'\n      // }\n      if (this.visible)\n        popup.style.transform = 'unset'\n    })\n  }\n\n  removePopup() {\n    const popupEl = document.getElementById(this.popupId || '')\n    if (popupEl)\n      document.body.removeChild(popupEl)\n  }\n\n  // 吸附到边缘\n  absorbedToSide() {\n    const bubble = document.querySelector(`#${this.bubbleId}`) as HTMLDivElement\n    const bound = bubble.getBoundingClientRect()\n    const clientWidth = document.documentElement.clientWidth\n    const clientHeight = document.documentElement.clientHeight\n\n    const left = bound.left\n    const right = clientWidth - bound.right\n    const top = bound.top\n\n    // 左右吸边\n    if (left > right) {\n      bubble.style.left = `calc(100vw - ${HORIZONTAL_SPACE}px - ${bound.width}px)`\n      bubble.dataset.side = 'right'\n    }\n    else {\n      bubble.style.left = `${HORIZONTAL_SPACE}px`\n      bubble.dataset.side = 'left'\n    }\n\n    // 上下边界判定\n    if (top < 0)\n      bubble.style.top = '10px'\n    else if (top > clientHeight - bound.height)\n      bubble.style.top = `calc(100vh - ${bound.height}px - ${VERTICAL_SPACE}px)`\n    else\n      bubble.style.top = `${top}px`\n  }\n\n  // 头部拖拽移动\n  addHeaderDrag() {\n    const popup = document.querySelector(`#${this.popupId}`) as HTMLDivElement\n    const div = document.createElement('div')\n    div.classList.add('drag-header')\n\n    div.style.height = '62px'\n    div.style.width = '100%'\n    div.style.position = 'absolute'\n    div.style.top = '0px'\n    div.style.left = '0px'\n    div.style.cursor = 'move'\n    div.style.userSelect = 'none'\n\n    let mouseOffset = { x: 0, y: 0 }\n    let popupOffset = { x: 0, y: 0 }\n    const mousedown = (e: MouseEvent) => {\n      const bound = popup.getBoundingClientRect()\n      mouseOffset = {\n        x: e.clientX,\n        y: e.clientY,\n      }\n      popupOffset = {\n        x: bound.left,\n        y: bound.top,\n      }\n\n      const mousemove = (e: MouseEvent) => {\n        popup.style.top = `${popupOffset.y + e.clientY - mouseOffset.y}px`\n        popup.style.left = `${popupOffset.x + e.clientX - mouseOffset.x}px`\n      }\n      const mouseup = () => {\n        div.removeEventListener('mouseup', mouseup)\n        document.removeEventListener('mousemove', mousemove)\n      }\n\n      const mouseleave = () => {\n        document.removeEventListener('mousemove', mousemove)\n      }\n\n      div.addEventListener('mouseup', mouseup)\n      document.addEventListener('mousemove', mousemove)\n      document.addEventListener('mouseleave', mouseleave)\n    }\n\n    div.addEventListener('mousedown', mousedown)\n    popup.appendChild(div)\n  }\n\n  // 添加拉伸功能\n  addResize() {\n    this.addTopEdgeResize()\n    this.addBottomEdgeResize()\n    this.addLeftEdgeResize()\n    this.addRightEdgeResize()\n    this.addTopLeftCornerResize()\n    this.addTopRightCornerResize()\n    this.addBottomLeftCornerResize()\n    this.addBottomRightCornerResize()\n  }\n\n  createResizeEdge({ style, onmousemove }: { [x: string]: any; onmousemove: Function }) {\n    const popup = document.querySelector(`#${this.popupId}`) as HTMLDivElement\n    const edgeEl = document.createElement('div')\n    const iframe = popup.querySelector('iframe') as HTMLIFrameElement\n    const dragHeader = popup.querySelector('.drag-header') as HTMLIFrameElement\n\n    for (const [key, value] of Object.entries(style))\n      edgeEl.style[key as any] = value as string\n\n    let mouseOffset = { x: 0, y: 0 }\n    let popupOffset = { x: 0, y: 0 }\n    let popupWidth = 0\n    let popupHeight = 0\n\n    const mousedown = (e: MouseEvent) => {\n      const bound = popup.getBoundingClientRect()\n      iframe.style.pointerEvents = 'none'\n      dragHeader.style.pointerEvents = 'none'\n\n      mouseOffset = {\n        x: e.clientX,\n        y: e.clientY,\n      }\n      popupOffset = {\n        x: bound.left,\n        y: bound.top,\n      }\n      popupHeight = bound.height\n      popupWidth = bound.width\n\n      const mousemove = (e: MouseEvent) => {\n        onmousemove?.({ popup, popupOffset, mouseOffset, popupHeight, popupWidth, e })\n      }\n      const mouseup = () => {\n        iframe.style.pointerEvents = 'all'\n        dragHeader.style.pointerEvents = 'all'\n        document.removeEventListener('mouseup', mouseup)\n        document.removeEventListener('mousemove', mousemove)\n      }\n\n      document.addEventListener('mouseup', mouseup)\n      document.addEventListener('mousemove', mousemove)\n    }\n\n    edgeEl.addEventListener('mousedown', mousedown)\n    popup.appendChild(edgeEl)\n  }\n\n  addTopEdgeResize() {\n    const style = {\n      position: 'absolute',\n      top: '0',\n      left: '0',\n      width: '100%',\n      height: '4px',\n      cursor: 'ns-resize',\n      userSelect: 'none',\n    }\n    const onmousemove = ({ popup, popupOffset, mouseOffset, popupHeight, e }: { [x: string]: any }) => {\n      const height = popupHeight + mouseOffset.y - e.clientY\n\n      if (height < POPUP_MIN_HEIGHT)\n        return\n\n      popup.style.top = `${popupOffset.y + e.clientY - mouseOffset.y}px`\n      popup.style.height = `${height < POPUP_MIN_HEIGHT ? POPUP_MIN_HEIGHT : height}px`\n    }\n    this.createResizeEdge({ style, onmousemove })\n  }\n\n  addBottomEdgeResize() {\n    const style = {\n      position: 'absolute',\n      bottom: '0',\n      left: '0',\n      width: '100%',\n      height: '4px',\n      cursor: 'ns-resize',\n      userSelect: 'none',\n    }\n    const onmousemove = ({ popup, mouseOffset, popupHeight, e }: { [x: string]: any }) => {\n      const height = popupHeight + e.clientY - mouseOffset.y\n\n      if (height < POPUP_MIN_HEIGHT)\n        return\n\n      popup.style.height = `${height < POPUP_MIN_HEIGHT ? POPUP_MIN_HEIGHT : height}px`\n    }\n    this.createResizeEdge({ style, onmousemove })\n  }\n\n  addLeftEdgeResize() {\n    const style = {\n      position: 'absolute',\n      bottom: '0',\n      left: '0',\n      width: '4px',\n      height: '100%',\n      cursor: 'ew-resize',\n      userSelect: 'none',\n    }\n    const onmousemove = ({ popup, mouseOffset, popupOffset, popupWidth, e }: { [x: string]: any }) => {\n      const width = popupWidth + mouseOffset.x - e.clientX\n\n      if (width < POPUP_MIN_WIDTH)\n        return\n\n      popup.style.width = `${width < POPUP_MIN_WIDTH ? POPUP_MIN_WIDTH : width}px`\n      popup.style.left = `${popupOffset.x + e.clientX - mouseOffset.x}px`\n    }\n    this.createResizeEdge({ style, onmousemove })\n  }\n\n  addRightEdgeResize() {\n    const style = {\n      position: 'absolute',\n      bottom: '0',\n      right: '0',\n      width: '4px',\n      height: '100%',\n      cursor: 'ew-resize',\n      userSelect: 'none',\n    }\n    const onmousemove = ({ popup, mouseOffset, popupWidth, e }: { [x: string]: any }) => {\n      const width = popupWidth + e.clientX - mouseOffset.x\n\n      if (width < POPUP_MIN_WIDTH)\n        return\n\n      popup.style.width = `${width < POPUP_MIN_WIDTH ? POPUP_MIN_WIDTH : width}px`\n    }\n    this.createResizeEdge({ style, onmousemove })\n  }\n\n  addTopLeftCornerResize() {\n    const style = {\n      position: 'absolute',\n      top: '0',\n      left: '0',\n      width: '10px',\n      height: '10px',\n      cursor: 'nw-resize',\n      userSelect: 'none',\n    }\n    const onmousemove = ({ popup, mouseOffset, popupOffset, popupWidth, popupHeight, e }: { [x: string]: any }) => {\n      const height = popupHeight + mouseOffset.y - e.clientY\n      const width = popupWidth + mouseOffset.x - e.clientX\n      if (height >= POPUP_MIN_HEIGHT) {\n        popup.style.top = `${popupOffset.y + e.clientY - mouseOffset.y}px`\n        popup.style.height = `${height < POPUP_MIN_HEIGHT ? POPUP_MIN_HEIGHT : height}px`\n      }\n      if (width > POPUP_MIN_WIDTH) {\n        popup.style.left = `${popupOffset.x + e.clientX - mouseOffset.x}px`\n        popup.style.width = `${width < POPUP_MIN_WIDTH ? POPUP_MIN_WIDTH : width}px`\n      }\n    }\n    this.createResizeEdge({ style, onmousemove })\n  }\n\n  addTopRightCornerResize() {\n    const style = {\n      position: 'absolute',\n      top: '0',\n      right: '0',\n      width: '10px',\n      height: '10px',\n      cursor: 'ne-resize',\n      userSelect: 'none',\n    }\n    const onmousemove = ({ popup, mouseOffset, popupOffset, popupWidth, popupHeight, e }: { [x: string]: any }) => {\n      const height = popupHeight + mouseOffset.y - e.clientY\n      const width = popupWidth + e.clientX - mouseOffset.x\n      if (height >= POPUP_MIN_HEIGHT) {\n        popup.style.top = `${popupOffset.y + e.clientY - mouseOffset.y}px`\n        popup.style.height = `${height < POPUP_MIN_HEIGHT ? POPUP_MIN_HEIGHT : height}px`\n      }\n      if (width >= POPUP_MIN_WIDTH)\n        popup.style.width = `${width < POPUP_MIN_WIDTH ? POPUP_MIN_WIDTH : width}px`\n    }\n    this.createResizeEdge({ style, onmousemove })\n  }\n\n  addBottomLeftCornerResize() {\n    const style = {\n      position: 'absolute',\n      bottom: '0',\n      left: '0',\n      width: '10px',\n      height: '10px',\n      cursor: 'sw-resize',\n      userSelect: 'none',\n    }\n    const onmousemove = ({ popup, mouseOffset, popupOffset, popupWidth, popupHeight, e }: { [x: string]: any }) => {\n      const width = popupWidth + mouseOffset.x - e.clientX\n      const height = popupHeight + e.clientY - mouseOffset.y\n      if (width >= POPUP_MIN_WIDTH) {\n        popup.style.left = `${popupOffset.x + e.clientX - mouseOffset.x}px`\n        popup.style.width = `${width < POPUP_MIN_WIDTH ? POPUP_MIN_WIDTH : width}px`\n      }\n      if (height >= POPUP_MIN_HEIGHT)\n        popup.style.height = `${height < POPUP_MIN_HEIGHT ? POPUP_MIN_HEIGHT : height}px`\n    }\n    this.createResizeEdge({ style, onmousemove })\n  }\n\n  addBottomRightCornerResize() {\n    const style = {\n      position: 'absolute',\n      bottom: '0',\n      right: '0',\n      width: '10px',\n      height: '10px',\n      cursor: 'se-resize',\n      userSelect: 'none',\n    }\n    const onmousemove = ({ popup, mouseOffset, popupWidth, popupHeight, e }: { [x: string]: any }) => {\n      const width = popupWidth + e.clientX - mouseOffset.x\n      const height = popupHeight + e.clientY - mouseOffset.y\n      // eslint-disable-next-line no-self-compare\n      if (width >= width)\n        popup.style.width = `${width < POPUP_MIN_WIDTH ? POPUP_MIN_WIDTH : width}px`\n\n      if (height >= POPUP_MIN_HEIGHT)\n        popup.style.height = `${height < POPUP_MIN_HEIGHT ? POPUP_MIN_HEIGHT : height}px`\n    }\n    this.createResizeEdge({ style, onmousemove })\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,WAAW;;;;AACX,MAAM,mBAAmB;AACzB,WAAW;AACX,MAAM,iBAAiB;AACvB,SAAS;AACT,MAAM,sBAAsB;AAC5B,SAAS;AACT,MAAM,uBAAuB;AAC7B,SAAS;AACT,MAAM,kBAAkB;AACxB,SAAS;AACT,MAAM,mBAAmB;AACzB,OAAO;AACP,MAAM,WAAW;AAEF,MAAM;IACX,QAAgB;IAChB,SAAgB;IAChB,QAAsB;IACtB,OAAe;IACf,SAAgB;IAExB,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG,CAAC,OAAO,EAAE,IAAI,OAAO,OAAO,IAAI;QAChD,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,aAAa;IACb,aAAa,UAAU;QACrB,UAAU;IACZ,CAAC,EAAE;QACD,IAAI,gBAAgB,EAAE,SAAS;;QAC/B,IAAI,cAAc,EAAE,SAAS;;QAC7B,IAAI,kBAAkB;YAAE,GAAG;YAAG,GAAG;QAAE,EAAE,QAAQ;;QAC7C,IAAI,gBAAgB;YAAE,GAAG;YAAG,GAAG;QAAE,EAAE,QAAQ;;QAC3C,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ;QAEhC,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ;QACzB,OAAO,KAAK,CAAC,QAAQ,GAAG;QACxB,oEAAoE;QACpE,OAAO,KAAK,CAAC,IAAI,GAAG;QACpB,iEAAiE;QACjE,OAAO,KAAK,CAAC,GAAG,GAAG;QACnB,OAAO,KAAK,CAAC,MAAM,GAAG;QACtB,OAAO,KAAK,CAAC,OAAO,GAAG;QACvB,OAAO,KAAK,CAAC,YAAY,GAAG;QAC5B,OAAO,KAAK,CAAC,cAAc,GAAG;QAC9B,OAAO,KAAK,CAAC,KAAK,GAAG;QACrB,OAAO,KAAK,CAAC,MAAM,GAAG;QACtB,OAAO,KAAK,CAAC,KAAK,GAAG;QACrB,OAAO,KAAK,CAAC,QAAQ,GAAG;QACxB,OAAO,KAAK,CAAC,UAAU,GAAG;QAC1B,OAAO,KAAK,CAAC,MAAM,GAAG;QACtB,OAAO,KAAK,CAAC,eAAe,GAAG;QAC/B,OAAO,KAAK,CAAC,YAAY,GAAG;QAC5B,OAAO,KAAK,CAAC,MAAM,GAAG;QAEtB,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,WAAW,GAAG;QACrB,OAAO,KAAK,CAAC,KAAK,GAAG;QACrB,OAAO,KAAK,CAAC,QAAQ,GAAG;QACxB,OAAO,KAAK,CAAC,UAAU,GAAG;QAC1B,OAAO,KAAK,CAAC,MAAM,GAAG;QACtB,OAAO,KAAK,CAAC,eAAe,GAAG;QAC/B,OAAO,KAAK,CAAC,YAAY,GAAG;QAC5B,OAAO,KAAK,CAAC,MAAM,GAAG;QACtB,OAAO,KAAK,CAAC,KAAK,GAAG;QACrB,WAAW;QACX,OAAO,gBAAgB,CAAC,SAAS,CAAC;YAChC,IAAI,cAAc,gBAAgB,KAChC;YACF,IACE,KAAK,GAAG,CAAC,gBAAgB,CAAC,GAAG,cAAc,CAAC,IAAI,MAC7C,KAAK,GAAG,CAAC,gBAAgB,CAAC,GAAG,cAAc,CAAC,IAAI,IACnD;YAEF,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,IAAI,CAAC,YAAY;QACnB;QAEA,OAAO,WAAW,GAAG,CAAC;YACpB,gBAAgB,KAAK,GAAG;YACxB,kBAAkB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YAE/C,YAAY;YACZ,IAAI,CAAC,IAAI,CAAC,MAAM,EACd;YAEF,OAAO,KAAK,CAAC,MAAM,GAAG;YAEtB,MAAM,UAAU,EAAE,OAAO,GAAG,OAAO,UAAU;YAC7C,MAAM,UAAU,EAAE,OAAO,GAAG,OAAO,SAAS;YAE5C,MAAM,OAAO,CAAC;gBACZ,OAAO,KAAK,CAAC,IAAI,GAAG,GAAG,MAAM,OAAO,GAAG,QAAQ,EAAE,CAAC;gBAClD,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,MAAM,OAAO,GAAG,QAAQ,EAAE,CAAC;YACnD;YAEA,MAAM,WAAW,CAAC;gBAChB,cAAc,KAAK,GAAG;gBACtB,gBAAgB;oBAAE,GAAG,EAAE,OAAO;oBAAE,GAAG,EAAE,OAAO;gBAAC;gBAC7C,SAAS,mBAAmB,CAAC,aAAa;gBAC1C,SAAS,mBAAmB,CAAC,WAAW;gBACxC,OAAO,KAAK,CAAC,MAAM,GAAG;gBACtB,IAAI,CAAC,cAAc;YACrB;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,WAAW;QACvC;QAEA,IAAI,CAAC,WAAW;IAChB,uBAAuB;IACvB,mBAAmB;IAEnB,6BAA6B;IAC7B,oCAAoC;IACtC;IAEA,OAAO;IACP,eAAe;QACb,+EAA+E;QAC/E,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,MAAM,QAAQ,SAAS,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE;YACvD,6DAA6D;YAC7D,+CAA+C;YAC/C,sDAAsD;YACtD,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,MAAM,GAAG;gBACd,+BAA+B;gBAC/B,MAAM,KAAK,CAAC,SAAS,GAAG;gBACxB,MAAM,qBAAqB;gBAE3B,MAAM,KAAK,CAAC,UAAU,GAAG;gBACzB,MAAM,KAAK,CAAC,SAAS,GAAG;gBACxB,MAAM,KAAK,CAAC,GAAG,GAAG;gBAClB,MAAM,KAAK,CAAC,IAAI,GAAG;YACrB,OACK;gBACH,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,MAAM,GAAG;gBACd,kCAAkC;gBAElC,yDAAyD;gBACzD,yDAAyD;gBACzD,qCAAqC;gBACrC,uCAAuC;gBACvC,MAAM,KAAK,CAAC,GAAG,GAAG;gBAClB,MAAM,KAAK,CAAC,IAAI,GAAG;gBACnB,MAAM,KAAK,CAAC,SAAS,GAAG;gBAExB,MAAM,qBAAqB;gBAE3B,MAAM,KAAK,CAAC,UAAU,GAAG;gBACzB,2EAA2E;gBAC3E,MAAM,KAAK,CAAC,SAAS,GAAG;gBAExB,MAAM,KAAK,CAAC,GAAG,GAAG;gBAClB,MAAM,KAAK,CAAC,IAAI,GAAG;YAEnB,YAAY;YACZ,oCAAoC;YACpC,yCAAyC;YACzC,uCAAuC;YACvC,8CAA8C;YAC9C,oEAAoE;YAEpE,qBAAqB;YACrB,oCAAoC;YACpC,yDAAyD;YACzD,IAAI;YACJ,SAAS;YACT,8BAA8B;YAC9B,IAAI;YACN;QAEA,wDAAwD;QAC1D,OACK;YACH,IAAI,CAAC,MAAM,GAAG;QACd,kCAAkC;QACpC;IACF;IAEA,OAAO;IACP,cAAc;QACZ,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,EAAE,IAAI,OAAO,OAAO,IAAI;QAC9C,WAAW;QACX,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO;QACvB,MAAM,KAAK,CAAC,QAAQ,GAAG;QACvB,MAAM,KAAK,CAAC,GAAG,GAAG;QAClB,MAAM,KAAK,CAAC,IAAI,GAAG;QACnB,MAAM,KAAK,CAAC,MAAM,GAAG;QACrB,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,oBAAoB,SAAS,CAAC;QAC1D,MAAM,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,EAAE,qBAAqB,SAAS,CAAC;QAC5D,MAAM,KAAK,CAAC,eAAe,GAAG;QAC9B,MAAM,KAAK,CAAC,MAAM,GAAG;QACrB,MAAM,KAAK,CAAC,YAAY,GAAG;QAC3B,MAAM,KAAK,CAAC,SAAS,GAAG;QACxB,yEAAyE;QACzE,MAAM,KAAK,CAAC,eAAe,GAAG;QAE9B,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,CAAC,KAAK,GAAG;QACrB,OAAO,KAAK,CAAC,MAAM,GAAG;QACtB,OAAO,KAAK,CAAC,MAAM,GAAG;QACtB,OAAO,KAAK,CAAC,YAAY,GAAG;QAC5B,IAAI,SAAS;QACb,IAAI,QAAQ,GAAG,CAAC,sBAAsB,KAAK,cACzC,SAAS;QACX,OAAO,GAAG,GAAG,UAAU,GAAG,OAAO,8BAA8B,EAAE,IAAI,CAAC,QAAQ,EAAE;QAChF,MAAM,WAAW,CAAC;QAElB,SAAS;QACT,MAAM,cAAc,SAAS,aAAa,CAAC;QAC3C,YAAY,KAAK,CAAC,QAAQ,GAAG;QAC7B,YAAY,KAAK,CAAC,KAAK,GAAG;QAC1B,YAAY,KAAK,CAAC,MAAM,GAAG;QAC3B,YAAY,KAAK,CAAC,KAAK,GAAG;QAC1B,YAAY,KAAK,CAAC,GAAG,GAAG;QACxB,YAAY,KAAK,CAAC,MAAM,GAAG;QAC3B,YAAY,KAAK,CAAC,eAAe,GAAG;QACpC,YAAY,KAAK,CAAC,KAAK,GAAG;QAC1B,YAAY,KAAK,CAAC,MAAM,GAAG;QAC3B,YAAY,KAAK,CAAC,MAAM,GAAG;QAC3B,YAAY,SAAS,GAAG;QAExB,YAAY,gBAAgB,CAAC,SAAS;YACpC,IAAI,CAAC,YAAY;QACnB;QAEA,MAAM,WAAW,CAAC;QAElB,cAAc;QACd,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,aAAa;QACb,MAAM,gBAAgB,CAAC,iBAAiB;YACtC,wDAAwD;YACxD,gBAAgB;YAChB,MAAM,KAAK,CAAC,UAAU,GAAG;YACzB,uBAAuB;YACvB,8BAA8B;YAC9B,+BAA+B;YAC/B,IAAI;YACJ,IAAI,IAAI,CAAC,OAAO,EACd,MAAM,KAAK,CAAC,SAAS,GAAG;QAC5B;IACF;IAEA,cAAc;QACZ,MAAM,UAAU,SAAS,cAAc,CAAC,IAAI,CAAC,OAAO,IAAI;QACxD,IAAI,SACF,SAAS,IAAI,CAAC,WAAW,CAAC;IAC9B;IAEA,QAAQ;IACR,iBAAiB;QACf,MAAM,SAAS,SAAS,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE;QACzD,MAAM,QAAQ,OAAO,qBAAqB;QAC1C,MAAM,cAAc,SAAS,eAAe,CAAC,WAAW;QACxD,MAAM,eAAe,SAAS,eAAe,CAAC,YAAY;QAE1D,MAAM,OAAO,MAAM,IAAI;QACvB,MAAM,QAAQ,cAAc,MAAM,KAAK;QACvC,MAAM,MAAM,MAAM,GAAG;QAErB,OAAO;QACP,IAAI,OAAO,OAAO;YAChB,OAAO,KAAK,CAAC,IAAI,GAAG,CAAC,aAAa,EAAE,iBAAiB,KAAK,EAAE,MAAM,KAAK,CAAC,GAAG,CAAC;YAC5E,OAAO,OAAO,CAAC,IAAI,GAAG;QACxB,OACK;YACH,OAAO,KAAK,CAAC,IAAI,GAAG,GAAG,iBAAiB,EAAE,CAAC;YAC3C,OAAO,OAAO,CAAC,IAAI,GAAG;QACxB;QAEA,SAAS;QACT,IAAI,MAAM,GACR,OAAO,KAAK,CAAC,GAAG,GAAG;aAChB,IAAI,MAAM,eAAe,MAAM,MAAM,EACxC,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,aAAa,EAAE,MAAM,MAAM,CAAC,KAAK,EAAE,eAAe,GAAG,CAAC;aAE1E,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;IACjC;IAEA,SAAS;IACT,gBAAgB;QACd,MAAM,QAAQ,SAAS,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE;QACvD,MAAM,MAAM,SAAS,aAAa,CAAC;QACnC,IAAI,SAAS,CAAC,GAAG,CAAC;QAElB,IAAI,KAAK,CAAC,MAAM,GAAG;QACnB,IAAI,KAAK,CAAC,KAAK,GAAG;QAClB,IAAI,KAAK,CAAC,QAAQ,GAAG;QACrB,IAAI,KAAK,CAAC,GAAG,GAAG;QAChB,IAAI,KAAK,CAAC,IAAI,GAAG;QACjB,IAAI,KAAK,CAAC,MAAM,GAAG;QACnB,IAAI,KAAK,CAAC,UAAU,GAAG;QAEvB,IAAI,cAAc;YAAE,GAAG;YAAG,GAAG;QAAE;QAC/B,IAAI,cAAc;YAAE,GAAG;YAAG,GAAG;QAAE;QAC/B,MAAM,YAAY,CAAC;YACjB,MAAM,QAAQ,MAAM,qBAAqB;YACzC,cAAc;gBACZ,GAAG,EAAE,OAAO;gBACZ,GAAG,EAAE,OAAO;YACd;YACA,cAAc;gBACZ,GAAG,MAAM,IAAI;gBACb,GAAG,MAAM,GAAG;YACd;YAEA,MAAM,YAAY,CAAC;gBACjB,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,OAAO,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;gBAClE,MAAM,KAAK,CAAC,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,OAAO,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;YACrE;YACA,MAAM,UAAU;gBACd,IAAI,mBAAmB,CAAC,WAAW;gBACnC,SAAS,mBAAmB,CAAC,aAAa;YAC5C;YAEA,MAAM,aAAa;gBACjB,SAAS,mBAAmB,CAAC,aAAa;YAC5C;YAEA,IAAI,gBAAgB,CAAC,WAAW;YAChC,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,cAAc;QAC1C;QAEA,IAAI,gBAAgB,CAAC,aAAa;QAClC,MAAM,WAAW,CAAC;IACpB;IAEA,SAAS;IACT,YAAY;QACV,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,mBAAmB;QACxB,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,uBAAuB;QAC5B,IAAI,CAAC,yBAAyB;QAC9B,IAAI,CAAC,0BAA0B;IACjC;IAEA,iBAAiB,EAAE,KAAK,EAAE,WAAW,EAA+C,EAAE;QACpF,MAAM,QAAQ,SAAS,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE;QACvD,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,MAAM,SAAS,MAAM,aAAa,CAAC;QACnC,MAAM,aAAa,MAAM,aAAa,CAAC;QAEvC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,OACxC,OAAO,KAAK,CAAC,IAAW,GAAG;QAE7B,IAAI,cAAc;YAAE,GAAG;YAAG,GAAG;QAAE;QAC/B,IAAI,cAAc;YAAE,GAAG;YAAG,GAAG;QAAE;QAC/B,IAAI,aAAa;QACjB,IAAI,cAAc;QAElB,MAAM,YAAY,CAAC;YACjB,MAAM,QAAQ,MAAM,qBAAqB;YACzC,OAAO,KAAK,CAAC,aAAa,GAAG;YAC7B,WAAW,KAAK,CAAC,aAAa,GAAG;YAEjC,cAAc;gBACZ,GAAG,EAAE,OAAO;gBACZ,GAAG,EAAE,OAAO;YACd;YACA,cAAc;gBACZ,GAAG,MAAM,IAAI;gBACb,GAAG,MAAM,GAAG;YACd;YACA,cAAc,MAAM,MAAM;YAC1B,aAAa,MAAM,KAAK;YAExB,MAAM,YAAY,CAAC;gBACjB,cAAc;oBAAE;oBAAO;oBAAa;oBAAa;oBAAa;oBAAY;gBAAE;YAC9E;YACA,MAAM,UAAU;gBACd,OAAO,KAAK,CAAC,aAAa,GAAG;gBAC7B,WAAW,KAAK,CAAC,aAAa,GAAG;gBACjC,SAAS,mBAAmB,CAAC,WAAW;gBACxC,SAAS,mBAAmB,CAAC,aAAa;YAC5C;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC,SAAS,gBAAgB,CAAC,aAAa;QACzC;QAEA,OAAO,gBAAgB,CAAC,aAAa;QACrC,MAAM,WAAW,CAAC;IACpB;IAEA,mBAAmB;QACjB,MAAM,QAAQ;YACZ,UAAU;YACV,KAAK;YACL,MAAM;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;QACA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC,EAAwB;YAC5F,MAAM,SAAS,cAAc,YAAY,CAAC,GAAG,EAAE,OAAO;YAEtD,IAAI,SAAS,kBACX;YAEF,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,OAAO,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;YAClE,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,SAAS,mBAAmB,mBAAmB,OAAO,EAAE,CAAC;QACnF;QACA,IAAI,CAAC,gBAAgB,CAAC;YAAE;YAAO;QAAY;IAC7C;IAEA,sBAAsB;QACpB,MAAM,QAAQ;YACZ,UAAU;YACV,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;QACA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC,EAAwB;YAC/E,MAAM,SAAS,cAAc,EAAE,OAAO,GAAG,YAAY,CAAC;YAEtD,IAAI,SAAS,kBACX;YAEF,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,SAAS,mBAAmB,mBAAmB,OAAO,EAAE,CAAC;QACnF;QACA,IAAI,CAAC,gBAAgB,CAAC;YAAE;YAAO;QAAY;IAC7C;IAEA,oBAAoB;QAClB,MAAM,QAAQ;YACZ,UAAU;YACV,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;QACA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC,EAAwB;YAC3F,MAAM,QAAQ,aAAa,YAAY,CAAC,GAAG,EAAE,OAAO;YAEpD,IAAI,QAAQ,iBACV;YAEF,MAAM,KAAK,CAAC,KAAK,GAAG,GAAG,QAAQ,kBAAkB,kBAAkB,MAAM,EAAE,CAAC;YAC5E,MAAM,KAAK,CAAC,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,OAAO,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;QACrE;QACA,IAAI,CAAC,gBAAgB,CAAC;YAAE;YAAO;QAAY;IAC7C;IAEA,qBAAqB;QACnB,MAAM,QAAQ;YACZ,UAAU;YACV,QAAQ;YACR,OAAO;YACP,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;QACA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC,EAAwB;YAC9E,MAAM,QAAQ,aAAa,EAAE,OAAO,GAAG,YAAY,CAAC;YAEpD,IAAI,QAAQ,iBACV;YAEF,MAAM,KAAK,CAAC,KAAK,GAAG,GAAG,QAAQ,kBAAkB,kBAAkB,MAAM,EAAE,CAAC;QAC9E;QACA,IAAI,CAAC,gBAAgB,CAAC;YAAE;YAAO;QAAY;IAC7C;IAEA,yBAAyB;QACvB,MAAM,QAAQ;YACZ,UAAU;YACV,KAAK;YACL,MAAM;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;QACA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,EAAwB;YACxG,MAAM,SAAS,cAAc,YAAY,CAAC,GAAG,EAAE,OAAO;YACtD,MAAM,QAAQ,aAAa,YAAY,CAAC,GAAG,EAAE,OAAO;YACpD,IAAI,UAAU,kBAAkB;gBAC9B,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,OAAO,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;gBAClE,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,SAAS,mBAAmB,mBAAmB,OAAO,EAAE,CAAC;YACnF;YACA,IAAI,QAAQ,iBAAiB;gBAC3B,MAAM,KAAK,CAAC,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,OAAO,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;gBACnE,MAAM,KAAK,CAAC,KAAK,GAAG,GAAG,QAAQ,kBAAkB,kBAAkB,MAAM,EAAE,CAAC;YAC9E;QACF;QACA,IAAI,CAAC,gBAAgB,CAAC;YAAE;YAAO;QAAY;IAC7C;IAEA,0BAA0B;QACxB,MAAM,QAAQ;YACZ,UAAU;YACV,KAAK;YACL,OAAO;YACP,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;QACA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,EAAwB;YACxG,MAAM,SAAS,cAAc,YAAY,CAAC,GAAG,EAAE,OAAO;YACtD,MAAM,QAAQ,aAAa,EAAE,OAAO,GAAG,YAAY,CAAC;YACpD,IAAI,UAAU,kBAAkB;gBAC9B,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,OAAO,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;gBAClE,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,SAAS,mBAAmB,mBAAmB,OAAO,EAAE,CAAC;YACnF;YACA,IAAI,SAAS,iBACX,MAAM,KAAK,CAAC,KAAK,GAAG,GAAG,QAAQ,kBAAkB,kBAAkB,MAAM,EAAE,CAAC;QAChF;QACA,IAAI,CAAC,gBAAgB,CAAC;YAAE;YAAO;QAAY;IAC7C;IAEA,4BAA4B;QAC1B,MAAM,QAAQ;YACZ,UAAU;YACV,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;QACA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,EAAwB;YACxG,MAAM,QAAQ,aAAa,YAAY,CAAC,GAAG,EAAE,OAAO;YACpD,MAAM,SAAS,cAAc,EAAE,OAAO,GAAG,YAAY,CAAC;YACtD,IAAI,SAAS,iBAAiB;gBAC5B,MAAM,KAAK,CAAC,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,OAAO,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;gBACnE,MAAM,KAAK,CAAC,KAAK,GAAG,GAAG,QAAQ,kBAAkB,kBAAkB,MAAM,EAAE,CAAC;YAC9E;YACA,IAAI,UAAU,kBACZ,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,SAAS,mBAAmB,mBAAmB,OAAO,EAAE,CAAC;QACrF;QACA,IAAI,CAAC,gBAAgB,CAAC;YAAE;YAAO;QAAY;IAC7C;IAEA,6BAA6B;QAC3B,MAAM,QAAQ;YACZ,UAAU;YACV,QAAQ;YACR,OAAO;YACP,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;QACA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,EAAwB;YAC3F,MAAM,QAAQ,aAAa,EAAE,OAAO,GAAG,YAAY,CAAC;YACpD,MAAM,SAAS,cAAc,EAAE,OAAO,GAAG,YAAY,CAAC;YACtD,2CAA2C;YAC3C,IAAI,SAAS,OACX,MAAM,KAAK,CAAC,KAAK,GAAG,GAAG,QAAQ,kBAAkB,kBAAkB,MAAM,EAAE,CAAC;YAE9E,IAAI,UAAU,kBACZ,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,SAAS,mBAAmB,mBAAmB,OAAO,EAAE,CAAC;QACrF;QACA,IAAI,CAAC,gBAAgB,CAAC;YAAE;YAAO;QAAY;IAC7C;AACF", "debugId": null}}, {"offset": {"line": 7251, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/toolbar/index.tsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react'\nimport { INTENT_INCLUDES_MEETING, INTENT_INCLUDES_SCHEDULE, INTENT_PPT } from '../u-const'\nimport { PortalToFollowElem, PortalToFollowElemContent, PortalToFollowElemTrigger } from '../../../portal-to-follow-elem'\nimport { useChatWithHistoryContext } from '../../chat-with-history/context'\nimport MySDK from './sdks/index'\nimport type AiPopup from './sdks/index'\nimport { ENTERPRISE_NAME } from '@/config/index'\n\ntype SvgProps = {\n  className?: string\n}\ntype ToolbarProps = {\n  label: string\n  icon: React.JSX.Element\n  prompt: string\n  style?: string\n  link?: string\n  type?: string\n}\n\nconst IconTalk = ({ className }: SvgProps) => (\n  <svg className={className} fill='currentColor' viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" ><path d=\"M261.3248 877.9264c-54.3744 43.52-64.9216 50.432-90.624 50.4832-23.8592 0-46.4896-10.8544-61.3888-29.5424-16.0256-20.0704-17.152-32.6144-17.152-102.2464V307.2c0-66.9696 1.28-82.8416 13.2096-106.24a121.1392 121.1392 0 0 1 52.9408-52.9408c23.3984-11.8784 39.2704-13.2096 106.24-13.2096h494.9504c66.9184 0 82.7904 1.3312 106.1376 13.2096 22.8352 11.6224 41.4208 30.208 52.992 52.9408 11.9296 23.3472 13.2096 39.2704 13.2096 106.0864v324.608c0 66.7648-1.28 82.688-13.2096 105.984-11.6224 22.8352-30.1568 41.3696-52.9408 52.992-23.3472 11.9296-39.2704 13.2096-106.0864 13.2096h-376.32c-14.4384 0-18.3296 0.256-21.9648 1.024a49.664 49.664 0 0 0-12.5952 4.4032l-1.024 0.512c-3.4816 2.048-7.168 4.7616-21.504 16.2304l-64.8704 51.9168z m20.6848-108.3392c17.3056-13.824 22.1696-17.5104 30.4128-22.1696l3.7376-1.9968a121.344 121.344 0 0 1 30.72-10.752c10.8544-2.2528 17.3568-2.5088 42.3424-2.5088h380.3648c44.0832-0.1536 55.552-1.3312 63.5392-5.376a49.5104 49.5104 0 0 0 21.6576-21.6576c4.352-8.5504 5.376-21.0944 5.376-73.5232V307.0464c0-52.4288-1.024-64.9728-5.376-73.5232a49.5616 49.5616 0 0 0-21.6576-21.6064c-8.6016-4.4032-21.1456-5.4272-73.6256-5.4272H254.5664c-44.2368 0.1536-55.7056 1.3312-63.6928 5.4272a49.4592 49.4592 0 0 0-21.6576 21.6064c-4.352 8.5504-5.376 21.1456-5.376 73.728v497.7664c0.1536 37.376 1.2288 48.7936 1.536 49.152 1.28 1.6384 3.2256 2.56 5.2736 2.56 0.512 0 11.52-7.2704 45.8752-34.816l65.536-52.3264z\" p-id=\"1001\"></path><path d=\"M341.3504 469.3504m-51.2 0a51.2 51.2 0 1 0 102.4 0 51.2 51.2 0 1 0-102.4 0Z\" p-id=\"1002\"></path><path d=\"M512 469.3504m-51.2 0a51.2 51.2 0 1 0 102.4 0 51.2 51.2 0 1 0-102.4 0Z\" p-id=\"1003\"></path><path d=\"M682.6496 469.3504m-51.2 0a51.2 51.2 0 1 0 102.4 0 51.2 51.2 0 1 0-102.4 0Z\" p-id=\"1004\"></path></svg>\n)\nconst IconSchedule = ({ className }: SvgProps) => (\n  <svg className={className} fill='currentColor' viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" ><path d=\"M716.8 66.56a35.84 35.84 0 0 1 35.84 35.84l-0.0512 55.1424h0.6144c66.7136 0 85.5552 1.28 107.9296 11.776l4.2496 2.048c23.8592 12.1856 43.3152 31.5904 55.4496 55.4496 12.4416 24.4224 13.824 41.2672 13.824 112.128v449.1776c-0.2048 60.8256-2.0992 77.056-13.824 100.0448a126.9248 126.9248 0 0 1-55.4496 55.4496c-24.4224 12.4416-41.2672 13.824-112.0256 13.824h-437.248c-70.8096 0-87.6544-1.3824-112.0256-13.824a126.8736 126.8736 0 0 1-55.4496-55.4496c-12.4416-24.4224-13.824-41.2672-13.824-112.1792V339.0976c0-70.9632 1.3824-87.808 13.824-112.2816a126.8224 126.8224 0 0 1 55.4496-55.3984c24.4224-12.4416 41.2672-13.824 112.1792-13.824l0.512-0.0512L316.8768 102.4a35.84 35.84 0 0 1 30.976-35.5328l4.864-0.3072a35.84 35.84 0 0 1 35.84 35.84v55.1424h292.352L680.96 102.4a35.84 35.84 0 0 1 30.976-35.5328L716.8 66.56z m146.1248 344.7296H206.4896v374.784c0.1536 44.9024 1.2288 58.7264 5.12 67.6864l0.8704 1.8432c5.3248 10.4448 13.7728 18.8928 24.1152 24.1664 9.6256 4.864 23.1424 5.9904 79.5648 5.9904h437.1968c56.3712 0 69.8368-1.1264 79.4624-5.9904 10.3936-5.3248 18.8416-13.7728 24.1664-24.1664 4.864-9.5744 5.9904-23.04 5.9904-79.4624l-0.0512-364.8512z m-328.192 87.6032a35.84 35.84 0 0 1 35.84 35.84l-0.0512 77.9264h77.9776a35.84 35.84 0 0 1 35.5328 31.0272l0.3072 4.864a35.84 35.84 0 0 1-35.84 35.84l-77.9776-0.0512v77.9776a35.84 35.84 0 0 1-30.9248 35.4816l-4.864 0.3584a35.84 35.84 0 0 1-35.84-35.84l-0.0512-77.9776H420.9664a35.84 35.84 0 0 1-35.5328-30.9248l-0.3072-4.864a35.84 35.84 0 0 1 35.84-35.84l77.824-0.0512 0.1024-77.9264a35.84 35.84 0 0 1 30.976-35.4816l4.864-0.3584zM352.6656 229.2736h-36.352c-56.5248 0-70.0928 1.0752-79.7184 5.9904a55.1424 55.1424 0 0 0-24.064 24.1152c-4.9664 9.6256-6.0416 23.1424-6.0416 79.6672v0.5632h656.4864v-0.7168c0-52.8384-0.9728-68.0448-5.12-77.6704l-0.8704-1.8432a55.1936 55.1936 0 0 0-24.1664-24.064c-9.6256-4.9664-23.1424-6.0416-79.616-6.0416H352.7168z\" p-id=\"1161\"></path></svg>\n)\nconst IconLog = ({ className }: SvgProps) => (\n  <svg className={className} fill='currentColor' viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" ><path d=\"M593.92 66.56h4.3008l10.4448 0.0512c14.336 0.1024 23.4496 0.6656 32.256 2.304l5.2224 1.1264a126.8224 126.8224 0 0 1 42.496 19.0976c9.0624 6.4 14.4896 11.4688 33.5872 30.5664l142.2336 142.2848c21.8624 21.8624 27.392 27.904 34.4064 39.424 6.9632 11.3152 12.1344 23.7056 15.2064 36.608 2.56 10.5984 3.328 20.3776 3.4816 37.0688l0.0512 10.9056V776.1408c0 70.8096-1.3824 87.6032-13.824 112.0256a126.976 126.976 0 0 1-55.4496 55.4496c-24.4224 12.4416-41.2672 13.824-112.0768 13.824H563.2a35.84 35.84 0 0 1 0-71.68h173.056c56.4224 0 69.888-1.1264 79.5136-5.9904a55.296 55.296 0 0 0 24.1664-24.1664c4.864-9.5744 5.9904-23.04 5.9904-79.4624V411.2896h-103.68c-57.088-0.1536-74.9568-1.8432-95.8976-11.7248l-4.2496-2.048A126.8736 126.8736 0 0 1 586.752 342.016c-12.4416-24.4224-13.824-41.2672-13.824-112.1792L572.8256 138.24H380.16c-44.9024 0.1536-58.7264 1.2288-67.7376 5.12l-1.8432 0.8704a55.1424 55.1424 0 0 0-24.064 24.1152c-4.9664 9.6256-6.0416 23.1936-6.0416 79.7184V466.432a35.84 35.84 0 0 1-71.68 0V248.064c0-70.9632 1.3824-87.808 13.824-112.2304A126.8224 126.8224 0 0 1 278.016 80.384c24.4736-12.4416 41.3184-13.824 112.2304-13.824H593.92zM469.6064 441.1392a35.84 35.84 0 0 1 50.688 0l113.7664 113.7664a35.84 35.84 0 0 1 0 50.688l-341.3504 341.3504a35.84 35.84 0 0 1-25.344 10.496H153.6a35.84 35.84 0 0 1-35.84-35.84v-113.7664a35.84 35.84 0 0 1 10.496-25.344l341.3504-341.3504z m-99.84 201.1648L189.44 822.5792v63.1296h63.1296l180.2752-180.3264-63.0784-63.0784z m125.1328-125.1328l-74.4448 74.4448 63.0784 63.0784 74.4448-74.4448-63.0784-63.0784z m150.4768-370.7904l-0.8704-0.512v83.968c0 52.992 1.024 68.1472 5.1712 77.824l0.8704 1.8432c5.3248 10.3936 13.7728 18.8416 24.1152 24.1152 9.6256 4.9152 23.1424 5.9904 79.5136 5.9904h84.0192l-0.4096-0.768c-2.56-4.1472-5.9392-7.9872-18.7392-20.8896l-5.2736-5.2736-142.2336-142.2848-8.9088-8.8064a130.4064 130.4064 0 0 0-15.4112-13.9776l-1.8432-1.2288z\" p-id=\"1011\"></path></svg>\n)\nconst IconSystem = ({ className }: SvgProps) => (\n  <svg className={className} fill='currentColor' viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" ><path d=\"M727.04 117.76c69.9392 0 86.528 1.3312 110.6944 13.6704 23.6032 12.032 42.8032 31.232 54.8352 54.784 12.288 24.1664 13.6704 40.7552 13.6704 110.592V422.4a35.84 35.84 0 0 1-71.68 0V296.8064c0-55.3984-1.0752-68.6592-5.8368-78.0288a53.76 53.76 0 0 0-23.552-23.5008c-9.3696-4.7616-22.6304-5.8368-78.1312-5.8368H287.0272c-47.2064 0.1536-59.4432 1.3824-68.2496 5.8368-10.0864 5.1712-18.3296 13.4144-23.5008 23.552-4.7616 9.3184-5.8368 22.6304-5.8368 78.1312v440.0128c0.1536 47.2576 1.3824 59.4432 5.8368 68.2496 5.1712 10.1376 13.4144 18.3296 23.552 23.5008 9.3184 4.7616 22.528 5.8368 77.9776 5.8368H422.4a35.84 35.84 0 0 1 0 71.68H296.8064c-69.8368 0-86.4256-1.3824-110.592-13.6704a125.44 125.44 0 0 1-54.784-54.784c-12.288-24.2176-13.6704-40.8064-13.6704-110.7456V296.96c0-69.9392 1.3824-86.528 13.6704-110.6944 12.032-23.6032 31.232-42.8032 54.784-54.8352 24.2176-12.288 40.8064-13.6704 110.7456-13.6704h430.08z m-147.8144 268.8a192.6144 192.6144 0 0 1 159.744 300.3392l-0.6144 0.8192 112.5888 112.5376a35.84 35.84 0 0 1 3.7888 46.2336l-3.7888 4.4544a35.84 35.84 0 0 1-50.688 0l-112.5376-112.5888-0.8192 0.5632a191.5904 191.5904 0 0 1-97.792 32.6656l-9.9328 0.256a192.6144 192.6144 0 1 1 0-385.28z m0 71.68a120.9344 120.9344 0 1 0 0 241.92 120.9344 120.9344 0 0 0 0-241.92z\" p-id=\"1168\"></path></svg>\n)\nconst IconImage = ({ className }: SvgProps) => (\n  <svg className={className} fill='currentColor' viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" ><path d=\"M765.44 117.76c69.9392 0 86.528 1.3312 110.6944 13.6704 23.6032 12.032 42.8032 31.232 54.8352 54.784 12.288 24.1664 13.6704 40.7552 13.6704 110.592v430.3872c0 31.0272-0.1024 41.9328-0.8704 54.6304-1.3312 23.5008-4.864 40.2944-12.8 55.9104-12.032 23.552-31.232 42.8032-54.784 54.8352-24.1664 12.288-40.7552 13.6704-110.592 13.6704H245.6064c-69.8368 0-86.4256-1.3824-110.592-13.6704a125.44 125.44 0 0 1-54.784-54.8352c-11.776-23.1936-13.6192-42.2912-13.6704-101.6832l0.0512 0.6656-0.0512-0.7168V296.96c0-69.9392 1.3824-86.528 13.6704-110.6944 12.032-23.6032 31.232-42.8032 54.784-54.8352 24.2176-12.288 40.8064-13.6704 110.7456-13.6704h519.68zM342.8864 510.464L138.24 749.1584l0.1024 4.864c0.3584 29.696 1.6384 41.5744 4.864 49.152l0.8704 2.048c5.1712 10.0864 13.4144 18.3296 23.552 23.5008 9.3184 4.7616 22.528 5.8368 77.9776 5.8368h519.9872c55.3984 0 68.608-1.0752 78.0288-5.8368 10.0864-5.12 18.3296-13.4144 23.5008-23.552a38.1952 38.1952 0 0 0 3.2256-10.24l0.6144-4.0448-119.296-149.1968c-15.2064-18.944-20.3264-24.6272-21.9648-25.856a8.96 8.96 0 0 0-6.2976-0.5632 7.8848 7.8848 0 0 0-1.9968 1.3312l-2.9184 2.56c-3.6864 3.328-9.8816 9.2672-20.1728 19.6096l-28.672 28.672c-24.3712 24.064-31.9488 29.7984-48.64 34.7136-17.2544 5.12-35.7376 4.3008-52.5312-2.3552-17.408-6.912-24.2688-13.824-50.8416-45.7216L397.9776 510.464c-18.0736-21.6576-23.04-26.624-24.1664-27.0848a8.96 8.96 0 0 0-6.0928 0 6.9632 6.9632 0 0 0-1.6896 1.3312l-2.4576 2.4576c-3.584 3.7376-10.0864 10.9056-21.4016 24.064l0.7168-0.768z m432.4864-321.024H235.8272c-47.2064 0.1536-59.4432 1.3824-68.2496 5.8368-10.0864 5.1712-18.3296 13.4144-23.5008 23.552-4.7616 9.3184-5.8368 22.6304-5.8368 78.1312v342.1696l151.6544-176.896-1.024 1.1776c29.0304-33.8944 36.3008-41.0112 54.9376-47.616 17.7664-6.2976 37.2224-6.144 54.9376 0.3584 18.4832 6.8608 25.7024 14.1312 54.272 48.4352l119.6544 143.616c10.6496 12.7488 16.3328 19.2 19.3024 22.3744l1.9968 1.8944c0.4608 0.4096 0.768 0.6656 0.9216 0.7168a8.96 8.96 0 0 0 5.8368 0.2048c1.1264-0.3072 6.0416-4.5056 24.6272-23.04l22.272-22.272c30.0032-30.0544 37.5808-36.4032 55.9104-41.6768a80.6912 80.6912 0 0 1 53.1456 2.9696c17.6128 7.2704 24.4736 14.4896 50.944 47.5648l65.3312 81.664V296.8064c0-51.9168-0.9728-66.8672-5.0176-76.2368l-0.8192-1.792a53.76 53.76 0 0 0-23.552-23.5008c-9.3184-4.7616-22.6304-5.8368-78.1312-5.8368h9.9328zM640 296.96a80.64 80.64 0 1 1 0 161.28 80.64 80.64 0 0 1 0-161.28z\" p-id=\"1325\"></path></svg>\n\n)\nconst IconInternet = ({ className }: SvgProps) => (\n  <svg className={className} fill='currentColor' viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" ><path d=\"M512 66.56l13.3632 0.2048A445.44 445.44 0 1 1 66.56 512l0.2048-13.3632A445.44 445.44 0 0 1 512 66.56z m145.408 481.28H366.5408l0.3584 8.192c9.1136 182.528 74.0352 322.4064 139.8784 329.472L512 885.76l5.2224-0.256c65.8432-7.0656 130.7648-146.944 139.8784-329.5232l0.3072-8.1408z m-362.6496 0H139.9296l0.1024 0.8704a374.0672 374.0672 0 0 0 231.8848 309.9136l0.4096 0.1536-2.2016-3.7376c-42.3424-72.96-69.4784-178.8928-74.9568-297.8816l-0.4096-9.3184z m589.2608 0h-154.8288l-0.3584 9.3184c-5.4784 118.9888-32.6144 224.9216-74.9568 297.8816l-2.2528 3.7376a374.1696 374.1696 0 0 0 232.3456-310.0672l0.0512-0.8704z m-232.3968-382.6688l2.2528 3.7888c42.3424 72.96 69.4784 178.8928 74.9568 297.8816l0.3584 9.3184h154.8288v-0.8704a374.0672 374.0672 0 0 0-231.936-309.9136l-0.4608-0.2048z m-144.8448-26.624c-65.8432 7.0144-130.7648 146.944-139.8784 329.472l-0.3584 8.1408h290.8672l-0.3072-8.192c-9.1136-182.528-74.0352-322.4064-139.8784-329.472L512 138.24l-5.2224 0.256z m-134.4512 26.624a374.3744 374.3744 0 0 0-232.2944 310.1184l-0.1024 0.8704h154.8288l0.4096-9.3184c5.4784-118.9888 32.6144-224.9216 74.9568-297.8816l2.2016-3.7888z\" p-id=\"1482\"></path></svg>\n)\nconst IconProcess = ({ className }: SvgProps) => (\n  <svg className={className} fill='currentColor' viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" ><path d=\"M373.76 476.16v71.68H307.2A66.56 66.56 0 0 0 240.64 614.4v93.0816a66.56 66.56 0 0 0 66.56 66.56h66.56v71.68H307.2a138.24 138.24 0 0 1-138.24-138.24V614.4A138.24 138.24 0 0 1 307.2 476.16h66.56z m358.4 297.8304l130.56 0.0512a35.84 35.84 0 0 1 0 71.68H732.16v-71.68z m28.16-595.712a138.24 138.24 0 0 1 138.24 138.24V409.6a138.24 138.24 0 0 1-138.24 138.24h-28.16V476.16h28.16a66.56 66.56 0 0 0 66.56-66.56V316.5184a66.56 66.56 0 0 0-66.56-66.56h-28.16v-71.68h28.16z m-386.56 71.68H204.8a35.84 35.84 0 1 1 0-71.68h168.96v71.68z\" p-id=\"1639\"></path><path d=\"M651.6224 102.4a111.7184 111.7184 0 0 1 0 223.4368H428.2368a111.7184 111.7184 0 1 1 0-223.4368h223.3856z m0 71.68H428.2368a40.0384 40.0384 0 1 0 0 80.0768h223.3856a40.0384 40.0384 0 1 0 0-80.0768zM651.6224 400.2816a111.7184 111.7184 0 0 1 0 223.4368H428.2368a111.7184 111.7184 0 0 1 0-223.4368h223.3856z m0 71.68H428.2368a40.0384 40.0384 0 1 0 0 80.0768h223.3856a40.0384 40.0384 0 1 0 0-80.0768zM651.6224 698.1632a111.7184 111.7184 0 0 1 0 223.4368H428.2368a111.7184 111.7184 0 0 1 0-223.4368h223.3856z m0 71.68H428.2368a40.0384 40.0384 0 0 0 0 80.0768h223.3856a40.0384 40.0384 0 1 0 0-80.0768z\" p-id=\"1640\"></path></svg>\n)\nconst IconPPT = ({ className }: SvgProps) => (\n  <svg className={className} fill='currentColor' viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" ><path d=\"M554.0864 66.56h4.3008l10.496 0.0512c14.2848 0.1024 23.3984 0.6656 32.1536 2.3552l5.2736 1.1264c12.9536 3.072 25.344 8.192 36.6592 15.1552 3.0208 1.8944 3.0208 1.8944 5.888 3.8912 9.0624 6.4 14.4384 11.52 33.536 30.5664l148.3264 148.3776c16.896 16.9984 22.016 22.9376 28.3648 33.28 6.9632 11.3664 12.0832 23.7568 15.2064 36.6592 2.56 10.5984 3.328 20.4288 3.4816 37.12v412.928c-0.1536 60.8768-2.048 77.1072-13.824 100.096a126.976 126.976 0 0 1-55.4496 55.4496c-24.4224 12.4416-41.216 13.824-112.0256 13.824H350.3104c-70.8608 0-87.6544-1.3824-112.128-13.824a126.8736 126.8736 0 0 1-55.3984-55.4496c-12.4416-24.4224-13.824-41.2672-13.824-112.1792V248.064c0-70.9632 1.3824-87.808 13.824-112.2304A126.8224 126.8224 0 0 1 238.2336 80.384c24.4224-12.4416 41.2672-13.824 112.2304-13.824h203.6224z m-21.0432 71.68H340.3264c-48.128 0.1536-60.5184 1.4336-69.5808 5.9904a55.1424 55.1424 0 0 0-24.064 24.1152c-4.9152 9.6256-6.0416 23.1936-6.0416 79.7184v538.0096c0.1536 48.128 1.4336 60.5184 5.9904 69.5296 5.3248 10.4448 13.7728 18.8928 24.1152 24.1664 9.6256 4.864 23.1424 5.9904 79.5648 5.9904h346.112c56.4224 0 69.9392-1.1264 79.5136-5.9904a55.296 55.296 0 0 0 24.1664-24.1664c4.9152-9.5744 5.9904-23.04 5.9904-79.4624V411.2896h-103.68c-57.0368-0.1536-74.9056-1.8432-95.8464-11.7248l-4.2496-2.048A126.8736 126.8736 0 0 1 546.816 342.016c-12.4416-24.4224-13.824-41.2672-13.824-112.1792V138.24z m72.4992 8.1408l-0.8192-0.4608v94.0032c0.1536 44.9024 1.2288 58.7264 5.12 67.7376l0.9216 1.8432c5.2736 10.3936 13.7216 18.8416 24.064 24.1152 9.6256 4.9152 23.1424 5.9904 79.5648 5.9904h84.0192l-0.4608-0.768c-2.56-4.1472-5.888-7.9872-18.7392-20.8896l-152.32-152.32c-11.776-11.776-15.6672-15.3088-19.456-18.0224l-1.8944-1.2288z\" p-id=\"1031\"></path><path d=\"M539.904 675.9424H477.5936v99.7376c0 14.2848-3.1744 25.088-9.6256 32.4608a30.7712 30.7712 0 0 1-24.2688 11.0592c-10.24 0-18.432-3.6352-24.7296-10.9568-6.2464-7.2704-9.3696-17.92-9.3696-32.0512V504.5248c0-15.6672 3.4304-26.88 10.2912-33.6384 6.8608-6.7072 17.8176-10.0864 32.768-10.0864h87.2448c25.8048 0 45.6192 2.048 59.5456 6.2464a87.3984 87.3984 0 0 1 58.368 54.0672c5.1712 13.4656 7.7824 28.672 7.7824 45.4656 0 35.84-10.5472 63.0784-31.5904 81.6128-21.0944 18.4832-52.4288 27.7504-94.1056 27.7504z m-16.4864-161.536h-45.824v107.6736h45.824c16.0256 0 29.3888-1.7408 40.192-5.2736a46.7968 46.7968 0 0 0 24.576-17.3056c5.632-8.0384 8.4992-18.5344 8.4992-31.488 0-15.5648-4.352-28.2112-13.056-37.9904-9.728-10.3936-29.8496-15.616-60.2112-15.616z\" p-id=\"1032\"></path></svg>\n)\n\nconst IconDocumentAnalysis = ({ className }: SvgProps) => (\n  <svg className={className} fill='currentColor' viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M842.667 981.333H181.333A53.393 53.393 0 0 1 128 928V96a53.393 53.393 0 0 1 53.333-53.333H648.08a52.987 52.987 0 0 1 37.713 15.62L880.38 252.873A52.987 52.987 0 0 1 896 290.587V928a53.393 53.393 0 0 1-53.333 53.333z m-661.334-896A10.667 10.667 0 0 0 170.667 96v832a10.667 10.667 0 0 0 10.666 10.667h661.334A10.667 10.667 0 0 0 853.333 928V298.667h-160A53.393 53.393 0 0 1 640 245.333v-160zM682.667 115.5v129.833A10.667 10.667 0 0 0 693.333 256h129.834zM704 768H320a21.333 21.333 0 0 1 0-42.667h384A21.333 21.333 0 0 1 704 768z m0-213.333H320A21.333 21.333 0 0 1 320 512h384a21.333 21.333 0 0 1 0 42.667z m-213.333-256H320A21.333 21.333 0 0 1 320 256h170.667a21.333 21.333 0 0 1 0 42.667z\"></path>\n  </svg>\n)\nconst IconAll = ({ className }: SvgProps) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" className={className}>\n    <path d=\"M17.4,10 L10.2,14.8 L3,10 M17.4,13.2 L10.2,18 L3,13.2 M17.4,6.8 L10.2,11.6 L3,6.8 L10.2,2 L17.4,6.8 Z\" stroke=\"#747E8C\" stroke-width=\"1.4\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" fill-rule=\"evenodd\" />\n  </svg>\n)\nconst SvgClass = 'w-[20px] h-[20px] mr-[4px]'\nconst defaultToolList = [\n  {\n    label: '自由对话',\n    icon: <IconTalk className={SvgClass} />,\n    prompt: '欢迎使用小海AI! 您可以直接向我提问。在自由对话场景下，可以选择使用不同的模型进行问答，您可以通过对话框左侧的模型切换按钮进行不同模型的选择',\n  },\n  {\n    label: '文档分析',\n    icon: <IconDocumentAnalysis className={SvgClass} />,\n    prompt: '您可以上传文档或图片，并基于文档或图片的内容向我提问。文档分析服务使用中海本地部署的私有化大模型，信息安全有保障。目前暂不支持扫描类PDF文档。',\n  },\n  {\n    label: '查制度',\n    icon: <IconSystem className={SvgClass} />,\n    prompt: `您可以向我提问有关${ENTERPRISE_NAME}集团的制度，比如“如何申请使用合同专用章？”“与合约管理有关的制度文件”，目前我支持【综合管理】、【成本管理】等制度内容的查询，制度类型持续拓展中。`,\n  },\n  {\n    label: 'PPT生成(内测)',\n    icon: <IconPPT className={SvgClass} />,\n    prompt: '您可以向我提出PPT的主题、要求、期望的页数范围，我将为您生成PPT大纲，您可以对我生成的大纲提出调整优化的要求。在您确认大纲后，我将为您生成最终的PPT文件',\n  },\n  {\n    label: '订日程',\n    icon: <IconSchedule className={SvgClass} />,\n    prompt: '我可以帮您创建日程，您可以通过下面类似的问法向我交办任务：“预定明天上午10点到11点的日程，主题是项目汇报”',\n  },\n  {\n    label: '写日志',\n    icon: <IconLog className={SvgClass} />,\n    prompt: '我可以帮您随手记录当天日志，并存储至OA工作日志系统中。您可以直接向我发送需要记录的日志内容',\n  },\n  {\n    label: '图片生成',\n    icon: <IconImage className={SvgClass} />,\n    prompt: '我可以帮您生成图片，您可以向我描述想要的图片效果，比如”帮我生成一张摩天大楼的图片”',\n  },\n  {\n    label: '联网检索',\n    icon: <IconInternet className={SvgClass} />,\n    prompt: '您可以直接向我提出需要联网检索的内容，我将实时联网检索，并将检索内容进行总结，为您提供高效的检索体验。',\n  },\n  {\n    label: '查流程',\n    icon: <IconProcess className={SvgClass} />,\n    prompt: '我可以帮您查询个人名下的OA流程，您可以这样查询“帮我查询我1月份发起的流程”、“我当前待办的流程有哪些”',\n  },\n]\n\n// 模型切换默认场景\nexport const LLM_DEFAULT_INTENT = defaultToolList[0]\n\nconst Toolbar = ({ active, isMobile, onClick }: { active?: string; isMobile?: boolean; onClick: (data: any) => void }) => {\n  const mySDK = useRef<AiPopup>()\n  const { isFold, embedSource, appData } = useChatWithHistoryContext()\n  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}\n  // 根据selected过滤出已选数据 没有selected的是以前老数据 !Object.hasOwn(item, 'selected')判断\n  const aiToolbars = chatPageConfigData?.aiToolbars?.filter((item: any) => !Object.hasOwn(item, 'selected') || (Object.hasOwn(item, 'selected') && item?.selected))\n  let toolList = (aiToolbars && aiToolbars.length) ? aiToolbars : []\n  // 移动端去掉AI写新闻\n  if (isMobile) {\n    toolList = toolList.filter((item: ToolbarProps) => {\n      return item?.type !== 'popup'\n    })\n  }\n\n  LLM_DEFAULT_INTENT.prompt = toolList[0]?.prompt\n  const btnBgGradientFrom = chatPageConfigData?.btnBgGradientFrom || '#5099FF'\n  const btnBgGradientTo = chatPageConfigData?.btnBgGradientTo || '#7D67FF'\n\n  const activeIntent = active?.includes('PPT') ? INTENT_PPT : active\n  const toolRef = useRef<any>()\n  const [open, setOpen] = useState(false)\n  // 新增状态来存储可见和隐藏的工具列表项\n  const [visibleTools, setVisibleTools] = useState<string[]>([])\n  const [hiddenTools, setHiddenTools] = useState<string[]>([])\n\n  const handleItemClick = (item: ToolbarProps) => {\n    if (item?.label?.includes(INTENT_INCLUDES_SCHEDULE))\n      localStorage.setItem('scheduleOrMeeting', 'schedule')\n    else if (item?.label?.includes(INTENT_INCLUDES_MEETING))\n      localStorage.setItem('scheduleOrMeeting', 'meeting')\n    else localStorage.removeItem('scheduleOrMeeting')\n\n    if (!item)\n      return\n    if (item.type === 'link')\n      return window.open(item.link, '_blank')\n    if (item?.type === 'popup') {\n      mySDK.current?.switchPopups()\n      return\n    }\n    onClick(item)\n  }\n  const showIcon = (icon: any, act: boolean) => {\n    if (!icon)\n      return null\n    if (typeof icon === 'string' && icon.startsWith('http'))\n      return <img className={SvgClass} src={icon} style={{ filter: act ? 'brightness(0) invert(1)' : '' }} />\n    else\n      return icon\n  }\n\n  const renderIntentBtns = (list: any[], className?: string) => (\n    list.map(item => (\n      <li\n        id={item?.id}\n        key={item?.label}\n        data-label={item.label}\n        className={\n          className\n            ? `${className} ${activeIntent === item?.label ? 'rounded-[2px] text-[#ffffff]' : ''}`\n            : `btn-intent flex h-[32px] shrink-0 cursor-pointer items-center rounded-[16px] border border-[#DFE4E8] bg-[#fff] px-[12px] text-[14px] \n          ${item?.style} ${activeIntent === item?.label ? 'text-[#ffffff]' : ''}\n          ${isMobile && '!h-[38px] rounded-[19px]'}`\n        }\n        onClick={() => handleItemClick(item)}\n      >\n        {showIcon(item?.icon, activeIntent === item?.label)}\n        {item?.label}\n      </li>\n    ))\n  )\n\n  const handleContentWidthChange = () => {\n    const contentWidth = toolRef.current?.offsetWidth\n    const intentBtns = document.querySelectorAll('.btn-intent')\n    const btnTotal = document.querySelector('.btn-total') as HTMLElement\n\n    // 重置按钮样式\n    Array.from(intentBtns).forEach((item: any) => {\n      item.style.display = 'flex'\n      item.style.background = `${activeIntent === item.dataset.label ? `linear-gradient(to right, ${btnBgGradientFrom}, ${btnBgGradientTo})` : ''}`\n    })\n    if (isMobile) {\n      btnTotal.style.display = 'none'\n      return\n    }\n    btnTotal.style.display = 'flex'\n\n    const totalBtnsWidth = Array.from(intentBtns).reduce((total, el: any) => total + el.offsetWidth + 12, 0)\n\n    if (totalBtnsWidth < contentWidth) {\n      btnTotal.style.display = 'none'\n      return\n    }\n\n    let totalWidth = btnTotal?.offsetWidth || 0\n    let visible: string[] = []\n    let hidden: string[] = []\n    Array.from(intentBtns).forEach((item: any) => {\n      totalWidth += (item.offsetWidth + 12)\n      if (totalWidth < contentWidth) {\n        item.style = `display:flex;background: ${activeIntent === item.dataset.label ? `linear-gradient(to right, ${btnBgGradientFrom}, ${btnBgGradientTo})` : ''} `\n        visible.push(item.dataset.label)\n      }\n      else {\n        item.style = 'display:none'\n        hidden.push(item.dataset.label)\n      }\n    })\n\n    // 当前项在更多里\n    if (activeIntent && hidden.includes(activeIntent)) {\n      // 重置按钮样式\n      Array.from(intentBtns).forEach((item: any) => {\n        item.style.display = 'flex'\n        item.style.background = `${activeIntent === item.dataset.label ? `linear-gradient(to right, ${btnBgGradientFrom}, ${btnBgGradientTo})` : ''}`\n      })\n      btnTotal.style.display = 'flex'\n      const activeBtn: any = Array.from(intentBtns).find((item: any) => item.dataset.label.includes(activeIntent))\n\n      totalWidth = btnTotal?.offsetWidth + activeBtn.offsetWidth\n      visible = [activeBtn.dataset.label]\n      hidden = []\n      Array.from(intentBtns).forEach((item: any) => {\n        totalWidth += (item.offsetWidth + 12)\n        if (totalWidth < contentWidth || visible.includes(item.dataset.label)) {\n          item.style = `display:flex;background: ${activeIntent === item.dataset.label ? `linear-gradient(to right, ${btnBgGradientFrom}, ${btnBgGradientTo})` : ''} `\n          visible.push(item.dataset.label)\n        }\n        else {\n          item.style = 'display:none'\n          hidden.push(item.dataset.label)\n        }\n      })\n    }\n\n    setVisibleTools(visible)\n    setHiddenTools(hidden)\n  }\n\n  useEffect(() => {\n    handleContentWidthChange()\n\n    if (!isMobile) {\n      const observer = new ResizeObserver(handleContentWidthChange)\n      observer.observe(toolRef.current)\n      return () => observer.disconnect()\n    }\n  }, [isMobile, activeIntent])\n\n  useEffect(() => {\n    embedSource && handleContentWidthChange()\n  }, [embedSource])\n\n  useEffect(() => {\n    handleContentWidthChange()\n  }, [isFold])\n\n  useEffect(() => {\n    if (!mySDK.current) {\n      mySDK.current = new MySDK()\n      mySDK.current.createButton({\n        userCode: new Date().getTime().toString(),\n      })\n    }\n\n    return () => {\n      mySDK.current?.removePopup()\n    }\n  }, [])\n\n  return (\n    <ul\n      ref={toolRef}\n      className={`mb-[10px] flex w-full gap-x-[12px] ${isMobile && 'mb-[20px] gap-x-[8px] overflow-auto'}`}\n      style={{ scrollbarWidth: 'none' }}\n    >\n      {renderIntentBtns(toolList)}\n      <PortalToFollowElem\n        open={open}\n        onOpenChange={setOpen}\n        placement='bottom-end'\n        offset={4}\n      >\n        <PortalToFollowElemTrigger\n          onClick={() => setOpen(v => !v)}\n        >\n          <li\n            className={'btn-total hidden h-[32px] shrink-0 cursor-pointer items-center whitespace-nowrap rounded-[16px] border border-[#DFE4E8] bg-[#fff] px-[12px] text-[14px]'}\n          >\n            <IconAll className={SvgClass} />\n            更多\n          </li>\n        </PortalToFollowElemTrigger>\n        <PortalToFollowElemContent\n          className=\"z-50\"\n        >\n          <div\n            className={'border--gray-200 min-w-[120px] rounded-lg border bg-white p-[16px] shadow-lg'}\n            onMouseEnter={() => setOpen(true)}\n            onMouseLeave={() => setOpen(false)}\n            onClick={(e) => {\n              setOpen(false)\n              e.stopPropagation()\n            }}\n          >\n            {renderIntentBtns(toolList.filter((item: any) => hiddenTools.includes(item.label)), 'flex items-center mb-[17px] cursor-pointer text-[#242933] last:mb-[0px] text-[14px]')\n            }\n          </div>\n        </PortalToFollowElemContent>\n      </PortalToFollowElem>\n    </ul>\n  )\n}\n\nexport default Toolbar\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAgBA,MAAM,WAAW,CAAC,EAAE,SAAS,EAAY,iBACvC,qZAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;QAAgB,OAAM;;0BAA8B,qZAAC;gBAAK,GAAE;gBAA84C,QAAK;;;;;;0BAAc,qZAAC;gBAAK,GAAE;gBAA8E,QAAK;;;;;;0BAAc,qZAAC;gBAAK,GAAE;gBAAyE,QAAK;;;;;;0BAAc,qZAAC;gBAAK,GAAE;gBAA8E,QAAK;;;;;;;;;;;;AAE9zD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAY,iBAC3C,qZAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;QAAgB,OAAM;kBAA8B,cAAA,qZAAC;YAAK,GAAE;YAAw2D,QAAK;;;;;;;;;;;AAEl+D,MAAM,UAAU,CAAC,EAAE,SAAS,EAAY,iBACtC,qZAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;QAAgB,OAAM;kBAA8B,cAAA,qZAAC;YAAK,GAAE;YAAw2D,QAAK;;;;;;;;;;;AAEl+D,MAAM,aAAa,CAAC,EAAE,SAAS,EAAY,iBACzC,qZAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;QAAgB,OAAM;kBAA8B,cAAA,qZAAC;YAAK,GAAE;YAA0vC,QAAK;;;;;;;;;;;AAEp3C,MAAM,YAAY,CAAC,EAAE,SAAS,EAAY,iBACxC,qZAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;QAAgB,OAAM;kBAA8B,cAAA,qZAAC;YAAK,GAAE;YAAu2E,QAAK;;;;;;;;;;;AAGj+E,MAAM,eAAe,CAAC,EAAE,SAAS,EAAY,iBAC3C,qZAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;QAAgB,OAAM;kBAA8B,cAAA,qZAAC;YAAK,GAAE;YAAsmC,QAAK;;;;;;;;;;;AAEhuC,MAAM,cAAc,CAAC,EAAE,SAAS,EAAY,iBAC1C,qZAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;QAAgB,OAAM;;0BAA8B,qZAAC;gBAAK,GAAE;gBAA+gB,QAAK;;;;;;0BAAc,qZAAC;gBAAK,GAAE;gBAAqlB,QAAK;;;;;;;;;;;;AAEzvC,MAAM,UAAU,CAAC,EAAE,SAAS,EAAY,iBACtC,qZAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;QAAgB,OAAM;;0BAA8B,qZAAC;gBAAK,GAAE;gBAAkrD,QAAK;;;;;;0BAAc,qZAAC;gBAAK,GAAE;gBAA6uB,QAAK;;;;;;;;;;;;AAGpjF,MAAM,uBAAuB,CAAC,EAAE,SAAS,EAAY,iBACnD,qZAAC;QAAI,WAAW;QAAW,MAAK;QAAe,SAAQ;QAAgB,OAAM;kBAC3E,cAAA,qZAAC;YAAK,GAAE;;;;;;;;;;;AAGZ,MAAM,UAAU,CAAC,EAAE,SAAS,EAAY,iBACtC,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAK,GAAE;YAAwG,QAAO;YAAU,gBAAa;YAAM,kBAAe;YAAQ,mBAAgB;YAAQ,MAAK;YAAO,aAAU;;;;;;;;;;;AAG7N,MAAM,WAAW;AACjB,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,oBAAM,qZAAC;YAAS,WAAW;;;;;;QAC3B,QAAQ;IACV;IACA;QACE,OAAO;QACP,oBAAM,qZAAC;YAAqB,WAAW;;;;;;QACvC,QAAQ;IACV;IACA;QACE,OAAO;QACP,oBAAM,qZAAC;YAAW,WAAW;;;;;;QAC7B,QAAQ,CAAC,SAAS,EAAE,gBAAgB,0EAA0E,CAAC;IACjH;IACA;QACE,OAAO;QACP,oBAAM,qZAAC;YAAQ,WAAW;;;;;;QAC1B,QAAQ;IACV;IACA;QACE,OAAO;QACP,oBAAM,qZAAC;YAAa,WAAW;;;;;;QAC/B,QAAQ;IACV;IACA;QACE,OAAO;QACP,oBAAM,qZAAC;YAAQ,WAAW;;;;;;QAC1B,QAAQ;IACV;IACA;QACE,OAAO;QACP,oBAAM,qZAAC;YAAU,WAAW;;;;;;QAC5B,QAAQ;IACV;IACA;QACE,OAAO;QACP,oBAAM,qZAAC;YAAa,WAAW;;;;;;QAC/B,QAAQ;IACV;IACA;QACE,OAAO;QACP,oBAAM,qZAAC;YAAY,WAAW;;;;;;QAC9B,QAAQ;IACV;CACD;AAGM,MAAM,qBAAqB,eAAe,CAAC,EAAE;AAEpD,MAAM,UAAU,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAyE;IACnH,MAAM,QAAQ,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD;IACnB,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,4BAAyB,AAAD;IACjE,MAAM,qBAAqB,SAAS,MAAM,iBAAiB,KAAK,KAAK,CAAC,SAAS,MAAM,kBAAkB,CAAC;IACxG,yEAAyE;IACzE,MAAM,aAAa,oBAAoB,YAAY,OAAO,CAAC,OAAc,CAAC,OAAO,MAAM,CAAC,MAAM,eAAgB,OAAO,MAAM,CAAC,MAAM,eAAe,MAAM;IACvJ,IAAI,WAAW,AAAC,cAAc,WAAW,MAAM,GAAI,aAAa,EAAE;IAClE,aAAa;IACb,IAAI,UAAU;QACZ,WAAW,SAAS,MAAM,CAAC,CAAC;YAC1B,OAAO,MAAM,SAAS;QACxB;IACF;IAEA,mBAAmB,MAAM,GAAG,QAAQ,CAAC,EAAE,EAAE;IACzC,MAAM,oBAAoB,oBAAoB,qBAAqB;IACnE,MAAM,kBAAkB,oBAAoB,mBAAmB;IAE/D,MAAM,eAAe,QAAQ,SAAS,SAAS,wJAAA,CAAA,aAAU,GAAG;IAC5D,MAAM,UAAU,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,qBAAqB;IACrB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE3D,MAAM,kBAAkB,CAAC;QACvB,IAAI,MAAM,OAAO,SAAS,wJAAA,CAAA,2BAAwB,GAChD,aAAa,OAAO,CAAC,qBAAqB;aACvC,IAAI,MAAM,OAAO,SAAS,wJAAA,CAAA,0BAAuB,GACpD,aAAa,OAAO,CAAC,qBAAqB;aACvC,aAAa,UAAU,CAAC;QAE7B,IAAI,CAAC,MACH;QACF,IAAI,KAAK,IAAI,KAAK,QAChB,OAAO,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE;QAChC,IAAI,MAAM,SAAS,SAAS;YAC1B,MAAM,OAAO,EAAE;YACf;QACF;QACA,QAAQ;IACV;IACA,MAAM,WAAW,CAAC,MAAW;QAC3B,IAAI,CAAC,MACH,OAAO;QACT,IAAI,OAAO,SAAS,YAAY,KAAK,UAAU,CAAC,SAC9C,qBAAO,qZAAC;YAAI,WAAW;YAAU,KAAK;YAAM,OAAO;gBAAE,QAAQ,MAAM,4BAA4B;YAAG;;;;;;aAElG,OAAO;IACX;IAEA,MAAM,mBAAmB,CAAC,MAAa,YACrC,KAAK,GAAG,CAAC,CAAA,qBACP,qZAAC;gBACC,IAAI,MAAM;gBAEV,cAAY,KAAK,KAAK;gBACtB,WACE,YACI,GAAG,UAAU,CAAC,EAAE,iBAAiB,MAAM,QAAQ,iCAAiC,IAAI,GACpF,CAAC;UACL,EAAE,MAAM,MAAM,CAAC,EAAE,iBAAiB,MAAM,QAAQ,mBAAmB,GAAG;UACtE,EAAE,YAAY,4BAA4B;gBAE5C,SAAS,IAAM,gBAAgB;;oBAE9B,SAAS,MAAM,MAAM,iBAAiB,MAAM;oBAC5C,MAAM;;eAZF,MAAM;;;;;IAiBjB,MAAM,2BAA2B;QAC/B,MAAM,eAAe,QAAQ,OAAO,EAAE;QACtC,MAAM,aAAa,SAAS,gBAAgB,CAAC;QAC7C,MAAM,WAAW,SAAS,aAAa,CAAC;QAExC,SAAS;QACT,MAAM,IAAI,CAAC,YAAY,OAAO,CAAC,CAAC;YAC9B,KAAK,KAAK,CAAC,OAAO,GAAG;YACrB,KAAK,KAAK,CAAC,UAAU,GAAG,GAAG,iBAAiB,KAAK,OAAO,CAAC,KAAK,GAAG,CAAC,0BAA0B,EAAE,kBAAkB,EAAE,EAAE,gBAAgB,CAAC,CAAC,GAAG,IAAI;QAC/I;QACA,IAAI,UAAU;YACZ,SAAS,KAAK,CAAC,OAAO,GAAG;YACzB;QACF;QACA,SAAS,KAAK,CAAC,OAAO,GAAG;QAEzB,MAAM,iBAAiB,MAAM,IAAI,CAAC,YAAY,MAAM,CAAC,CAAC,OAAO,KAAY,QAAQ,GAAG,WAAW,GAAG,IAAI;QAEtG,IAAI,iBAAiB,cAAc;YACjC,SAAS,KAAK,CAAC,OAAO,GAAG;YACzB;QACF;QAEA,IAAI,aAAa,UAAU,eAAe;QAC1C,IAAI,UAAoB,EAAE;QAC1B,IAAI,SAAmB,EAAE;QACzB,MAAM,IAAI,CAAC,YAAY,OAAO,CAAC,CAAC;YAC9B,cAAe,KAAK,WAAW,GAAG;YAClC,IAAI,aAAa,cAAc;gBAC7B,KAAK,KAAK,GAAG,CAAC,yBAAyB,EAAE,iBAAiB,KAAK,OAAO,CAAC,KAAK,GAAG,CAAC,0BAA0B,EAAE,kBAAkB,EAAE,EAAE,gBAAgB,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBAC5J,QAAQ,IAAI,CAAC,KAAK,OAAO,CAAC,KAAK;YACjC,OACK;gBACH,KAAK,KAAK,GAAG;gBACb,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,KAAK;YAChC;QACF;QAEA,UAAU;QACV,IAAI,gBAAgB,OAAO,QAAQ,CAAC,eAAe;YACjD,SAAS;YACT,MAAM,IAAI,CAAC,YAAY,OAAO,CAAC,CAAC;gBAC9B,KAAK,KAAK,CAAC,OAAO,GAAG;gBACrB,KAAK,KAAK,CAAC,UAAU,GAAG,GAAG,iBAAiB,KAAK,OAAO,CAAC,KAAK,GAAG,CAAC,0BAA0B,EAAE,kBAAkB,EAAE,EAAE,gBAAgB,CAAC,CAAC,GAAG,IAAI;YAC/I;YACA,SAAS,KAAK,CAAC,OAAO,GAAG;YACzB,MAAM,YAAiB,MAAM,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,OAAc,KAAK,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC;YAE9F,aAAa,UAAU,cAAc,UAAU,WAAW;YAC1D,UAAU;gBAAC,UAAU,OAAO,CAAC,KAAK;aAAC;YACnC,SAAS,EAAE;YACX,MAAM,IAAI,CAAC,YAAY,OAAO,CAAC,CAAC;gBAC9B,cAAe,KAAK,WAAW,GAAG;gBAClC,IAAI,aAAa,gBAAgB,QAAQ,QAAQ,CAAC,KAAK,OAAO,CAAC,KAAK,GAAG;oBACrE,KAAK,KAAK,GAAG,CAAC,yBAAyB,EAAE,iBAAiB,KAAK,OAAO,CAAC,KAAK,GAAG,CAAC,0BAA0B,EAAE,kBAAkB,EAAE,EAAE,gBAAgB,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;oBAC5J,QAAQ,IAAI,CAAC,KAAK,OAAO,CAAC,KAAK;gBACjC,OACK;oBACH,KAAK,KAAK,GAAG;oBACb,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,KAAK;gBAChC;YACF;QACF;QAEA,gBAAgB;QAChB,eAAe;IACjB;IAEA,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,IAAI,CAAC,UAAU;YACb,MAAM,WAAW,IAAI,eAAe;YACpC,SAAS,OAAO,CAAC,QAAQ,OAAO;YAChC,OAAO,IAAM,SAAS,UAAU;QAClC;IACF,GAAG;QAAC;QAAU;KAAa;IAE3B,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;IACjB,GAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM,OAAO,EAAE;YAClB,MAAM,OAAO,GAAG,IAAI,sKAAA,CAAA,UAAK;YACzB,MAAM,OAAO,CAAC,YAAY,CAAC;gBACzB,UAAU,IAAI,OAAO,OAAO,GAAG,QAAQ;YACzC;QACF;QAEA,OAAO;YACL,MAAM,OAAO,EAAE;QACjB;IACF,GAAG,EAAE;IAEL,qBACE,qZAAC;QACC,KAAK;QACL,WAAW,CAAC,mCAAmC,EAAE,YAAY,uCAAuC;QACpG,OAAO;YAAE,gBAAgB;QAAO;;YAE/B,iBAAiB;0BAClB,qZAAC,qKAAA,CAAA,qBAAkB;gBACjB,MAAM;gBACN,cAAc;gBACd,WAAU;gBACV,QAAQ;;kCAER,qZAAC,qKAAA,CAAA,4BAAyB;wBACxB,SAAS,IAAM,QAAQ,CAAA,IAAK,CAAC;kCAE7B,cAAA,qZAAC;4BACC,WAAW;;8CAEX,qZAAC;oCAAQ,WAAW;;;;;;gCAAY;;;;;;;;;;;;kCAIpC,qZAAC,qKAAA,CAAA,4BAAyB;wBACxB,WAAU;kCAEV,cAAA,qZAAC;4BACC,WAAW;4BACX,cAAc,IAAM,QAAQ;4BAC5B,cAAc,IAAM,QAAQ;4BAC5B,SAAS,CAAC;gCACR,QAAQ;gCACR,EAAE,eAAe;4BACnB;sCAEC,iBAAiB,SAAS,MAAM,CAAC,CAAC,OAAc,YAAY,QAAQ,CAAC,KAAK,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAOhG;uCAEe", "debugId": null}}, {"offset": {"line": 7841, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/model-switching/index.tsx"], "sourcesContent": ["import React, { useEffect, useMemo, useState } from 'react'\nimport './style.css'\nimport {\n  FloatingPortal,\n  flip,\n  offset,\n  shift,\n  useClick,\n  useDismiss,\n  useFloating,\n  useInteractions,\n  useRole,\n} from '@floating-ui/react'\nimport Image from 'next/image'\nimport { RiArrowDownSLine } from '@remixicon/react'\nimport Tooltip from '@/components/base/tooltip'\n\n/** 单条数据type */\ntype ModelsType = {\n  value: string\n  label: string\n  icon: string\n}\n\n/** 组件传入参数 */\ntype ModelTooltipProps = {\n  models: any[]\n  item?: ModelsType\n  options?: ModelsType[]\n  isMobile?: boolean\n  showBuiltIn?: boolean\n  placement?: 'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'right' | 'right-start' | 'right-end' | 'left' | 'left-start' | 'left-end'\n  /** 选中数据传递出去的方法 */\n  onClickActive?: (item: ModelsType) => void\n}\n\nconst ModelTooltip = ({\n  // item = activeItem,\n  // options = models,\n  models,\n  isMobile,\n  placement: placements = 'top-start',\n  showBuiltIn,\n  onClickActive,\n}: ModelTooltipProps) => {\n  const [isOpen, setIsOpen] = useState(false)\n  const [clickObject, setClickObject] = useState<any>()\n\n  const { refs, floatingStyles, context } = useFloating({\n    placement: placements,\n    open: isOpen,\n    onOpenChange: (bool: boolean) => setIsOpen(bool),\n    middleware: [\n      offset(10),\n      flip(),\n      shift(),\n    ],\n  })\n\n  const click = useClick(context)\n  const role = useRole(context)\n  const dismiss = useDismiss(context)\n\n  const { getReferenceProps, getFloatingProps } = useInteractions([\n    click,\n    role,\n    dismiss,\n  ])\n\n  /**\n * 处理模型激活事件的函数\n * @param model 激活的模型，属于 ModelsType 类型\n */\n  const handleActive = (model: ModelsType) => {\n    setIsOpen(false)\n    setClickObject(model)\n    // 如果 onClickActive 函数存在，则调用它，并传入当前模型作为参数\n    onClickActive && onClickActive(model)\n  }\n\n  const options = useMemo(() => models?.filter(item => item.enable)?.map((item) => {\n    const temp = {\n      label: item.model_name_cn,\n      value: item.model_name_en,\n      icon: item.icon,\n      isBuiltIn: item.is_built_in,\n      isDefault: item.is_default,\n    }\n\n    return temp\n  }), [models])\n\n  // 非自由对话场景展示内置模型\n  const builtInLlm = options?.find(item => item.isBuiltIn)\n\n  useEffect(() => {\n    if(showBuiltIn) {\n      // 非自由对话场景选中内置模型\n      const temp = options.find(item => item.isBuiltIn)\n\n      temp && handleActive(temp)\n    }\n    else {\n      // 自由对话场景选中默认模型\n      const temp = options.find(item => item.isDefault)\n\n      temp && handleActive(temp)\n    }\n  }, [showBuiltIn, options])\n\n  if (!options?.length)\n    return null\n\n  if (showBuiltIn) {\n    return builtInLlm\n    ? <Tooltip popupContent=\"模型选择\">\n      <div className={\n        `flex h-9 w-fit cursor-pointer items-center rounded-2xl bg-[#F1F2F3] p-1 text-sm text-[#434B5B]\n        ${isMobile && 'flex h-[48px] !w-[48px] items-center justify-center rounded-[24px] bg-[#fff] !p-[0px]'}\n        `\n      }>\n        <Image src={builtInLlm?.icon || ''} width={isMobile ? 36 : 26} height={isMobile ? 36 : 26} alt='icon'></Image>\n      </div>\n    </Tooltip>\n    : null\n  }\n\n  return (\n      <div className={'inline-block'}>\n          {clickObject && (\n            <Tooltip popupContent=\"模型选择\">\n              <div>\n                <div\n                  ref={refs.setReference}\n                  {...getReferenceProps()}\n                  className={\n                    `flex h-9 cursor-pointer items-center rounded-2xl bg-[#F1F2F3] p-1 text-sm text-[#434B5B]\n                    ${isMobile && 'h-[48px] min-w-fit rounded-[24px] bg-[#fff] px-[10px]'}\n                    `\n                  }\n                >\n                  <Image src={clickObject?.icon} width={26} height={26} alt='icon'></Image>\n                  {options.length > 1 && <RiArrowDownSLine className={`min-w-5 text-[#A3AFBB] ${isMobile && 'w-1'}`}></RiArrowDownSLine>}\n                </div>\n              </div>\n            </Tooltip>\n          )}\n\n        {isOpen && options.length > 1 && (\n          <FloatingPortal>\n            <div\n              ref={refs.setFloating}\n              style={floatingStyles}\n              {...getFloatingProps()}\n              className=\"model-body z-50 min-w-44 border border-gray-200\"\n            >\n              {options?.map((model, index) => (\n                <div\n                  key={index}\n                  className={`model-item mb-2 flex cursor-pointer items-center rounded-lg px-2 py-1 ${model.label === clickObject?.label ? 'active' : ''}`}\n                  onClick={() => handleActive(model)}>\n                  {\n                    model.icon && <Image className='mr-3 h-[30px] w-[30px]' src={model.icon} width={30} height={30} alt='icon'></Image>\n                  }\n                  <span className=\"model-item-text flex-1 whitespace-nowrap text-sm\">{model.label}</span>\n                </div>\n              ))}\n            </div>\n          </FloatingPortal>\n        )}\n      </div>\n  )\n}\n\nexport default ModelTooltip\n"], "names": [], "mappings": ";;;;AAAA;;;;;;AAaA;;;;;;;;;;;;;;;;;;AAuBA,MAAM,eAAe,CAAC,EACpB,qBAAqB;AACrB,oBAAoB;AACpB,MAAM,EACN,QAAQ,EACR,WAAW,aAAa,WAAW,EACnC,WAAW,EACX,aAAa,EACK;IAClB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD;IAE7C,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,YAAY;QACpD,WAAW;QACX,MAAM;QACN,cAAc,CAAC,OAAkB,UAAU;QAC3C,YAAY;YACV,OAAO;YACP;YACA;SACD;IACH;IAEA,MAAM,QAAQ,SAAS;IACvB,MAAM,OAAO,QAAQ;IACrB,MAAM,UAAU,WAAW;IAE3B,MAAM,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,GAAG,gBAAgB;QAC9D;QACA;QACA;KACD;IAED;;;CAGD,GACC,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,eAAe;QACf,yCAAyC;QACzC,iBAAiB,cAAc;IACjC;IAEA,MAAM,UAAU,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE,IAAM,QAAQ,OAAO,CAAA,OAAQ,KAAK,MAAM,GAAG,IAAI,CAAC;YACtE,MAAM,OAAO;gBACX,OAAO,KAAK,aAAa;gBACzB,OAAO,KAAK,aAAa;gBACzB,MAAM,KAAK,IAAI;gBACf,WAAW,KAAK,WAAW;gBAC3B,WAAW,KAAK,UAAU;YAC5B;YAEA,OAAO;QACT,IAAI;QAAC;KAAO;IAEZ,gBAAgB;IAChB,MAAM,aAAa,SAAS,KAAK,CAAA,OAAQ,KAAK,SAAS;IAEvD,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAG,aAAa;YACd,gBAAgB;YAChB,MAAM,OAAO,QAAQ,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS;YAEhD,QAAQ,aAAa;QACvB,OACK;YACH,eAAe;YACf,MAAM,OAAO,QAAQ,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS;YAEhD,QAAQ,aAAa;QACvB;IACF,GAAG;QAAC;QAAa;KAAQ;IAEzB,IAAI,CAAC,SAAS,QACZ,OAAO;IAET,IAAI,aAAa;QACf,OAAO,2BACL,qZAAC;YAAQ,cAAa;sBACtB,cAAA,qZAAC;gBAAI,WACH,CAAC;QACD,EAAE,YAAY,wFAAwF;QACtG,CAAC;0BAED,cAAA,qZAAC,oSAAA,CAAA,UAAK;oBAAC,KAAK,YAAY,QAAQ;oBAAI,OAAO,WAAW,KAAK;oBAAI,QAAQ,WAAW,KAAK;oBAAI,KAAI;;;;;;;;;;;;;;;mBAGjG;IACJ;IAEA,qBACI,qZAAC;QAAI,WAAW;;YACX,6BACC,qZAAC;gBAAQ,cAAa;0BACpB,cAAA,qZAAC;8BACC,cAAA,qZAAC;wBACC,KAAK,KAAK,YAAY;wBACrB,GAAG,mBAAmB;wBACvB,WACE,CAAC;oBACD,EAAE,YAAY,wDAAwD;oBACtE,CAAC;;0CAGH,qZAAC,oSAAA,CAAA,UAAK;gCAAC,KAAK,aAAa;gCAAM,OAAO;gCAAI,QAAQ;gCAAI,KAAI;;;;;;4BACzD,QAAQ,MAAM,GAAG,mBAAK,qZAAC;gCAAiB,WAAW,CAAC,uBAAuB,EAAE,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;YAM1G,UAAU,QAAQ,MAAM,GAAG,mBAC1B,qZAAC;0BACC,cAAA,qZAAC;oBACC,KAAK,KAAK,WAAW;oBACrB,OAAO;oBACN,GAAG,kBAAkB;oBACtB,WAAU;8BAET,SAAS,IAAI,CAAC,OAAO,sBACpB,qZAAC;4BAEC,WAAW,CAAC,sEAAsE,EAAE,MAAM,KAAK,KAAK,aAAa,QAAQ,WAAW,IAAI;4BACxI,SAAS,IAAM,aAAa;;gCAE1B,MAAM,IAAI,kBAAI,qZAAC,oSAAA,CAAA,UAAK;oCAAC,WAAU;oCAAyB,KAAK,MAAM,IAAI;oCAAE,OAAO;oCAAI,QAAQ;oCAAI,KAAI;;;;;;8CAEtG,qZAAC;oCAAK,WAAU;8CAAoD,MAAM,KAAK;;;;;;;2BAN1E;;;;;;;;;;;;;;;;;;;;;AAcvB;uCAEe", "debugId": null}}, {"offset": {"line": 8062, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/base/chat/chat/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"appName\": \"index-module__kXCF8G__appName\",\n  \"mobilePromptBg\": \"index-module__kXCF8G__mobilePromptBg\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 8072, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/mcp-tools/create-modal.tsx"], "sourcesContent": ["import Modal from '@/components/base/modal'\nimport Input from '@/components/datasets/create/website/base/input'\nimport Textarea from '@/components/base/textarea'\nimport Image from 'next/image'\nimport { useRef } from 'react'\nimport IconTool from '@/assets/tool.svg'\nimport { PortalSelect } from '@/components/base/select'\n\n// import * as Yup from 'yup'\n\n// const schema = Yup.object().shape({\n//     username: Yup.string()\n//       .min(3, '用户名至少3个字符')\n//       .max(20, '用户名不能超过20个字符')\n//       .required('用户名必填'),\n//     email: Yup.string()\n//       .email('请输入有效的邮箱地址')\n//       .required('邮箱必填'),\n//     age: Yup.number()\n//       .typeError('年龄必须是数字')\n//       .integer('年龄必须是整数')\n//       .min(18, '年龄必须大于18岁')\n//       .max(100, '年龄必须小于100岁'),\n//     password: Yup.string()\n//       .min(8, '密码至少8个字符')\n//       .matches(\n//         /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/,\n//         '密码必须包含大小写字母和数字',\n//       ),\n//     confirmPassword: Yup.string()\n//       .oneOf([Yup.ref('password'), null], '密码必须匹配')\n//       .required('请确认密码'),\n//   })\n\n// 传输方式\nconst responseType = ['SSE', 'HTTPStreaming']\n\nconst CreateModal = ({ isShow, onClose }) => {\n    const uploadRef = useRef<HTMLInputElement>(null)\n    const tempUrl = useRef('')\n\n    const handleUpload = () => {\n        uploadRef.current?.click()\n    }\n\n    const handleFileChange = (e) => {\n        const file = e.target.files[0]\n\n        tempUrl.current = URL.createObjectURL(file)\n\n        if (uploadRef.current)\n            uploadRef.current.value = ''\n    }\n\n    return <Modal isShow={isShow} title=\"新增MCP工具\" closable onClose={onClose}>\n        <div className='mb-4'>\n            <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>图标 <span className='ml-1 text-red-500'>*</span></div>\n            <div className=\"flex items-center\">\n                <Image src={tempUrl.current || IconTool} width=\"28\" height=\"28\" alt=\"\"/>\n                <span className=\"ml-[10px] cursor-pointer text-[14px] text-[#3B67FF]\" onClick={handleUpload}>重新上传</span>\n                <input ref={uploadRef} accept='image/*' className=\"hidden\" type=\"file\" onChange={handleFileChange}></input>\n            </div>\n        </div>\n        <div className='mb-4'>\n            <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>名称 <span className='ml-1 text-red-500'>*</span></div>\n            <Input value={''} placeholder='请输入服务名称' onChange={() => {}}/>\n        </div>\n        <div className='mb-4'>\n            <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>描述 <span className='ml-1 text-red-500'>*</span></div>\n            <Textarea value={''} placeholder='请描述该服务的作用' onChange={() => {}}/>\n        </div>\n        <div className='mb-4'>\n            <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>请求地址 <span className='ml-1 text-red-500'>*</span></div>\n            <Input value={''} placeholder='连接服务的URL' onChange={() => {}}/>\n        </div>\n        <div className='mb-4'>\n            <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>传输方式 <span className='ml-1 text-red-500'>*</span></div>\n            <PortalSelect\n                value={responseType[0]}\n                onSelect={(i) => { }}\n                popupClassName={'min-w-[200px] z-[999]'}\n                items={(responseType || []).map(i => ({ name: i, value: i }))}\n            />\n        </div>\n    </Modal>\n}\n\nexport default CreateModal\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA;AACA;;;;;;;;;;;;;;;;;;;AAIA,6BAA6B;AAE7B,sCAAsC;AACtC,6BAA6B;AAC7B,6BAA6B;AAC7B,iCAAiC;AACjC,4BAA4B;AAC5B,0BAA0B;AAC1B,6BAA6B;AAC7B,2BAA2B;AAC3B,wBAAwB;AACxB,8BAA8B;AAC9B,4BAA4B;AAC5B,8BAA8B;AAC9B,iCAAiC;AACjC,6BAA6B;AAC7B,4BAA4B;AAC5B,kBAAkB;AAClB,6CAA6C;AAC7C,4BAA4B;AAC5B,WAAW;AACX,oCAAoC;AACpC,sDAAsD;AACtD,4BAA4B;AAC5B,OAAO;AAEP,OAAO;AACP,MAAM,eAAe;IAAC;IAAO;CAAgB;AAE7C,MAAM,cAAc,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IACpC,MAAM,YAAY,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAoB;IAC3C,MAAM,UAAU,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,MAAM,eAAe;QACjB,UAAU,OAAO,EAAE;IACvB;IAEA,MAAM,mBAAmB,CAAC;QACtB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAE9B,QAAQ,OAAO,GAAG,IAAI,eAAe,CAAC;QAEtC,IAAI,UAAU,OAAO,EACjB,UAAU,OAAO,CAAC,KAAK,GAAG;IAClC;IAEA,qBAAO,qZAAC;QAAM,QAAQ;QAAQ,OAAM;QAAU,QAAQ;QAAC,SAAS;;0BAC5D,qZAAC;gBAAI,WAAU;;kCACX,qZAAC;wBAAI,WAAU;;4BAA4D;0CAAG,qZAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;kCAClH,qZAAC;wBAAI,WAAU;;0CACX,qZAAC,oSAAA,CAAA,UAAK;gCAAC,KAAK,QAAQ,OAAO,IAAI;gCAAU,OAAM;gCAAK,QAAO;gCAAK,KAAI;;;;;;0CACpE,qZAAC;gCAAK,WAAU;gCAAsD,SAAS;0CAAc;;;;;;0CAC7F,qZAAC;gCAAM,KAAK;gCAAW,QAAO;gCAAU,WAAU;gCAAS,MAAK;gCAAO,UAAU;;;;;;;;;;;;;;;;;;0BAGzF,qZAAC;gBAAI,WAAU;;kCACX,qZAAC;wBAAI,WAAU;;4BAA4D;0CAAG,qZAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;kCAClH,qZAAC;wBAAM,OAAO;wBAAI,aAAY;wBAAU,UAAU,KAAO;;;;;;;;;;;;0BAE7D,qZAAC;gBAAI,WAAU;;kCACX,qZAAC;wBAAI,WAAU;;4BAA4D;0CAAG,qZAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;kCAClH,qZAAC;wBAAS,OAAO;wBAAI,aAAY;wBAAY,UAAU,KAAO;;;;;;;;;;;;0BAElE,qZAAC;gBAAI,WAAU;;kCACX,qZAAC;wBAAI,WAAU;;4BAA4D;0CAAK,qZAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;kCACpH,qZAAC;wBAAM,OAAO;wBAAI,aAAY;wBAAW,UAAU,KAAO;;;;;;;;;;;;0BAE9D,qZAAC;gBAAI,WAAU;;kCACX,qZAAC;wBAAI,WAAU;;4BAA4D;0CAAK,qZAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;kCACpH,qZAAC;wBACG,OAAO,YAAY,CAAC,EAAE;wBACtB,UAAU,CAAC,KAAQ;wBACnB,gBAAgB;wBAChB,OAAO,CAAC,gBAAgB,EAAE,EAAE,GAAG,CAAC,CAAA,IAAK,CAAC;gCAAE,MAAM;gCAAG,OAAO;4BAAE,CAAC;;;;;;;;;;;;;;;;;;AAI3E;uCAEe", "debugId": null}}, {"offset": {"line": 8385, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/mcp-tools/tabs/index.tsx"], "sourcesContent": ["import cn from 'classnames'\n\ntype Item = {\n  key: string;\n  label: string;\n  children: any;\n}\n\ntype Props = {\n  activeKey: string;\n  items: Item[];\n  onChange: (key: string) => void;\n}\nconst Tabs = ({ activeKey, items, onChange }: Props) => {\n  return (\n    <div>\n      <ul className=\"flex rounded-[4px] bg-[#f5f6f7] p-[2px]\">\n        {items?.map(item => (\n          <li\n            className={cn(\n              'flex-1 cursor-pointer py-[4px] text-center text-[14px] text-[#646a73] hover:bg-[#e4e6e7]',\n              activeKey === item.key && 'rounded-[4px] !bg-white text-black',\n            )}\n            onClick={() => onChange?.(item.key)}\n          >\n            {item.label}\n          </li>\n        ))}\n      </ul>\n      <div className=\"mt-[10px]\">{items?.find(item => item.key === activeKey)?.children}</div>\n    </div>\n  )\n}\n\nexport default Tabs\n"], "names": [], "mappings": ";;;;AAAA;;;AAaA,MAAM,OAAO,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAS;IACjD,qBACE,qZAAC;;0BACC,qZAAC;gBAAG,WAAU;0BACX,OAAO,IAAI,CAAA,qBACV,qZAAC;wBACC,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EACV,4FACA,cAAc,KAAK,GAAG,IAAI;wBAE5B,SAAS,IAAM,WAAW,KAAK,GAAG;kCAEjC,KAAK,KAAK;;;;;;;;;;;0BAIjB,qZAAC;gBAAI,WAAU;0BAAa,OAAO,KAAK,CAAA,OAAQ,KAAK,GAAG,KAAK,YAAY;;;;;;;;;;;;AAG/E;uCAEe", "debugId": null}}, {"offset": {"line": 8433, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/mcp-tools/tools-list/index.tsx"], "sourcesContent": ["import Loading from '@/components/base/loading'\nimport type { McpToolsData } from '@/models/share'\nimport Image from 'next/image'\nimport type { ReactElement } from 'react'\nimport useSWR from 'swr'\n\nconst ToolsList = ({ apiKey, api, extra }: { apiKey: string, api: () => Promise<{ data: McpToolsData[] }>, extra?: (data: McpToolsData, reload: () => void) => ReactElement }) => {\n    const { data, isLoading, mutate } = useSWR(apiKey, api)\n\n    if(isLoading) return <Loading className='!h-[300px]' />\n\n    return (\n        <ul className='h-[300px] overflow-scroll'>\n            {\n                data?.data?.map(item => (\n                    <li className='mb-[10px] flex items-center rounded-[4px] p-[10px] hover:bg-[#f5f6f7]'>\n                        <Image src={item.icon} width='32' height=\"32\" alt=\"\" />\n                        <div className=\"ml-[10px] flex-1 overflow-hidden\">\n                            <h2 className='overflow-hidden text-ellipsis text-nowrap text-[14px]' title={item.name}>{item.name}</h2>\n                            <p className='overflow-hidden text-ellipsis text-nowrap text-[12px] leading-normal text-[#646a73]' title={item.description}>{item.description}</p>\n                        </div>\n                        <div>\n                            {extra?.(item, mutate)}\n                        </div>\n                    </li>\n                ))\n            }\n        </ul>\n    )\n}\n\nexport default ToolsList\n"], "names": [], "mappings": ";;;;;;;;;AAEA;;;;;;;;;;AAIA,MAAM,YAAY,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAsI;IACzK,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,OAAO,QAAQ;IAEnD,IAAG,WAAW,qBAAO,qZAAC;QAAQ,WAAU;;;;;;IAExC,qBACI,qZAAC;QAAG,WAAU;kBAEN,MAAM,MAAM,IAAI,CAAA,qBACZ,qZAAC;gBAAG,WAAU;;kCACV,qZAAC,oSAAA,CAAA,UAAK;wBAAC,KAAK,KAAK,IAAI;wBAAE,OAAM;wBAAK,QAAO;wBAAK,KAAI;;;;;;kCAClD,qZAAC;wBAAI,WAAU;;0CACX,qZAAC;gCAAG,WAAU;gCAAwD,OAAO,KAAK,IAAI;0CAAG,KAAK,IAAI;;;;;;0CAClG,qZAAC;gCAAE,WAAU;gCAAsF,OAAO,KAAK,WAAW;0CAAG,KAAK,WAAW;;;;;;;;;;;;kCAEjJ,qZAAC;kCACI,QAAQ,MAAM;;;;;;;;;;;;;;;;;AAO3C;uCAEe", "debugId": null}}, {"offset": {"line": 8529, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/mcp-tools/tools-manage-modal.tsx"], "sourcesContent": ["import Modal from '@/components/base/modal'\nimport Tabs from './tabs'\nimport ToolsList from './tools-list'\nimport {\n  fetchMcpTools,\n  fetchUserMcpTools,\n  removeUserMcpTool,\n  updateMcpStatus,\n} from '@/service/share'\nimport { useState } from 'react'\nimport cn from 'classnames'\nimport Button from '@/components/base/button'\nimport type { McpToolsData } from '@/models/share'\n\ntype Props = {\n  isShow: boolean;\n  isMobile: boolean;\n  onClose: () => void;\n  onRefresh: () => void;\n}\n\nconst BtnDelete = ({ isHover, item, disabled, reload }: { isHover: boolean, item: McpToolsData, disabled?: boolean, reload: () => void }) => {\n  const [loading, setLoading] = useState(false)\n  const toggleClass = (isHover || loading) && !item.default_enabled && !disabled\n\n  const handleRemove = async () => {\n      try {\n          setLoading(true)\n\n          const res = await removeUserMcpTool({ tool_id: item.id })\n\n          res?.success && reload?.()\n      }\n      finally {\n          setLoading(false)\n      }\n  }\n\n  return <Button\n    className={cn('w-[90px] rounded-[4px] px-[20px]', toggleClass && 'border-[1px] border-[#f54a45] text-[#f54a45] hover:bg-[#fee3e2]')}\n    variant={toggleClass ? 'ghost' : 'secondary'}\n    spinnerClassName={toggleClass ? '!text-[#f54a45]' : ''}\n    loading={loading}\n    disabled={item.default_enabled || disabled}\n    onClick={() => handleRemove()}\n  >\n    {toggleClass ? '移除' : '已添加'}\n  </Button>\n}\n\nconst MarketExtra = ({ item, isMobile, reload }: { item: McpToolsData, isMobile: boolean, reload: () => void }) => {\n  const [isHover, setIsHover] = useState(false)\n  const [loading, setLoading] = useState(false)\n\n  const handleAdd = async (item: McpToolsData) => {\n    try {\n        setLoading(true)\n\n        const res = await updateMcpStatus({ tool_id: item.id, enabled: true })\n\n        res?.success && reload?.()\n    }\n    finally {\n        setLoading(false)\n    }\n  }\n\n  return item.added_status ? (\n    <div\n      onMouseOver={() => setIsHover(true)}\n      onMouseLeave={() => setIsHover(false)}\n    >\n      <BtnDelete disabled={isMobile} isHover={isHover} item={item} reload={reload}/>\n    </div>\n  ) : (\n    <Button\n      className=\"w-[90px] rounded-[4px] bg-[#3B67FF] px-[20px] hover:bg-[#3B67FF]\"\n      variant=\"primary\"\n      onClick={() => handleAdd(item)}\n      loading={loading}\n      disabled={item.default_enabled}\n    >\n      添加\n    </Button>\n  )\n}\n\nconst ToolsManageModal = ({ isShow, isMobile, onRefresh, onClose }: Props) => {\n  const [activeTab, setActiveTab] = useState('market')\n\n  const tabsItems = [\n    {\n      key: 'market',\n      label: '服务市场',\n      children: (\n        <ToolsList\n          apiKey=\"market\"\n          api={fetchMcpTools}\n          extra={(item, reload) => <MarketExtra isMobile={isMobile} item={item} reload={() => {\n            reload()\n            onRefresh()\n          }}/>}\n        />\n      ),\n    },\n    {\n      key: 'added',\n      label: '已添加',\n      children: (\n        <ToolsList\n          apiKey=\"added\"\n          api={fetchUserMcpTools}\n          extra={(item, reload) => <BtnDelete isHover item={item} reload={() => {\n            reload()\n            onRefresh()\n          }}/>}\n        />\n      ),\n    },\n  ]\n\n  return (\n    <Modal\n      title={\n        <div className=\"mb-[30px] flex items-center justify-between pr-[30px]\">\n          服务管理\n        </div>\n      }\n      isShow={isShow}\n      onClose={onClose}\n      closable\n    >\n      {/* <div className=\"my-[10px] flex justify-end\">\n        <span className=\"cursor-pointer text-[12px] text-[#3B67FF]\">\n          新增自定义服务\n        </span>\n      </div> */}\n      <Tabs\n        items={tabsItems}\n        activeKey={activeTab}\n        onChange={key => setActiveTab(key)}\n      ></Tabs>\n    </Modal>\n  )\n}\n\nexport default ToolsManageModal\n"], "names": [], "mappings": ";;;;;;;;;AACA;AACA;;;;;;AAOA;AACA;;;;;;;;;;;;;;AAWA,MAAM,YAAY,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAoF;IACtI,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,cAAc,CAAC,WAAW,OAAO,KAAK,CAAC,KAAK,eAAe,IAAI,CAAC;IAEtE,MAAM,eAAe;QACjB,IAAI;YACA,WAAW;YAEX,MAAM,MAAM,MAAM,kBAAkB;gBAAE,SAAS,KAAK,EAAE;YAAC;YAEvD,KAAK,WAAW;QACpB,SACQ;YACJ,WAAW;QACf;IACJ;IAEA,qBAAO,qZAAC;QACN,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE,oCAAoC,eAAe;QACjE,SAAS,cAAc,UAAU;QACjC,kBAAkB,cAAc,oBAAoB;QACpD,SAAS;QACT,UAAU,KAAK,eAAe,IAAI;QAClC,SAAS,IAAM;kBAEd,cAAc,OAAO;;;;;;AAE1B;AAEA,MAAM,cAAc,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAiE;IAC5G,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,YAAY,OAAO;QACvB,IAAI;YACA,WAAW;YAEX,MAAM,MAAM,MAAM,gBAAgB;gBAAE,SAAS,KAAK,EAAE;gBAAE,SAAS;YAAK;YAEpE,KAAK,WAAW;QACpB,SACQ;YACJ,WAAW;QACf;IACF;IAEA,OAAO,KAAK,YAAY,iBACtB,qZAAC;QACC,aAAa,IAAM,WAAW;QAC9B,cAAc,IAAM,WAAW;kBAE/B,cAAA,qZAAC;YAAU,UAAU;YAAU,SAAS;YAAS,MAAM;YAAM,QAAQ;;;;;;;;;;6BAGvE,qZAAC;QACC,WAAU;QACV,SAAQ;QACR,SAAS,IAAM,UAAU;QACzB,SAAS;QACT,UAAU,KAAK,eAAe;kBAC/B;;;;;;AAIL;AAEA,MAAM,mBAAmB,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAS;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,YAAY;QAChB;YACE,KAAK;YACL,OAAO;YACP,wBACE,qZAAC,oLAAA,CAAA,UAAS;gBACR,QAAO;gBACP,KAAK;gBACL,OAAO,CAAC,MAAM,uBAAW,qZAAC;wBAAY,UAAU;wBAAU,MAAM;wBAAM,QAAQ;4BAC5E;4BACA;wBACF;;;;;;;;;;;QAGN;QACA;YACE,KAAK;YACL,OAAO;YACP,wBACE,qZAAC,oLAAA,CAAA,UAAS;gBACR,QAAO;gBACP,KAAK;gBACL,OAAO,CAAC,MAAM,uBAAW,qZAAC;wBAAU,OAAO;wBAAC,MAAM;wBAAM,QAAQ;4BAC9D;4BACA;wBACF;;;;;;;;;;;QAGN;KACD;IAED,qBACE,qZAAC;QACC,qBACE,qZAAC;YAAI,WAAU;sBAAwD;;;;;;QAIzE,QAAQ;QACR,SAAS;QACT,QAAQ;kBAOR,cAAA,qZAAC,2KAAA,CAAA,UAAI;YACH,OAAO;YACP,WAAW;YACX,UAAU,CAAA,MAAO,aAAa;;;;;;;;;;;AAItC;uCAEe", "debugId": null}}, {"offset": {"line": 8719, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/mcp-tools/index.tsx"], "sourcesContent": ["import { RiArrowDownSLine } from '@remixicon/react'\nimport Image from 'next/image'\nimport Tooltip from '@/components/base/tooltip'\nimport { useMemo, useState } from 'react'\nimport {\n  FloatingPortal,\n  flip,\n  offset,\n  shift,\n  useClick,\n  useDismiss,\n  useFloating,\n  useInteractions,\n  useRole,\n} from '@floating-ui/react'\nimport useSWR from 'swr'\nimport { fetchUserMcpTools, updateMcpStatus } from '@/service/share'\nimport type { McpToolsData } from '@/models/share'\nimport Switch from '@/components/base/switch'\n\nimport IconTools from '@/assets/tools.svg'\nimport CreateModal from './create-modal'\nimport ToolsManageModal from './tools-manage-modal'\n\ntype Props = {\n  isMobile: boolean\n}\n\nconst McpTools = ({ isMobile }: Props) => {\n  const [isOpen, setIsOpen] = useState(false)\n  const [visible, setVisible] = useState({ manage: false, create: false })\n\n  const { data, isLoading, mutate } = useSWR('user-mcp-tools', fetchUserMcpTools)\n  const mcpTools = useMemo(() => data?.data || [], [data])\n\n  const { refs, floatingStyles, context } = useFloating({\n    placement: 'top-start',\n    open: isOpen,\n    onOpenChange: (bool: boolean) => setIsOpen(bool),\n    middleware: [offset(10), flip(), shift()],\n  })\n  const click = useClick(context)\n  const role = useRole(context)\n  const dismiss = useDismiss(context)\n  const { getReferenceProps, getFloatingProps } = useInteractions([\n    click,\n    role,\n    dismiss,\n  ])\n\n  const handleSwitchChange = (tool: McpToolsData, bool: boolean) => {\n    const params = {\n        tool_id: tool.id,\n        enabled: bool,\n    }\n    updateMcpStatus(params)\n  }\n\n  return (\n    <div>\n      <Tooltip popupContent=\"MCP工具\">\n        <div>\n            <div\n            ref={refs.setReference}\n            {...getReferenceProps()}\n            className={\n                `flex h-9 cursor-pointer items-center rounded-2xl bg-[#F1F2F3] p-1 text-sm text-[#434B5B]\n                ${isMobile && 'h-[48px] min-w-fit rounded-[24px] bg-[#fff] px-[10px]'}\n            `}\n            >\n            <Image src={IconTools} width={24} height={24} alt=\"icon\"></Image>\n            <RiArrowDownSLine\n                className={`min-w-5 text-[#A3AFBB] ${isMobile && 'w-1'}`}\n            ></RiArrowDownSLine>\n            </div>\n        </div>\n      </Tooltip>\n\n      {isOpen && (\n        <FloatingPortal>\n          <div\n            ref={refs.setFloating}\n            style={floatingStyles}\n            {...getFloatingProps()}\n            className=\"model-body z-50 min-w-44 border border-gray-200\"\n          >\n            <div className=\"mb-[10px] flex justify-between px-2 text-[12px] !text-[#8f959e]\">\n                MCP工具\n                {/* <span className='cursor-pointer text-[#3B67FF]' onClick={() => setVisible(pre => ({ ...pre, manage: true }))}>管理</span> */}\n            </div>\n            {mcpTools?.map((tool: McpToolsData, index) => (\n                <div\n                    key={index}\n                    className={'model-item mb-2 flex cursor-pointer items-center rounded-lg px-2 py-1'}\n                    >\n                    {\n                    tool.icon && <Image className='mr-3' src={tool.icon} width={22} height={22} alt='icon'></Image>\n                    }\n                    <div className='mr-[20px] min-w-[100px] flex-1'>\n                        <Tooltip popupContent={tool.description}>\n                          <div className=\"model-item-text whitespace-nowrap text-sm\">{tool.name}</div>\n                        </Tooltip>\n                    </div>\n                    <Switch\n                      className='rounded-[8px]'\n                      circleClassName=\"rounded-full\"\n                      defaultValue={tool.enabled}\n                      onChange={bool => handleSwitchChange(tool, bool)}\n                      size='md'\n                    />\n                </div>\n            ))}\n            {!mcpTools.length && !isLoading && <div className=\"text-center text-[12px]\">暂无可用工具</div>}\n          </div>\n        </FloatingPortal>\n      )}\n\n      <ToolsManageModal isShow={visible.manage} isMobile={isMobile} onRefresh={mutate} onClose={() => setVisible(pre => ({ ...pre, manage: false }))}></ToolsManageModal>\n\n      <CreateModal isShow={visible.create} onClose={() => setVisible(pre => ({ ...pre, create: false }))}></CreateModal>\n    </div>\n  )\n}\n\nexport default McpTools\n"], "names": [], "mappings": ";;;;;;;;;AACA;;;;;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA;AACA;;;;;;;;;;;;;AAMA,MAAM,WAAW,CAAC,EAAE,QAAQ,EAAS;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,QAAQ;QAAO,QAAQ;IAAM;IAEtE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,OAAO,kBAAkB;IAC7D,MAAM,WAAW,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE,IAAM,MAAM,QAAQ,EAAE,EAAE;QAAC;KAAK;IAEvD,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,YAAY;QACpD,WAAW;QACX,MAAM;QACN,cAAc,CAAC,OAAkB,UAAU;QAC3C,YAAY;YAAC,OAAO;YAAK;YAAQ;SAAQ;IAC3C;IACA,MAAM,QAAQ,SAAS;IACvB,MAAM,OAAO,QAAQ;IACrB,MAAM,UAAU,WAAW;IAC3B,MAAM,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,GAAG,gBAAgB;QAC9D;QACA;QACA;KACD;IAED,MAAM,qBAAqB,CAAC,MAAoB;QAC9C,MAAM,SAAS;YACX,SAAS,KAAK,EAAE;YAChB,SAAS;QACb;QACA,gBAAgB;IAClB;IAEA,qBACE,qZAAC;;0BACC,qZAAC;gBAAQ,cAAa;0BACpB,cAAA,qZAAC;8BACG,cAAA,qZAAC;wBACD,KAAK,KAAK,YAAY;wBACrB,GAAG,mBAAmB;wBACvB,WACI,CAAC;gBACD,EAAE,YAAY,wDAAwD;YAC1E,CAAC;;0CAED,qZAAC,oSAAA,CAAA,UAAK;gCAAC,KAAK;gCAAW,OAAO;gCAAI,QAAQ;gCAAI,KAAI;;;;;;0CAClD,qZAAC;gCACG,WAAW,CAAC,uBAAuB,EAAE,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;YAMjE,wBACC,qZAAC;0BACC,cAAA,qZAAC;oBACC,KAAK,KAAK,WAAW;oBACrB,OAAO;oBACN,GAAG,kBAAkB;oBACtB,WAAU;;sCAEV,qZAAC;4BAAI,WAAU;sCAAkE;;;;;;wBAIhF,UAAU,IAAI,CAAC,MAAoB,sBAChC,qZAAC;gCAEG,WAAW;;oCAGX,KAAK,IAAI,kBAAI,qZAAC,oSAAA,CAAA,UAAK;wCAAC,WAAU;wCAAO,KAAK,KAAK,IAAI;wCAAE,OAAO;wCAAI,QAAQ;wCAAI,KAAI;;;;;;kDAEhF,qZAAC;wCAAI,WAAU;kDACX,cAAA,qZAAC;4CAAQ,cAAc,KAAK,WAAW;sDACrC,cAAA,qZAAC;gDAAI,WAAU;0DAA6C,KAAK,IAAI;;;;;;;;;;;;;;;;kDAG3E,qZAAC;wCACC,WAAU;wCACV,iBAAgB;wCAChB,cAAc,KAAK,OAAO;wCAC1B,UAAU,CAAA,OAAQ,mBAAmB,MAAM;wCAC3C,MAAK;;;;;;;+BAhBF;;;;;wBAoBZ,CAAC,SAAS,MAAM,IAAI,CAAC,2BAAa,qZAAC;4BAAI,WAAU;sCAA0B;;;;;;;;;;;;;;;;;0BAKlF,qZAAC,sLAAA,CAAA,UAAgB;gBAAC,QAAQ,QAAQ,MAAM;gBAAE,UAAU;gBAAU,WAAW;gBAAQ,SAAS,IAAM,WAAW,CAAA,MAAO,CAAC;4BAAE,GAAG,GAAG;4BAAE,QAAQ;wBAAM,CAAC;;;;;;0BAE5I,qZAAC,6KAAA,CAAA,UAAW;gBAAC,QAAQ,QAAQ,MAAM;gBAAE,SAAS,IAAM,WAAW,CAAA,MAAO,CAAC;4BAAE,GAAG,GAAG;4BAAE,QAAQ;wBAAM,CAAC;;;;;;;;;;;;AAGtG;uCAEe", "debugId": null}}, {"offset": {"line": 8979, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/index.tsx"], "sourcesContent": ["import type {\n  FC,\n  ReactNode,\n} from 'react'\nimport {\n  memo,\n  useCallback,\n  useEffect,\n  useMemo,\n  useRef,\n  useState,\n} from 'react'\nimport { useTranslation } from 'react-i18next'\nimport { cloneDeep, debounce } from 'lodash-es'\nimport { useShallow } from 'zustand/react/shallow'\nimport Image from 'next/image'\nimport type {\n  ChatConfig,\n  ChatItem,\n  Feedback,\n  OnRegenerate,\n  OnSend,\n} from '../types'\nimport type { ThemeBuilder } from '../embedded-chatbot/theme/theme-context'\nimport Question from './question'\nimport Answer from './answer'\nimport ChatInputArea from './chat-input-area'\nimport TryToAsk from './try-to-ask'\nimport { ChatContextProvider } from './context'\nimport type { InputForm } from './type'\nimport Toolbar, { LLM_DEFAULT_INTENT } from './toolbar'\nimport { INTENT_INCLUDES_MEETING, INTENT_INCLUDES_SCHEDULE, SHOW_SUGGESTION_INTENT } from './u-const'\nimport ModelSwitching from './model-switching'\nimport styles from './index.module.css'\nimport cn from '@/utils/classnames'\nimport type { Emoji } from '@/components/tools/types'\nimport Button from '@/components/base/button'\nimport { StopCircle } from '@/components/base/icons/src/vender/solid/mediaAndDevices'\nimport AgentLogModal from '@/components/base/agent-log-modal'\nimport PromptLogModal from '@/components/base/prompt-log-modal'\nimport { useStore as useAppStore } from '@/components/app/store'\nimport type { AppData, EmbedSource } from '@/models/share'\nimport LarkRobot from '@/assets/lark-app-robot.gif'\nimport IconSuggest from '@/assets/lark-suggest.svg'\nimport IconSuggestTitle from '@/assets/lark-suggest-title.svg'\nimport { fetchAiPptConfigApi, fetchAiPptCreatTaskApi, fetchAiPptResourcesSaveApi } from '@/service/share'\nimport McpTools from './mcp-tools'\n\nexport type ChatProps = {\n  appData?: AppData\n  chatList: ChatItem[]\n  config?: ChatConfig\n  isResponding?: boolean\n  noStopResponding?: boolean\n  onStopResponding?: () => void\n  noChatInput?: boolean\n  onSend?: OnSend\n  inputs?: Record<string, any>\n  inputsForm?: InputForm[]\n  onRegenerate?: OnRegenerate\n  chatContainerClassName?: string\n  chatContainerInnerClassName?: string\n  chatFooterClassName?: string\n  chatFooterInnerClassName?: string\n  suggestedQuestions?: string[]\n  showPromptLog?: boolean\n  questionIcon?: ReactNode\n  answerIcon?: ReactNode\n  allToolIcons?: Record<string, string | Emoji>\n  onAnnotationEdited?: (question: string, answer: string, index: number) => void\n  onAnnotationAdded?: (annotationId: string, authorName: string, question: string, answer: string, index: number) => void\n  onAnnotationRemoved?: (index: number) => void\n  chatNode?: ReactNode\n  onFeedback?: (messageId: string, feedback: Feedback) => void\n  chatAnswerContainerInner?: string\n  hideProcessDetail?: boolean\n  hideLogModal?: boolean\n  themeBuilder?: ThemeBuilder\n  switchSibling?: (siblingMessageId: string) => void\n  showFeatureBar?: boolean\n  showFileUpload?: boolean\n  onFeatureBarClick?: (state: boolean) => void\n  noSpacing?: boolean\n  inputDisabled?: boolean\n  isMobile?: boolean\n  sidebarCollapseState?: boolean\n  embedSource?: EmbedSource\n  larkInfo?: any\n  conversationId?: string\n  currentIntent?: { current: string }\n  updateIntent?: (intent: string, prompt?: string) => void\n  updateLlm?: (name: string, key: string) => void\n  updateIntentSilent: (intent: string) => void\n  updateLastChatIntent: (intent: string) => void\n}\n\nconst IconRefresh = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" className={className}>\n    <path d=\"M8.38191281,13.1111111 L4.33731165,13.1111111 L4.33731165,17 M11.6175937,6.88888889 L15.6621949,6.88888889 L15.6621949,3 M4,7.66931111 C4.4535454,6.58995778 5.21291927,5.65458667 6.19180173,4.96951222 C7.17068419,4.28443 8.33083759,3.87696 9.53850696,3.79373 C10.746144,3.7105 11.953053,3.95464444 13.023578,4.49847444 C14.094103,5.04230444 14.9836726,5.86419778 15.5927087,6.87031556 M16,12.3310778 C15.5464384,13.4104 14.7870241,14.3457556 13.8081497,15.0308222 C12.8292754,15.7159667 11.6703353,16.1229 10.4626983,16.2061222 C9.25506131,16.2893444 8.04706837,16.0452778 6.97657569,15.5013778 C5.90607492,14.9575556 5.01585012,14.1358333 4.40683025,13.1297\" stroke=\"#3B67FF\" stroke-width=\"1.6\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" fill-rule=\"evenodd\" />\n  </svg>\n)\n\nconst IconClose = ({ className }: { className: string }) => (\n  <svg className={className} viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"5061\" width=\"24\" height=\"24\"><path d=\"M572.267 512l225.867-225.867c16.533-16.533 16.533-43.733 0-60.267s-43.733-16.533-60.267 0L512 451.733 286.133 225.866c-16.533-16.533-43.733-16.533-60.267 0s-16.533 43.733 0 60.267L451.733 512 225.866 737.867c-16.533 16.533-16.533 43.733 0 60.267 8.267 8.267 19.2 12.533 30.133 12.533s21.867-4.267 30.133-12.533l225.867-225.867 225.867 225.867c8.267 8.267 19.2 12.533 30.133 12.533s21.867-4.267 30.133-12.533c16.533-16.533 16.533-43.733 0-60.267L572.265 512z\" fill=\"#707070\" p-id=\"5062\"></path></svg>\n)\nconst IconArrowDown = ({ className }: { className: string }) => (\n  <svg className={className} viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M758.976 305.28L534.208 531.712a32 32 0 0 1-45.376 0L264.576 306.624a28.992 28.992 0 0 1 0-41.152 28.992 28.992 0 0 1 41.152 0l205.824 205.824 206.272-206.72a28.992 28.992 0 0 1 41.152 0 29.248 29.248 0 0 1 0 40.704z m-41.152 207.616a28.992 28.992 0 0 1 41.152 0c0 0.448 0.448 0.448 0.448 0.896a28.992 28.992 0 0 1 0 41.152l-225.28 224.832a32 32 0 0 1-45.184 0L264.576 554.88a28.992 28.992 0 0 1 0-41.152 28.992 28.992 0 0 1 41.152 0l205.824 205.824 206.272-206.72z\" fill=\"#037DF3\" p-id=\"6599\"></path></svg>\n)\n\nconst Chat: FC<ChatProps> = ({\n  appData,\n  config,\n  onSend,\n  inputs,\n  inputsForm,\n  onRegenerate,\n  chatList,\n  isResponding,\n  noStopResponding,\n  onStopResponding,\n  noChatInput,\n  chatContainerClassName,\n  chatContainerInnerClassName,\n  chatFooterClassName,\n  chatFooterInnerClassName,\n  suggestedQuestions,\n  showPromptLog,\n  questionIcon,\n  answerIcon,\n  onAnnotationAdded,\n  onAnnotationEdited,\n  onAnnotationRemoved,\n  chatNode,\n  onFeedback,\n  chatAnswerContainerInner,\n  hideProcessDetail,\n  hideLogModal,\n  themeBuilder,\n  switchSibling,\n  showFeatureBar,\n  showFileUpload,\n  onFeatureBarClick,\n  noSpacing,\n  inputDisabled,\n  isMobile,\n  sidebarCollapseState,\n  embedSource,\n  larkInfo,\n  conversationId,\n  currentIntent,\n  updateIntent,\n  updateLlm,\n  updateIntentSilent,\n  updateLastChatIntent,\n}) => {\n  // 提问示例条数\n  const SUGGEST_MAX = isMobile ? 3 : 4\n  const { t } = useTranslation()\n  const { currentLogItem, setCurrentLogItem, showPromptLogModal, setShowPromptLogModal, showAgentLogModal, setShowAgentLogModal } = useAppStore(useShallow(state => ({\n    currentLogItem: state.currentLogItem,\n    setCurrentLogItem: state.setCurrentLogItem,\n    showPromptLogModal: state.showPromptLogModal,\n    setShowPromptLogModal: state.setShowPromptLogModal,\n    showAgentLogModal: state.showAgentLogModal,\n    setShowAgentLogModal: state.setShowAgentLogModal,\n  })))\n  const [width, setWidth] = useState(0)\n  const chatContainerRef = useRef<HTMLDivElement>(null)\n  const chatContainerInnerRef = useRef<HTMLDivElement>(null)\n  const chatFooterRef = useRef<HTMLDivElement>(null)\n  const chatFooterInnerRef = useRef<HTMLDivElement>(null)\n  const userScrolledRef = useRef(false)\n  const [selectLlm, setSelectLlm] = useState<any>()\n  const [refreshCount, setRefreshCount] = useState<number>(0)\n  const [randomSuggest, setRandomSuggest] = useState<any[]>([])\n  const [pptCloseBtnShow, setPptCloseBtnShow] = useState<boolean>(false)\n  const [pptCloseBtnCanClick, setPptCloseBtnCanClick] = useState<boolean>(true)\n  const [pptLoadingSate, setPptLoadingSate] = useState<boolean>(false)\n  const isEmbedMobile = embedSource && isMobile\n  const extensionData = useMemo(() => appData?.site?.extension_data ? JSON.parse(appData.site.extension_data) : {}, [appData])\n  // 根据selected过滤出已选数据 没有selected的是以前老数据 !Object.hasOwn(item, 'selected')判断\n  const aiToolbars = extensionData?.aiToolbars?.filter((item: any) => !Object.hasOwn(item, 'selected') || (Object.hasOwn(item, 'selected') && item?.selected))\n\n  // 新增状态，用于控制回到底部图标的显示和隐藏\n  const [showScrollToBottomIcon, setShowScrollToBottomIcon] = useState(false)\n\n  // 处理滚动事件，当向上滚动时显示图标\n  const handleScroll = useCallback(() => {\n    if (chatContainerRef.current) {\n      const { scrollTop, clientHeight, scrollHeight } = chatContainerRef.current\n      const distanceToBottom = scrollHeight - (scrollTop + clientHeight)\n      const shouldShow = distanceToBottom > 20 && userScrolledRef.current\n      setShowScrollToBottomIcon(prev => prev !== shouldShow ? shouldShow : prev)\n    }\n  }, [])\n\n  // 处理点击图标事件，回到最底部并隐藏图标\n  const handleScrollToBottomAndHideIcon = useCallback(() => {\n    if (chatContainerRef.current) {\n      const { scrollHeight } = chatContainerRef.current\n      // 仅当不在底部时执行滚动\n      chatContainerRef.current.scrollTo({\n        top: scrollHeight,\n        behavior: 'smooth',\n      })\n      setShowScrollToBottomIcon(false)\n      userScrolledRef.current = false\n    }\n  }, [])\n\n  const handleScrollToBottom = useCallback(() => {\n    if (chatList.length > 1 && chatContainerRef.current && !userScrolledRef.current)\n      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight\n  }, [chatList.length])\n\n  const handleWindowResize = useCallback(() => {\n    if (chatContainerRef.current)\n      setWidth(document.body.clientWidth - (chatContainerRef.current?.clientWidth + 16) - 8)\n\n    if (chatContainerRef.current && chatFooterRef.current)\n      chatFooterRef.current.style.width = `${chatContainerRef.current.clientWidth}px`\n\n    if (chatContainerInnerRef.current && chatFooterInnerRef.current)\n      chatFooterInnerRef.current.style.width = `${chatContainerInnerRef.current.clientWidth}px`\n  }, [])\n\n  const handleNewTopic = () => {\n    updateIntent?.('')\n  }\n  // 一键生成PPT\n  const handleOneKeyGenerationPpt = async (content: string, title: string, curChatId: string) => {\n    // onSend?.('确认大纲内容，直接生成', [])\n    if (pptLoadingSate)\n      return false\n    setPptLoadingSate(true)\n    const configRes: any = await fetchAiPptConfigApi()\n    const { api_key, channel, code } = configRes || { }\n    const res: any = await fetchAiPptCreatTaskApi({\n      type: 7,\n      title,\n      content,\n    })\n    const { id: taskId } = res?.data || {}\n    try {\n      await AipptIframe.show({\n        appkey: api_key,\n        channel,\n        code,\n        editorModel: true,\n        options: {\n          custom_generate: {\n            taskId,\n            step: 2,\n          },\n        },\n        async onMessage(eventType: any, data: any) {\n          // console.log(eventType, data, 'onMessage----add')\n          setPptCloseBtnCanClick(true)\n          if (eventType === 'START')\n            setPptCloseBtnCanClick(false)\n          if (['PPT_SAVE', 'GENERATE_PPT_SUCCESS'].includes(eventType)) {\n            setPptCloseBtnCanClick(true)\n            chatList.map((item) => {\n              if (item.id === curChatId)\n                Object.assign(item, { ppt: data })\n              return item\n            })\n            await fetchAiPptResourcesSaveApi(conversationId || '', { ...data, messageId: curChatId })\n          }\n        },\n      })\n      setPptCloseBtnShow(true)\n    }\n    catch (e) {\n      // console.log(e, 'catch exception')\n    }\n  }\n  const handleCloseAiPptFrame = () => {\n    if (pptCloseBtnCanClick) {\n      AipptIframe.deleteIframe()\n      setPptCloseBtnShow(false)\n      document.getElementById('aippt-iframe-modal')?.remove()\n    }\n    setPptLoadingSate(false)\n  }\n  const hanldePptEdit = async (ppt: any, curChatId: string) => {\n    const { id } = ppt\n    const configRes: any = await fetchAiPptConfigApi()\n    const { api_key, channel, code } = configRes || {}\n    try {\n      await AipptIframe.show({\n        appkey: api_key,\n        channel,\n        code,\n        editorModel: true,\n        routerOptions: {\n          list: ['workspace', 'generate', 'editor'],\n          // 存在editor 并且存在id的情况下 则直接跳转至编辑器页面\n          editor: {\n            showLogo: 3,\n            id,\n          },\n        },\n        async onMessage(eventType: any, data: any) {\n          // console.log(eventType, data, 'onMessage----edit')\n          setPptCloseBtnCanClick(true)\n          if (eventType === 'START')\n            setPptCloseBtnCanClick(false)\n          if (['PPT_SAVE', 'GENERATE_PPT_SUCCESS'].includes(eventType)) {\n            setPptCloseBtnCanClick(true)\n            chatList.map((item) => {\n              if (item.id === curChatId)\n                item.ppt = data\n              return item\n            })\n            await fetchAiPptResourcesSaveApi(conversationId || '', { ...data, messageId: curChatId })\n          }\n        },\n      })\n    }\n    catch (e) {\n      // console.log(e, 'catch exception')\n    }\n    setPptCloseBtnShow(true)\n  }\n\n  const handleChangeIntent = ({ label, prompt }: { label: string; prompt: string }) => {\n    updateIntent?.(label, prompt)\n    handleScrollToBottomAndHideIcon()\n  }\n\n  const handleChangeModel = (data: any) => {\n    // TODO: 临时方案，后续需要根据需求调整\n\n    // if (currentIntent.current === LLM_DEFAULT_INTENT.label)\n    //   updateLlm(data.label, data.value)\n    // else if (data && selectLlm) {\n    //   updateIntent(LLM_DEFAULT_INTENT.label, LLM_DEFAULT_INTENT.prompt)\n    // }\n\n    // 切换模型\n    updateLlm?.(data.label, data.value)\n    setSelectLlm(data)\n    handleScrollToBottomAndHideIcon()\n  }\n\n  const handleRefreshSuggest = () => {\n    const temp = []\n    const suggests = cloneDeep(config?.suggested_questions) || []\n\n    for (let i = 0; i < SUGGEST_MAX; i++) {\n      const randomIndex = Math.floor(Math.random() * suggests?.length)\n      temp.push(suggests[randomIndex])\n      suggests.splice(randomIndex, 1) // 从副本中移除已选中的元素\n    }\n\n    setRandomSuggest(temp)\n  }\n\n  const handleCreateSchedule = (data: any) => {\n    const scheduleOrMeeting = localStorage.getItem('scheduleOrMeeting')\n    const typeTit = scheduleOrMeeting === 'schedule' ? INTENT_INCLUDES_SCHEDULE : scheduleOrMeeting === 'meeting' ? INTENT_INCLUDES_MEETING : ''\n    onSend?.(`创建${typeTit}`, [], false, null, data)\n  }\n  const handleGenerationMeeting = (data: any) => {\n    onSend?.('同步预定腾讯会议', [], false, null, data)\n  }\n\n  // 意图识别选择意图\n  const handleSelectIntent = ({ intent, question }: { intent: string; question: string }) => {\n    // updateIntentSilent?.(intent)\n    const questionList = chatList.filter(item => !item.isAnswer)\n    const lastQuestion = questionList[questionList.length - 1]?.content\n    if (lastQuestion === question) {\n      updateLastChatIntent?.(intent)\n      onSend?.(question, [], false, null, { isIntentRecognition: true })\n    }\n    else {\n      return false\n    }\n  }\n\n  useEffect(() => {\n    handleScrollToBottom()\n    handleWindowResize()\n  }, [handleScrollToBottom, handleWindowResize])\n\n  useEffect(() => {\n    if (chatContainerRef.current) {\n      requestAnimationFrame(() => {\n        handleScrollToBottom()\n        handleWindowResize()\n      })\n    }\n  })\n\n  // 处理通过历史会话进入更新意图\n  useEffect(() => {\n    if (chatList?.length > 0) {\n      const chatLastObj = chatList[chatList.length - 1]\n      // chatLastObj?.intent && updateLastChatIntent?.(chatLastObj.intent)\n      chatLastObj?.intent && updateIntent?.(chatLastObj.intent)\n    }\n  }, [conversationId])\n\n  useEffect(() => {\n    window.addEventListener('resize', debounce(handleWindowResize))\n    return () => window.removeEventListener('resize', handleWindowResize)\n  }, [handleWindowResize])\n\n  useEffect(() => {\n    if (chatFooterRef.current && chatContainerRef.current) {\n      // container padding bottom\n      const resizeContainerObserver = new ResizeObserver((entries) => {\n        for (const entry of entries) {\n          const { blockSize } = entry.borderBoxSize[0]\n          chatContainerRef.current!.style.paddingBottom = `${blockSize + 20}px`\n          handleScrollToBottom()\n        }\n      })\n      resizeContainerObserver.observe(chatFooterRef.current)\n\n      // footer width\n      const resizeFooterObserver = new ResizeObserver((entries) => {\n        for (const entry of entries) {\n          const { inlineSize } = entry.borderBoxSize[0]\n          chatFooterRef.current!.style.width = `${inlineSize}px`\n        }\n      })\n      resizeFooterObserver.observe(chatContainerRef.current)\n\n      return () => {\n        resizeContainerObserver.disconnect()\n        resizeFooterObserver.disconnect()\n      }\n    }\n  }, [handleScrollToBottom])\n\n  useEffect(() => {\n    const chatContainer = chatContainerRef.current\n    if (chatContainer) {\n      const setUserScrolled = () => {\n        // eslint-disable-next-line sonarjs/no-gratuitous-expressions\n        if (chatContainer) // its in event callback, chatContainer may be null\n          userScrolledRef.current = chatContainer.scrollHeight - chatContainer.scrollTop >= chatContainer.clientHeight + 100\n      }\n      chatContainer.addEventListener('scroll', setUserScrolled)\n      return () => chatContainer.removeEventListener('scroll', setUserScrolled)\n    }\n  }, [])\n\n  useEffect(() => {\n    if (!sidebarCollapseState)\n      setTimeout(() => handleWindowResize(), 200)\n  }, [handleWindowResize, sidebarCollapseState])\n\n  useEffect(() => {\n    handleRefreshSuggest()\n  }, [config?.suggested_questions])\n\n  // 发送消息时滚至底部\n  useEffect(() => {\n    userScrolledRef.current = false\n    handleScrollToBottom()\n  }, [chatList.length])\n\n  useEffect(() => {\n    if (!chatList.length && embedSource && aiToolbars?.length)\n      updateIntent?.(aiToolbars[0].label, aiToolbars[0].prompt)\n  }, [chatList, embedSource, aiToolbars])\n\n  useEffect(() => {\n    const container = chatContainerRef.current\n    const debouncedScroll = debounce(handleScroll, 300)\n    if (container) {\n      container.addEventListener('scroll', debouncedScroll)\n      // 主动触发一次初始化\n      handleScroll()\n    }\n    return () => {\n      container?.removeEventListener('scroll', debouncedScroll)\n      debouncedScroll.cancel()\n    }\n  }, [handleScroll])\n\n  const hasTryToAsk = config?.suggested_questions_after_answer?.enabled && !!suggestedQuestions?.length && onSend\n\n  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}\n  const aiRobotGifUrl = chatPageConfigData?.aiRobotGifUrl || LarkRobot // AI机器人动画\n  const aiTopBgGradientFrom = chatPageConfigData?.aiTopBgGradientFrom || '#d7dafc'// AI顶部背景渐变色起始\n  const aiTopBgGradientTo = chatPageConfigData?.aiTopBgGradientTo || '#fafafd'// AI顶部背景渐变色结束\n  const aiBtmGradientFrom = chatPageConfigData?.aiBtmGradientFrom || '#d4ddfa' // AI底部背景渐变色起始\n  const aiBtmBgGradientTo = chatPageConfigData?.aiBtmBgGradientTo || '#fafafd' // AI底部背景渐变色结束\n\n  return (\n    <ChatContextProvider\n      config={config}\n      chatList={chatList}\n      isResponding={isResponding}\n      showPromptLog={showPromptLog}\n      questionIcon={questionIcon}\n      answerIcon={answerIcon}\n      onSend={onSend}\n      onRegenerate={onRegenerate}\n      onAnnotationAdded={onAnnotationAdded}\n      onAnnotationEdited={onAnnotationEdited}\n      onAnnotationRemoved={onAnnotationRemoved}\n      onFeedback={onFeedback}\n    >\n      {pptCloseBtnShow && <div className='absolute left-[60px] top-[40px] z-20 flex h-[40px] w-[40px] cursor-pointer items-center justify-center rounded-[50%] bg-white ' style={{ boxShadow: '0 0 16px 1px rgba(0,0,0,.12)' }} onClick={() => { handleCloseAiPptFrame() }}>\n        <IconClose className=\"h-[24px] w-[24px]\"/>\n      </div>}\n      <div className='relative h-full' style={{ backgroundImage: (embedSource && !isMobile) ? `linear-gradient(to bottom, ${aiTopBgGradientFrom}, ${aiTopBgGradientTo} 20%)` : '' }}>\n        <div\n          ref={chatContainerRef}\n          className={cn('relative h-full overflow-y-auto overflow-x-hidden', chatContainerClassName)}\n        >\n          {chatNode}\n          <div\n            ref={chatContainerInnerRef}\n            className={cn('w-full', !noSpacing && 'px-8', chatContainerInnerClassName, embedSource && 'mb-100px')}\n          >\n            {\n              embedSource && (\n                <div className=\"mb-[26px]\">\n                  {!isMobile && <h1\n                    className='flex items-center justify-center text-center text-[24px] font-semibold text-[#242933]'\n                    style={{ fontFamily: 'PingFangSC' }}\n                  >\n                    <Image width={80} height={80} src={aiRobotGifUrl} alt=\"\" />\n                      您好，欢迎使用<span className='lark-gradient-title  ml-[10px]'>{appData?.site.title}</span>\n                  </h1>}\n                  {config?.opening_statement && (\n                    <>\n                      <div\n                        className={\n                          `mb-[64px] ${isMobile && styles.mobilePromptBg} \n                            ${isMobile && '!mb-[0px] flex rounded-[12px] bg-[#fff] py-[16px] pr-[16px] text-[14px] !leading-[26px] !text-[#242933]'}\n                            ${config?.suggested_questions && isEmbedMobile && 'pb-[22px]'}\n                          `}\n                      >\n                        {isMobile && <Image src={aiRobotGifUrl} width={78} height={78} className='mx-auto h-[78px] w-[78px] shrink-0' alt=\"\" />}\n                        <p className={`mx-auto max-w-[640px] text-justify text-[12px] leading-[20px] text-[#5C6574] ${isMobile && 'text-[14px] leading-[26px] !text-[#242933]'}`}>\n                          {config?.opening_statement}\n                        </p>\n                      </div>\n\n                      {!chatList.length && config?.suggested_questions && (\n                        <div className={`${isMobile && 'rounded-[12px] bg-white p-[16px]'} ${isEmbedMobile && '-mt-[14px]'}`}>\n                          <div className=\"mb-[20px] flex justify-between text-[14px]\">\n                            <p className={`text-[#8C97A4] ${isMobile && 'flex items-center font-semibold !text-[#242933]'}`}>{isEmbedMobile && <Image src={IconSuggestTitle} className=\"mr-[8px] h-[20px] w-[20px]\" alt=\"\" />}您可以这样向我提问</p>\n                            {config?.suggested_questions.length > SUGGEST_MAX && (\n                              <p className='flex cursor-pointer items-center text-[#3B67FF]' onClick={handleRefreshSuggest} ><IconRefresh className={`mr-[6px] h-[20px] w-[20px] ${isMobile && '!h-[16px] !w-[16px]'}`} />换一批</p>\n                            )}\n                          </div>\n                          <ul className={`flex min-h-[110px] flex-wrap justify-between gap-y-[16px] ${isMobile && 'flex-col'}`}>\n                            {\n                              randomSuggest?.map(question => (\n                                <li\n                                  className={`flex min-h-[48px] shrink-0 cursor-pointer rounded-[8px] bg-[#E2E2FF] px-[20px] py-[14px] text-[14px] text-[#242933] last:mr-0 ${isMobile && 'bg-[#f8f8f9] p-[14px]'}`}\n                                  style={{ width: isMobile ? '100%' : 'calc(50% - 12px)' }}\n                                  key={question}\n                                  onClick={() => onSend?.(question)}\n                                >\n                                  <Image src={IconSuggest} className=\"mr-[8px] h-[20px] w-[20px]\" alt=\"\" />\n                                  {question}\n                                </li>\n                              ))\n                            }\n                          </ul>\n                        </div>\n                      )}\n                    </>\n                  )}\n                </div>\n              )\n            }\n            {\n              chatList.filter(item => !item.isHide).map((item, index) => {\n                if (item.isAnswer) {\n                  const isLast = item.id === chatList[chatList.length - 1]?.id\n                  return (\n                    <Answer\n                      appData={appData}\n                      key={item.id}\n                      item={item}\n                      ppt={item?.ppt || {}}\n                      question={chatList[index - 1]?.content}\n                      index={index}\n                      config={config}\n                      answerIcon={answerIcon}\n                      responding={isLast && isResponding}\n                      showPromptLog={showPromptLog}\n                      chatAnswerContainerInner={chatAnswerContainerInner}\n                      hideProcessDetail={hideProcessDetail}\n                      noChatInput={noChatInput}\n                      switchSibling={switchSibling}\n                      embedSource={embedSource}\n                      isLast={isLast}\n                      isMobile={isMobile}\n                      onNewTopic={handleNewTopic}\n                      onOneKeyGenerationPpt={() => { handleOneKeyGenerationPpt(item.content, chatList[index - 1]?.content, item.id) }}\n                      onEditClick={() => { hanldePptEdit(item?.ppt, item.id) }}\n                      onCreateSchedule={handleCreateSchedule}\n                      onOneKeyGenerationMeeting={handleGenerationMeeting}\n                      hideOperator={!SHOW_SUGGESTION_INTENT.includes(currentIntent?.current || '')}\n                      updateIntent={updateIntent}\n                      onSelectIntent={handleSelectIntent}\n                      activeIntent={currentIntent?.current}\n                      generationPptLoading={pptLoadingSate}\n                    />\n                  )\n                }\n                return (\n                  <Question\n                    key={item.id}\n                    item={item}\n                    questionIcon={questionIcon}\n                    theme={themeBuilder?.theme}\n                    switchSibling={switchSibling}\n                    avatar={larkInfo?.avatar_url || larkInfo?.avatarUrl }\n                    userName={larkInfo?.name || larkInfo?.userName}\n                    embedSource={embedSource}\n                  />\n                )\n              })\n            }\n            {\n              hasTryToAsk && embedSource && SHOW_SUGGESTION_INTENT.includes(currentIntent?.current || '') && (\n                <TryToAsk\n                  embedSource={embedSource}\n                  isMobile={isMobile}\n                  suggestedQuestions={suggestedQuestions}\n                  onSend={onSend}\n                />\n              )\n            }\n          </div>\n        </div>\n        <div\n          className={`absolute bottom-0 flex justify-center bg-chat-input-mask ${(hasTryToAsk || !noChatInput || !noStopResponding) && chatFooterClassName} ${embedSource && !isMobile && 'px-[15.8vw]'}`}\n          ref={chatFooterRef}\n          style={{\n            background: embedSource ? (isMobile ? 'linear-gradient(0deg, #EEF3FF 50%, #f5f6f8 100%)' : `linear-gradient(0deg, ${aiBtmGradientFrom}, ${aiBtmBgGradientTo} 60%)`) : 'linear-gradient(0deg, #F9FAFB 40%, rgba(255, 255, 255, 0.00) 100%)',\n          }}\n        >\n          <div\n            ref={chatFooterInnerRef}\n            className={cn('relative', chatFooterInnerClassName)}\n          >\n            {\n              !noStopResponding && isResponding && (\n                <div className='mb-2 flex justify-center'>\n                  <Button onClick={onStopResponding}>\n                    <StopCircle className='mr-[5px] h-3.5 w-3.5 text-gray-500' />\n                    <span className='text-xs font-normal text-gray-500'>{t('appDebug.operation.stopResponding')}</span>\n                  </Button>\n                </div>\n              )\n            }\n            {\n              hasTryToAsk && !embedSource && (\n                <TryToAsk\n                  embedSource={embedSource}\n                  suggestedQuestions={suggestedQuestions}\n                  onSend={onSend}\n                  isMobile={isMobile}\n                />\n              )\n            }\n            {\n              !noChatInput && (\n                <div className={embedSource ? 'relative' : 'relative mx-auto w-full max-w-[720px]' } >\n                  {embedSource && (<Toolbar active={currentIntent?.current} isMobile={isMobile} onClick={handleChangeIntent} />)}\n                  <div className={`${isMobile && 'flex items-center gap-[10px]'} relative ${isMobile && embedSource && 'mb-10px'}`}>\n                    {showScrollToBottomIcon && (\n                      <div\n                        className={'absolute bottom-[245px] right-[0px] z-[16] flex h-[40px] w-[40px] cursor-pointer items-center justify-center rounded-[50%] bg-white'}\n                        style={{ boxShadow: '0 0 16px 1px rgba(0,0,0,.12)' }}\n                        onClick={handleScrollToBottomAndHideIcon}\n                      >\n                        <IconArrowDown className=\"h-[24px] w-[24px]\"/>\n                      </div>)}\n                    <ChatInputArea\n                      disabled={inputDisabled}\n                      showFeatureBar={showFeatureBar}\n                      showFileUpload={showFileUpload}\n                      featureBarDisabled={isResponding}\n                      onFeatureBarClick={onFeatureBarClick}\n                      visionConfig={config?.file_upload}\n                      speechToTextConfig={config?.speech_to_text}\n                      onSend={onSend}\n                      inputs={inputs}\n                      inputsForm={inputsForm}\n                      theme={themeBuilder?.theme}\n                      isResponding={isResponding}\n                      embedSource={embedSource}\n                      isMobile={isMobile}\n                      intent={currentIntent?.current}\n                      footer={\n                        embedSource\n                        ? <div className={(embedSource && !isMobile) ? 'absolute bottom-[10px] left-[4px] z-[11] flex h-[32px] gap-[10px]' : 'flex gap-[10px]'}>\n                            <ModelSwitching models={extensionData.chatModels || []} showBuiltIn={Boolean(currentIntent?.current && (LLM_DEFAULT_INTENT.label !== currentIntent.current))} isMobile={isMobile} onClickActive={handleChangeModel} />\n                            {LLM_DEFAULT_INTENT.label === currentIntent?.current && <McpTools isMobile={Boolean(isMobile)}/>}\n                        </div>\n                        : null\n                      }\n                    />\n                  </div>\n                  {appData?.site?.custom_disclaimer && <div className='mt-[16px] text-center text-xs text-gray-500'>\n                    {appData.site.custom_disclaimer}\n                  </div>}\n                </div>\n              )\n            }\n          </div>\n        </div>\n        {showPromptLogModal && !hideLogModal && (\n          <PromptLogModal\n            width={width}\n            currentLogItem={currentLogItem}\n            onCancel={() => {\n              setCurrentLogItem()\n              setShowPromptLogModal(false)\n            }}\n          />\n        )}\n        {showAgentLogModal && !hideLogModal && (\n          <AgentLogModal\n            width={width}\n            currentLogItem={currentLogItem}\n            onCancel={() => {\n              setCurrentLogItem()\n              setShowAgentLogModal(false)\n            }}\n          />\n        )}\n      </div>\n    </ChatContextProvider>\n  )\n}\n\nexport default memo(Chat)\n"], "names": [], "mappings": ";;;;AAIA;;;;;;;;;;;AAUA;AACA;AASA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA,MAAM,cAAc,CAAC,EAAE,SAAS,EAAyB,iBACvD,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAK,GAAE;YAAqpB,QAAO;YAAU,gBAAa;YAAM,kBAAe;YAAQ,mBAAgB;YAAQ,MAAK;YAAO,aAAU;;;;;;;;;;;AAI1wB,MAAM,YAAY,CAAC,EAAE,SAAS,EAAyB,iBACrD,qZAAC;QAAI,WAAW;QAAW,SAAQ;QAAgB,OAAM;QAA6B,QAAK;QAAO,OAAM;QAAK,QAAO;kBAAK,cAAA,qZAAC;YAAK,GAAE;YAA4c,MAAK;YAAU,QAAK;;;;;;;;;;;AAEnmB,MAAM,gBAAgB,CAAC,EAAE,SAAS,EAAyB,iBACzD,qZAAC;QAAI,WAAW;QAAW,SAAQ;QAAgB,SAAQ;QAAM,OAAM;kBAA6B,cAAA,qZAAC;YAAK,GAAE;YAAod,MAAK;YAAU,QAAK;;;;;;;;;;;AAGtlB,MAAM,OAAsB,CAAC,EAC3B,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,EAChB,WAAW,EACX,sBAAsB,EACtB,2BAA2B,EAC3B,mBAAmB,EACnB,wBAAwB,EACxB,kBAAkB,EAClB,aAAa,EACb,YAAY,EACZ,UAAU,EACV,iBAAiB,EACjB,kBAAkB,EAClB,mBAAmB,EACnB,QAAQ,EACR,UAAU,EACV,wBAAwB,EACxB,iBAAiB,EACjB,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,cAAc,EACd,cAAc,EACd,iBAAiB,EACjB,SAAS,EACT,aAAa,EACb,QAAQ,EACR,oBAAoB,EACpB,WAAW,EACX,QAAQ,EACR,cAAc,EACd,aAAa,EACb,YAAY,EACZ,SAAS,EACT,kBAAkB,EAClB,oBAAoB,EACrB;IACC,SAAS;IACT,MAAM,cAAc,WAAW,IAAI;IACnC,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,EAAE,cAAc,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,GAAG,YAAY,CAAA,GAAA,6VAAA,CAAA,aAAU,AAAD,EAAE,CAAA,QAAS,CAAC;YACjK,gBAAgB,MAAM,cAAc;YACpC,mBAAmB,MAAM,iBAAiB;YAC1C,oBAAoB,MAAM,kBAAkB;YAC5C,uBAAuB,MAAM,qBAAqB;YAClD,mBAAmB,MAAM,iBAAiB;YAC1C,sBAAsB,MAAM,oBAAoB;QAClD,CAAC;IACD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,mBAAmB,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAkB;IAChD,MAAM,wBAAwB,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAkB;IACrD,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAkB;IAC7C,MAAM,qBAAqB,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAkB;IAClD,MAAM,kBAAkB,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAW;IACxE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,gBAAgB,eAAe;IACrC,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE,IAAM,SAAS,MAAM,iBAAiB,KAAK,KAAK,CAAC,QAAQ,IAAI,CAAC,cAAc,IAAI,CAAC,GAAG;QAAC;KAAQ;IAC3H,yEAAyE;IACzE,MAAM,aAAa,eAAe,YAAY,OAAO,CAAC,OAAc,CAAC,OAAO,MAAM,CAAC,MAAM,eAAgB,OAAO,MAAM,CAAC,MAAM,eAAe,MAAM;IAElJ,wBAAwB;IACxB,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,oBAAoB;IACpB,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,iBAAiB,OAAO,EAAE;YAC5B,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,iBAAiB,OAAO;YAC1E,MAAM,mBAAmB,eAAe,CAAC,YAAY,YAAY;YACjE,MAAM,aAAa,mBAAmB,MAAM,gBAAgB,OAAO;YACnE,0BAA0B,CAAA,OAAQ,SAAS,aAAa,aAAa;QACvE;IACF,GAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,kCAAkC,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QAClD,IAAI,iBAAiB,OAAO,EAAE;YAC5B,MAAM,EAAE,YAAY,EAAE,GAAG,iBAAiB,OAAO;YACjD,cAAc;YACd,iBAAiB,OAAO,CAAC,QAAQ,CAAC;gBAChC,KAAK;gBACL,UAAU;YACZ;YACA,0BAA0B;YAC1B,gBAAgB,OAAO,GAAG;QAC5B;IACF,GAAG,EAAE;IAEL,MAAM,uBAAuB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI,SAAS,MAAM,GAAG,KAAK,iBAAiB,OAAO,IAAI,CAAC,gBAAgB,OAAO,EAC7E,iBAAiB,OAAO,CAAC,SAAS,GAAG,iBAAiB,OAAO,CAAC,YAAY;IAC9E,GAAG;QAAC,SAAS,MAAM;KAAC;IAEpB,MAAM,qBAAqB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,iBAAiB,OAAO,EAC1B,SAAS,SAAS,IAAI,CAAC,WAAW,GAAG,CAAC,iBAAiB,OAAO,EAAE,cAAc,EAAE,IAAI;QAEtF,IAAI,iBAAiB,OAAO,IAAI,cAAc,OAAO,EACnD,cAAc,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,iBAAiB,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;QAEjF,IAAI,sBAAsB,OAAO,IAAI,mBAAmB,OAAO,EAC7D,mBAAmB,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,sBAAsB,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;IAC7F,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,eAAe;IACjB;IACA,UAAU;IACV,MAAM,4BAA4B,OAAO,SAAiB,OAAe;QACvE,8BAA8B;QAC9B,IAAI,gBACF,OAAO;QACT,kBAAkB;QAClB,MAAM,YAAiB,MAAM;QAC7B,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,aAAa,CAAE;QAClD,MAAM,MAAW,MAAM,uBAAuB;YAC5C,MAAM;YACN;YACA;QACF;QACA,MAAM,EAAE,IAAI,MAAM,EAAE,GAAG,KAAK,QAAQ,CAAC;QACrC,IAAI;YACF,MAAM,YAAY,IAAI,CAAC;gBACrB,QAAQ;gBACR;gBACA;gBACA,aAAa;gBACb,SAAS;oBACP,iBAAiB;wBACf;wBACA,MAAM;oBACR;gBACF;gBACA,MAAM,WAAU,SAAc,EAAE,IAAS;oBACvC,mDAAmD;oBACnD,uBAAuB;oBACvB,IAAI,cAAc,SAChB,uBAAuB;oBACzB,IAAI;wBAAC;wBAAY;qBAAuB,CAAC,QAAQ,CAAC,YAAY;wBAC5D,uBAAuB;wBACvB,SAAS,GAAG,CAAC,CAAC;4BACZ,IAAI,KAAK,EAAE,KAAK,WACd,OAAO,MAAM,CAAC,MAAM;gCAAE,KAAK;4BAAK;4BAClC,OAAO;wBACT;wBACA,MAAM,2BAA2B,kBAAkB,IAAI;4BAAE,GAAG,IAAI;4BAAE,WAAW;wBAAU;oBACzF;gBACF;YACF;YACA,mBAAmB;QACrB,EACA,OAAO,GAAG;QACR,oCAAoC;QACtC;IACF;IACA,MAAM,wBAAwB;QAC5B,IAAI,qBAAqB;YACvB,YAAY,YAAY;YACxB,mBAAmB;YACnB,SAAS,cAAc,CAAC,uBAAuB;QACjD;QACA,kBAAkB;IACpB;IACA,MAAM,gBAAgB,OAAO,KAAU;QACrC,MAAM,EAAE,EAAE,EAAE,GAAG;QACf,MAAM,YAAiB,MAAM;QAC7B,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC;QACjD,IAAI;YACF,MAAM,YAAY,IAAI,CAAC;gBACrB,QAAQ;gBACR;gBACA;gBACA,aAAa;gBACb,eAAe;oBACb,MAAM;wBAAC;wBAAa;wBAAY;qBAAS;oBACzC,kCAAkC;oBAClC,QAAQ;wBACN,UAAU;wBACV;oBACF;gBACF;gBACA,MAAM,WAAU,SAAc,EAAE,IAAS;oBACvC,oDAAoD;oBACpD,uBAAuB;oBACvB,IAAI,cAAc,SAChB,uBAAuB;oBACzB,IAAI;wBAAC;wBAAY;qBAAuB,CAAC,QAAQ,CAAC,YAAY;wBAC5D,uBAAuB;wBACvB,SAAS,GAAG,CAAC,CAAC;4BACZ,IAAI,KAAK,EAAE,KAAK,WACd,KAAK,GAAG,GAAG;4BACb,OAAO;wBACT;wBACA,MAAM,2BAA2B,kBAAkB,IAAI;4BAAE,GAAG,IAAI;4BAAE,WAAW;wBAAU;oBACzF;gBACF;YACF;QACF,EACA,OAAO,GAAG;QACR,oCAAoC;QACtC;QACA,mBAAmB;IACrB;IAEA,MAAM,qBAAqB,CAAC,EAAE,KAAK,EAAE,MAAM,EAAqC;QAC9E,eAAe,OAAO;QACtB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,wBAAwB;QAExB,0DAA0D;QAC1D,sCAAsC;QACtC,gCAAgC;QAChC,sEAAsE;QACtE,IAAI;QAEJ,OAAO;QACP,YAAY,KAAK,KAAK,EAAE,KAAK,KAAK;QAClC,aAAa;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,OAAO,EAAE;QACf,MAAM,WAAW,UAAU,QAAQ,wBAAwB,EAAE;QAE7D,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;YACpC,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU;YACzD,KAAK,IAAI,CAAC,QAAQ,CAAC,YAAY;YAC/B,SAAS,MAAM,CAAC,aAAa,GAAG,eAAe;;QACjD;QAEA,iBAAiB;IACnB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,oBAAoB,aAAa,OAAO,CAAC;QAC/C,MAAM,UAAU,sBAAsB,aAAa,wJAAA,CAAA,2BAAwB,GAAG,sBAAsB,YAAY,wJAAA,CAAA,0BAAuB,GAAG;QAC1I,SAAS,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,MAAM;IAC5C;IACA,MAAM,0BAA0B,CAAC;QAC/B,SAAS,YAAY,EAAE,EAAE,OAAO,MAAM;IACxC;IAEA,WAAW;IACX,MAAM,qBAAqB,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAwC;QACpF,+BAA+B;QAC/B,MAAM,eAAe,SAAS,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;QAC3D,MAAM,eAAe,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,EAAE;QAC5D,IAAI,iBAAiB,UAAU;YAC7B,uBAAuB;YACvB,SAAS,UAAU,EAAE,EAAE,OAAO,MAAM;gBAAE,qBAAqB;YAAK;QAClE,OACK;YACH,OAAO;QACT;IACF;IAEA,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG;QAAC;QAAsB;KAAmB;IAE7C,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,OAAO,EAAE;YAC5B,sBAAsB;gBACpB;gBACA;YACF;QACF;IACF;IAEA,iBAAiB;IACjB,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,SAAS,GAAG;YACxB,MAAM,cAAc,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;YACjD,oEAAoE;YACpE,aAAa,UAAU,eAAe,YAAY,MAAM;QAC1D;IACF,GAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO,gBAAgB,CAAC,UAAU,SAAS;QAC3C,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAmB;IAEvB,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,OAAO,IAAI,iBAAiB,OAAO,EAAE;YACrD,2BAA2B;YAC3B,MAAM,0BAA0B,IAAI,eAAe,CAAC;gBAClD,KAAK,MAAM,SAAS,QAAS;oBAC3B,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,aAAa,CAAC,EAAE;oBAC5C,iBAAiB,OAAO,CAAE,KAAK,CAAC,aAAa,GAAG,GAAG,YAAY,GAAG,EAAE,CAAC;oBACrE;gBACF;YACF;YACA,wBAAwB,OAAO,CAAC,cAAc,OAAO;YAErD,eAAe;YACf,MAAM,uBAAuB,IAAI,eAAe,CAAC;gBAC/C,KAAK,MAAM,SAAS,QAAS;oBAC3B,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,aAAa,CAAC,EAAE;oBAC7C,cAAc,OAAO,CAAE,KAAK,CAAC,KAAK,GAAG,GAAG,WAAW,EAAE,CAAC;gBACxD;YACF;YACA,qBAAqB,OAAO,CAAC,iBAAiB,OAAO;YAErD,OAAO;gBACL,wBAAwB,UAAU;gBAClC,qBAAqB,UAAU;YACjC;QACF;IACF,GAAG;QAAC;KAAqB;IAEzB,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,iBAAiB,OAAO;QAC9C,IAAI,eAAe;YACjB,MAAM,kBAAkB;gBACtB,6DAA6D;gBAC7D,IAAI,eACF,gBAAgB,OAAO,GAAG,cAAc,YAAY,GAAG,cAAc,SAAS,IAAI,cAAc,YAAY,GAAG;YACnH;YACA,cAAc,gBAAgB,CAAC,UAAU;YACzC,OAAO,IAAM,cAAc,mBAAmB,CAAC,UAAU;QAC3D;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,sBACH,WAAW,IAAM,sBAAsB;IAC3C,GAAG;QAAC;QAAoB;KAAqB;IAE7C,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,QAAQ;KAAoB;IAEhC,YAAY;IACZ,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB,OAAO,GAAG;QAC1B;IACF,GAAG;QAAC,SAAS,MAAM;KAAC;IAEpB,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,MAAM,IAAI,eAAe,YAAY,QACjD,eAAe,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC,MAAM;IAC5D,GAAG;QAAC;QAAU;QAAa;KAAW;IAEtC,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,iBAAiB,OAAO;QAC1C,MAAM,kBAAkB,SAAS,cAAc;QAC/C,IAAI,WAAW;YACb,UAAU,gBAAgB,CAAC,UAAU;YACrC,YAAY;YACZ;QACF;QACA,OAAO;YACL,WAAW,oBAAoB,UAAU;YACzC,gBAAgB,MAAM;QACxB;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,cAAc,QAAQ,kCAAkC,WAAW,CAAC,CAAC,oBAAoB,UAAU;IAEzG,MAAM,qBAAqB,SAAS,MAAM,iBAAiB,KAAK,KAAK,CAAC,SAAS,MAAM,kBAAkB,CAAC;IACxG,MAAM,gBAAgB,oBAAoB,iBAAiB,UAAU,UAAU;;IAC/E,MAAM,sBAAsB,oBAAoB,uBAAuB,UAAS,cAAc;;IAC9F,MAAM,oBAAoB,oBAAoB,qBAAqB,UAAS,cAAc;;IAC1F,MAAM,oBAAoB,oBAAoB,qBAAqB,UAAU,cAAc;;IAC3F,MAAM,oBAAoB,oBAAoB,qBAAqB,UAAU,cAAc;;IAE3F,qBACE,qZAAC,qJAAA,CAAA,sBAAmB;QAClB,QAAQ;QACR,UAAU;QACV,cAAc;QACd,eAAe;QACf,cAAc;QACd,YAAY;QACZ,QAAQ;QACR,cAAc;QACd,mBAAmB;QACnB,oBAAoB;QACpB,qBAAqB;QACrB,YAAY;;YAEX,iCAAmB,qZAAC;gBAAI,WAAU;gBAAiI,OAAO;oBAAE,WAAW;gBAA+B;gBAAG,SAAS;oBAAQ;gBAAwB;0BACjQ,cAAA,qZAAC;oBAAU,WAAU;;;;;;;;;;;0BAEvB,qZAAC;gBAAI,WAAU;gBAAkB,OAAO;oBAAE,iBAAiB,AAAC,eAAe,CAAC,WAAY,CAAC,2BAA2B,EAAE,oBAAoB,EAAE,EAAE,kBAAkB,KAAK,CAAC,GAAG;gBAAG;;kCAC1K,qZAAC;wBACC,KAAK;wBACL,WAAW,GAAG,qDAAqD;;4BAElE;0CACD,qZAAC;gCACC,KAAK;gCACL,WAAW,GAAG,UAAU,CAAC,aAAa,QAAQ,6BAA6B,eAAe;;oCAGxF,6BACE,qZAAC;wCAAI,WAAU;;4CACZ,CAAC,0BAAY,qZAAC;gDACb,WAAU;gDACV,OAAO;oDAAE,YAAY;gDAAa;;kEAElC,qZAAC,oSAAA,CAAA,UAAK;wDAAC,OAAO;wDAAI,QAAQ;wDAAI,KAAK;wDAAe,KAAI;;;;;;oDAAK;kEAClD,qZAAC;wDAAK,WAAU;kEAAkC,SAAS,KAAK;;;;;;;;;;;;4CAE1E,QAAQ,mCACP;;kEACE,qZAAC;wDACC,WACE,CAAC,UAAU,EAAE,YAAY,8JAAA,CAAA,UAAM,CAAC,cAAc,CAAC;4BAC7C,EAAE,YAAY,0GAA0G;4BACxH,EAAE,QAAQ,uBAAuB,iBAAiB,YAAY;0BAChE,CAAC;;4DAEF,0BAAY,qZAAC,oSAAA,CAAA,UAAK;gEAAC,KAAK;gEAAe,OAAO;gEAAI,QAAQ;gEAAI,WAAU;gEAAqC,KAAI;;;;;;0EAClH,qZAAC;gEAAE,WAAW,CAAC,6EAA6E,EAAE,YAAY,8CAA8C;0EACrJ,QAAQ;;;;;;;;;;;;oDAIZ,CAAC,SAAS,MAAM,IAAI,QAAQ,qCAC3B,qZAAC;wDAAI,WAAW,GAAG,YAAY,mCAAmC,CAAC,EAAE,iBAAiB,cAAc;;0EAClG,qZAAC;gEAAI,WAAU;;kFACb,qZAAC;wEAAE,WAAW,CAAC,eAAe,EAAE,YAAY,mDAAmD;;4EAAG,+BAAiB,qZAAC,oSAAA,CAAA,UAAK;gFAAC,KAAK;gFAAkB,WAAU;gFAA6B,KAAI;;;;;;4EAAM;;;;;;;oEACjM,QAAQ,oBAAoB,SAAS,6BACpC,qZAAC;wEAAE,WAAU;wEAAkD,SAAS;;0FAAuB,qZAAC;gFAAY,WAAW,CAAC,2BAA2B,EAAE,YAAY,uBAAuB;;;;;;4EAAI;;;;;;;;;;;;;0EAGhM,qZAAC;gEAAG,WAAW,CAAC,0DAA0D,EAAE,YAAY,YAAY;0EAEhG,eAAe,IAAI,CAAA,yBACjB,qZAAC;wEACC,WAAW,CAAC,8HAA8H,EAAE,YAAY,yBAAyB;wEACjL,OAAO;4EAAE,OAAO,WAAW,SAAS;wEAAmB;wEAEvD,SAAS,IAAM,SAAS;;0FAExB,qZAAC,oSAAA,CAAA,UAAK;gFAAC,KAAK;gFAAa,WAAU;gFAA6B,KAAI;;;;;;4EACnE;;uEAJI;;;;;;;;;;;;;;;;;;;;;;;;oCAiBzB,SAAS,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM;wCAC/C,IAAI,KAAK,QAAQ,EAAE;4CACjB,MAAM,SAAS,KAAK,EAAE,KAAK,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,EAAE;4CAC1D,qBACE,qZAAC,6JAAA,CAAA,UAAM;gDACL,SAAS;gDAET,MAAM;gDACN,KAAK,MAAM,OAAO,CAAC;gDACnB,UAAU,QAAQ,CAAC,QAAQ,EAAE,EAAE;gDAC/B,OAAO;gDACP,QAAQ;gDACR,YAAY;gDACZ,YAAY,UAAU;gDACtB,eAAe;gDACf,0BAA0B;gDAC1B,mBAAmB;gDACnB,aAAa;gDACb,eAAe;gDACf,aAAa;gDACb,QAAQ;gDACR,UAAU;gDACV,YAAY;gDACZ,uBAAuB;oDAAQ,0BAA0B,KAAK,OAAO,EAAE,QAAQ,CAAC,QAAQ,EAAE,EAAE,SAAS,KAAK,EAAE;gDAAE;gDAC9G,aAAa;oDAAQ,cAAc,MAAM,KAAK,KAAK,EAAE;gDAAE;gDACvD,kBAAkB;gDAClB,2BAA2B;gDAC3B,cAAc,CAAC,wJAAA,CAAA,yBAAsB,CAAC,QAAQ,CAAC,eAAe,WAAW;gDACzE,cAAc;gDACd,gBAAgB;gDAChB,cAAc,eAAe;gDAC7B,sBAAsB;+CAzBjB,KAAK,EAAE;;;;;wCA4BlB;wCACA,qBACE,qZAAC,sJAAA,CAAA,UAAQ;4CAEP,MAAM;4CACN,cAAc;4CACd,OAAO,cAAc;4CACrB,eAAe;4CACf,QAAQ,UAAU,cAAc,UAAU;4CAC1C,UAAU,UAAU,QAAQ,UAAU;4CACtC,aAAa;2CAPR,KAAK,EAAE;;;;;oCAUlB;oCAGA,eAAe,eAAe,wJAAA,CAAA,yBAAsB,CAAC,QAAQ,CAAC,eAAe,WAAW,qBACtF,qZAAC,8JAAA,CAAA,UAAQ;wCACP,aAAa;wCACb,UAAU;wCACV,oBAAoB;wCACpB,QAAQ;;;;;;;;;;;;;;;;;;kCAMlB,qZAAC;wBACC,WAAW,CAAC,yDAAyD,EAAE,CAAC,eAAe,CAAC,eAAe,CAAC,gBAAgB,KAAK,oBAAoB,CAAC,EAAE,eAAe,CAAC,YAAY,eAAe;wBAC/L,KAAK;wBACL,OAAO;4BACL,YAAY,cAAe,WAAW,qDAAqD,CAAC,sBAAsB,EAAE,kBAAkB,EAAE,EAAE,kBAAkB,KAAK,CAAC,GAAI;wBACxK;kCAEA,cAAA,qZAAC;4BACC,KAAK;4BACL,WAAW,GAAG,YAAY;;gCAGxB,CAAC,oBAAoB,8BACnB,qZAAC;oCAAI,WAAU;8CACb,cAAA,qZAAC;wCAAO,SAAS;;0DACf,qZAAC;gDAAW,WAAU;;;;;;0DACtB,qZAAC;gDAAK,WAAU;0DAAqC,EAAE;;;;;;;;;;;;;;;;;gCAM7D,eAAe,CAAC,6BACd,qZAAC,8JAAA,CAAA,UAAQ;oCACP,aAAa;oCACb,oBAAoB;oCACpB,QAAQ;oCACR,UAAU;;;;;;gCAKd,CAAC,6BACC,qZAAC;oCAAI,WAAW,cAAc,aAAa;;wCACxC,6BAAgB,qZAAC,8JAAA,CAAA,UAAO;4CAAC,QAAQ,eAAe;4CAAS,UAAU;4CAAU,SAAS;;;;;;sDACvF,qZAAC;4CAAI,WAAW,GAAG,YAAY,+BAA+B,UAAU,EAAE,YAAY,eAAe,WAAW;;gDAC7G,wCACC,qZAAC;oDACC,WAAW;oDACX,OAAO;wDAAE,WAAW;oDAA+B;oDACnD,SAAS;8DAET,cAAA,qZAAC;wDAAc,WAAU;;;;;;;;;;;8DAE7B,qZAAC,4KAAA,CAAA,UAAa;oDACZ,UAAU;oDACV,gBAAgB;oDAChB,gBAAgB;oDAChB,oBAAoB;oDACpB,mBAAmB;oDACnB,cAAc,QAAQ;oDACtB,oBAAoB,QAAQ;oDAC5B,QAAQ;oDACR,QAAQ;oDACR,YAAY;oDACZ,OAAO,cAAc;oDACrB,cAAc;oDACd,aAAa;oDACb,UAAU;oDACV,QAAQ,eAAe;oDACvB,QACE,4BACE,qZAAC;wDAAI,WAAW,AAAC,eAAe,CAAC,WAAY,sEAAsE;;0EACjH,qZAAC,yKAAA,CAAA,UAAc;gEAAC,QAAQ,cAAc,UAAU,IAAI,EAAE;gEAAE,aAAa,QAAQ,eAAe,WAAY,8JAAA,CAAA,qBAAkB,CAAC,KAAK,KAAK,cAAc,OAAO;gEAAI,UAAU;gEAAU,eAAe;;;;;;4DAChM,8JAAA,CAAA,qBAAkB,CAAC,KAAK,KAAK,eAAe,yBAAW,qZAAC,mKAAA,CAAA,UAAQ;gEAAC,UAAU,QAAQ;;;;;;;;;;;iEAEtF;;;;;;;;;;;;wCAIP,SAAS,MAAM,mCAAqB,qZAAC;4CAAI,WAAU;sDACjD,QAAQ,IAAI,CAAC,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;oBAO1C,sBAAsB,CAAC,8BACtB,qZAAC;wBACC,OAAO;wBACP,gBAAgB;wBAChB,UAAU;4BACR;4BACA,sBAAsB;wBACxB;;;;;;oBAGH,qBAAqB,CAAC,8BACrB,qZAAC;wBACC,OAAO;wBACP,gBAAgB;wBAChB,UAAU;4BACR;4BACA,qBAAqB;wBACvB;;;;;;;;;;;;;;;;;;AAMZ;qDAEe,CAAA,GAAA,4WAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 10003, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/utils.ts"], "sourcesContent": ["import { UUID_NIL } from './constants'\nimport type { IChatItem } from './chat/type'\nimport type { ChatItem, ChatItemInTree } from './types'\nimport { INTENT_INCLUDES_MEETING, INTENT_INCLUDES_SCHEDULE, NEED_CREATE_MEETING } from './chat/u-const'\nimport { extractJSONFromMarkdown } from './chat/utils'\nimport { getProcessedFilesFromResponse } from '@/components/base/file-uploader/utils'\n\nasync function decodeBase64AndDecompress(base64String: string) {\n  try {\n    const binaryString = atob(base64String)\n    const compressedUint8Array = Uint8Array.from(binaryString, char => char.charCodeAt(0))\n    const decompressedStream = new Response(compressedUint8Array).body?.pipeThrough(new DecompressionStream('gzip'))\n    const decompressedArrayBuffer = await new Response(decompressedStream).arrayBuffer()\n    return new TextDecoder().decode(decompressedArrayBuffer)\n  }\n  catch {\n    return undefined\n  }\n}\n\nasync function getProcessedInputsFromUrlParams(): Promise<Record<string, any>> {\n  const urlParams = new URLSearchParams(window.location.search)\n  const inputs: Record<string, any> = {}\n  const entriesArray = Array.from(urlParams.entries())\n  await Promise.all(\n    entriesArray.map(async ([key, value]) => {\n      if (!key.startsWith('sys.'))\n        inputs[key] = await decodeBase64AndDecompress(decodeURIComponent(value))\n    }),\n  )\n  return inputs\n}\n\nasync function getProcessedSystemVariablesFromUrlParams(): Promise<Record<string, any>> {\n  const urlParams = new URLSearchParams(window.location.search)\n  const systemVariables: Record<string, any> = {}\n  const entriesArray = Array.from(urlParams.entries())\n  await Promise.all(\n    entriesArray.map(async ([key, value]) => {\n      if (key.startsWith('sys.'))\n        systemVariables[key.slice(4)] = await decodeBase64AndDecompress(decodeURIComponent(value))\n    }),\n  )\n  return systemVariables\n}\n\nfunction isValidGeneratedAnswer(item?: ChatItem | ChatItemInTree): boolean {\n  return !!item && item.isAnswer && !item.id.startsWith('answer-placeholder-') && !item.isOpeningStatement\n}\n\nfunction getLastAnswer<T extends ChatItem | ChatItemInTree>(chatList: T[]): T | null {\n  for (let i = chatList.length - 1; i >= 0; i--) {\n    const item = chatList[i]\n    if (isValidGeneratedAnswer(item))\n      return item\n  }\n  return null\n}\n\n/**\n * Build a chat item tree from a chat list\n * @param allMessages - The chat list, sorted from oldest to newest\n * @returns The chat item tree\n */\nfunction buildChatItemTree(allMessages: IChatItem[]): ChatItemInTree[] {\n  const map: Record<string, ChatItemInTree> = {}\n  const rootNodes: ChatItemInTree[] = []\n  const childrenCount: Record<string, number> = {}\n\n  let lastAppendedLegacyAnswer: ChatItemInTree | null = null\n  for (let i = 0; i < allMessages.length; i += 2) {\n    const question = allMessages[i]!\n    const answer = allMessages[i + 1]!\n\n    const isLegacy = question.parentMessageId === UUID_NIL\n    const parentMessageId = isLegacy\n      ? (lastAppendedLegacyAnswer?.id || '')\n      : (question.parentMessageId || '')\n\n    // Process question\n    childrenCount[parentMessageId] = (childrenCount[parentMessageId] || 0) + 1\n    const questionNode: ChatItemInTree = {\n      ...question,\n      children: [],\n    }\n    map[question.id] = questionNode\n\n    // Process answer\n    childrenCount[question.id] = 1\n    const answerNode: ChatItemInTree = {\n      ...answer,\n      children: [],\n      siblingIndex: isLegacy ? 0 : childrenCount[parentMessageId] - 1,\n    }\n    map[answer.id] = answerNode\n\n    // Connect question and answer\n    questionNode.children!.push(answerNode)\n\n    // Append to parent or add to root\n    if (isLegacy) {\n      if (!lastAppendedLegacyAnswer)\n        rootNodes.push(questionNode)\n      else\n        lastAppendedLegacyAnswer.children!.push(questionNode)\n\n      lastAppendedLegacyAnswer = answerNode\n    }\n    else {\n      if (\n        !parentMessageId\n        || !allMessages.some(item => item.id === parentMessageId) // parent message might not be fetched yet, in this case we will append the question to the root nodes\n      )\n        rootNodes.push(questionNode)\n      else\n        map[parentMessageId]?.children!.push(questionNode)\n    }\n  }\n\n  return rootNodes\n}\n\nfunction getThreadMessages(tree: ChatItemInTree[], targetMessageId?: string): ChatItemInTree[] {\n  let ret: ChatItemInTree[] = []\n  let targetNode: ChatItemInTree | undefined\n\n  // find path to the target message\n  const stack = tree.slice().reverse().map(rootNode => ({\n    node: rootNode,\n    path: [rootNode],\n  }))\n  while (stack.length > 0) {\n    const { node, path } = stack.pop()!\n    if (\n      node.id === targetMessageId\n      || (!targetMessageId && !node.children?.length && !stack.length) // if targetMessageId is not provided, we use the last message in the tree as the target\n    ) {\n      targetNode = node\n      ret = path.map((item, index) => {\n        if (!item.isAnswer)\n          return item\n\n        const parentAnswer = path[index - 2]\n        const siblingCount = !parentAnswer ? tree.length : parentAnswer.children!.length\n        const prevSibling = !parentAnswer ? tree[item.siblingIndex! - 1]?.children?.[0]?.id : parentAnswer.children![item.siblingIndex! - 1]?.children?.[0].id\n        const nextSibling = !parentAnswer ? tree[item.siblingIndex! + 1]?.children?.[0]?.id : parentAnswer.children![item.siblingIndex! + 1]?.children?.[0].id\n\n        return { ...item, siblingCount, prevSibling, nextSibling }\n      })\n      break\n    }\n    if (node.children) {\n      for (let i = node.children.length - 1; i >= 0; i--) {\n        stack.push({\n          node: node.children[i],\n          path: [...path, node.children[i]],\n        })\n      }\n    }\n  }\n\n  // append all descendant messages to the path\n  if (targetNode) {\n    const stack = [targetNode]\n    while (stack.length > 0) {\n      const node = stack.pop()!\n      if (node !== targetNode)\n        ret.push(node)\n      if (node.children?.length) {\n        const lastChild = node.children.at(-1)!\n\n        if (!lastChild.isAnswer) {\n          stack.push(lastChild)\n          continue\n        }\n\n        const parentAnswer = ret.at(-2)\n        const siblingCount = parentAnswer?.children?.length\n        const prevSibling = parentAnswer?.children?.at(-2)?.children?.[0]?.id\n\n        stack.push({ ...lastChild, siblingCount, prevSibling })\n      }\n    }\n  }\n\n  return ret\n}\n\nexport {\n  getProcessedInputsFromUrlParams,\n  getProcessedSystemVariablesFromUrlParams,\n  isValidGeneratedAnswer,\n  getLastAnswer,\n  buildChatItemTree,\n  getThreadMessages,\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAOA,eAAe,0BAA0B,YAAoB;IAC3D,IAAI;QACF,MAAM,eAAe,KAAK;QAC1B,MAAM,uBAAuB,WAAW,IAAI,CAAC,cAAc,CAAA,OAAQ,KAAK,UAAU,CAAC;QACnF,MAAM,qBAAqB,IAAI,SAAS,sBAAsB,IAAI,EAAE,YAAY,IAAI,oBAAoB;QACxG,MAAM,0BAA0B,MAAM,IAAI,SAAS,oBAAoB,WAAW;QAClF,OAAO,IAAI,cAAc,MAAM,CAAC;IAClC,EACA,OAAM;QACJ,OAAO;IACT;AACF;AAEA,eAAe;IACb,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;IAC5D,MAAM,SAA8B,CAAC;IACrC,MAAM,eAAe,MAAM,IAAI,CAAC,UAAU,OAAO;IACjD,MAAM,QAAQ,GAAG,CACf,aAAa,GAAG,CAAC,OAAO,CAAC,KAAK,MAAM;QAClC,IAAI,CAAC,IAAI,UAAU,CAAC,SAClB,MAAM,CAAC,IAAI,GAAG,MAAM,0BAA0B,mBAAmB;IACrE;IAEF,OAAO;AACT;AAEA,eAAe;IACb,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;IAC5D,MAAM,kBAAuC,CAAC;IAC9C,MAAM,eAAe,MAAM,IAAI,CAAC,UAAU,OAAO;IACjD,MAAM,QAAQ,GAAG,CACf,aAAa,GAAG,CAAC,OAAO,CAAC,KAAK,MAAM;QAClC,IAAI,IAAI,UAAU,CAAC,SACjB,eAAe,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,MAAM,0BAA0B,mBAAmB;IACvF;IAEF,OAAO;AACT;AAEA,SAAS,uBAAuB,IAAgC;IAC9D,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC,UAAU,CAAC,0BAA0B,CAAC,KAAK,kBAAkB;AAC1G;AAEA,SAAS,cAAmD,QAAa;IACvE,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC7C,MAAM,OAAO,QAAQ,CAAC,EAAE;QACxB,IAAI,uBAAuB,OACzB,OAAO;IACX;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,kBAAkB,WAAwB;IACjD,MAAM,MAAsC,CAAC;IAC7C,MAAM,YAA8B,EAAE;IACtC,MAAM,gBAAwC,CAAC;IAE/C,IAAI,2BAAkD;IACtD,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,KAAK,EAAG;QAC9C,MAAM,WAAW,WAAW,CAAC,EAAE;QAC/B,MAAM,SAAS,WAAW,CAAC,IAAI,EAAE;QAEjC,MAAM,WAAW,SAAS,eAAe,KAAK,8IAAA,CAAA,WAAQ;QACtD,MAAM,kBAAkB,WACnB,0BAA0B,MAAM,KAChC,SAAS,eAAe,IAAI;QAEjC,mBAAmB;QACnB,aAAa,CAAC,gBAAgB,GAAG,CAAC,aAAa,CAAC,gBAAgB,IAAI,CAAC,IAAI;QACzE,MAAM,eAA+B;YACnC,GAAG,QAAQ;YACX,UAAU,EAAE;QACd;QACA,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG;QAEnB,iBAAiB;QACjB,aAAa,CAAC,SAAS,EAAE,CAAC,GAAG;QAC7B,MAAM,aAA6B;YACjC,GAAG,MAAM;YACT,UAAU,EAAE;YACZ,cAAc,WAAW,IAAI,aAAa,CAAC,gBAAgB,GAAG;QAChE;QACA,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG;QAEjB,8BAA8B;QAC9B,aAAa,QAAQ,CAAE,IAAI,CAAC;QAE5B,kCAAkC;QAClC,IAAI,UAAU;YACZ,IAAI,CAAC,0BACH,UAAU,IAAI,CAAC;iBAEf,yBAAyB,QAAQ,CAAE,IAAI,CAAC;YAE1C,2BAA2B;QAC7B,OACK;YACH,IACE,CAAC,mBACE,CAAC,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,iBAAiB,sGAAsG;cAEhK,UAAU,IAAI,CAAC;iBAEf,GAAG,CAAC,gBAAgB,EAAE,SAAU,KAAK;QACzC;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBAAkB,IAAsB,EAAE,eAAwB;IACzE,IAAI,MAAwB,EAAE;IAC9B,IAAI;IAEJ,kCAAkC;IAClC,MAAM,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,GAAG,CAAC,CAAA,WAAY,CAAC;YACpD,MAAM;YACN,MAAM;gBAAC;aAAS;QAClB,CAAC;IACD,MAAO,MAAM,MAAM,GAAG,EAAG;QACvB,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,GAAG;QAChC,IACE,KAAK,EAAE,KAAK,mBACR,CAAC,mBAAmB,CAAC,KAAK,QAAQ,EAAE,UAAU,CAAC,MAAM,MAAM,CAAE,wFAAwF;UACzJ;YACA,aAAa;YACb,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM;gBACpB,IAAI,CAAC,KAAK,QAAQ,EAChB,OAAO;gBAET,MAAM,eAAe,IAAI,CAAC,QAAQ,EAAE;gBACpC,MAAM,eAAe,CAAC,eAAe,KAAK,MAAM,GAAG,aAAa,QAAQ,CAAE,MAAM;gBAChF,MAAM,cAAc,CAAC,eAAe,IAAI,CAAC,KAAK,YAAY,GAAI,EAAE,EAAE,UAAU,CAAC,EAAE,EAAE,KAAK,aAAa,QAAQ,AAAC,CAAC,KAAK,YAAY,GAAI,EAAE,EAAE,UAAU,CAAC,EAAE,CAAC;gBACpJ,MAAM,cAAc,CAAC,eAAe,IAAI,CAAC,KAAK,YAAY,GAAI,EAAE,EAAE,UAAU,CAAC,EAAE,EAAE,KAAK,aAAa,QAAQ,AAAC,CAAC,KAAK,YAAY,GAAI,EAAE,EAAE,UAAU,CAAC,EAAE,CAAC;gBAEpJ,OAAO;oBAAE,GAAG,IAAI;oBAAE;oBAAc;oBAAa;gBAAY;YAC3D;YACA;QACF;QACA,IAAI,KAAK,QAAQ,EAAE;YACjB,IAAK,IAAI,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;gBAClD,MAAM,IAAI,CAAC;oBACT,MAAM,KAAK,QAAQ,CAAC,EAAE;oBACtB,MAAM;2BAAI;wBAAM,KAAK,QAAQ,CAAC,EAAE;qBAAC;gBACnC;YACF;QACF;IACF;IAEA,6CAA6C;IAC7C,IAAI,YAAY;QACd,MAAM,QAAQ;YAAC;SAAW;QAC1B,MAAO,MAAM,MAAM,GAAG,EAAG;YACvB,MAAM,OAAO,MAAM,GAAG;YACtB,IAAI,SAAS,YACX,IAAI,IAAI,CAAC;YACX,IAAI,KAAK,QAAQ,EAAE,QAAQ;gBACzB,MAAM,YAAY,KAAK,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAEpC,IAAI,CAAC,UAAU,QAAQ,EAAE;oBACvB,MAAM,IAAI,CAAC;oBACX;gBACF;gBAEA,MAAM,eAAe,IAAI,EAAE,CAAC,CAAC;gBAC7B,MAAM,eAAe,cAAc,UAAU;gBAC7C,MAAM,cAAc,cAAc,UAAU,GAAG,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE;gBAEnE,MAAM,IAAI,CAAC;oBAAE,GAAG,SAAS;oBAAE;oBAAc;gBAAY;YACvD;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 10172, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/utils.ts"], "sourcesContent": ["import type { InputForm } from './type'\nimport { InputVarType } from '@/components/workflow/types'\nimport { getProcessedFiles } from '@/components/base/file-uploader/utils'\n\nexport const processOpeningStatement = (openingStatement: string, inputs: Record<string, any>, inputsForm: InputForm[]) => {\n  if (!openingStatement)\n    return openingStatement\n\n  return openingStatement.replace(/\\{\\{([^}]+)\\}\\}/g, (match, key) => {\n    const name = inputs[key]\n    if (name) { // has set value\n      return name\n    }\n\n    const valueObj = inputsForm.find(v => v.variable === key)\n    return valueObj ? `{{${valueObj.label}}}` : match\n  })\n}\n\nexport const processInputFileFromServer = (fileItem: Record<string, any>) => {\n  return {\n    type: fileItem.type,\n    transfer_method: fileItem.transfer_method,\n    url: fileItem.remote_url,\n    upload_file_id: fileItem.related_id,\n  }\n}\n\nexport const getProcessedInputs = (inputs: Record<string, any>, inputsForm: InputForm[]) => {\n  const processedInputs = { ...inputs }\n\n  inputsForm.forEach((item) => {\n    const inputValue = inputs[item.variable]\n    if (!inputValue)\n      return\n\n    if (item.type === InputVarType.singleFile) {\n      if ('transfer_method' in inputValue)\n        processedInputs[item.variable] = processInputFileFromServer(inputValue)\n      else\n        processedInputs[item.variable] = getProcessedFiles([inputValue])[0]\n    }\n    else if (item.type === InputVarType.multiFiles) {\n      if ('transfer_method' in inputValue[0])\n        processedInputs[item.variable] = inputValue.map(processInputFileFromServer)\n      else\n        processedInputs[item.variable] = getProcessedFiles(inputValue)\n    }\n  })\n\n  return processedInputs\n}\nexport const extractJSONFromMarkdown = (markdown: string) => {\n  // 匹配包含json代码块的正则表达式\n  const match = markdown.match(/```json\\n([\\s\\S]*?)\\n```/)\n  if (match && match[1]) {\n    try {\n      // 使用正则表达式去除不可打印的控制字符\n      // eslint-disable-next-line no-control-regex\n      const cleanJson = match[1].replace(/[\\x00-\\x1F\\x7F]/g, '')\n      return JSON.parse(cleanJson) || {}\n    }\n    catch (error) {\n      console.error('提取的JSON格式无效')\n    }\n  }\n  console.error('未找到JSON代码块')\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAIO,MAAM,0BAA0B,CAAC,kBAA0B,QAA6B;IAC7F,IAAI,CAAC,kBACH,OAAO;IAET,OAAO,iBAAiB,OAAO,CAAC,oBAAoB,CAAC,OAAO;QAC1D,MAAM,OAAO,MAAM,CAAC,IAAI;QACxB,IAAI,MAAM;YACR,OAAO;QACT;QAEA,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QACrD,OAAO,WAAW,CAAC,EAAE,EAAE,SAAS,KAAK,CAAC,EAAE,CAAC,GAAG;IAC9C;AACF;AAEO,MAAM,6BAA6B,CAAC;IACzC,OAAO;QACL,MAAM,SAAS,IAAI;QACnB,iBAAiB,SAAS,eAAe;QACzC,KAAK,SAAS,UAAU;QACxB,gBAAgB,SAAS,UAAU;IACrC;AACF;AAEO,MAAM,qBAAqB,CAAC,QAA6B;IAC9D,MAAM,kBAAkB;QAAE,GAAG,MAAM;IAAC;IAEpC,WAAW,OAAO,CAAC,CAAC;QAClB,MAAM,aAAa,MAAM,CAAC,KAAK,QAAQ,CAAC;QACxC,IAAI,CAAC,YACH;QAEF,IAAI,KAAK,IAAI,KAAK,aAAa,UAAU,EAAE;YACzC,IAAI,qBAAqB,YACvB,eAAe,CAAC,KAAK,QAAQ,CAAC,GAAG,2BAA2B;iBAE5D,eAAe,CAAC,KAAK,QAAQ,CAAC,GAAG,kBAAkB;gBAAC;aAAW,CAAC,CAAC,EAAE;QACvE,OACK,IAAI,KAAK,IAAI,KAAK,aAAa,UAAU,EAAE;YAC9C,IAAI,qBAAqB,UAAU,CAAC,EAAE,EACpC,eAAe,CAAC,KAAK,QAAQ,CAAC,GAAG,WAAW,GAAG,CAAC;iBAEhD,eAAe,CAAC,KAAK,QAAQ,CAAC,GAAG,kBAAkB;QACvD;IACF;IAEA,OAAO;AACT;AACO,MAAM,0BAA0B,CAAC;IACtC,oBAAoB;IACpB,MAAM,QAAQ,SAAS,KAAK,CAAC;IAC7B,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;QACrB,IAAI;YACF,qBAAqB;YACrB,4CAA4C;YAC5C,MAAM,YAAY,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,oBAAoB;YACvD,OAAO,KAAK,KAAK,CAAC,cAAc,CAAC;QACnC,EACA,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC;QAChB;IACF;IACA,QAAQ,KAAK,CAAC;AAChB", "debugId": null}}, {"offset": {"line": 10249, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat/hooks.ts"], "sourcesContent": ["import {\n  useCallback,\n  useEffect,\n  useMemo,\n  useRef,\n  useState,\n} from 'react'\nimport { useTranslation } from 'react-i18next'\nimport { produce, setAutoFreeze } from 'immer'\nimport { uniqBy } from 'lodash-es'\nimport { useParams, usePathname } from 'next/navigation'\nimport { v4 as uuidV4 } from 'uuid'\nimport type {\n  ChatConfig,\n  ChatItem,\n  ChatItemInTree,\n  Inputs,\n} from '../types'\nimport { getThreadMessages } from '../utils'\nimport type { InputForm } from './type'\nimport {\n  extractJSONFromMarkdown,\n  getProcessedInputs,\n  processOpeningStatement,\n} from './utils'\nimport { INTENT_INCLUDES_SCHEDULE, NEED_CREATE_MEETING } from './u-const'\nimport { TransferMethod } from '@/types/app'\nimport { useToastContext } from '@/components/base/toast'\nimport { ssePost } from '@/service/base'\nimport type { Annotation } from '@/models/log'\nimport { WorkflowRunningStatus } from '@/components/workflow/types'\nimport useTimestamp from '@/hooks/use-timestamp'\nimport { AudioPlayerManager } from '@/components/base/audio-btn/audio.player.manager'\nimport type { FileEntity } from '@/components/base/file-uploader/types'\nimport {\n  getProcessedFiles,\n  getProcessedFilesFromResponse,\n} from '@/components/base/file-uploader/utils'\nimport { noop } from 'lodash-es'\nimport { fetchLarkIntent } from '@/service/share'\nimport { DISPLAY_TYPE } from '../constants'\n\ntype GetAbortController = (abortController: AbortController) => void\ntype SendCallback = {\n  onGetConversationMessages?: (conversationId: string, getAbortController: GetAbortController) => Promise<any>\n  onGetSuggestedQuestions?: (responseItemId: string, getAbortController: GetAbortController) => Promise<any>\n  onConversationComplete?: (conversationId: string) => void\n  isPublicAPI?: boolean\n  isIntentRecognition?: boolean\n}\n\n// 场景切换对象\nconst createIntentObj = (intentInfo: { label: string, prompt: string }) => ({\n  id: `answer-placeholder-${URL.createObjectURL(new Blob([])).slice(-36)}`,\n  content: intentInfo?.prompt || '',\n  agent_thoughts: [],\n  message_files: [],\n  isAnswer: true,\n  intent: intentInfo?.label || '', // 意图\n  llm: '', // 大模型\n  frontTip: intentInfo?.label && `已进入[${intentInfo?.label}]场景`, // 前置提示\n  behindTip: '', // 后置提示\n  isPrompt: true,\n})\n\nexport const useChat = (\n  config?: ChatConfig,\n  formSettings?: {\n    inputs: Inputs\n    inputsForm: InputForm[]\n  },\n  prevChatTree?: ChatItemInTree[],\n  stopChat?: (taskId: string) => void,\n  clearChatList?: boolean,\n  clearChatListCallback?: (state: boolean) => void,\n  aiToolbars?: any[],\n  embedSource?: string,\n) => {\n  const { t } = useTranslation()\n  const { formatTime } = useTimestamp()\n  const { notify } = useToastContext()\n  const conversationId = useRef('')\n  const hasStopResponded = useRef(false)\n  const [isResponding, setIsResponding] = useState(false)\n  const isRespondingRef = useRef(false)\n  const taskIdRef = useRef('')\n  const [suggestedQuestions, setSuggestQuestions] = useState<string[]>([])\n  const conversationMessagesAbortControllerRef = useRef<AbortController | null>(null)\n  const suggestedQuestionsAbortControllerRef = useRef<AbortController | null>(null)\n  const params = useParams()\n  const pathname = usePathname()\n  const currentIntent = useRef('') // 当前意图\n  const currentLlm = useRef('') // 当前模型\n\n  const [chatTree, setChatTree] = useState<ChatItemInTree[]>(prevChatTree || [])\n  const chatTreeRef = useRef<ChatItemInTree[]>(chatTree)\n  const [targetMessageId, setTargetMessageId] = useState<string>()\n  const threadMessages = useMemo(() => getThreadMessages(chatTree, targetMessageId), [chatTree, targetMessageId])\n\n  const getIntroduction = useCallback((str: string) => {\n    return processOpeningStatement(str, formSettings?.inputs || {}, formSettings?.inputsForm || [])\n  }, [formSettings?.inputs, formSettings?.inputsForm])\n\n  /** Final chat list that will be rendered */\n  const chatList = useMemo(() => {\n    const ret = [...threadMessages]\n    if (config?.opening_statement && !config?.embedSource) {\n      const index = threadMessages.findIndex(item => item.isOpeningStatement)\n\n      if (index > -1) {\n        ret[index] = {\n          ...ret[index],\n          content: getIntroduction(config.opening_statement),\n          suggestedQuestions: config.suggested_questions,\n        }\n      }\n      else {\n        ret.unshift({\n          id: 'opening-statement',\n          content: getIntroduction(config.opening_statement),\n          isAnswer: true,\n          isOpeningStatement: true,\n          suggestedQuestions: config.suggested_questions,\n        })\n      }\n    }\n\n    if(config?.embedSource) {\n      // 新会话\n      if(currentIntent.current && !ret.length) {\n        const intentItem = aiToolbars?.find((item: any) => item.label === currentIntent.current)\n        ret.push(createIntentObj(intentItem))\n      }\n      else {\n        let newList: any[] = []\n\n        for(let i = 0; i < ret.length; i++) {\n          // 不同场景之间插入对象\n          if(ret[i]?.intent !== ret[i - 1]?.intent || !ret[i - 1]) {\n            const intentItem = aiToolbars?.find((item: any) => item.label === ret[i].intent)\n\n            newList = newList.concat(createIntentObj(intentItem), ret[i])\n          }\n          else {\n            newList.push(ret[i])\n          }\n\n          // 场景变化时，列表最后插入对象\n          if((i === ret.length - 1) && ret[i].intent && currentIntent.current && ret[i].intent !== currentIntent.current) {\n            const intentItem = aiToolbars?.find((item: any) => item.label === currentIntent.current)\n            newList.push(createIntentObj(intentItem))\n          }\n        }\n\n        return newList\n      }\n    }\n\n    return ret\n  }, [threadMessages, config?.opening_statement, getIntroduction, config?.suggested_questions, config?.embedSource])\n\n  useEffect(() => {\n    setAutoFreeze(false)\n    return () => {\n      setAutoFreeze(true)\n    }\n  }, [])\n\n  /** Find the target node by bfs and then operate on it */\n  const produceChatTreeNode = useCallback((targetId: string, operation: (node: ChatItemInTree) => void) => {\n    return produce(chatTreeRef.current, (draft) => {\n      const queue: ChatItemInTree[] = [...draft]\n      while (queue.length > 0) {\n        const current = queue.shift()!\n        if (current.id === targetId) {\n          operation(current)\n          break\n        }\n        if (current.children)\n          queue.push(...current.children)\n      }\n    })\n  }, [])\n\n  type UpdateChatTreeNode = {\n    (id: string, fields: Partial<ChatItemInTree>): void\n    (id: string, update: (node: ChatItemInTree) => void): void\n  }\n\n  const updateChatTreeNode: UpdateChatTreeNode = useCallback((\n    id: string,\n    fieldsOrUpdate: Partial<ChatItemInTree> | ((node: ChatItemInTree) => void),\n  ) => {\n    const nextState = produceChatTreeNode(id, (node) => {\n      if (typeof fieldsOrUpdate === 'function') {\n        fieldsOrUpdate(node)\n      }\n      else {\n        Object.keys(fieldsOrUpdate).forEach((key) => {\n          (node as any)[key] = (fieldsOrUpdate as any)[key]\n        })\n      }\n    })\n    setChatTree(nextState)\n    chatTreeRef.current = nextState\n  }, [produceChatTreeNode])\n\n  const handleResponding = useCallback((isResponding: boolean) => {\n    setIsResponding(isResponding)\n    isRespondingRef.current = isResponding\n  }, [])\n\n  const handleStop = useCallback(() => {\n    hasStopResponded.current = true\n    handleResponding(false)\n    if (stopChat && taskIdRef.current)\n      stopChat(taskIdRef.current)\n    if (conversationMessagesAbortControllerRef.current)\n      conversationMessagesAbortControllerRef.current.abort()\n    if (suggestedQuestionsAbortControllerRef.current)\n      suggestedQuestionsAbortControllerRef.current.abort()\n  }, [stopChat, handleResponding])\n\n  const handleRestart = useCallback((cb?: any) => {\n    conversationId.current = ''\n    taskIdRef.current = ''\n    currentIntent.current = ''\n    handleStop()\n    setChatTree([])\n    setSuggestQuestions([])\n    cb?.()\n  }, [handleStop])\n\n  const updateCurrentQAOnTree = useCallback(({\n    parentId,\n    responseItem,\n    placeholderQuestionId,\n    questionItem,\n  }: {\n    parentId?: string\n    responseItem: ChatItem\n    placeholderQuestionId: string\n    questionItem: ChatItem\n  }) => {\n    let nextState: ChatItemInTree[]\n    const currentQA = { ...questionItem, children: [{ ...responseItem, children: [] }] }\n    if (!parentId && !chatTree.some(item => [placeholderQuestionId, questionItem.id].includes(item.id))) {\n      // QA whose parent is not provided is considered as a first message of the conversation,\n      // and it should be a root node of the chat tree\n      nextState = produce(chatTree, (draft) => {\n        draft.push(currentQA)\n      })\n    }\n    else {\n      // find the target QA in the tree and update it; if not found, insert it to its parent node\n      nextState = produceChatTreeNode(parentId!, (parentNode) => {\n        const questionNodeIndex = parentNode.children!.findIndex(item => [placeholderQuestionId, questionItem.id].includes(item.id))\n        if (questionNodeIndex === -1)\n          parentNode.children!.push(currentQA)\n        else\n          parentNode.children![questionNodeIndex] = currentQA\n      })\n    }\n    setChatTree(nextState)\n    chatTreeRef.current = nextState\n  }, [chatTree, produceChatTreeNode])\n\n  // 识别意图\n  const identifyingIntent = async (query: string, oneIntentSend: (query: string) => void) => {\n    const questionId = `question-${Date.now()}`\n    const questionItem = {\n      id: questionId,\n      content: query,\n      isAnswer: false,\n      message_files: '',\n      isHide: false,\n    }\n\n    const placeholderAnswerId = `answer-placeholder-${Date.now()}`\n    const placeholderAnswerItem = {\n      id: placeholderAnswerId,\n      content: '',\n      isAnswer: true,\n    }\n\n    const newList = [...chatTreeRef.current, questionItem, placeholderAnswerItem]\n    setChatTree([...newList])\n    chatTreeRef.current = [...newList]\n\n    // answer\n    const responseItem: ChatItem = {\n      id: placeholderAnswerId,\n      content: '',\n      agent_thoughts: [],\n      message_files: [],\n      isAnswer: true,\n      intent: '', // 意图\n      llm: '', // 大模型\n      frontTip: '', // 前置提示\n      behindTip: '', // 后置提示\n      intents: [], // 意图选项\n    }\n\n    handleResponding(true)\n\n    const { data } = await fetchLarkIntent({\n      inputs: { query },\n      response_mode: 'blocking',\n      user: 'abc-123',\n    })\n    handleResponding(false)\n\n    responseItem.content = data.outputs.response\n    responseItem.intents = JSON.parse(data.outputs.intent)\n\n    if (responseItem.intents?.length === 1) {\n      // 只有一条意图识别结果就直接操作发送，隐藏该条意图识别的对话\n      responseItem.isHide = true\n      questionItem.isHide = true\n      currentIntent.current = responseItem.intents?.[0]\n      oneIntentSend(query)\n    }\n    setChatTree([...chatTreeRef.current])\n    chatTreeRef.current = [...chatTreeRef.current]\n  }\n\n  const handleSend = useCallback(async (\n    url: string,\n    data: {\n      query: string\n      files?: FileEntity[]\n      parent_message_id?: string\n      [key: string]: any\n    },\n    {\n      onGetConversationMessages,\n      onGetSuggestedQuestions,\n      onConversationComplete,\n      isPublicAPI,\n      isIntentRecognition,\n    }: SendCallback,\n  ) => {\n    setSuggestQuestions([])\n\n    if (isRespondingRef.current) {\n      notify({ type: 'info', message: t('appDebug.errorMessage.waitForResponse') })\n      return false\n    }\n\n    const parentMessageId = data.parent_message_id || ''\n    const parentMessage = threadMessages.find(item => item.id === data.parent_message_id)\n\n    const placeholderQuestionId = `question-${Date.now()}`\n    const questionItem = {\n      id: placeholderQuestionId,\n      content: data.query,\n      isAnswer: false,\n      message_files: data.files,\n      parentMessageId: data.parent_message_id,\n      isHide: isIntentRecognition, // 意图识别时隐藏\n      intent: currentIntent.current, // 意图\n    }\n\n    const placeholderAnswerId = `answer-placeholder-${Date.now()}`\n    const placeholderAnswerItem = {\n      id: placeholderAnswerId,\n      content: '',\n      isAnswer: true,\n      parentMessageId: questionItem.id,\n      siblingIndex: parentMessage?.children?.length ?? chatTree.length,\n      intent: currentIntent.current, // 意图\n    }\n\n    setTargetMessageId(parentMessage?.id)\n    updateCurrentQAOnTree({\n      parentId: data.parent_message_id,\n      responseItem: placeholderAnswerItem,\n      placeholderQuestionId,\n      questionItem,\n    })\n\n    // answer\n    const responseItem: ChatItemInTree = {\n      id: placeholderAnswerId,\n      content: '',\n      agent_thoughts: [],\n      message_files: [],\n      isAnswer: true,\n      parentMessageId: questionItem.id,\n      siblingIndex: parentMessage?.children?.length ?? chatTree.length,\n      intent: currentIntent.current, // 意图\n      llm: '', // 大模型\n      frontTip: '', // 前置提示\n      behindTip: '', // 后置提示\n    }\n\n    handleResponding(true)\n    hasStopResponded.current = false\n\n    const { query, files, inputs, ...restData } = data\n    const bodyParams = {\n      response_mode: 'streaming',\n      conversation_id: conversationId.current,\n      files: getProcessedFiles(files || []),\n      query,\n      inputs: getProcessedInputs(inputs || {}, formSettings?.inputsForm || []),\n      ...restData,\n    }\n    if (bodyParams?.files?.length) {\n      bodyParams.files = bodyParams.files.map((item) => {\n        if (item.transfer_method === TransferMethod.local_file) {\n          return {\n            ...item,\n            url: '',\n          }\n        }\n        return item\n      })\n    }\n\n    let isAgentMode = false\n    let hasSetResponseId = false\n    let isMcpMode = false // MCP模式\n\n    let ttsUrl = ''\n    let ttsIsPublic = false\n    if (params.token) {\n      ttsUrl = '/text-to-audio'\n      ttsIsPublic = true\n    }\n    else if (params.appId) {\n      if (pathname.search('explore/installed') > -1)\n        ttsUrl = `/installed-apps/${params.appId}/text-to-audio`\n      else\n        ttsUrl = `/apps/${params.appId}/text-to-audio`\n    }\n    const MediaSource = window.ManagedMediaSource || window.MediaSource\n    let player = null\n    if (MediaSource)\n      player = AudioPlayerManager.getInstance().getAudioPlayer(ttsUrl, ttsIsPublic, uuidV4(), 'none', 'none', noop)\n    ssePost(\n      url,\n      {\n        body: bodyParams,\n      },\n      {\n        isPublicAPI,\n        onData: (message: string, isFirstMessage: boolean, { conversationId: newConversationId, messageId, taskId }: any) => {\n          if(isMcpMode) {\n            const lastNode = responseItem.workflowProcess!.tracing?.[responseItem.workflowProcess!.tracing?.length - 1]\n            const lastMcp = lastNode?.execution_metadata?.agent_log?.[lastNode.execution_metadata.agent_log.length - 1]\n            if(lastMcp) {\n              lastMcp.content = lastMcp.content + message\n              responseItem.content = responseItem.content + message\n            }\n          }\n          else if (!isAgentMode) {\n            responseItem.content = responseItem.content + message\n          }\n          else {\n            const lastThought = responseItem.agent_thoughts?.[responseItem.agent_thoughts?.length - 1]\n            if (lastThought)\n              lastThought.thought = lastThought.thought + message // need immer setAutoFreeze\n          }\n\n          if (messageId && !hasSetResponseId) {\n            questionItem.id = `question-${messageId}`\n            responseItem.id = messageId\n            responseItem.parentMessageId = questionItem.id\n            hasSetResponseId = true\n          }\n\n          if (isFirstMessage && newConversationId)\n            conversationId.current = newConversationId\n\n          taskIdRef.current = taskId\n          if (messageId)\n            responseItem.id = messageId\n\n          updateCurrentQAOnTree({\n            placeholderQuestionId,\n            questionItem,\n            responseItem,\n            parentId: data.parent_message_id,\n          })\n        },\n        async onCompleted(hasError?: boolean) {\n          handleResponding(false)\n\n          if (hasError)\n            return\n\n          if (onConversationComplete)\n            onConversationComplete(conversationId.current)\n\n          if (conversationId.current && !hasStopResponded.current && onGetConversationMessages) {\n            const { data }: any = await onGetConversationMessages(\n              conversationId.current,\n              newAbortController => conversationMessagesAbortControllerRef.current = newAbortController,\n            )\n            const newResponseItem = data.find((item: any) => item.id === responseItem.id)\n            if (!newResponseItem)\n              return\n\n            updateChatTreeNode(responseItem.id, {\n              content: newResponseItem.answer,\n              log: [\n                ...newResponseItem.message,\n                ...(newResponseItem.message[newResponseItem.message.length - 1].role !== 'assistant'\n                  ? [\n                    {\n                      role: 'assistant',\n                      text: newResponseItem.answer,\n                      files: newResponseItem.message_files?.filter((file: any) => file.belongs_to === 'assistant') || [],\n                    },\n                  ]\n                  : []),\n              ],\n              more: {\n                time: formatTime(newResponseItem.created_at, 'hh:mm A'),\n                tokens: newResponseItem.answer_tokens + newResponseItem.message_tokens,\n                latency: newResponseItem.provider_response_latency.toFixed(2),\n              },\n              // for agent log\n              conversationId: conversationId.current,\n              input: {\n                inputs: newResponseItem.inputs,\n                query: newResponseItem.query,\n              },\n            })\n          }\n          if (config?.suggested_questions_after_answer?.enabled && !hasStopResponded.current && onGetSuggestedQuestions) {\n            try {\n              const { data }: any = await onGetSuggestedQuestions(\n                responseItem.id,\n                newAbortController => suggestedQuestionsAbortControllerRef.current = newAbortController,\n              )\n              setSuggestQuestions(data)\n            }\n            // eslint-disable-next-line unused-imports/no-unused-vars\n            catch (e) {\n              setSuggestQuestions([])\n            }\n          }\n        },\n        onFile(file) {\n          const lastThought = responseItem.agent_thoughts?.[responseItem.agent_thoughts?.length - 1]\n          if (lastThought)\n            responseItem.agent_thoughts![responseItem.agent_thoughts!.length - 1].message_files = [...(lastThought as any).message_files, file]\n\n          updateCurrentQAOnTree({\n            placeholderQuestionId,\n            questionItem,\n            responseItem,\n            parentId: data.parent_message_id,\n          })\n        },\n        onThought(thought) {\n          isAgentMode = true\n          const response = responseItem as any\n          if (thought.message_id && !hasSetResponseId)\n            response.id = thought.message_id\n\n          if (response.agent_thoughts.length === 0) {\n            response.agent_thoughts.push(thought)\n          }\n          else {\n            const lastThought = response.agent_thoughts[response.agent_thoughts.length - 1]\n            // thought changed but still the same thought, so update.\n            if (lastThought.id === thought.id) {\n              thought.thought = lastThought.thought\n              thought.message_files = lastThought.message_files\n              responseItem.agent_thoughts![response.agent_thoughts.length - 1] = thought\n            }\n            else {\n              responseItem.agent_thoughts!.push(thought)\n            }\n          }\n          updateCurrentQAOnTree({\n            placeholderQuestionId,\n            questionItem,\n            responseItem,\n            parentId: data.parent_message_id,\n          })\n        },\n        onMessageEnd: (messageEnd) => {\n          if (messageEnd.metadata?.annotation_reply) {\n            responseItem.id = messageEnd.id\n            responseItem.annotation = ({\n              id: messageEnd.metadata.annotation_reply.id,\n              authorName: messageEnd.metadata.annotation_reply.account.name,\n            })\n            updateCurrentQAOnTree({\n              placeholderQuestionId,\n              questionItem,\n              responseItem,\n              parentId: data.parent_message_id,\n            })\n            return\n          }\n          responseItem.citation = messageEnd.metadata?.retriever_resources || []\n          const processedFilesFromResponse = getProcessedFilesFromResponse(messageEnd.files || [])\n          responseItem.allFiles = uniqBy([...(responseItem.allFiles || []), ...(processedFilesFromResponse || [])], 'id')\n\n          updateCurrentQAOnTree({\n            placeholderQuestionId,\n            questionItem,\n            responseItem,\n            parentId: data.parent_message_id,\n          })\n        },\n        onMessageReplace: (messageReplace) => {\n          responseItem.content = messageReplace.answer\n        },\n        onError() {\n          handleResponding(false)\n          updateCurrentQAOnTree({\n            placeholderQuestionId,\n            questionItem,\n            responseItem,\n            parentId: data.parent_message_id,\n          })\n        },\n        onWorkflowStarted: ({ workflow_run_id, task_id }) => {\n          taskIdRef.current = task_id\n          responseItem.workflow_run_id = workflow_run_id\n          responseItem.workflowProcess = {\n            status: WorkflowRunningStatus.Running,\n            tracing: [],\n          }\n          updateCurrentQAOnTree({\n            placeholderQuestionId,\n            questionItem,\n            responseItem,\n            parentId: data.parent_message_id,\n          })\n        },\n        onWorkflowFinished: ({ data: workflowFinishedData }) => {\n          if (data.status !== 'stopped') {\n            responseItem.workflowProcess!.status = data.status as WorkflowRunningStatus\n            // responseItem.content = data.outputs.answer\n\n            responseItem.intent = data.outputs?.intent || responseItem.intent\n            responseItem.llm = data.outputs?.llm || responseItem.llm\n\n            if (data.outputs?.intent && currentIntent.current !== data.outputs?.intent) {\n              responseItem.frontTip = `已进入[${data.outputs?.intent}]场景`\n              currentIntent.current = data.outputs?.intent\n            }\n          }\n\n          if (currentIntent.current?.includes(INTENT_INCLUDES_SCHEDULE)\n            && String(data.outputs?.answer)?.includes(NEED_CREATE_MEETING)) {\n            const jsonData = extractJSONFromMarkdown(data.outputs?.answer)\n            const subContent = data.outputs?.answer.replace(/```json\\n([\\s\\S]*?)\\n```/g, '').replaceAll('/\\n', '')\n            responseItem.meetingJsonData = jsonData\n            responseItem.content = subContent\n          }\n\n          if (data.status === 'failed')\n            responseItem.content = data.outputs?.answer\n\n          responseItem.workflowProcess!.status = workflowFinishedData.status as WorkflowRunningStatus\n          updateCurrentQAOnTree({\n            placeholderQuestionId,\n            questionItem,\n            responseItem,\n            parentId: data.parent_message_id,\n          })\n        },\n        onIterationStart: ({ data: iterationStartedData }) => {\n          responseItem.workflowProcess!.tracing!.push({\n            ...iterationStartedData,\n            status: WorkflowRunningStatus.Running,\n          })\n          updateCurrentQAOnTree({\n            placeholderQuestionId,\n            questionItem,\n            responseItem,\n            parentId: data.parent_message_id,\n          })\n        },\n        onIterationFinish: ({ data: iterationFinishedData }) => {\n          const tracing = responseItem.workflowProcess!.tracing!\n          const iterationIndex = tracing.findIndex(item => item.node_id === iterationFinishedData.node_id\n            && (item.execution_metadata?.parallel_id === iterationFinishedData.execution_metadata?.parallel_id || item.parallel_id === iterationFinishedData.execution_metadata?.parallel_id))!\n          tracing[iterationIndex] = {\n            ...tracing[iterationIndex],\n            ...iterationFinishedData,\n            status: WorkflowRunningStatus.Succeeded,\n          }\n\n          updateCurrentQAOnTree({\n            placeholderQuestionId,\n            questionItem,\n            responseItem,\n            parentId: data.parent_message_id,\n          })\n        },\n        onNodeStarted: ({ data: nodeStartedData }) => {\n          if (nodeStartedData.iteration_id)\n            return\n\n          if (data.loop_id)\n            return\n\n          responseItem.workflowProcess!.tracing!.push({\n            ...nodeStartedData,\n            status: WorkflowRunningStatus.Running,\n          })\n          updateCurrentQAOnTree({\n            placeholderQuestionId,\n            questionItem,\n            responseItem,\n            parentId: data.parent_message_id,\n          })\n        },\n        onNodeFinished: ({ data: nodeFinishedData }) => {\n          if (nodeFinishedData.iteration_id)\n            return\n\n          if (data.loop_id)\n            return\n\n          const currentIndex = responseItem.workflowProcess!.tracing!.findIndex((item) => {\n            if (!item.execution_metadata?.parallel_id)\n              return item.node_id === nodeFinishedData.node_id\n\n            return item.node_id === nodeFinishedData.node_id && (item.execution_metadata?.parallel_id === nodeFinishedData.execution_metadata?.parallel_id)\n          })\n\n          // MCP模式下不替换\n          if(!isMcpMode)\n            responseItem.workflowProcess!.tracing[currentIndex] = nodeFinishedData as any\n\n          updateCurrentQAOnTree({\n            placeholderQuestionId,\n            questionItem,\n            responseItem,\n            parentId: data.parent_message_id,\n          })\n        },\n        onTTSChunk: (messageId: string, audio: string) => {\n          if (!audio || audio === '')\n            return\n          player?.playAudioWithAudio(audio, true)\n          AudioPlayerManager.getInstance().resetMsgId(messageId)\n        },\n        onTTSEnd: (messageId: string, audio: string) => {\n          player?.playAudioWithAudio(audio, false)\n        },\n        onLoopStart: ({ data: loopStartedData }) => {\n          responseItem.workflowProcess!.tracing!.push({\n            ...loopStartedData,\n            status: WorkflowRunningStatus.Running,\n          })\n          updateCurrentQAOnTree({\n            placeholderQuestionId,\n            questionItem,\n            responseItem,\n            parentId: data.parent_message_id,\n          })\n        },\n        onLoopFinish: ({ data: loopFinishedData }) => {\n          const tracing = responseItem.workflowProcess!.tracing!\n          const loopIndex = tracing.findIndex(item => item.node_id === loopFinishedData.node_id\n            && (item.execution_metadata?.parallel_id === loopFinishedData.execution_metadata?.parallel_id || item.parallel_id === loopFinishedData.execution_metadata?.parallel_id))!\n          tracing[loopIndex] = {\n            ...tracing[loopIndex],\n            ...loopFinishedData,\n            status: WorkflowRunningStatus.Succeeded,\n          }\n\n          updateCurrentQAOnTree({\n            placeholderQuestionId,\n            questionItem,\n            responseItem,\n            parentId: data.parent_message_id,\n          })\n        },\n        onAgentLog: ({ data }) => {\n          const process = (data?.data as { description?: string })?.description\n\n          if(process) {\n            responseItem.displayType = DISPLAY_TYPE.MCP\n            isMcpMode = true\n          }\n\n          const currentNodeIndex = responseItem.workflowProcess!.tracing!.findIndex(item => item.node_id === data.node_id)\n          if (currentNodeIndex > -1) {\n            const current = responseItem.workflowProcess!.tracing![currentNodeIndex]\n\n            if (current.execution_metadata) {\n              if (current.execution_metadata.agent_log) {\n                const currentLogIndex = current.execution_metadata.agent_log.findIndex(log => log.node_id === data.node_id)\n                if (currentLogIndex > -1) {\n                  current.execution_metadata.agent_log[currentLogIndex] = {\n                    ...current.execution_metadata.agent_log[currentLogIndex],\n                    ...data,\n                    ...(process && { process }),\n                    references: (data?.data as { list?: any[] })?.list || [],\n                  }\n                }\n                else {\n                  current.execution_metadata.agent_log.push(data)\n                }\n              }\n            }\n            else {\n              current.execution_metadata = {\n                agent_log: [{ ...data, content: '' }],\n              } as any\n            }\n\n            responseItem.workflowProcess!.tracing[currentNodeIndex] = {\n              ...current,\n            }\n\n            updateCurrentQAOnTree({\n              placeholderQuestionId,\n              questionItem,\n              responseItem,\n              parentId: parentMessageId,\n            })\n          }\n        },\n      })\n    return true\n  }, [\n    t,\n    chatTree.length,\n    threadMessages,\n    config?.suggested_questions_after_answer,\n    updateCurrentQAOnTree,\n    updateChatTreeNode,\n    notify,\n    handleResponding,\n    formatTime,\n    params.token,\n    params.appId,\n    pathname,\n    formSettings,\n  ])\n\n  const handleAnnotationEdited = useCallback((query: string, answer: string, index: number) => {\n    const targetQuestionId = chatList[index - 1].id\n    const targetAnswerId = chatList[index].id\n\n    updateChatTreeNode(targetQuestionId, {\n      content: query,\n    })\n    updateChatTreeNode(targetAnswerId, {\n      content: answer,\n      annotation: {\n        ...chatList[index].annotation,\n        logAnnotation: undefined,\n      } as any,\n    })\n  }, [chatList, updateChatTreeNode])\n\n  const handleAnnotationAdded = useCallback((annotationId: string, authorName: string, query: string, answer: string, index: number) => {\n    const targetQuestionId = chatList[index - 1].id\n    const targetAnswerId = chatList[index].id\n\n    updateChatTreeNode(targetQuestionId, {\n      content: query,\n    })\n\n    updateChatTreeNode(targetAnswerId, {\n      content: chatList[index].content,\n      annotation: {\n        id: annotationId,\n        authorName,\n        logAnnotation: {\n          content: answer,\n          account: {\n            id: '',\n            name: authorName,\n            email: '',\n          },\n        },\n      } as Annotation,\n    })\n  }, [chatList, updateChatTreeNode])\n\n  const handleAnnotationRemoved = useCallback((index: number) => {\n    const targetAnswerId = chatList[index].id\n\n    updateChatTreeNode(targetAnswerId, {\n      content: chatList[index].content,\n      annotation: {\n        ...(chatList[index].annotation || {}),\n        id: '',\n      } as Annotation,\n    })\n  }, [chatList, updateChatTreeNode])\n\n  const updateIntent = (intent: string, prompt?: string) => {\n    if (currentIntent.current === intent)\n      return\n\n    // if (intent === '') {\n    //   const lastItem = chatTreeRef.current[chatTreeRef.current.length - 1]\n    //   lastItem.behindTip = '已开启新话题'\n    //   currentIntent.current = intent\n    //   chatTreeRef.current = [...chatTreeRef.current]\n    //   setChatTree([...chatTreeRef.current])\n    //   return\n    // }\n\n    currentIntent.current = intent\n    // const responseItem: ChatItem = {\n    //   id: `answer-placeholder-${Date.now()}`,\n    //   content: prompt || '',\n    //   agent_thoughts: [],\n    //   message_files: [],\n    //   isAnswer: true,\n    //   intent, // 意图\n    //   llm: '', // 大模型\n    //   frontTip: intent && `已进入[${intent}]场景`, // 前置提示\n    //   behindTip: '', // 后置提示\n    //   isPrompt: !!prompt as boolean,\n    // }\n\n    // const newData = [...chatTreeRef.current, responseItem]\n    // chatTreeRef.current = [...newData]\n    setChatTree([...chatTreeRef.current])\n  }\n\n  const updateLlm = (name: string, key: string) => {\n    currentLlm.current = key\n    if (name) {\n      chatTreeRef.current.forEach((item, i) => {\n        if (i === chatTreeRef.current.length - 1) {\n          // 模型切换提示词设置\n          item.behindTip = `对话模型已切换为${name}`\n        }\n        return item\n      })\n      chatTreeRef.current = [...chatTreeRef.current]\n      setChatTree([...chatTreeRef.current])\n    }\n  }\n\n  const updateIntentSilent = (intent: string) => {\n    currentIntent.current = intent\n  }\n  const updateLastChatIntent = (intent: string) => {\n    currentIntent.current = chatTree[chatTree.length - 1].intent = intent\n    chatTreeRef.current = [...chatList]\n    setChatTree([...chatList])\n  }\n\n  useEffect(() => {\n    if (clearChatList)\n      handleRestart(() => clearChatListCallback?.(false))\n  }, [clearChatList, clearChatListCallback, handleRestart])\n\n  return {\n    chatList,\n    setTargetMessageId,\n    conversationId: conversationId.current,\n    isResponding,\n    setIsResponding,\n    handleSend,\n    suggestedQuestions,\n    handleRestart,\n    handleStop,\n    handleAnnotationEdited,\n    handleAnnotationAdded,\n    handleAnnotationRemoved,\n    currentIntent,\n    currentLlm,\n    updateIntent,\n    updateLlm,\n    identifyingIntent,\n    updateIntentSilent,\n    updateLastChatIntent,\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;;;;;AAQA;;;;;;AAEA;;;;;;AAQA;AAEA;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;;;;;;;AAWA,SAAS;AACT,MAAM,kBAAkB,CAAC,aAAkD,CAAC;QAC1E,IAAI,CAAC,mBAAmB,EAAE,IAAI,eAAe,CAAC,IAAI,KAAK,EAAE,GAAG,KAAK,CAAC,CAAC,KAAK;QACxE,SAAS,YAAY,UAAU;QAC/B,gBAAgB,EAAE;QAClB,eAAe,EAAE;QACjB,UAAU;QACV,QAAQ,YAAY,SAAS;QAC7B,KAAK;QACL,UAAU,YAAY,SAAS,CAAC,IAAI,EAAE,YAAY,MAAM,GAAG,CAAC;QAC5D,WAAW;QACX,UAAU;IACZ,CAAC;AAEM,MAAM,UAAU,CACrB,QACA,cAIA,cACA,UACA,eACA,uBACA,YACA;IAEA,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,EAAE,UAAU,EAAE,GAAG;IACvB,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,iBAAiB,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,mBAAmB,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,kBAAkB,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,YAAY,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,CAAC,oBAAoB,oBAAoB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvE,MAAM,yCAAyC,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAA0B;IAC9E,MAAM,uCAAuC,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAA0B;IAC5E,MAAM,SAAS,CAAA,GAAA,ySAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,ySAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE,IAAI,OAAO;;IACxC,MAAM,aAAa,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE,IAAI,OAAO;;IAErC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAoB,gBAAgB,EAAE;IAC7E,MAAM,cAAc,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAoB;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD;IACrD,MAAM,iBAAiB,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAA,GAAA,0IAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,kBAAkB;QAAC;QAAU;KAAgB;IAE9G,MAAM,kBAAkB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,OAAO,CAAA,GAAA,kJAAA,CAAA,0BAAuB,AAAD,EAAE,KAAK,cAAc,UAAU,CAAC,GAAG,cAAc,cAAc,EAAE;IAChG,GAAG;QAAC,cAAc;QAAQ,cAAc;KAAW;IAEnD,0CAA0C,GAC1C,MAAM,WAAW,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE;QACvB,MAAM,MAAM;eAAI;SAAe;QAC/B,IAAI,QAAQ,qBAAqB,CAAC,QAAQ,aAAa;YACrD,MAAM,QAAQ,eAAe,SAAS,CAAC,CAAA,OAAQ,KAAK,kBAAkB;YAEtE,IAAI,QAAQ,CAAC,GAAG;gBACd,GAAG,CAAC,MAAM,GAAG;oBACX,GAAG,GAAG,CAAC,MAAM;oBACb,SAAS,gBAAgB,OAAO,iBAAiB;oBACjD,oBAAoB,OAAO,mBAAmB;gBAChD;YACF,OACK;gBACH,IAAI,OAAO,CAAC;oBACV,IAAI;oBACJ,SAAS,gBAAgB,OAAO,iBAAiB;oBACjD,UAAU;oBACV,oBAAoB;oBACpB,oBAAoB,OAAO,mBAAmB;gBAChD;YACF;QACF;QAEA,IAAG,QAAQ,aAAa;YACtB,MAAM;YACN,IAAG,cAAc,OAAO,IAAI,CAAC,IAAI,MAAM,EAAE;gBACvC,MAAM,aAAa,YAAY,KAAK,CAAC,OAAc,KAAK,KAAK,KAAK,cAAc,OAAO;gBACvF,IAAI,IAAI,CAAC,gBAAgB;YAC3B,OACK;gBACH,IAAI,UAAiB,EAAE;gBAEvB,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;oBAClC,aAAa;oBACb,IAAG,GAAG,CAAC,EAAE,EAAE,WAAW,GAAG,CAAC,IAAI,EAAE,EAAE,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE;wBACvD,MAAM,aAAa,YAAY,KAAK,CAAC,OAAc,KAAK,KAAK,KAAK,GAAG,CAAC,EAAE,CAAC,MAAM;wBAE/E,UAAU,QAAQ,MAAM,CAAC,gBAAgB,aAAa,GAAG,CAAC,EAAE;oBAC9D,OACK;wBACH,QAAQ,IAAI,CAAC,GAAG,CAAC,EAAE;oBACrB;oBAEA,iBAAiB;oBACjB,IAAG,AAAC,MAAM,IAAI,MAAM,GAAG,KAAM,GAAG,CAAC,EAAE,CAAC,MAAM,IAAI,cAAc,OAAO,IAAI,GAAG,CAAC,EAAE,CAAC,MAAM,KAAK,cAAc,OAAO,EAAE;wBAC9G,MAAM,aAAa,YAAY,KAAK,CAAC,OAAc,KAAK,KAAK,KAAK,cAAc,OAAO;wBACvF,QAAQ,IAAI,CAAC,gBAAgB;oBAC/B;gBACF;gBAEA,OAAO;YACT;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAgB,QAAQ;QAAmB;QAAiB,QAAQ;QAAqB,QAAQ;KAAY;IAEjH,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,0LAAA,CAAA,gBAAa,AAAD,EAAE;QACd,OAAO;YACL,CAAA,GAAA,0LAAA,CAAA,gBAAa,AAAD,EAAE;QAChB;IACF,GAAG,EAAE;IAEL,uDAAuD,GACvD,MAAM,sBAAsB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAkB;QACzD,OAAO,CAAA,GAAA,0LAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,EAAE,CAAC;YACnC,MAAM,QAA0B;mBAAI;aAAM;YAC1C,MAAO,MAAM,MAAM,GAAG,EAAG;gBACvB,MAAM,UAAU,MAAM,KAAK;gBAC3B,IAAI,QAAQ,EAAE,KAAK,UAAU;oBAC3B,UAAU;oBACV;gBACF;gBACA,IAAI,QAAQ,QAAQ,EAClB,MAAM,IAAI,IAAI,QAAQ,QAAQ;YAClC;QACF;IACF,GAAG,EAAE;IAOL,MAAM,qBAAyC,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CACzD,IACA;QAEA,MAAM,YAAY,oBAAoB,IAAI,CAAC;YACzC,IAAI,OAAO,mBAAmB,YAAY;gBACxC,eAAe;YACjB,OACK;gBACH,OAAO,IAAI,CAAC,gBAAgB,OAAO,CAAC,CAAC;oBAClC,IAAY,CAAC,IAAI,GAAG,AAAC,cAAsB,CAAC,IAAI;gBACnD;YACF;QACF;QACA,YAAY;QACZ,YAAY,OAAO,GAAG;IACxB,GAAG;QAAC;KAAoB;IAExB,MAAM,mBAAmB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,gBAAgB;QAChB,gBAAgB,OAAO,GAAG;IAC5B,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,iBAAiB,OAAO,GAAG;QAC3B,iBAAiB;QACjB,IAAI,YAAY,UAAU,OAAO,EAC/B,SAAS,UAAU,OAAO;QAC5B,IAAI,uCAAuC,OAAO,EAChD,uCAAuC,OAAO,CAAC,KAAK;QACtD,IAAI,qCAAqC,OAAO,EAC9C,qCAAqC,OAAO,CAAC,KAAK;IACtD,GAAG;QAAC;QAAU;KAAiB;IAE/B,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,eAAe,OAAO,GAAG;QACzB,UAAU,OAAO,GAAG;QACpB,cAAc,OAAO,GAAG;QACxB;QACA,YAAY,EAAE;QACd,oBAAoB,EAAE;QACtB;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,wBAAwB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC,EACzC,QAAQ,EACR,YAAY,EACZ,qBAAqB,EACrB,YAAY,EAMb;QACC,IAAI;QACJ,MAAM,YAAY;YAAE,GAAG,YAAY;YAAE,UAAU;gBAAC;oBAAE,GAAG,YAAY;oBAAE,UAAU,EAAE;gBAAC;aAAE;QAAC;QACnF,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,CAAC,CAAA,OAAQ;gBAAC;gBAAuB,aAAa,EAAE;aAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI;YACnG,wFAAwF;YACxF,gDAAgD;YAChD,YAAY,CAAA,GAAA,0LAAA,CAAA,UAAO,AAAD,EAAE,UAAU,CAAC;gBAC7B,MAAM,IAAI,CAAC;YACb;QACF,OACK;YACH,2FAA2F;YAC3F,YAAY,oBAAoB,UAAW,CAAC;gBAC1C,MAAM,oBAAoB,WAAW,QAAQ,CAAE,SAAS,CAAC,CAAA,OAAQ;wBAAC;wBAAuB,aAAa,EAAE;qBAAC,CAAC,QAAQ,CAAC,KAAK,EAAE;gBAC1H,IAAI,sBAAsB,CAAC,GACzB,WAAW,QAAQ,CAAE,IAAI,CAAC;qBAE1B,WAAW,QAAQ,AAAC,CAAC,kBAAkB,GAAG;YAC9C;QACF;QACA,YAAY;QACZ,YAAY,OAAO,GAAG;IACxB,GAAG;QAAC;QAAU;KAAoB;IAElC,OAAO;IACP,MAAM,oBAAoB,OAAO,OAAe;QAC9C,MAAM,aAAa,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;QAC3C,MAAM,eAAe;YACnB,IAAI;YACJ,SAAS;YACT,UAAU;YACV,eAAe;YACf,QAAQ;QACV;QAEA,MAAM,sBAAsB,CAAC,mBAAmB,EAAE,KAAK,GAAG,IAAI;QAC9D,MAAM,wBAAwB;YAC5B,IAAI;YACJ,SAAS;YACT,UAAU;QACZ;QAEA,MAAM,UAAU;eAAI,YAAY,OAAO;YAAE;YAAc;SAAsB;QAC7E,YAAY;eAAI;SAAQ;QACxB,YAAY,OAAO,GAAG;eAAI;SAAQ;QAElC,SAAS;QACT,MAAM,eAAyB;YAC7B,IAAI;YACJ,SAAS;YACT,gBAAgB,EAAE;YAClB,eAAe,EAAE;YACjB,UAAU;YACV,QAAQ;YACR,KAAK;YACL,UAAU;YACV,WAAW;YACX,SAAS,EAAE;QACb;QAEA,iBAAiB;QAEjB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gBAAgB;YACrC,QAAQ;gBAAE;YAAM;YAChB,eAAe;YACf,MAAM;QACR;QACA,iBAAiB;QAEjB,aAAa,OAAO,GAAG,KAAK,OAAO,CAAC,QAAQ;QAC5C,aAAa,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,OAAO,CAAC,MAAM;QAErD,IAAI,aAAa,OAAO,EAAE,WAAW,GAAG;YACtC,gCAAgC;YAChC,aAAa,MAAM,GAAG;YACtB,aAAa,MAAM,GAAG;YACtB,cAAc,OAAO,GAAG,aAAa,OAAO,EAAE,CAAC,EAAE;YACjD,cAAc;QAChB;QACA,YAAY;eAAI,YAAY,OAAO;SAAC;QACpC,YAAY,OAAO,GAAG;eAAI,YAAY,OAAO;SAAC;IAChD;IAEA,MAAM,aAAa,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,OAC7B,KACA,MAMA,EACE,yBAAyB,EACzB,uBAAuB,EACvB,sBAAsB,EACtB,WAAW,EACX,mBAAmB,EACN;QAEf,oBAAoB,EAAE;QAEtB,IAAI,gBAAgB,OAAO,EAAE;YAC3B,OAAO;gBAAE,MAAM;gBAAQ,SAAS,EAAE;YAAyC;YAC3E,OAAO;QACT;QAEA,MAAM,kBAAkB,KAAK,iBAAiB,IAAI;QAClD,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,iBAAiB;QAEpF,MAAM,wBAAwB,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;QACtD,MAAM,eAAe;YACnB,IAAI;YACJ,SAAS,KAAK,KAAK;YACnB,UAAU;YACV,eAAe,KAAK,KAAK;YACzB,iBAAiB,KAAK,iBAAiB;YACvC,QAAQ;YACR,QAAQ,cAAc,OAAO;QAC/B;QAEA,MAAM,sBAAsB,CAAC,mBAAmB,EAAE,KAAK,GAAG,IAAI;QAC9D,MAAM,wBAAwB;YAC5B,IAAI;YACJ,SAAS;YACT,UAAU;YACV,iBAAiB,aAAa,EAAE;YAChC,cAAc,eAAe,UAAU,UAAU,SAAS,MAAM;YAChE,QAAQ,cAAc,OAAO;QAC/B;QAEA,mBAAmB,eAAe;QAClC,sBAAsB;YACpB,UAAU,KAAK,iBAAiB;YAChC,cAAc;YACd;YACA;QACF;QAEA,SAAS;QACT,MAAM,eAA+B;YACnC,IAAI;YACJ,SAAS;YACT,gBAAgB,EAAE;YAClB,eAAe,EAAE;YACjB,UAAU;YACV,iBAAiB,aAAa,EAAE;YAChC,cAAc,eAAe,UAAU,UAAU,SAAS,MAAM;YAChE,QAAQ,cAAc,OAAO;YAC7B,KAAK;YACL,UAAU;YACV,WAAW;QACb;QAEA,iBAAiB;QACjB,iBAAiB,OAAO,GAAG;QAE3B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,UAAU,GAAG;QAC9C,MAAM,aAAa;YACjB,eAAe;YACf,iBAAiB,eAAe,OAAO;YACvC,OAAO,kBAAkB,SAAS,EAAE;YACpC;YACA,QAAQ,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,CAAC,GAAG,cAAc,cAAc,EAAE;YACvE,GAAG,QAAQ;QACb;QACA,IAAI,YAAY,OAAO,QAAQ;YAC7B,WAAW,KAAK,GAAG,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC;gBACvC,IAAI,KAAK,eAAe,KAAK,eAAe,UAAU,EAAE;oBACtD,OAAO;wBACL,GAAG,IAAI;wBACP,KAAK;oBACP;gBACF;gBACA,OAAO;YACT;QACF;QAEA,IAAI,cAAc;QAClB,IAAI,mBAAmB;QACvB,IAAI,YAAY,MAAM,QAAQ;;QAE9B,IAAI,SAAS;QACb,IAAI,cAAc;QAClB,IAAI,OAAO,KAAK,EAAE;YAChB,SAAS;YACT,cAAc;QAChB,OACK,IAAI,OAAO,KAAK,EAAE;YACrB,IAAI,SAAS,MAAM,CAAC,uBAAuB,CAAC,GAC1C,SAAS,CAAC,gBAAgB,EAAE,OAAO,KAAK,CAAC,cAAc,CAAC;iBAExD,SAAS,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,cAAc,CAAC;QAClD;QACA,MAAM,cAAc,OAAO,kBAAkB,IAAI,OAAO,WAAW;QACnE,IAAI,SAAS;QACb,IAAI,aACF,SAAS,mBAAmB,WAAW,GAAG,cAAc,CAAC,QAAQ,aAAa,UAAU,QAAQ,QAAQ;QAC1G,QACE,KACA;YACE,MAAM;QACR,GACA;YACE;YACA,QAAQ,CAAC,SAAiB,gBAAyB,EAAE,gBAAgB,iBAAiB,EAAE,SAAS,EAAE,MAAM,EAAO;gBAC9G,IAAG,WAAW;oBACZ,MAAM,WAAW,aAAa,eAAe,CAAE,OAAO,EAAE,CAAC,aAAa,eAAe,CAAE,OAAO,EAAE,SAAS,EAAE;oBAC3G,MAAM,UAAU,UAAU,oBAAoB,WAAW,CAAC,SAAS,kBAAkB,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE;oBAC3G,IAAG,SAAS;wBACV,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAG;wBACpC,aAAa,OAAO,GAAG,aAAa,OAAO,GAAG;oBAChD;gBACF,OACK,IAAI,CAAC,aAAa;oBACrB,aAAa,OAAO,GAAG,aAAa,OAAO,GAAG;gBAChD,OACK;oBACH,MAAM,cAAc,aAAa,cAAc,EAAE,CAAC,aAAa,cAAc,EAAE,SAAS,EAAE;oBAC1F,IAAI,aACF,YAAY,OAAO,GAAG,YAAY,OAAO,GAAG,QAAQ,2BAA2B;;gBACnF;gBAEA,IAAI,aAAa,CAAC,kBAAkB;oBAClC,aAAa,EAAE,GAAG,CAAC,SAAS,EAAE,WAAW;oBACzC,aAAa,EAAE,GAAG;oBAClB,aAAa,eAAe,GAAG,aAAa,EAAE;oBAC9C,mBAAmB;gBACrB;gBAEA,IAAI,kBAAkB,mBACpB,eAAe,OAAO,GAAG;gBAE3B,UAAU,OAAO,GAAG;gBACpB,IAAI,WACF,aAAa,EAAE,GAAG;gBAEpB,sBAAsB;oBACpB;oBACA;oBACA;oBACA,UAAU,KAAK,iBAAiB;gBAClC;YACF;YACA,MAAM,aAAY,QAAkB;gBAClC,iBAAiB;gBAEjB,IAAI,UACF;gBAEF,IAAI,wBACF,uBAAuB,eAAe,OAAO;gBAE/C,IAAI,eAAe,OAAO,IAAI,CAAC,iBAAiB,OAAO,IAAI,2BAA2B;oBACpF,MAAM,EAAE,IAAI,EAAE,GAAQ,MAAM,0BAC1B,eAAe,OAAO,EACtB,CAAA,qBAAsB,uCAAuC,OAAO,GAAG;oBAEzE,MAAM,kBAAkB,KAAK,IAAI,CAAC,CAAC,OAAc,KAAK,EAAE,KAAK,aAAa,EAAE;oBAC5E,IAAI,CAAC,iBACH;oBAEF,mBAAmB,aAAa,EAAE,EAAE;wBAClC,SAAS,gBAAgB,MAAM;wBAC/B,KAAK;+BACA,gBAAgB,OAAO;+BACtB,gBAAgB,OAAO,CAAC,gBAAgB,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,IAAI,KAAK,cACrE;gCACA;oCACE,MAAM;oCACN,MAAM,gBAAgB,MAAM;oCAC5B,OAAO,gBAAgB,aAAa,EAAE,OAAO,CAAC,OAAc,KAAK,UAAU,KAAK,gBAAgB,EAAE;gCACpG;6BACD,GACC,EAAE;yBACP;wBACD,MAAM;4BACJ,MAAM,WAAW,gBAAgB,UAAU,EAAE;4BAC7C,QAAQ,gBAAgB,aAAa,GAAG,gBAAgB,cAAc;4BACtE,SAAS,gBAAgB,yBAAyB,CAAC,OAAO,CAAC;wBAC7D;wBACA,gBAAgB;wBAChB,gBAAgB,eAAe,OAAO;wBACtC,OAAO;4BACL,QAAQ,gBAAgB,MAAM;4BAC9B,OAAO,gBAAgB,KAAK;wBAC9B;oBACF;gBACF;gBACA,IAAI,QAAQ,kCAAkC,WAAW,CAAC,iBAAiB,OAAO,IAAI,yBAAyB;oBAC7G,IAAI;wBACF,MAAM,EAAE,IAAI,EAAE,GAAQ,MAAM,wBAC1B,aAAa,EAAE,EACf,CAAA,qBAAsB,qCAAqC,OAAO,GAAG;wBAEvE,oBAAoB;oBACtB,EACA,yDAAyD;oBACzD,OAAO,GAAG;wBACR,oBAAoB,EAAE;oBACxB;gBACF;YACF;YACA,QAAO,IAAI;gBACT,MAAM,cAAc,aAAa,cAAc,EAAE,CAAC,aAAa,cAAc,EAAE,SAAS,EAAE;gBAC1F,IAAI,aACF,aAAa,cAAc,AAAC,CAAC,aAAa,cAAc,CAAE,MAAM,GAAG,EAAE,CAAC,aAAa,GAAG;uBAAI,AAAC,YAAoB,aAAa;oBAAE;iBAAK;gBAErI,sBAAsB;oBACpB;oBACA;oBACA;oBACA,UAAU,KAAK,iBAAiB;gBAClC;YACF;YACA,WAAU,OAAO;gBACf,cAAc;gBACd,MAAM,WAAW;gBACjB,IAAI,QAAQ,UAAU,IAAI,CAAC,kBACzB,SAAS,EAAE,GAAG,QAAQ,UAAU;gBAElC,IAAI,SAAS,cAAc,CAAC,MAAM,KAAK,GAAG;oBACxC,SAAS,cAAc,CAAC,IAAI,CAAC;gBAC/B,OACK;oBACH,MAAM,cAAc,SAAS,cAAc,CAAC,SAAS,cAAc,CAAC,MAAM,GAAG,EAAE;oBAC/E,yDAAyD;oBACzD,IAAI,YAAY,EAAE,KAAK,QAAQ,EAAE,EAAE;wBACjC,QAAQ,OAAO,GAAG,YAAY,OAAO;wBACrC,QAAQ,aAAa,GAAG,YAAY,aAAa;wBACjD,aAAa,cAAc,AAAC,CAAC,SAAS,cAAc,CAAC,MAAM,GAAG,EAAE,GAAG;oBACrE,OACK;wBACH,aAAa,cAAc,CAAE,IAAI,CAAC;oBACpC;gBACF;gBACA,sBAAsB;oBACpB;oBACA;oBACA;oBACA,UAAU,KAAK,iBAAiB;gBAClC;YACF;YACA,cAAc,CAAC;gBACb,IAAI,WAAW,QAAQ,EAAE,kBAAkB;oBACzC,aAAa,EAAE,GAAG,WAAW,EAAE;oBAC/B,aAAa,UAAU,GAAI;wBACzB,IAAI,WAAW,QAAQ,CAAC,gBAAgB,CAAC,EAAE;wBAC3C,YAAY,WAAW,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI;oBAC/D;oBACA,sBAAsB;wBACpB;wBACA;wBACA;wBACA,UAAU,KAAK,iBAAiB;oBAClC;oBACA;gBACF;gBACA,aAAa,QAAQ,GAAG,WAAW,QAAQ,EAAE,uBAAuB,EAAE;gBACtE,MAAM,6BAA6B,8BAA8B,WAAW,KAAK,IAAI,EAAE;gBACvF,aAAa,QAAQ,GAAG,OAAO;uBAAK,aAAa,QAAQ,IAAI,EAAE;uBAAO,8BAA8B,EAAE;iBAAE,EAAE;gBAE1G,sBAAsB;oBACpB;oBACA;oBACA;oBACA,UAAU,KAAK,iBAAiB;gBAClC;YACF;YACA,kBAAkB,CAAC;gBACjB,aAAa,OAAO,GAAG,eAAe,MAAM;YAC9C;YACA;gBACE,iBAAiB;gBACjB,sBAAsB;oBACpB;oBACA;oBACA;oBACA,UAAU,KAAK,iBAAiB;gBAClC;YACF;YACA,mBAAmB,CAAC,EAAE,eAAe,EAAE,OAAO,EAAE;gBAC9C,UAAU,OAAO,GAAG;gBACpB,aAAa,eAAe,GAAG;gBAC/B,aAAa,eAAe,GAAG;oBAC7B,QAAQ,sBAAsB,OAAO;oBACrC,SAAS,EAAE;gBACb;gBACA,sBAAsB;oBACpB;oBACA;oBACA;oBACA,UAAU,KAAK,iBAAiB;gBAClC;YACF;YACA,oBAAoB,CAAC,EAAE,MAAM,oBAAoB,EAAE;gBACjD,IAAI,KAAK,MAAM,KAAK,WAAW;oBAC7B,aAAa,eAAe,CAAE,MAAM,GAAG,KAAK,MAAM;oBAClD,6CAA6C;oBAE7C,aAAa,MAAM,GAAG,KAAK,OAAO,EAAE,UAAU,aAAa,MAAM;oBACjE,aAAa,GAAG,GAAG,KAAK,OAAO,EAAE,OAAO,aAAa,GAAG;oBAExD,IAAI,KAAK,OAAO,EAAE,UAAU,cAAc,OAAO,KAAK,KAAK,OAAO,EAAE,QAAQ;wBAC1E,aAAa,QAAQ,GAAG,CAAC,IAAI,EAAE,KAAK,OAAO,EAAE,OAAO,GAAG,CAAC;wBACxD,cAAc,OAAO,GAAG,KAAK,OAAO,EAAE;oBACxC;gBACF;gBAEA,IAAI,cAAc,OAAO,EAAE,SAAS,wJAAA,CAAA,2BAAwB,KACvD,OAAO,KAAK,OAAO,EAAE,SAAS,SAAS,wJAAA,CAAA,sBAAmB,GAAG;oBAChE,MAAM,WAAW,CAAA,GAAA,kJAAA,CAAA,0BAAuB,AAAD,EAAE,KAAK,OAAO,EAAE;oBACvD,MAAM,aAAa,KAAK,OAAO,EAAE,OAAO,QAAQ,6BAA6B,IAAI,WAAW,OAAO;oBACnG,aAAa,eAAe,GAAG;oBAC/B,aAAa,OAAO,GAAG;gBACzB;gBAEA,IAAI,KAAK,MAAM,KAAK,UAClB,aAAa,OAAO,GAAG,KAAK,OAAO,EAAE;gBAEvC,aAAa,eAAe,CAAE,MAAM,GAAG,qBAAqB,MAAM;gBAClE,sBAAsB;oBACpB;oBACA;oBACA;oBACA,UAAU,KAAK,iBAAiB;gBAClC;YACF;YACA,kBAAkB,CAAC,EAAE,MAAM,oBAAoB,EAAE;gBAC/C,aAAa,eAAe,CAAE,OAAO,CAAE,IAAI,CAAC;oBAC1C,GAAG,oBAAoB;oBACvB,QAAQ,sBAAsB,OAAO;gBACvC;gBACA,sBAAsB;oBACpB;oBACA;oBACA;oBACA,UAAU,KAAK,iBAAiB;gBAClC;YACF;YACA,mBAAmB,CAAC,EAAE,MAAM,qBAAqB,EAAE;gBACjD,MAAM,UAAU,aAAa,eAAe,CAAE,OAAO;gBACrD,MAAM,iBAAiB,QAAQ,SAAS,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK,sBAAsB,OAAO,IAC1F,CAAC,KAAK,kBAAkB,EAAE,gBAAgB,sBAAsB,kBAAkB,EAAE,eAAe,KAAK,WAAW,KAAK,sBAAsB,kBAAkB,EAAE,WAAW;gBAClL,OAAO,CAAC,eAAe,GAAG;oBACxB,GAAG,OAAO,CAAC,eAAe;oBAC1B,GAAG,qBAAqB;oBACxB,QAAQ,sBAAsB,SAAS;gBACzC;gBAEA,sBAAsB;oBACpB;oBACA;oBACA;oBACA,UAAU,KAAK,iBAAiB;gBAClC;YACF;YACA,eAAe,CAAC,EAAE,MAAM,eAAe,EAAE;gBACvC,IAAI,gBAAgB,YAAY,EAC9B;gBAEF,IAAI,KAAK,OAAO,EACd;gBAEF,aAAa,eAAe,CAAE,OAAO,CAAE,IAAI,CAAC;oBAC1C,GAAG,eAAe;oBAClB,QAAQ,sBAAsB,OAAO;gBACvC;gBACA,sBAAsB;oBACpB;oBACA;oBACA;oBACA,UAAU,KAAK,iBAAiB;gBAClC;YACF;YACA,gBAAgB,CAAC,EAAE,MAAM,gBAAgB,EAAE;gBACzC,IAAI,iBAAiB,YAAY,EAC/B;gBAEF,IAAI,KAAK,OAAO,EACd;gBAEF,MAAM,eAAe,aAAa,eAAe,CAAE,OAAO,CAAE,SAAS,CAAC,CAAC;oBACrE,IAAI,CAAC,KAAK,kBAAkB,EAAE,aAC5B,OAAO,KAAK,OAAO,KAAK,iBAAiB,OAAO;oBAElD,OAAO,KAAK,OAAO,KAAK,iBAAiB,OAAO,IAAK,KAAK,kBAAkB,EAAE,gBAAgB,iBAAiB,kBAAkB,EAAE;gBACrI;gBAEA,YAAY;gBACZ,IAAG,CAAC,WACF,aAAa,eAAe,CAAE,OAAO,CAAC,aAAa,GAAG;gBAExD,sBAAsB;oBACpB;oBACA;oBACA;oBACA,UAAU,KAAK,iBAAiB;gBAClC;YACF;YACA,YAAY,CAAC,WAAmB;gBAC9B,IAAI,CAAC,SAAS,UAAU,IACtB;gBACF,QAAQ,mBAAmB,OAAO;gBAClC,mBAAmB,WAAW,GAAG,UAAU,CAAC;YAC9C;YACA,UAAU,CAAC,WAAmB;gBAC5B,QAAQ,mBAAmB,OAAO;YACpC;YACA,aAAa,CAAC,EAAE,MAAM,eAAe,EAAE;gBACrC,aAAa,eAAe,CAAE,OAAO,CAAE,IAAI,CAAC;oBAC1C,GAAG,eAAe;oBAClB,QAAQ,sBAAsB,OAAO;gBACvC;gBACA,sBAAsB;oBACpB;oBACA;oBACA;oBACA,UAAU,KAAK,iBAAiB;gBAClC;YACF;YACA,cAAc,CAAC,EAAE,MAAM,gBAAgB,EAAE;gBACvC,MAAM,UAAU,aAAa,eAAe,CAAE,OAAO;gBACrD,MAAM,YAAY,QAAQ,SAAS,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK,iBAAiB,OAAO,IAChF,CAAC,KAAK,kBAAkB,EAAE,gBAAgB,iBAAiB,kBAAkB,EAAE,eAAe,KAAK,WAAW,KAAK,iBAAiB,kBAAkB,EAAE,WAAW;gBACxK,OAAO,CAAC,UAAU,GAAG;oBACnB,GAAG,OAAO,CAAC,UAAU;oBACrB,GAAG,gBAAgB;oBACnB,QAAQ,sBAAsB,SAAS;gBACzC;gBAEA,sBAAsB;oBACpB;oBACA;oBACA;oBACA,UAAU,KAAK,iBAAiB;gBAClC;YACF;YACA,YAAY,CAAC,EAAE,IAAI,EAAE;gBACnB,MAAM,UAAW,MAAM,MAAmC;gBAE1D,IAAG,SAAS;oBACV,aAAa,WAAW,GAAG,8IAAA,CAAA,eAAY,CAAC,GAAG;oBAC3C,YAAY;gBACd;gBAEA,MAAM,mBAAmB,aAAa,eAAe,CAAE,OAAO,CAAE,SAAS,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK,KAAK,OAAO;gBAC/G,IAAI,mBAAmB,CAAC,GAAG;oBACzB,MAAM,UAAU,aAAa,eAAe,CAAE,OAAO,AAAC,CAAC,iBAAiB;oBAExE,IAAI,QAAQ,kBAAkB,EAAE;wBAC9B,IAAI,QAAQ,kBAAkB,CAAC,SAAS,EAAE;4BACxC,MAAM,kBAAkB,QAAQ,kBAAkB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA,MAAO,IAAI,OAAO,KAAK,KAAK,OAAO;4BAC1G,IAAI,kBAAkB,CAAC,GAAG;gCACxB,QAAQ,kBAAkB,CAAC,SAAS,CAAC,gBAAgB,GAAG;oCACtD,GAAG,QAAQ,kBAAkB,CAAC,SAAS,CAAC,gBAAgB;oCACxD,GAAG,IAAI;oCACP,GAAI,WAAW;wCAAE;oCAAQ,CAAC;oCAC1B,YAAY,AAAC,MAAM,MAA2B,QAAQ,EAAE;gCAC1D;4BACF,OACK;gCACH,QAAQ,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC;4BAC5C;wBACF;oBACF,OACK;wBACH,QAAQ,kBAAkB,GAAG;4BAC3B,WAAW;gCAAC;oCAAE,GAAG,IAAI;oCAAE,SAAS;gCAAG;6BAAE;wBACvC;oBACF;oBAEA,aAAa,eAAe,CAAE,OAAO,CAAC,iBAAiB,GAAG;wBACxD,GAAG,OAAO;oBACZ;oBAEA,sBAAsB;wBACpB;wBACA;wBACA;wBACA,UAAU;oBACZ;gBACF;YACF;QACF;QACF,OAAO;IACT,GAAG;QACD;QACA,SAAS,MAAM;QACf;QACA,QAAQ;QACR;QACA;QACA;QACA;QACA;QACA,OAAO,KAAK;QACZ,OAAO,KAAK;QACZ;QACA;KACD;IAED,MAAM,yBAAyB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe,QAAgB;QACzE,MAAM,mBAAmB,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE;QAC/C,MAAM,iBAAiB,QAAQ,CAAC,MAAM,CAAC,EAAE;QAEzC,mBAAmB,kBAAkB;YACnC,SAAS;QACX;QACA,mBAAmB,gBAAgB;YACjC,SAAS;YACT,YAAY;gBACV,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU;gBAC7B,eAAe;YACjB;QACF;IACF,GAAG;QAAC;QAAU;KAAmB;IAEjC,MAAM,wBAAwB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC,cAAsB,YAAoB,OAAe,QAAgB;QAClH,MAAM,mBAAmB,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE;QAC/C,MAAM,iBAAiB,QAAQ,CAAC,MAAM,CAAC,EAAE;QAEzC,mBAAmB,kBAAkB;YACnC,SAAS;QACX;QAEA,mBAAmB,gBAAgB;YACjC,SAAS,QAAQ,CAAC,MAAM,CAAC,OAAO;YAChC,YAAY;gBACV,IAAI;gBACJ;gBACA,eAAe;oBACb,SAAS;oBACT,SAAS;wBACP,IAAI;wBACJ,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;QACF;IACF,GAAG;QAAC;QAAU;KAAmB;IAEjC,MAAM,0BAA0B,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3C,MAAM,iBAAiB,QAAQ,CAAC,MAAM,CAAC,EAAE;QAEzC,mBAAmB,gBAAgB;YACjC,SAAS,QAAQ,CAAC,MAAM,CAAC,OAAO;YAChC,YAAY;gBACV,GAAI,QAAQ,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC;gBACpC,IAAI;YACN;QACF;IACF,GAAG;QAAC;QAAU;KAAmB;IAEjC,MAAM,eAAe,CAAC,QAAgB;QACpC,IAAI,cAAc,OAAO,KAAK,QAC5B;QAEF,uBAAuB;QACvB,yEAAyE;QACzE,kCAAkC;QAClC,mCAAmC;QACnC,mDAAmD;QACnD,0CAA0C;QAC1C,WAAW;QACX,IAAI;QAEJ,cAAc,OAAO,GAAG;QACxB,mCAAmC;QACnC,4CAA4C;QAC5C,2BAA2B;QAC3B,wBAAwB;QACxB,uBAAuB;QACvB,oBAAoB;QACpB,kBAAkB;QAClB,oBAAoB;QACpB,oDAAoD;QACpD,2BAA2B;QAC3B,mCAAmC;QACnC,IAAI;QAEJ,yDAAyD;QACzD,qCAAqC;QACrC,YAAY;eAAI,YAAY,OAAO;SAAC;IACtC;IAEA,MAAM,YAAY,CAAC,MAAc;QAC/B,WAAW,OAAO,GAAG;QACrB,IAAI,MAAM;YACR,YAAY,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM;gBACjC,IAAI,MAAM,YAAY,OAAO,CAAC,MAAM,GAAG,GAAG;oBACxC,YAAY;oBACZ,KAAK,SAAS,GAAG,CAAC,QAAQ,EAAE,MAAM;gBACpC;gBACA,OAAO;YACT;YACA,YAAY,OAAO,GAAG;mBAAI,YAAY,OAAO;aAAC;YAC9C,YAAY;mBAAI,YAAY,OAAO;aAAC;QACtC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,cAAc,OAAO,GAAG;IAC1B;IACA,MAAM,uBAAuB,CAAC;QAC5B,cAAc,OAAO,GAAG,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,MAAM,GAAG;QAC/D,YAAY,OAAO,GAAG;eAAI;SAAS;QACnC,YAAY;eAAI;SAAS;IAC3B;IAEA,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eACF,cAAc,IAAM,wBAAwB;IAChD,GAAG;QAAC;QAAe;QAAuB;KAAc;IAExD,OAAO;QACL;QACA;QACA,gBAAgB,eAAe,OAAO;QACtC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 11182, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/chat-wrapper.tsx"], "sourcesContent": ["import { useCallback, useEffect, useMemo, useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport Image from 'next/image'\nimport Chat from '../chat'\nimport type {\n  ChatConfig,\n  ChatItem,\n  ChatItemInTree,\n  OnSend,\n} from '../types'\nimport { useChat } from '../chat/hooks'\nimport { getLastAnswer, isValidGeneratedAnswer } from '../utils'\nimport Button from '../../button'\nimport { useChatWithHistoryContext } from './context'\nimport { InputVarType } from '@/components/workflow/types'\nimport { TransferMethod } from '@/types/app'\nimport InputsForm from '@/components/base/chat/chat-with-history/inputs-form'\nimport BtnFold from './btn-fold'\nimport styles from './index.module.css'\nimport { EMBED_SOURCE_TYPE } from '@/config'\nimport {\n  fetchInstitutionApi,\n  fetchSuggestedQuestions,\n  getUrl,\n  stopChatMessageResponding,\n} from '@/service/share'\nimport AppIcon from '@/components/base/app-icon'\nimport AnswerIcon from '@/components/base/answer-icon'\nimport SuggestedQuestions from '@/components/base/chat/chat/answer/suggested-questions'\nimport { Markdown } from '@/components/base/markdown'\nimport cn from '@/utils/classnames'\nimport type { FileEntity } from '../../file-uploader/types'\nimport LarkAppLogo from '@/assets/lark-app-logo.svg'\nimport { INTENT_DOCUMENT_ANALYSIS, INTENT_FREE_TALK, INTENT_INSTITUTION, INTENT_MEETING, LLM_QWQ, LOCATION_ZJ, NEED_CREATE_MEETING } from '@/components/base/chat/chat/u-const'\nconst NewChatIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" className={className}>\n    <defs>\n      <linearGradient x1=\"50%\" y1=\"0%\" x2=\"50%\" y2=\"100%\" id=\"ca3toeybn__hi9n9hsm3a\">\n        <stop stop-color=\"#40A4FF\" offset=\"0%\" />\n        <stop stop-color=\"#1E6AFF\" offset=\"100%\" />\n      </linearGradient>\n    </defs>\n    <path d=\"M9,12 L9,9 M9,9 L9,6 M9,9 L6,9 M9,9 L12,9 M9.0001,18 C7.365,18 5.83174,17.5639 4.51025,16.8018 C4.3797,16.7265 4.31434,16.6888 4.25293,16.6719 C4.19578,16.6561 4.14475,16.6507 4.08559,16.6548 C4.02253,16.6591 3.9573,16.6808 3.82759,16.7241 L1.51807,17.4939 L1.51625,17.4947 C1.02892,17.6572 0.7848,17.7386 0.62256,17.6807 C0.4812,17.6303 0.36979,17.5187 0.31938,17.3774 C0.26157,17.2152 0.34268,16.9719 0.50489,16.4853 L0.50586,16.4823 L1.27468,14.1758 L1.27651,14.171 C1.31936,14.0424 1.34106,13.9773 1.34535,13.9146 C1.3494,13.8554 1.34401,13.804 1.32821,13.7469 C1.31146,13.6863 1.27448,13.6221 1.20114,13.495 L1.19819,13.4899 C0.43604,12.1684 0,10.6351 0,9 C0,4.02944 4.02944,0 9,0 C13.9706,0 18,4.02944 18,9 C18,13.9706 13.9707,18 9.0001,18 Z\" transform=\"translate(1 1)\" stroke=\"url(#ca3toeybn__hi9n9hsm3a)\" stroke-width=\"1.6\" fill=\"none\" fill-rule=\"evenodd\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n  </svg>\n)\n\nconst ChatWrapper = () => {\n  const { t } = useTranslation()\n  const {\n    appParams,\n    appPrevChatTree,\n    currentConversationId,\n    currentConversationItem,\n    currentConversationInputs,\n    inputsForms,\n    newConversationInputsRef,\n    handleNewConversation,\n    handleNewConversationCompleted,\n    isMobile,\n    isInstalledApp,\n    appId,\n    appMeta,\n    handleFeedback,\n    currentChatInstanceRef,\n    appData,\n    themeBuilder,\n    sidebarCollapseState,\n    clearChatList,\n    setClearChatList,\n    setIsResponding,\n    embedSource,\n    isFold,\n    setIsFold,\n    larkInfo,\n  } = useChatWithHistoryContext()\n  let {\n    newConversationInputs,\n  } = useChatWithHistoryContext()\n  const aiToolbarsConfig = appData?.site?.extension_data ? JSON.parse(appData.site.extension_data) : {}\n  // 根据selected过滤出已选数据 没有selected的是以前老数据 !Object.hasOwn(item, 'selected')判断\n  const aiToolbars = aiToolbarsConfig?.aiToolbars?.filter((item: any) => !Object.hasOwn(item, 'selected') || (Object.hasOwn(item, 'selected') && item?.selected))\n  if (!newConversationInputs?.intent && !!embedSource)\n    newConversationInputs = Object.assign(newConversationInputs, { intent: INTENT_FREE_TALK })\n\n  const appConfig = useMemo(() => {\n    const config = appParams || {}\n\n    return {\n      ...config,\n      file_upload: {\n        ...(config as any).file_upload,\n        fileUploadConfig: (config as any).system_parameters,\n      },\n      embedSource,\n      supportFeedback: true,\n      opening_statement: currentConversationId ? currentConversationItem?.introduction : (config as any).opening_statement,\n    } as ChatConfig\n  }, [appParams, currentConversationItem?.introduction, currentConversationId, embedSource])\n  const {\n    chatList,\n    setTargetMessageId,\n    handleSend,\n    handleStop,\n    isResponding: respondingState,\n    suggestedQuestions,\n    currentIntent,\n    currentLlm,\n    updateIntent,\n    updateLlm,\n    identifyingIntent,\n    updateIntentSilent,\n    updateLastChatIntent,\n  } = useChat(\n    appConfig,\n    {\n      inputs: (currentConversationId ? currentConversationInputs : newConversationInputs) as any,\n      inputsForm: inputsForms,\n    },\n    appPrevChatTree,\n    taskId => stopChatMessageResponding('', taskId, isInstalledApp, appId),\n    clearChatList,\n    (bool) => {\n      setClearChatList(bool)\n      // if (!chatList.length && embedSource)\n      //   updateIntent?.(LLM_DEFAULT_INTENT.label, LLM_DEFAULT_INTENT.prompt)\n    },\n    aiToolbars,\n  )\n  const inputsFormValue = currentConversationId ? currentConversationInputs : newConversationInputsRef?.current\n  const inputDisabled = useMemo(() => {\n    let hasEmptyInput = ''\n    let fileIsUploading = false\n    const requiredVars = inputsForms.filter(({ required }) => required)\n\n    if(!embedSource) {\n      if (requiredVars.length) {\n        requiredVars.forEach(({ variable, label, type }) => {\n          if (hasEmptyInput)\n            return\n\n          if (fileIsUploading)\n            return\n\n          if (!inputsFormValue?.[variable])\n            hasEmptyInput = label as string\n\n          if ((type === InputVarType.singleFile || type === InputVarType.multiFiles) && inputsFormValue?.[variable]) {\n            const files = inputsFormValue[variable]\n            if (Array.isArray(files))\n              fileIsUploading = files.find(item => item.transferMethod === TransferMethod.local_file && !item.uploadedId)\n            else\n              fileIsUploading = files.transferMethod === TransferMethod.local_file && !files.uploadedId\n          }\n        })\n      }\n      if (hasEmptyInput)\n        return true\n    }\n\n    if (fileIsUploading)\n      return true\n    return false\n  }, [inputsFormValue, inputsForms])\n\n  useEffect(() => {\n    if (currentChatInstanceRef.current)\n      currentChatInstanceRef.current.handleStop = handleStop\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [])\n\n  useEffect(() => {\n    setIsResponding(respondingState)\n  }, [respondingState, setIsResponding])\n\n  const doSend: OnSend = useCallback((message, files, isRegenerate = false, parentAnswer: ChatItem | null = null, extract = null) => {\n    const data: any = {\n      query: message,\n      files,\n      inputs: currentConversationId ? currentConversationInputs : newConversationInputs,\n      conversation_id: currentConversationId,\n      parent_message_id: (isRegenerate ? parentAnswer?.id : getLastAnswer(chatList)?.id) || null,\n      user: larkInfo?.user_id,\n      embedSource,\n    }\n\n    if (embedSource) {\n      data.inputs = {}\n\n      currentIntent.current && (data.inputs.intent = currentIntent.current)\n      currentLlm.current && (data.inputs.llm = currentLlm.current)\n\n      // 文档分析固定传qwq模型\n      currentIntent.current === INTENT_DOCUMENT_ANALYSIS && (data.inputs.llm = LLM_QWQ)\n\n      // 提交日程\n      if (extract && extract.event === 'create_calendar_submit') {\n        data.inputs.schedule_info = JSON.stringify(\n          extract,\n        )\n      }\n      // 日程同步创建会议\n      if (extract && extract.event === NEED_CREATE_MEETING) {\n        const createMeetingData = {\n          title: extract?.data?.subject,\n          meeting_start: extract?.data?.start_time,\n          meeting_end: extract?.data?.end_time,\n          create_meeting_submit: 'create_meeting_success',\n          attendees_ids: extract?.data?.attendees,\n          attendees_names: extract?.data?.attendees_names,\n          meeting_location: LOCATION_ZJ,\n        }\n        data.inputs.intent = INTENT_MEETING\n        data.inputs.meeting_info = JSON.stringify(createMeetingData)\n      }\n      // 提交会议\n      if (extract && extract.create_meeting_submit === 'create_meeting_success') {\n        data.inputs.meeting_info = JSON.stringify(\n          extract,\n        )\n      }\n    }\n\n    // 调用意图识别\n    // if (!currentIntent.current && embedSource) {\n    //   identifyingIntent(message, (msg) => {\n    //     doSend(msg)\n    //   })\n    // }\n    // else {\n    handleSend(\n      getUrl('chat-messages', isInstalledApp, appId || ''),\n      data,\n      {\n        onGetSuggestedQuestions: (responseItemId) => {\n          if (currentIntent.current?.includes(INTENT_INSTITUTION))\n            return fetchInstitutionApi({ question: message })\n          else\n            return fetchSuggestedQuestions(responseItemId, isInstalledApp, appId)\n        },\n        onConversationComplete: currentConversationId ? undefined : handleNewConversationCompleted,\n        isPublicAPI: !isInstalledApp,\n        isIntentRecognition: extract?.isIntentRecognition,\n      },\n    )\n  }, [chatList, handleNewConversationCompleted, handleSend, currentConversationId, currentConversationInputs, newConversationInputs, isInstalledApp, appId, embedSource, currentIntent.current])\n\n  const doRegenerate = useCallback((chatItem: ChatItemInTree, editedQuestion?: { message: string, files?: FileEntity[] }) => {\n    const question = editedQuestion ? chatItem : chatList.find(item => item.id === chatItem.parentMessageId)!\n    const parentAnswer = chatList.find(item => item.id === question.parentMessageId)\n    doSend(editedQuestion ? editedQuestion.message : question.content,\n      editedQuestion ? editedQuestion.files : question.message_files,\n      true,\n      isValidGeneratedAnswer(parentAnswer) ? parentAnswer : null,\n    )\n  }, [chatList, doSend])\n\n  const messageList = useMemo(() => {\n    if (currentConversationId)\n      return chatList\n    return chatList.filter(item => !item.isOpeningStatement)\n  }, [chatList, currentConversationId])\n\n  const [collapsed, setCollapsed] = useState(!!currentConversationId)\n\n  const ShowLogoContent = (url: string) => {\n    if (!url)\n      return embedSource === EMBED_SOURCE_TYPE.FS ? <Image src={LarkAppLogo} className='h-[34px]' alt='logo'/> : <div className='h-[34px]'></div>\n    else\n      return <img src={url} className='h-[34px]' alt=\"logo\" />\n  }\n\n  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}\n  const aiTopLogoUrl = chatPageConfigData?.aiTopLogoUrl || ''\n  const pageBgColorFrom = chatPageConfigData?.aiPageBgGradientFrom || 'rgb(215, 218, 252)'\n  const pageBgColorTo = chatPageConfigData?.aiPageBgGradientTo || 'rgb(250, 250, 253)'\n  const chatNode = useMemo(() => {\n    if (embedSource && !isMobile) {\n      return (\n        <div className={`relative sticky top-0 z-10 flex shrink-0 overflow-hidden px-[15.8vw] py-[16px] ${styles.navBarBg}`}\n             style={{\n               // 注入 CSS 变量\n               '--start-color': pageBgColorFrom,\n               '--end-color': pageBgColorTo,\n             }}>\n          {isFold && <BtnFold className=\"absolute left-[10px] top-1/2 mr-[10px] flex -translate-y-1/2 items-center\" isFold={isFold} onClick={() => setIsFold?.(!isFold)} />}\n\n          <Button\n            variant='secondary-accent'\n            className={`absolute ${isFold ? 'left-[128px]' : 'left-[12px]'}  z-11 top-1/2 h-[40px] w-[123px] -translate-y-1/2 justify-start rounded-[20px] border-[#356CFF] text-[#434B5B]`}\n            onClick={handleNewConversation}>\n            <NewChatIcon className=\"mr-[8px] w-[18px]\" />\n            开始新对话\n          </Button>\n          <div className='flex flex-1 items-center justify-center'>\n            <div className=\"flex items-center\">\n              {ShowLogoContent(aiTopLogoUrl)}\n            </div>\n          </div>\n        </div>\n      )\n    }\n\n    if (!inputsForms.length || embedSource)\n      return null\n    if (isMobile) {\n      if (!currentConversationId)\n        return <InputsForm collapsed={collapsed} setCollapsed={setCollapsed} />\n      return null\n    }\n    else {\n      return <InputsForm collapsed={collapsed} setCollapsed={setCollapsed} />\n    }\n  }, [inputsForms.length, isMobile, currentConversationId, collapsed, isFold])\n\n  const welcome = useMemo(() => {\n    const welcomeMessage = chatList.find(item => item.isOpeningStatement)\n    if (respondingState)\n      return null\n    if (currentConversationId)\n      return null\n    if (!welcomeMessage)\n      return null\n    if (!collapsed && inputsForms.length > 0)\n      return null\n    if (welcomeMessage.suggestedQuestions && welcomeMessage.suggestedQuestions?.length > 0) {\n      return (\n        <div className='flex min-h-[50vh] items-center justify-center px-4 py-12'>\n          <div className='flex max-w-[720px] grow gap-4'>\n            <AppIcon\n              size='xl'\n              iconType={appData?.site.icon_type}\n              icon={appData?.site.icon}\n              background={appData?.site.icon_background}\n              imageUrl={appData?.site.icon_url}\n            />\n            <div className='w-0 grow'>\n              <div className='body-lg-regular grow rounded-2xl bg-chat-bubble-bg px-4 py-3 text-text-primary'>\n                <Markdown content={welcomeMessage.content} />\n                <SuggestedQuestions item={welcomeMessage} />\n              </div>\n            </div>\n          </div>\n        </div>\n      )\n    }\n    return (\n      <div className={cn('flex h-[50vh] flex-col items-center justify-center gap-3 py-12')}>\n        <AppIcon\n          size='xl'\n          iconType={appData?.site.icon_type}\n          icon={appData?.site.icon}\n          background={appData?.site.icon_background}\n          imageUrl={appData?.site.icon_url}\n        />\n        <div className='max-w-[768px] px-4'>\n          <Markdown className='!body-2xl-regular !text-text-tertiary' content={welcomeMessage.content} />\n        </div>\n      </div>\n    )\n  }, [appData?.site.icon, appData?.site.icon_background, appData?.site.icon_type, appData?.site.icon_url, chatList, collapsed, currentConversationId, inputsForms.length, respondingState, embedSource, isFold])\n\n  const answerIcon = (appData?.site && appData.site.use_icon_as_answer_icon)\n    ? <AnswerIcon\n      iconType={appData.site.icon_type}\n      icon={appData.site.icon}\n      background={embedSource ? 'transparent' : appData.site.icon_background}\n      imageUrl={appData.site.icon_url}\n    />\n    : null\n\n  return (\n    <div\n      className='h-full overflow-hidden bg-chatbot-bg'\n    >\n      <Chat\n        appData={appData}\n        config={appConfig}\n        chatList={messageList}\n        isResponding={respondingState}\n        chatContainerInnerClassName={`mx-auto pt-6 w-full max-w-[768px] ${embedSource && 'px-[15.8vw] max-w-none'} ${isMobile && 'px-4'}`}\n        chatFooterClassName='pb-4'\n        chatFooterInnerClassName={`mx-auto w-full max-w-[768px] ${isMobile ? 'px-2' : 'px-4'} ${embedSource && 'max-w-none px-0'}`}\n        onSend={doSend}\n        inputs={currentConversationId ? currentConversationInputs as any : newConversationInputs}\n        inputsForm={inputsForms}\n        onRegenerate={doRegenerate}\n        onStopResponding={handleStop}\n        chatNode={\n          <>\n            {chatNode}\n            {welcome}\n          </>\n        }\n        allToolIcons={appMeta?.tool_icons || {}}\n        onFeedback={handleFeedback}\n        suggestedQuestions={suggestedQuestions}\n        answerIcon={answerIcon}\n        hideProcessDetail\n        themeBuilder={themeBuilder}\n        switchSibling={siblingMessageId => setTargetMessageId(siblingMessageId)}\n        inputDisabled={inputDisabled}\n        isMobile={isMobile}\n        sidebarCollapseState={sidebarCollapseState}\n        embedSource={embedSource}\n        larkInfo={larkInfo}\n        currentIntent={currentIntent}\n        updateIntent={updateIntent}\n        updateLlm={updateLlm}\n        updateIntentSilent={updateIntentSilent}\n        updateLastChatIntent={updateLastChatIntent}\n      />\n    </div>\n  )\n}\n\nexport default ChatWrapper\n"], "names": [], "mappings": ";;;;AAAA;;;;;;AAEA;AACA;AAOA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAIA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,MAAM,cAAc,CAAC,EAAE,SAAS,EAAyB,iBACvD,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;;0BACrE,qZAAC;0BACC,cAAA,qZAAC;oBAAe,IAAG;oBAAM,IAAG;oBAAK,IAAG;oBAAM,IAAG;oBAAO,IAAG;;sCACrD,qZAAC;4BAAK,cAAW;4BAAU,QAAO;;;;;;sCAClC,qZAAC;4BAAK,cAAW;4BAAU,QAAO;;;;;;;;;;;;;;;;;0BAGtC,qZAAC;gBAAK,GAAE;gBAA8uB,WAAU;gBAAiB,QAAO;gBAA8B,gBAAa;gBAAM,MAAK;gBAAO,aAAU;gBAAU,kBAAe;gBAAQ,mBAAgB;;;;;;;;;;;;AAIp5B,MAAM,cAAc;IAClB,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,EACJ,SAAS,EACT,eAAe,EACf,qBAAqB,EACrB,uBAAuB,EACvB,yBAAyB,EACzB,WAAW,EACX,wBAAwB,EACxB,qBAAqB,EACrB,8BAA8B,EAC9B,QAAQ,EACR,cAAc,EACd,KAAK,EACL,OAAO,EACP,cAAc,EACd,sBAAsB,EACtB,OAAO,EACP,YAAY,EACZ,oBAAoB,EACpB,aAAa,EACb,gBAAgB,EAChB,eAAe,EACf,WAAW,EACX,MAAM,EACN,SAAS,EACT,QAAQ,EACT,GAAG,CAAA,GAAA,wKAAA,CAAA,4BAAyB,AAAD;IAC5B,IAAI,EACF,qBAAqB,EACtB,GAAG,CAAA,GAAA,wKAAA,CAAA,4BAAyB,AAAD;IAC5B,MAAM,mBAAmB,SAAS,MAAM,iBAAiB,KAAK,KAAK,CAAC,QAAQ,IAAI,CAAC,cAAc,IAAI,CAAC;IACpG,yEAAyE;IACzE,MAAM,aAAa,kBAAkB,YAAY,OAAO,CAAC,OAAc,CAAC,OAAO,MAAM,CAAC,MAAM,eAAgB,OAAO,MAAM,CAAC,MAAM,eAAe,MAAM;IACrJ,IAAI,CAAC,uBAAuB,UAAU,CAAC,CAAC,aACtC,wBAAwB,OAAO,MAAM,CAAC,uBAAuB;QAAE,QAAQ;IAAiB;IAE1F,MAAM,YAAY,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE;QACxB,MAAM,SAAS,aAAa,CAAC;QAE7B,OAAO;YACL,GAAG,MAAM;YACT,aAAa;gBACX,GAAG,AAAC,OAAe,WAAW;gBAC9B,kBAAkB,AAAC,OAAe,iBAAiB;YACrD;YACA;YACA,iBAAiB;YACjB,mBAAmB,wBAAwB,yBAAyB,eAAe,AAAC,OAAe,iBAAiB;QACtH;IACF,GAAG;QAAC;QAAW,yBAAyB;QAAc;QAAuB;KAAY;IACzF,MAAM,EACJ,QAAQ,EACR,kBAAkB,EAClB,UAAU,EACV,UAAU,EACV,cAAc,eAAe,EAC7B,kBAAkB,EAClB,aAAa,EACb,UAAU,EACV,YAAY,EACZ,SAAS,EACT,iBAAiB,EACjB,kBAAkB,EAClB,oBAAoB,EACrB,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD,EACR,WACA;QACE,QAAS,wBAAwB,4BAA4B;QAC7D,YAAY;IACd,GACA,iBACA,CAAA,SAAU,0BAA0B,IAAI,QAAQ,gBAAgB,QAChE,eACA,CAAC;QACC,iBAAiB;IACjB,uCAAuC;IACvC,wEAAwE;IAC1E,GACA;IAEF,MAAM,kBAAkB,wBAAwB,4BAA4B,0BAA0B;IACtG,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,IAAI,gBAAgB;QACpB,IAAI,kBAAkB;QACtB,MAAM,eAAe,YAAY,MAAM,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAK;QAE1D,IAAG,CAAC,aAAa;YACf,IAAI,aAAa,MAAM,EAAE;gBACvB,aAAa,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;oBAC7C,IAAI,eACF;oBAEF,IAAI,iBACF;oBAEF,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAC9B,gBAAgB;oBAElB,IAAI,CAAC,SAAS,aAAa,UAAU,IAAI,SAAS,aAAa,UAAU,KAAK,iBAAiB,CAAC,SAAS,EAAE;wBACzG,MAAM,QAAQ,eAAe,CAAC,SAAS;wBACvC,IAAI,MAAM,OAAO,CAAC,QAChB,kBAAkB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,cAAc,KAAK,eAAe,UAAU,IAAI,CAAC,KAAK,UAAU;6BAE1G,kBAAkB,MAAM,cAAc,KAAK,eAAe,UAAU,IAAI,CAAC,MAAM,UAAU;oBAC7F;gBACF;YACF;YACA,IAAI,eACF,OAAO;QACX;QAEA,IAAI,iBACF,OAAO;QACT,OAAO;IACT,GAAG;QAAC;QAAiB;KAAY;IAEjC,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,uBAAuB,OAAO,EAChC,uBAAuB,OAAO,CAAC,UAAU,GAAG;IAC9C,uDAAuD;IACzD,GAAG,EAAE;IAEL,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;IAClB,GAAG;QAAC;QAAiB;KAAgB;IAErC,MAAM,SAAiB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC,SAAS,OAAO,eAAe,KAAK,EAAE,eAAgC,IAAI,EAAE,UAAU,IAAI;QAC5H,MAAM,OAAY;YAChB,OAAO;YACP;YACA,QAAQ,wBAAwB,4BAA4B;YAC5D,iBAAiB;YACjB,mBAAmB,CAAC,eAAe,cAAc,KAAK,CAAA,GAAA,0IAAA,CAAA,gBAAa,AAAD,EAAE,WAAW,EAAE,KAAK;YACtF,MAAM,UAAU;YAChB;QACF;QAEA,IAAI,aAAa;YACf,KAAK,MAAM,GAAG,CAAC;YAEf,cAAc,OAAO,IAAI,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,cAAc,OAAO;YACpE,WAAW,OAAO,IAAI,CAAC,KAAK,MAAM,CAAC,GAAG,GAAG,WAAW,OAAO;YAE3D,eAAe;YACf,cAAc,OAAO,KAAK,4BAA4B,CAAC,KAAK,MAAM,CAAC,GAAG,GAAG,OAAO;YAEhF,OAAO;YACP,IAAI,WAAW,QAAQ,KAAK,KAAK,0BAA0B;gBACzD,KAAK,MAAM,CAAC,aAAa,GAAG,KAAK,SAAS,CACxC;YAEJ;YACA,WAAW;YACX,IAAI,WAAW,QAAQ,KAAK,KAAK,qBAAqB;gBACpD,MAAM,oBAAoB;oBACxB,OAAO,SAAS,MAAM;oBACtB,eAAe,SAAS,MAAM;oBAC9B,aAAa,SAAS,MAAM;oBAC5B,uBAAuB;oBACvB,eAAe,SAAS,MAAM;oBAC9B,iBAAiB,SAAS,MAAM;oBAChC,kBAAkB;gBACpB;gBACA,KAAK,MAAM,CAAC,MAAM,GAAG;gBACrB,KAAK,MAAM,CAAC,YAAY,GAAG,KAAK,SAAS,CAAC;YAC5C;YACA,OAAO;YACP,IAAI,WAAW,QAAQ,qBAAqB,KAAK,0BAA0B;gBACzE,KAAK,MAAM,CAAC,YAAY,GAAG,KAAK,SAAS,CACvC;YAEJ;QACF;QAEA,SAAS;QACT,+CAA+C;QAC/C,0CAA0C;QAC1C,kBAAkB;QAClB,OAAO;QACP,IAAI;QACJ,SAAS;QACT,WACE,OAAO,iBAAiB,gBAAgB,SAAS,KACjD,MACA;YACE,yBAAyB,CAAC;gBACxB,IAAI,cAAc,OAAO,EAAE,SAAS,qBAClC,OAAO,oBAAoB;oBAAE,UAAU;gBAAQ;qBAE/C,OAAO,wBAAwB,gBAAgB,gBAAgB;YACnE;YACA,wBAAwB,wBAAwB,YAAY;YAC5D,aAAa,CAAC;YACd,qBAAqB,SAAS;QAChC;IAEJ,GAAG;QAAC;QAAU;QAAgC;QAAY;QAAuB;QAA2B;QAAuB;QAAgB;QAAO;QAAa,cAAc,OAAO;KAAC;IAE7L,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAA0B;QAC1D,MAAM,WAAW,iBAAiB,WAAW,SAAS,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,eAAe;QACvG,MAAM,eAAe,SAAS,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,eAAe;QAC/E,OAAO,iBAAiB,eAAe,OAAO,GAAG,SAAS,OAAO,EAC/D,iBAAiB,eAAe,KAAK,GAAG,SAAS,aAAa,EAC9D,MACA,CAAA,GAAA,0IAAA,CAAA,yBAAsB,AAAD,EAAE,gBAAgB,eAAe;IAE1D,GAAG;QAAC;QAAU;KAAO;IAErB,MAAM,cAAc,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,IAAI,uBACF,OAAO;QACT,OAAO,SAAS,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,kBAAkB;IACzD,GAAG;QAAC;QAAU;KAAsB;IAEpC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,CAAC;IAE7C,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,KACH,OAAO,gBAAgB,kBAAkB,EAAE,iBAAG,qZAAC,oSAAA,CAAA,UAAK;YAAC,KAAK;YAAa,WAAU;YAAW,KAAI;;;;;iCAAW,qZAAC;YAAI,WAAU;;;;;;aAE1H,qBAAO,qZAAC;YAAI,KAAK;YAAK,WAAU;YAAW,KAAI;;;;;;IACnD;IAEA,MAAM,qBAAqB,SAAS,MAAM,iBAAiB,KAAK,KAAK,CAAC,SAAS,MAAM,kBAAkB,CAAC;IACxG,MAAM,eAAe,oBAAoB,gBAAgB;IACzD,MAAM,kBAAkB,oBAAoB,wBAAwB;IACpE,MAAM,gBAAgB,oBAAoB,sBAAsB;IAChE,MAAM,WAAW,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE;QACvB,IAAI,eAAe,CAAC,UAAU;YAC5B,qBACE,qZAAC;gBAAI,WAAW,CAAC,+EAA+E,EAAE,iLAAA,CAAA,UAAM,CAAC,QAAQ,EAAE;gBAC9G,OAAO;oBACL,YAAY;oBACZ,iBAAiB;oBACjB,eAAe;gBACjB;;oBACF,wBAAU,qZAAC,4KAAA,CAAA,UAAO;wBAAC,WAAU;wBAA4E,QAAQ;wBAAQ,SAAS,IAAM,YAAY,CAAC;;;;;;kCAEtJ,qZAAC,6IAAA,CAAA,UAAM;wBACL,SAAQ;wBACR,WAAW,CAAC,SAAS,EAAE,SAAS,iBAAiB,cAAc,+GAA+G,CAAC;wBAC/K,SAAS;;0CACT,qZAAC;gCAAY,WAAU;;;;;;4BAAsB;;;;;;;kCAG/C,qZAAC;wBAAI,WAAU;kCACb,cAAA,qZAAC;4BAAI,WAAU;sCACZ,gBAAgB;;;;;;;;;;;;;;;;;QAK3B;QAEA,IAAI,CAAC,YAAY,MAAM,IAAI,aACzB,OAAO;QACT,IAAI,UAAU;YACZ,IAAI,CAAC,uBACH,qBAAO,qZAAC;gBAAW,WAAW;gBAAW,cAAc;;;;;;YACzD,OAAO;QACT,OACK;YACH,qBAAO,qZAAC;gBAAW,WAAW;gBAAW,cAAc;;;;;;QACzD;IACF,GAAG;QAAC,YAAY,MAAM;QAAE;QAAU;QAAuB;QAAW;KAAO;IAE3E,MAAM,UAAU,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE;QACtB,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAA,OAAQ,KAAK,kBAAkB;QACpE,IAAI,iBACF,OAAO;QACT,IAAI,uBACF,OAAO;QACT,IAAI,CAAC,gBACH,OAAO;QACT,IAAI,CAAC,aAAa,YAAY,MAAM,GAAG,GACrC,OAAO;QACT,IAAI,eAAe,kBAAkB,IAAI,eAAe,kBAAkB,EAAE,SAAS,GAAG;YACtF,qBACE,qZAAC;gBAAI,WAAU;0BACb,cAAA,qZAAC;oBAAI,WAAU;;sCACb,qZAAC;4BACC,MAAK;4BACL,UAAU,SAAS,KAAK;4BACxB,MAAM,SAAS,KAAK;4BACpB,YAAY,SAAS,KAAK;4BAC1B,UAAU,SAAS,KAAK;;;;;;sCAE1B,qZAAC;4BAAI,WAAU;sCACb,cAAA,qZAAC;gCAAI,WAAU;;kDACb,qZAAC;wCAAS,SAAS,eAAe,OAAO;;;;;;kDACzC,qZAAC;wCAAmB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAMtC;QACA,qBACE,qZAAC;YAAI,WAAW,GAAG;;8BACjB,qZAAC;oBACC,MAAK;oBACL,UAAU,SAAS,KAAK;oBACxB,MAAM,SAAS,KAAK;oBACpB,YAAY,SAAS,KAAK;oBAC1B,UAAU,SAAS,KAAK;;;;;;8BAE1B,qZAAC;oBAAI,WAAU;8BACb,cAAA,qZAAC;wBAAS,WAAU;wBAAwC,SAAS,eAAe,OAAO;;;;;;;;;;;;;;;;;IAInG,GAAG;QAAC,SAAS,KAAK;QAAM,SAAS,KAAK;QAAiB,SAAS,KAAK;QAAW,SAAS,KAAK;QAAU;QAAU;QAAW;QAAuB,YAAY,MAAM;QAAE;QAAiB;QAAa;KAAO;IAE7M,MAAM,aAAa,AAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,uBAAuB,iBACrE,qZAAC;QACD,UAAU,QAAQ,IAAI,CAAC,SAAS;QAChC,MAAM,QAAQ,IAAI,CAAC,IAAI;QACvB,YAAY,cAAc,gBAAgB,QAAQ,IAAI,CAAC,eAAe;QACtE,UAAU,QAAQ,IAAI,CAAC,QAAQ;;;;;eAE/B;IAEJ,qBACE,qZAAC;QACC,WAAU;kBAEV,cAAA,qZAAC,mJAAA,CAAA,UAAI;YACH,SAAS;YACT,QAAQ;YACR,UAAU;YACV,cAAc;YACd,6BAA6B,CAAC,kCAAkC,EAAE,eAAe,yBAAyB,CAAC,EAAE,YAAY,QAAQ;YACjI,qBAAoB;YACpB,0BAA0B,CAAC,6BAA6B,EAAE,WAAW,SAAS,OAAO,CAAC,EAAE,eAAe,mBAAmB;YAC1H,QAAQ;YACR,QAAQ,wBAAwB,4BAAmC;YACnE,YAAY;YACZ,cAAc;YACd,kBAAkB;YAClB,wBACE;;oBACG;oBACA;;;YAGL,cAAc,SAAS,cAAc,CAAC;YACtC,YAAY;YACZ,oBAAoB;YACpB,YAAY;YACZ,iBAAiB;YACjB,cAAc;YACd,eAAe,CAAA,mBAAoB,mBAAmB;YACtD,eAAe;YACf,UAAU;YACV,sBAAsB;YACtB,aAAa;YACb,UAAU;YACV,eAAe;YACf,cAAc;YACd,WAAW;YACX,oBAAoB;YACpB,sBAAsB;;;;;;;;;;;AAI9B;uCAEe", "debugId": null}}, {"offset": {"line": 11797, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/index.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport {\n  useState,\n} from 'react'\nimport Sidebar from './sidebar'\nimport Header from './header'\nimport HeaderInMobile from './header-in-mobile'\nimport ChatWrapper from './chat-wrapper'\nimport styles from './index.module.css'\n// import Loading from '@/components/base/loading'\nimport cn from 'classnames'\nimport RightSidebar from './right-sidebar'\n\ntype ChatWithHistoryProps = {\n  className?: string\n}\n\nconst ChatWithHistory: FC<ChatWithHistoryProps> = ({\n  className,\n}) => {\n  const [showSidePanel, setShowSidePanel] = useState(false)\n  const isMobile = false\n  const embedSource = true\n  const isSidebarCollapsed = false\n  const isFold = false\n  const appChatListDataLoading = false\n  const chatShouldReloadKey = false\n  const refreshRenderKey = false\n  const rightSideInfo = false\n\n  return (\n    <div className={cn(\n      'flex h-full bg-background-default-burn',\n      isMobile && 'flex-col',\n      className,\n      embedSource && isMobile && '!bg-[#f5f6f8]',\n      embedSource && isMobile && styles.bg,\n    )}>\n      {(!isMobile && !isFold) && (\n        <div className={cn(\n          'flex w-[236px] flex-col pr-0 transition-all duration-200 ease-in-out',\n          isSidebarCollapsed && 'w-0 overflow-hidden !p-0',\n          embedSource && !isMobile && '!bg-white',\n        )}>\n          <Sidebar />\n        </div>\n      )}\n      {isMobile && (\n        <HeaderInMobile />\n      )}\n      <div className={cn('relative grow p-2', embedSource && 'p-0', isMobile && 'h-[calc(100%_-_56px)] p-0')}>\n        {isSidebarCollapsed && (\n          <div\n            className={cn(\n              'absolute top-0 z-20 flex h-full w-[256px] flex-col p-2 transition-all duration-500 ease-in-out',\n              showSidePanel ? 'left-0' : 'left-[-248px]',\n            )}\n            onMouseEnter={() => setShowSidePanel(true)}\n            onMouseLeave={() => setShowSidePanel(false)}\n          >\n            <Sidebar isPanel />\n          </div>\n        )}\n        <div className={cn('flex h-full flex-col overflow-hidden border-[0,5px] border-components-panel-border-subtle bg-chatbot-bg', isMobile ? 'rounded-t-2xl' : 'rounded-2xl', embedSource && 'rounded-none')}>\n          {!isMobile && !embedSource && <Header />}\n          {/* {appChatListDataLoading && (\n            <Loading />\n          )} */}\n          {!appChatListDataLoading && (\n            <ChatWrapper key={chatShouldReloadKey || refreshRenderKey} />\n          )}\n        </div>\n      </div>\n      <RightSidebar isMobile={isMobile} visible={Boolean(rightSideInfo)}/>\n    </div>\n  )\n}\n\nexport default ChatWithHistory\n"], "names": [], "mappings": ";;;;AACA;AAGA;AACA;AACA;AACA;AACA;AACA,sDAAsD;AACtD;AACA;;;;;;;;;;AAMA,MAAM,kBAA4C,CAAC,EACjD,SAAS,EACV;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW;IACjB,MAAM,cAAc;IACpB,MAAM,qBAAqB;IAC3B,MAAM,SAAS;IACf,MAAM,yBAAyB;IAC/B,MAAM,sBAAsB;IAC5B,MAAM,mBAAmB;IACzB,MAAM,gBAAgB;IAEtB,qBACE,qZAAC;QAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EACf,0CACA,YAAY,YACZ,WACA,eAAe,YAAY,iBAC3B,eAAe,YAAY,iLAAA,CAAA,UAAM,CAAC,EAAE;;YAElC,CAAC,YAAY,CAAC,wBACd,qZAAC;gBAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EACf,wEACA,sBAAsB,4BACtB,eAAe,CAAC,YAAY;0BAE5B,cAAA,qZAAC,iLAAA,CAAA,UAAO;;;;;;;;;;YAGX,0BACC,qZAAC,uLAAA,CAAA,UAAc;;;;;0BAEjB,qZAAC;gBAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE,qBAAqB,eAAe,OAAO,YAAY;;oBACvE,oCACC,qZAAC;wBACC,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EACV,kGACA,gBAAgB,WAAW;wBAE7B,cAAc,IAAM,iBAAiB;wBACrC,cAAc,IAAM,iBAAiB;kCAErC,cAAA,qZAAC,iLAAA,CAAA,UAAO;4BAAC,OAAO;;;;;;;;;;;kCAGpB,qZAAC;wBAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE,2GAA2G,6EAA6B,eAAe,eAAe;;4BACtL,CAAC,YAAY,CAAC,6BAAe,qZAAC,gLAAA,CAAA,UAAM;;;;;4BAIpC,CAAC,wCACA,qZAAC,gLAAA,CAAA,UAAW,MAAM,uBAAuB;;;;;;;;;;;;;;;;;0BAI/C,qZAAC,0LAAA,CAAA,UAAY;gBAAC,UAAU;gBAAU,SAAS,QAAQ;;;;;;;;;;;;AAGzD;uCAEe", "debugId": null}}, {"offset": {"line": 11915, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/app/chat/page.tsx"], "sourcesContent": ["'use client'\nimport React from 'react'\n// import Chat<PERSON>ithHistoryWrap from '@/components/base/chat/chat-with-history'\nimport ChatWithHistoryWrap from '@/components/base/chat/chat-with-history/index'\n\nconst Chat = () => {\n  return (\n    <>\n      <ChatWithHistoryWrap />\n    </>\n  )\n}\n\nexport default React.memo(Chat)\n"], "names": [], "mappings": ";;;;AACA;AACA,6EAA6E;AAC7E;AAHA;;;;AAKA,MAAM,OAAO;IACX,qBACE;kBACE,cAAA,qZAAC,sKAAA,CAAA,UAAmB;;;;;;AAG1B;qDAEe,4WAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}]}