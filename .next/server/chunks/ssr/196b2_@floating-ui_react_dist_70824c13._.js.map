{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40floating-ui%2Breact%400.27.13_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40floating-ui/react/dist/floating-ui.react.utils.mjs"], "sourcesContent": ["import { isShadowRoot, isHTMLElement } from '@floating-ui/utils/dom';\nimport * as React from 'react';\nimport { useLayoutEffect } from 'react';\nimport { floor } from '@floating-ui/utils';\nimport { tabbable } from 'tabbable';\n\n// Avoid Chrome DevTools blue warning.\nfunction getPlatform() {\n  const uaData = navigator.userAgentData;\n  if (uaData != null && uaData.platform) {\n    return uaData.platform;\n  }\n  return navigator.platform;\n}\nfunction getUserAgent() {\n  const uaData = navigator.userAgentData;\n  if (uaData && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(_ref => {\n      let {\n        brand,\n        version\n      } = _ref;\n      return brand + \"/\" + version;\n    }).join(' ');\n  }\n  return navigator.userAgent;\n}\nfunction isSafari() {\n  // Chrome DevTools does not complain about navigator.vendor\n  return /apple/i.test(navigator.vendor);\n}\nfunction isAndroid() {\n  const re = /android/i;\n  return re.test(getPlatform()) || re.test(getUserAgent());\n}\nfunction isMac() {\n  return getPlatform().toLowerCase().startsWith('mac') && !navigator.maxTouchPoints;\n}\nfunction isJSDOM() {\n  return getUserAgent().includes('jsdom/');\n}\n\nconst FOCUSABLE_ATTRIBUTE = 'data-floating-ui-focusable';\nconst TYPEABLE_SELECTOR = \"input:not([type='hidden']):not([disabled]),\" + \"[contenteditable]:not([contenteditable='false']),textarea:not([disabled])\";\nconst ARROW_LEFT = 'ArrowLeft';\nconst ARROW_RIGHT = 'ArrowRight';\nconst ARROW_UP = 'ArrowUp';\nconst ARROW_DOWN = 'ArrowDown';\n\nfunction activeElement(doc) {\n  let activeElement = doc.activeElement;\n  while (((_activeElement = activeElement) == null || (_activeElement = _activeElement.shadowRoot) == null ? void 0 : _activeElement.activeElement) != null) {\n    var _activeElement;\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n  return activeElement;\n}\nfunction contains(parent, child) {\n  if (!parent || !child) {\n    return false;\n  }\n  const rootNode = child.getRootNode == null ? void 0 : child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n\n  // then fallback to custom implementation with Shadow DOM support\n  if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    while (next) {\n      if (parent === next) {\n        return true;\n      }\n      // @ts-ignore\n      next = next.parentNode || next.host;\n    }\n  }\n\n  // Give up, the result is false\n  return false;\n}\nfunction getTarget(event) {\n  if ('composedPath' in event) {\n    return event.composedPath()[0];\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support\n  // `composedPath()`, but browsers without shadow DOM don't.\n  return event.target;\n}\nfunction isEventTargetWithin(event, node) {\n  if (node == null) {\n    return false;\n  }\n  if ('composedPath' in event) {\n    return event.composedPath().includes(node);\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support composedPath, but browsers without shadow dom don't\n  const e = event;\n  return e.target != null && node.contains(e.target);\n}\nfunction isRootElement(element) {\n  return element.matches('html,body');\n}\nfunction getDocument(node) {\n  return (node == null ? void 0 : node.ownerDocument) || document;\n}\nfunction isTypeableElement(element) {\n  return isHTMLElement(element) && element.matches(TYPEABLE_SELECTOR);\n}\nfunction isTypeableCombobox(element) {\n  if (!element) return false;\n  return element.getAttribute('role') === 'combobox' && isTypeableElement(element);\n}\nfunction matchesFocusVisible(element) {\n  // We don't want to block focus from working with `visibleOnly`\n  // (JSDOM doesn't match `:focus-visible` when the element has `:focus`)\n  if (!element || isJSDOM()) return true;\n  try {\n    return element.matches(':focus-visible');\n  } catch (_e) {\n    return true;\n  }\n}\nfunction getFloatingFocusElement(floatingElement) {\n  if (!floatingElement) {\n    return null;\n  }\n  // Try to find the element that has `{...getFloatingProps()}` spread on it.\n  // This indicates the floating element is acting as a positioning wrapper, and\n  // so focus should be managed on the child element with the event handlers and\n  // aria props.\n  return floatingElement.hasAttribute(FOCUSABLE_ATTRIBUTE) ? floatingElement : floatingElement.querySelector(\"[\" + FOCUSABLE_ATTRIBUTE + \"]\") || floatingElement;\n}\n\nfunction getNodeChildren(nodes, id, onlyOpenChildren) {\n  if (onlyOpenChildren === void 0) {\n    onlyOpenChildren = true;\n  }\n  const directChildren = nodes.filter(node => {\n    var _node$context;\n    return node.parentId === id && (!onlyOpenChildren || ((_node$context = node.context) == null ? void 0 : _node$context.open));\n  });\n  return directChildren.flatMap(child => [child, ...getNodeChildren(nodes, child.id, onlyOpenChildren)]);\n}\nfunction getDeepestNode(nodes, id) {\n  let deepestNodeId;\n  let maxDepth = -1;\n  function findDeepest(nodeId, depth) {\n    if (depth > maxDepth) {\n      deepestNodeId = nodeId;\n      maxDepth = depth;\n    }\n    const children = getNodeChildren(nodes, nodeId);\n    children.forEach(child => {\n      findDeepest(child.id, depth + 1);\n    });\n  }\n  findDeepest(id, 0);\n  return nodes.find(node => node.id === deepestNodeId);\n}\nfunction getNodeAncestors(nodes, id) {\n  var _nodes$find;\n  let allAncestors = [];\n  let currentParentId = (_nodes$find = nodes.find(node => node.id === id)) == null ? void 0 : _nodes$find.parentId;\n  while (currentParentId) {\n    const currentNode = nodes.find(node => node.id === currentParentId);\n    currentParentId = currentNode == null ? void 0 : currentNode.parentId;\n    if (currentNode) {\n      allAncestors = allAncestors.concat(currentNode);\n    }\n  }\n  return allAncestors;\n}\n\nfunction stopEvent(event) {\n  event.preventDefault();\n  event.stopPropagation();\n}\nfunction isReactEvent(event) {\n  return 'nativeEvent' in event;\n}\n\n// License: https://github.com/adobe/react-spectrum/blob/b35d5c02fe900badccd0cf1a8f23bb593419f238/packages/@react-aria/utils/src/isVirtualEvent.ts\nfunction isVirtualClick(event) {\n  // FIXME: Firefox is now emitting a deprecation warning for `mozInputSource`.\n  // Try to find a workaround for this. `react-aria` source still has the check.\n  if (event.mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n  if (isAndroid() && event.pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n  return event.detail === 0 && !event.pointerType;\n}\nfunction isVirtualPointerEvent(event) {\n  if (isJSDOM()) return false;\n  return !isAndroid() && event.width === 0 && event.height === 0 || isAndroid() && event.width === 1 && event.height === 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'mouse' ||\n  // iOS VoiceOver returns 0.333• for width/height.\n  event.width < 1 && event.height < 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'touch';\n}\nfunction isMouseLikePointerType(pointerType, strict) {\n  // On some Linux machines with Chromium, mouse inputs return a `pointerType`\n  // of \"pen\": https://github.com/floating-ui/floating-ui/issues/2015\n  const values = ['mouse', 'pen'];\n  if (!strict) {\n    values.push('', undefined);\n  }\n  return values.includes(pointerType);\n}\n\nvar isClient = typeof document !== 'undefined';\n\nvar noop = function noop() {};\nvar index = isClient ? useLayoutEffect : noop;\n\n// https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379\nconst SafeReact = {\n  ...React\n};\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\nconst useInsertionEffect = SafeReact.useInsertionEffect;\nconst useSafeInsertionEffect = useInsertionEffect || (fn => fn());\nfunction useEffectEvent(callback) {\n  const ref = React.useRef(() => {\n    if (process.env.NODE_ENV !== \"production\") {\n      throw new Error('Cannot call an event handler while rendering.');\n    }\n  });\n  useSafeInsertionEffect(() => {\n    ref.current = callback;\n  });\n  return React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return ref.current == null ? void 0 : ref.current(...args);\n  }, []);\n}\n\nfunction isDifferentGridRow(index, cols, prevRow) {\n  return Math.floor(index / cols) !== prevRow;\n}\nfunction isIndexOutOfListBounds(listRef, index) {\n  return index < 0 || index >= listRef.current.length;\n}\nfunction getMinListIndex(listRef, disabledIndices) {\n  return findNonDisabledListIndex(listRef, {\n    disabledIndices\n  });\n}\nfunction getMaxListIndex(listRef, disabledIndices) {\n  return findNonDisabledListIndex(listRef, {\n    decrement: true,\n    startingIndex: listRef.current.length,\n    disabledIndices\n  });\n}\nfunction findNonDisabledListIndex(listRef, _temp) {\n  let {\n    startingIndex = -1,\n    decrement = false,\n    disabledIndices,\n    amount = 1\n  } = _temp === void 0 ? {} : _temp;\n  let index = startingIndex;\n  do {\n    index += decrement ? -amount : amount;\n  } while (index >= 0 && index <= listRef.current.length - 1 && isListIndexDisabled(listRef, index, disabledIndices));\n  return index;\n}\nfunction getGridNavigatedIndex(listRef, _ref) {\n  let {\n    event,\n    orientation,\n    loop,\n    rtl,\n    cols,\n    disabledIndices,\n    minIndex,\n    maxIndex,\n    prevIndex,\n    stopEvent: stop = false\n  } = _ref;\n  let nextIndex = prevIndex;\n  if (event.key === ARROW_UP) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = maxIndex;\n    } else {\n      nextIndex = findNonDisabledListIndex(listRef, {\n        startingIndex: nextIndex,\n        amount: cols,\n        decrement: true,\n        disabledIndices\n      });\n      if (loop && (prevIndex - cols < minIndex || nextIndex < 0)) {\n        const col = prevIndex % cols;\n        const maxCol = maxIndex % cols;\n        const offset = maxIndex - (maxCol - col);\n        if (maxCol === col) {\n          nextIndex = maxIndex;\n        } else {\n          nextIndex = maxCol > col ? offset : offset - cols;\n        }\n      }\n    }\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n  if (event.key === ARROW_DOWN) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = minIndex;\n    } else {\n      nextIndex = findNonDisabledListIndex(listRef, {\n        startingIndex: prevIndex,\n        amount: cols,\n        disabledIndices\n      });\n      if (loop && prevIndex + cols > maxIndex) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex % cols - cols,\n          amount: cols,\n          disabledIndices\n        });\n      }\n    }\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n\n  // Remains on the same row/column.\n  if (orientation === 'both') {\n    const prevRow = floor(prevIndex / cols);\n    if (event.key === (rtl ? ARROW_LEFT : ARROW_RIGHT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== cols - 1) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex,\n          disabledIndices\n        });\n        if (loop && isDifferentGridRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledListIndex(listRef, {\n            startingIndex: prevIndex - prevIndex % cols - 1,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      }\n      if (isDifferentGridRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    if (event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== 0) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex,\n          decrement: true,\n          disabledIndices\n        });\n        if (loop && isDifferentGridRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledListIndex(listRef, {\n            startingIndex: prevIndex + (cols - prevIndex % cols),\n            decrement: true,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex + (cols - prevIndex % cols),\n          decrement: true,\n          disabledIndices\n        });\n      }\n      if (isDifferentGridRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    const lastRow = floor(maxIndex / cols) === prevRow;\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      if (loop && lastRow) {\n        nextIndex = event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT) ? maxIndex : findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      } else {\n        nextIndex = prevIndex;\n      }\n    }\n  }\n  return nextIndex;\n}\n\n/** For each cell index, gets the item index that occupies that cell */\nfunction createGridCellMap(sizes, cols, dense) {\n  const cellMap = [];\n  let startIndex = 0;\n  sizes.forEach((_ref2, index) => {\n    let {\n      width,\n      height\n    } = _ref2;\n    if (width > cols) {\n      if (process.env.NODE_ENV !== \"production\") {\n        throw new Error(\"[Floating UI]: Invalid grid - item width at index \" + index + \" is greater than grid columns\");\n      }\n    }\n    let itemPlaced = false;\n    if (dense) {\n      startIndex = 0;\n    }\n    while (!itemPlaced) {\n      const targetCells = [];\n      for (let i = 0; i < width; i++) {\n        for (let j = 0; j < height; j++) {\n          targetCells.push(startIndex + i + j * cols);\n        }\n      }\n      if (startIndex % cols + width <= cols && targetCells.every(cell => cellMap[cell] == null)) {\n        targetCells.forEach(cell => {\n          cellMap[cell] = index;\n        });\n        itemPlaced = true;\n      } else {\n        startIndex++;\n      }\n    }\n  });\n\n  // convert into a non-sparse array\n  return [...cellMap];\n}\n\n/** Gets cell index of an item's corner or -1 when index is -1. */\nfunction getGridCellIndexOfCorner(index, sizes, cellMap, cols, corner) {\n  if (index === -1) return -1;\n  const firstCellIndex = cellMap.indexOf(index);\n  const sizeItem = sizes[index];\n  switch (corner) {\n    case 'tl':\n      return firstCellIndex;\n    case 'tr':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + sizeItem.width - 1;\n    case 'bl':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + (sizeItem.height - 1) * cols;\n    case 'br':\n      return cellMap.lastIndexOf(index);\n  }\n}\n\n/** Gets all cell indices that correspond to the specified indices */\nfunction getGridCellIndices(indices, cellMap) {\n  return cellMap.flatMap((index, cellIndex) => indices.includes(index) ? [cellIndex] : []);\n}\nfunction isListIndexDisabled(listRef, index, disabledIndices) {\n  if (typeof disabledIndices === 'function') {\n    return disabledIndices(index);\n  } else if (disabledIndices) {\n    return disabledIndices.includes(index);\n  }\n  const element = listRef.current[index];\n  return element == null || element.hasAttribute('disabled') || element.getAttribute('aria-disabled') === 'true';\n}\n\nconst getTabbableOptions = () => ({\n  getShadowRoot: true,\n  displayCheck:\n  // JSDOM does not support the `tabbable` library. To solve this we can\n  // check if `ResizeObserver` is a real function (not polyfilled), which\n  // determines if the current environment is JSDOM-like.\n  typeof ResizeObserver === 'function' && ResizeObserver.toString().includes('[native code]') ? 'full' : 'none'\n});\nfunction getTabbableIn(container, dir) {\n  const list = tabbable(container, getTabbableOptions());\n  const len = list.length;\n  if (len === 0) return;\n  const active = activeElement(getDocument(container));\n  const index = list.indexOf(active);\n  const nextIndex = index === -1 ? dir === 1 ? 0 : len - 1 : index + dir;\n  return list[nextIndex];\n}\nfunction getNextTabbable(referenceElement) {\n  return getTabbableIn(getDocument(referenceElement).body, 1) || referenceElement;\n}\nfunction getPreviousTabbable(referenceElement) {\n  return getTabbableIn(getDocument(referenceElement).body, -1) || referenceElement;\n}\nfunction isOutsideEvent(event, container) {\n  const containerElement = container || event.currentTarget;\n  const relatedTarget = event.relatedTarget;\n  return !relatedTarget || !contains(containerElement, relatedTarget);\n}\nfunction disableFocusInside(container) {\n  const tabbableElements = tabbable(container, getTabbableOptions());\n  tabbableElements.forEach(element => {\n    element.dataset.tabindex = element.getAttribute('tabindex') || '';\n    element.setAttribute('tabindex', '-1');\n  });\n}\nfunction enableFocusInside(container) {\n  const elements = container.querySelectorAll('[data-tabindex]');\n  elements.forEach(element => {\n    const tabindex = element.dataset.tabindex;\n    delete element.dataset.tabindex;\n    if (tabindex) {\n      element.setAttribute('tabindex', tabindex);\n    } else {\n      element.removeAttribute('tabindex');\n    }\n  });\n}\n\nexport { activeElement, contains, createGridCellMap, disableFocusInside, enableFocusInside, findNonDisabledListIndex, getDeepestNode, getDocument, getFloatingFocusElement, getGridCellIndexOfCorner, getGridCellIndices, getGridNavigatedIndex, getMaxListIndex, getMinListIndex, getNextTabbable, getNodeAncestors, getNodeChildren, getPlatform, getPreviousTabbable, getTabbableOptions, getTarget, getUserAgent, isAndroid, isDifferentGridRow, isEventTargetWithin, isIndexOutOfListBounds, isJSDOM, isListIndexDisabled, isMac, isMouseLikePointerType, isOutsideEvent, isReactEvent, isRootElement, isSafari, isTypeableCombobox, isTypeableElement, isVirtualClick, isVirtualPointerEvent, matchesFocusVisible, stopEvent, useEffectEvent, useLatestRef, index as useModernLayoutEffect };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAEA;AACA;;;;;;AAEA,sCAAsC;AACtC,SAAS;IACP,MAAM,SAAS,UAAU,aAAa;IACtC,IAAI,UAAU,QAAQ,OAAO,QAAQ,EAAE;QACrC,OAAO,OAAO,QAAQ;IACxB;IACA,OAAO,UAAU,QAAQ;AAC3B;AACA,SAAS;IACP,MAAM,SAAS,UAAU,aAAa;IACtC,IAAI,UAAU,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG;QAC1C,OAAO,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA;YACvB,IAAI,EACF,KAAK,EACL,OAAO,EACR,GAAG;YACJ,OAAO,QAAQ,MAAM;QACvB,GAAG,IAAI,CAAC;IACV;IACA,OAAO,UAAU,SAAS;AAC5B;AACA,SAAS;IACP,2DAA2D;IAC3D,OAAO,SAAS,IAAI,CAAC,UAAU,MAAM;AACvC;AACA,SAAS;IACP,MAAM,KAAK;IACX,OAAO,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAC3C;AACA,SAAS;IACP,OAAO,cAAc,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC,UAAU,cAAc;AACnF;AACA,SAAS;IACP,OAAO,eAAe,QAAQ,CAAC;AACjC;AAEA,MAAM,sBAAsB;AAC5B,MAAM,oBAAoB,gDAAgD;AAC1E,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,WAAW;AACjB,MAAM,aAAa;AAEnB,SAAS,cAAc,GAAG;IACxB,IAAI,gBAAgB,IAAI,aAAa;IACrC,MAAO,CAAC,CAAC,iBAAiB,aAAa,KAAK,QAAQ,CAAC,iBAAiB,eAAe,UAAU,KAAK,OAAO,KAAK,IAAI,eAAe,aAAa,KAAK,KAAM;QACzJ,IAAI;QACJ,gBAAgB,cAAc,UAAU,CAAC,aAAa;IACxD;IACA,OAAO;AACT;AACA,SAAS,SAAS,MAAM,EAAE,KAAK;IAC7B,IAAI,CAAC,UAAU,CAAC,OAAO;QACrB,OAAO;IACT;IACA,MAAM,WAAW,MAAM,WAAW,IAAI,OAAO,KAAK,IAAI,MAAM,WAAW;IAEvE,2CAA2C;IAC3C,IAAI,OAAO,QAAQ,CAAC,QAAQ;QAC1B,OAAO;IACT;IAEA,iEAAiE;IACjE,IAAI,YAAY,CAAA,GAAA,2PAAA,CAAA,eAAY,AAAD,EAAE,WAAW;QACtC,IAAI,OAAO;QACX,MAAO,KAAM;YACX,IAAI,WAAW,MAAM;gBACnB,OAAO;YACT;YACA,aAAa;YACb,OAAO,KAAK,UAAU,IAAI,KAAK,IAAI;QACrC;IACF;IAEA,+BAA+B;IAC/B,OAAO;AACT;AACA,SAAS,UAAU,KAAK;IACtB,IAAI,kBAAkB,OAAO;QAC3B,OAAO,MAAM,YAAY,EAAE,CAAC,EAAE;IAChC;IAEA,wEAAwE;IACxE,2DAA2D;IAC3D,OAAO,MAAM,MAAM;AACrB;AACA,SAAS,oBAAoB,KAAK,EAAE,IAAI;IACtC,IAAI,QAAQ,MAAM;QAChB,OAAO;IACT;IACA,IAAI,kBAAkB,OAAO;QAC3B,OAAO,MAAM,YAAY,GAAG,QAAQ,CAAC;IACvC;IAEA,4HAA4H;IAC5H,MAAM,IAAI;IACV,OAAO,EAAE,MAAM,IAAI,QAAQ,KAAK,QAAQ,CAAC,EAAE,MAAM;AACnD;AACA,SAAS,cAAc,OAAO;IAC5B,OAAO,QAAQ,OAAO,CAAC;AACzB;AACA,SAAS,YAAY,IAAI;IACvB,OAAO,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,aAAa,KAAK;AACzD;AACA,SAAS,kBAAkB,OAAO;IAChC,OAAO,CAAA,GAAA,2PAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,QAAQ,OAAO,CAAC;AACnD;AACA,SAAS,mBAAmB,OAAO;IACjC,IAAI,CAAC,SAAS,OAAO;IACrB,OAAO,QAAQ,YAAY,CAAC,YAAY,cAAc,kBAAkB;AAC1E;AACA,SAAS,oBAAoB,OAAO;IAClC,+DAA+D;IAC/D,uEAAuE;IACvE,IAAI,CAAC,WAAW,WAAW,OAAO;IAClC,IAAI;QACF,OAAO,QAAQ,OAAO,CAAC;IACzB,EAAE,OAAO,IAAI;QACX,OAAO;IACT;AACF;AACA,SAAS,wBAAwB,eAAe;IAC9C,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IACA,2EAA2E;IAC3E,8EAA8E;IAC9E,8EAA8E;IAC9E,cAAc;IACd,OAAO,gBAAgB,YAAY,CAAC,uBAAuB,kBAAkB,gBAAgB,aAAa,CAAC,MAAM,sBAAsB,QAAQ;AACjJ;AAEA,SAAS,gBAAgB,KAAK,EAAE,EAAE,EAAE,gBAAgB;IAClD,IAAI,qBAAqB,KAAK,GAAG;QAC/B,mBAAmB;IACrB;IACA,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA;QAClC,IAAI;QACJ,OAAO,KAAK,QAAQ,KAAK,MAAM,CAAC,CAAC,oBAAoB,CAAC,CAAC,gBAAgB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,cAAc,IAAI,CAAC;IAC7H;IACA,OAAO,eAAe,OAAO,CAAC,CAAA,QAAS;YAAC;eAAU,gBAAgB,OAAO,MAAM,EAAE,EAAE;SAAkB;AACvG;AACA,SAAS,eAAe,KAAK,EAAE,EAAE;IAC/B,IAAI;IACJ,IAAI,WAAW,CAAC;IAChB,SAAS,YAAY,MAAM,EAAE,KAAK;QAChC,IAAI,QAAQ,UAAU;YACpB,gBAAgB;YAChB,WAAW;QACb;QACA,MAAM,WAAW,gBAAgB,OAAO;QACxC,SAAS,OAAO,CAAC,CAAA;YACf,YAAY,MAAM,EAAE,EAAE,QAAQ;QAChC;IACF;IACA,YAAY,IAAI;IAChB,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AACxC;AACA,SAAS,iBAAiB,KAAK,EAAE,EAAE;IACjC,IAAI;IACJ,IAAI,eAAe,EAAE;IACrB,IAAI,kBAAkB,CAAC,cAAc,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,GAAG,KAAK,OAAO,KAAK,IAAI,YAAY,QAAQ;IAChH,MAAO,gBAAiB;QACtB,MAAM,cAAc,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACnD,kBAAkB,eAAe,OAAO,KAAK,IAAI,YAAY,QAAQ;QACrE,IAAI,aAAa;YACf,eAAe,aAAa,MAAM,CAAC;QACrC;IACF;IACA,OAAO;AACT;AAEA,SAAS,UAAU,KAAK;IACtB,MAAM,cAAc;IACpB,MAAM,eAAe;AACvB;AACA,SAAS,aAAa,KAAK;IACzB,OAAO,iBAAiB;AAC1B;AAEA,kJAAkJ;AAClJ,SAAS,eAAe,KAAK;IAC3B,6EAA6E;IAC7E,8EAA8E;IAC9E,IAAI,MAAM,cAAc,KAAK,KAAK,MAAM,SAAS,EAAE;QACjD,OAAO;IACT;IACA,IAAI,eAAe,MAAM,WAAW,EAAE;QACpC,OAAO,MAAM,IAAI,KAAK,WAAW,MAAM,OAAO,KAAK;IACrD;IACA,OAAO,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM,WAAW;AACjD;AACA,SAAS,sBAAsB,KAAK;IAClC,IAAI,WAAW,OAAO;IACtB,OAAO,CAAC,eAAe,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,eAAe,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,WAAW,KAAK,WAChM,iDAAiD;IACjD,MAAM,KAAK,GAAG,KAAK,MAAM,MAAM,GAAG,KAAK,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,WAAW,KAAK;AAC7G;AACA,SAAS,uBAAuB,WAAW,EAAE,MAAM;IACjD,4EAA4E;IAC5E,mEAAmE;IACnE,MAAM,SAAS;QAAC;QAAS;KAAM;IAC/B,IAAI,CAAC,QAAQ;QACX,OAAO,IAAI,CAAC,IAAI;IAClB;IACA,OAAO,OAAO,QAAQ,CAAC;AACzB;AAEA,IAAI,WAAW,OAAO,aAAa;AAEnC,IAAI,OAAO,SAAS,QAAQ;AAC5B,IAAI,QAAQ,WAAW,4WAAA,CAAA,kBAAe,GAAG;AAEzC,0EAA0E;AAC1E,MAAM,YAAY;IAChB,GAAG,4WAAK;AACV;AAEA,SAAS,aAAa,KAAK;IACzB,MAAM,MAAM,6WAAM,MAAM,CAAC;IACzB,MAAM;QACJ,IAAI,OAAO,GAAG;IAChB;IACA,OAAO;AACT;AACA,MAAM,qBAAqB,UAAU,kBAAkB;AACvD,MAAM,yBAAyB,sBAAsB,CAAC,CAAA,KAAM,IAAI;AAChE,SAAS,eAAe,QAAQ;IAC9B,MAAM,MAAM,6WAAM,MAAM,CAAC;QACvB,wCAA2C;YACzC,MAAM,IAAI,MAAM;QAClB;IACF;IACA,uBAAuB;QACrB,IAAI,OAAO,GAAG;IAChB;IACA,OAAO,6WAAM,WAAW,CAAC;QACvB,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAC9B;QACA,OAAO,IAAI,OAAO,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,IAAI;IACvD,GAAG,EAAE;AACP;AAEA,SAAS,mBAAmB,KAAK,EAAE,IAAI,EAAE,OAAO;IAC9C,OAAO,KAAK,KAAK,CAAC,QAAQ,UAAU;AACtC;AACA,SAAS,uBAAuB,OAAO,EAAE,KAAK;IAC5C,OAAO,QAAQ,KAAK,SAAS,QAAQ,OAAO,CAAC,MAAM;AACrD;AACA,SAAS,gBAAgB,OAAO,EAAE,eAAe;IAC/C,OAAO,yBAAyB,SAAS;QACvC;IACF;AACF;AACA,SAAS,gBAAgB,OAAO,EAAE,eAAe;IAC/C,OAAO,yBAAyB,SAAS;QACvC,WAAW;QACX,eAAe,QAAQ,OAAO,CAAC,MAAM;QACrC;IACF;AACF;AACA,SAAS,yBAAyB,OAAO,EAAE,KAAK;IAC9C,IAAI,EACF,gBAAgB,CAAC,CAAC,EAClB,YAAY,KAAK,EACjB,eAAe,EACf,SAAS,CAAC,EACX,GAAG,UAAU,KAAK,IAAI,CAAC,IAAI;IAC5B,IAAI,QAAQ;IACZ,GAAG;QACD,SAAS,YAAY,CAAC,SAAS;IACjC,QAAS,SAAS,KAAK,SAAS,QAAQ,OAAO,CAAC,MAAM,GAAG,KAAK,oBAAoB,SAAS,OAAO,iBAAkB;IACpH,OAAO;AACT;AACA,SAAS,sBAAsB,OAAO,EAAE,IAAI;IAC1C,IAAI,EACF,KAAK,EACL,WAAW,EACX,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,eAAe,EACf,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,WAAW,OAAO,KAAK,EACxB,GAAG;IACJ,IAAI,YAAY;IAChB,IAAI,MAAM,GAAG,KAAK,UAAU;QAC1B,QAAQ,UAAU;QAClB,IAAI,cAAc,CAAC,GAAG;YACpB,YAAY;QACd,OAAO;YACL,YAAY,yBAAyB,SAAS;gBAC5C,eAAe;gBACf,QAAQ;gBACR,WAAW;gBACX;YACF;YACA,IAAI,QAAQ,CAAC,YAAY,OAAO,YAAY,YAAY,CAAC,GAAG;gBAC1D,MAAM,MAAM,YAAY;gBACxB,MAAM,SAAS,WAAW;gBAC1B,MAAM,SAAS,WAAW,CAAC,SAAS,GAAG;gBACvC,IAAI,WAAW,KAAK;oBAClB,YAAY;gBACd,OAAO;oBACL,YAAY,SAAS,MAAM,SAAS,SAAS;gBAC/C;YACF;QACF;QACA,IAAI,uBAAuB,SAAS,YAAY;YAC9C,YAAY;QACd;IACF;IACA,IAAI,MAAM,GAAG,KAAK,YAAY;QAC5B,QAAQ,UAAU;QAClB,IAAI,cAAc,CAAC,GAAG;YACpB,YAAY;QACd,OAAO;YACL,YAAY,yBAAyB,SAAS;gBAC5C,eAAe;gBACf,QAAQ;gBACR;YACF;YACA,IAAI,QAAQ,YAAY,OAAO,UAAU;gBACvC,YAAY,yBAAyB,SAAS;oBAC5C,eAAe,YAAY,OAAO;oBAClC,QAAQ;oBACR;gBACF;YACF;QACF;QACA,IAAI,uBAAuB,SAAS,YAAY;YAC9C,YAAY;QACd;IACF;IAEA,kCAAkC;IAClC,IAAI,gBAAgB,QAAQ;QAC1B,MAAM,UAAU,CAAA,GAAA,oPAAA,CAAA,QAAK,AAAD,EAAE,YAAY;QAClC,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,aAAa,WAAW,GAAG;YAClD,QAAQ,UAAU;YAClB,IAAI,YAAY,SAAS,OAAO,GAAG;gBACjC,YAAY,yBAAyB,SAAS;oBAC5C,eAAe;oBACf;gBACF;gBACA,IAAI,QAAQ,mBAAmB,WAAW,MAAM,UAAU;oBACxD,YAAY,yBAAyB,SAAS;wBAC5C,eAAe,YAAY,YAAY,OAAO;wBAC9C;oBACF;gBACF;YACF,OAAO,IAAI,MAAM;gBACf,YAAY,yBAAyB,SAAS;oBAC5C,eAAe,YAAY,YAAY,OAAO;oBAC9C;gBACF;YACF;YACA,IAAI,mBAAmB,WAAW,MAAM,UAAU;gBAChD,YAAY;YACd;QACF;QACA,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,cAAc,UAAU,GAAG;YAClD,QAAQ,UAAU;YAClB,IAAI,YAAY,SAAS,GAAG;gBAC1B,YAAY,yBAAyB,SAAS;oBAC5C,eAAe;oBACf,WAAW;oBACX;gBACF;gBACA,IAAI,QAAQ,mBAAmB,WAAW,MAAM,UAAU;oBACxD,YAAY,yBAAyB,SAAS;wBAC5C,eAAe,YAAY,CAAC,OAAO,YAAY,IAAI;wBACnD,WAAW;wBACX;oBACF;gBACF;YACF,OAAO,IAAI,MAAM;gBACf,YAAY,yBAAyB,SAAS;oBAC5C,eAAe,YAAY,CAAC,OAAO,YAAY,IAAI;oBACnD,WAAW;oBACX;gBACF;YACF;YACA,IAAI,mBAAmB,WAAW,MAAM,UAAU;gBAChD,YAAY;YACd;QACF;QACA,MAAM,UAAU,CAAA,GAAA,oPAAA,CAAA,QAAK,AAAD,EAAE,WAAW,UAAU;QAC3C,IAAI,uBAAuB,SAAS,YAAY;YAC9C,IAAI,QAAQ,SAAS;gBACnB,YAAY,MAAM,GAAG,KAAK,CAAC,MAAM,cAAc,UAAU,IAAI,WAAW,yBAAyB,SAAS;oBACxG,eAAe,YAAY,YAAY,OAAO;oBAC9C;gBACF;YACF,OAAO;gBACL,YAAY;YACd;QACF;IACF;IACA,OAAO;AACT;AAEA,qEAAqE,GACrE,SAAS,kBAAkB,KAAK,EAAE,IAAI,EAAE,KAAK;IAC3C,MAAM,UAAU,EAAE;IAClB,IAAI,aAAa;IACjB,MAAM,OAAO,CAAC,CAAC,OAAO;QACpB,IAAI,EACF,KAAK,EACL,MAAM,EACP,GAAG;QACJ,IAAI,QAAQ,MAAM;YAChB,wCAA2C;gBACzC,MAAM,IAAI,MAAM,uDAAuD,QAAQ;YACjF;QACF;QACA,IAAI,aAAa;QACjB,IAAI,OAAO;YACT,aAAa;QACf;QACA,MAAO,CAAC,WAAY;YAClB,MAAM,cAAc,EAAE;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;oBAC/B,YAAY,IAAI,CAAC,aAAa,IAAI,IAAI;gBACxC;YACF;YACA,IAAI,aAAa,OAAO,SAAS,QAAQ,YAAY,KAAK,CAAC,CAAA,OAAQ,OAAO,CAAC,KAAK,IAAI,OAAO;gBACzF,YAAY,OAAO,CAAC,CAAA;oBAClB,OAAO,CAAC,KAAK,GAAG;gBAClB;gBACA,aAAa;YACf,OAAO;gBACL;YACF;QACF;IACF;IAEA,kCAAkC;IAClC,OAAO;WAAI;KAAQ;AACrB;AAEA,gEAAgE,GAChE,SAAS,yBAAyB,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM;IACnE,IAAI,UAAU,CAAC,GAAG,OAAO,CAAC;IAC1B,MAAM,iBAAiB,QAAQ,OAAO,CAAC;IACvC,MAAM,WAAW,KAAK,CAAC,MAAM;IAC7B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,IAAI,CAAC,UAAU;gBACb,OAAO;YACT;YACA,OAAO,iBAAiB,SAAS,KAAK,GAAG;QAC3C,KAAK;YACH,IAAI,CAAC,UAAU;gBACb,OAAO;YACT;YACA,OAAO,iBAAiB,CAAC,SAAS,MAAM,GAAG,CAAC,IAAI;QAClD,KAAK;YACH,OAAO,QAAQ,WAAW,CAAC;IAC/B;AACF;AAEA,mEAAmE,GACnE,SAAS,mBAAmB,OAAO,EAAE,OAAO;IAC1C,OAAO,QAAQ,OAAO,CAAC,CAAC,OAAO,YAAc,QAAQ,QAAQ,CAAC,SAAS;YAAC;SAAU,GAAG,EAAE;AACzF;AACA,SAAS,oBAAoB,OAAO,EAAE,KAAK,EAAE,eAAe;IAC1D,IAAI,OAAO,oBAAoB,YAAY;QACzC,OAAO,gBAAgB;IACzB,OAAO,IAAI,iBAAiB;QAC1B,OAAO,gBAAgB,QAAQ,CAAC;IAClC;IACA,MAAM,UAAU,QAAQ,OAAO,CAAC,MAAM;IACtC,OAAO,WAAW,QAAQ,QAAQ,YAAY,CAAC,eAAe,QAAQ,YAAY,CAAC,qBAAqB;AAC1G;AAEA,MAAM,qBAAqB,IAAM,CAAC;QAChC,eAAe;QACf,cACA,sEAAsE;QACtE,uEAAuE;QACvE,uDAAuD;QACvD,OAAO,mBAAmB,cAAc,eAAe,QAAQ,GAAG,QAAQ,CAAC,mBAAmB,SAAS;IACzG,CAAC;AACD,SAAS,cAAc,SAAS,EAAE,GAAG;IACnC,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;IACjC,MAAM,MAAM,KAAK,MAAM;IACvB,IAAI,QAAQ,GAAG;IACf,MAAM,SAAS,cAAc,YAAY;IACzC,MAAM,QAAQ,KAAK,OAAO,CAAC;IAC3B,MAAM,YAAY,UAAU,CAAC,IAAI,QAAQ,IAAI,IAAI,MAAM,IAAI,QAAQ;IACnE,OAAO,IAAI,CAAC,UAAU;AACxB;AACA,SAAS,gBAAgB,gBAAgB;IACvC,OAAO,cAAc,YAAY,kBAAkB,IAAI,EAAE,MAAM;AACjE;AACA,SAAS,oBAAoB,gBAAgB;IAC3C,OAAO,cAAc,YAAY,kBAAkB,IAAI,EAAE,CAAC,MAAM;AAClE;AACA,SAAS,eAAe,KAAK,EAAE,SAAS;IACtC,MAAM,mBAAmB,aAAa,MAAM,aAAa;IACzD,MAAM,gBAAgB,MAAM,aAAa;IACzC,OAAO,CAAC,iBAAiB,CAAC,SAAS,kBAAkB;AACvD;AACA,SAAS,mBAAmB,SAAS;IACnC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;IAC7C,iBAAiB,OAAO,CAAC,CAAA;QACvB,QAAQ,OAAO,CAAC,QAAQ,GAAG,QAAQ,YAAY,CAAC,eAAe;QAC/D,QAAQ,YAAY,CAAC,YAAY;IACnC;AACF;AACA,SAAS,kBAAkB,SAAS;IAClC,MAAM,WAAW,UAAU,gBAAgB,CAAC;IAC5C,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;QACzC,OAAO,QAAQ,OAAO,CAAC,QAAQ;QAC/B,IAAI,UAAU;YACZ,QAAQ,YAAY,CAAC,YAAY;QACnC,OAAO;YACL,QAAQ,eAAe,CAAC;QAC1B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40floating-ui%2Breact%400.27.13_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40floating-ui/react/dist/floating-ui.react.mjs"], "sourcesContent": ["import * as React from 'react';\nimport { useModernLayoutEffect, useEffectEvent, getMinListIndex, getMaxListIndex, createGridCellMap, isListIndexDisabled, getGridNavigatedIndex, getGridCellIndexOfCorner, getGridCellIndices, findNonDisabledListIndex, isIndexOutOfListBounds, useLatestRef, getDocument as getDocument$1, isMouseLikePointerType, contains as contains$1, isSafari, enableFocusInside, isOutsideEvent, getPreviousTabbable, getNextTabbable, disableFocusInside, isTypeableCombobox, getFloatingFocusElement, getTabbableOptions, getNodeAncestors, activeElement, getNodeChildren as getNodeChildren$1, stopEvent, getTarget as getTarget$1, isVirtualClick, isVirtualPointerEvent, getPlatform, isTypeableElement, isReactEvent, isRootElement, isEventTargetWithin, matchesFocusVisible, isMac, getDeepestNode, getUserAgent } from '@floating-ui/react/utils';\nimport { jsx, jsxs, Fragment } from 'react/jsx-runtime';\nimport { getComputedStyle, isElement, isShadowRoot, getNodeName, isHTMLElement, getWindow, isLastTraversableNode, getParentNode, isWebKit } from '@floating-ui/utils/dom';\nimport { tabbable, isTabbable, focusable } from 'tabbable';\nimport * as ReactDOM from 'react-dom';\nimport { getOverflowAncestors, useFloating as useFloating$1, offset, detectOverflow } from '@floating-ui/react-dom';\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, getOverflowAncestors, hide, inline, limitShift, offset, platform, shift, size } from '@floating-ui/react-dom';\nimport { evaluate, max, round, min } from '@floating-ui/utils';\n\n/**\n * Merges an array of refs into a single memoized callback ref or `null`.\n * @see https://floating-ui.com/docs/react-utils#usemergerefs\n */\nfunction useMergeRefs(refs) {\n  const cleanupRef = React.useRef(undefined);\n  const refEffect = React.useCallback(instance => {\n    const cleanups = refs.map(ref => {\n      if (ref == null) {\n        return;\n      }\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return typeof refCleanup === 'function' ? refCleanup : () => {\n          refCallback(null);\n        };\n      }\n      ref.current = instance;\n      return () => {\n        ref.current = null;\n      };\n    });\n    return () => {\n      cleanups.forEach(refCleanup => refCleanup == null ? void 0 : refCleanup());\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      if (cleanupRef.current) {\n        cleanupRef.current();\n        cleanupRef.current = undefined;\n      }\n      if (value != null) {\n        cleanupRef.current = refEffect(value);\n      }\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}\n\nfunction sortByDocumentPosition(a, b) {\n  const position = a.compareDocumentPosition(b);\n  if (position & Node.DOCUMENT_POSITION_FOLLOWING || position & Node.DOCUMENT_POSITION_CONTAINED_BY) {\n    return -1;\n  }\n  if (position & Node.DOCUMENT_POSITION_PRECEDING || position & Node.DOCUMENT_POSITION_CONTAINS) {\n    return 1;\n  }\n  return 0;\n}\nconst FloatingListContext = /*#__PURE__*/React.createContext({\n  register: () => {},\n  unregister: () => {},\n  map: /*#__PURE__*/new Map(),\n  elementsRef: {\n    current: []\n  }\n});\n/**\n * Provides context for a list of items within the floating element.\n * @see https://floating-ui.com/docs/FloatingList\n */\nfunction FloatingList(props) {\n  const {\n    children,\n    elementsRef,\n    labelsRef\n  } = props;\n  const [nodes, setNodes] = React.useState(() => new Set());\n  const register = React.useCallback(node => {\n    setNodes(prevSet => new Set(prevSet).add(node));\n  }, []);\n  const unregister = React.useCallback(node => {\n    setNodes(prevSet => {\n      const set = new Set(prevSet);\n      set.delete(node);\n      return set;\n    });\n  }, []);\n  const map = React.useMemo(() => {\n    const newMap = new Map();\n    const sortedNodes = Array.from(nodes.keys()).sort(sortByDocumentPosition);\n    sortedNodes.forEach((node, index) => {\n      newMap.set(node, index);\n    });\n    return newMap;\n  }, [nodes]);\n  return /*#__PURE__*/jsx(FloatingListContext.Provider, {\n    value: React.useMemo(() => ({\n      register,\n      unregister,\n      map,\n      elementsRef,\n      labelsRef\n    }), [register, unregister, map, elementsRef, labelsRef]),\n    children: children\n  });\n}\n/**\n * Used to register a list item and its index (DOM position) in the\n * `FloatingList`.\n * @see https://floating-ui.com/docs/FloatingList#uselistitem\n */\nfunction useListItem(props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    label\n  } = props;\n  const {\n    register,\n    unregister,\n    map,\n    elementsRef,\n    labelsRef\n  } = React.useContext(FloatingListContext);\n  const [index, setIndex] = React.useState(null);\n  const componentRef = React.useRef(null);\n  const ref = React.useCallback(node => {\n    componentRef.current = node;\n    if (index !== null) {\n      elementsRef.current[index] = node;\n      if (labelsRef) {\n        var _node$textContent;\n        const isLabelDefined = label !== undefined;\n        labelsRef.current[index] = isLabelDefined ? label : (_node$textContent = node == null ? void 0 : node.textContent) != null ? _node$textContent : null;\n      }\n    }\n  }, [index, elementsRef, labelsRef, label]);\n  useModernLayoutEffect(() => {\n    const node = componentRef.current;\n    if (node) {\n      register(node);\n      return () => {\n        unregister(node);\n      };\n    }\n  }, [register, unregister]);\n  useModernLayoutEffect(() => {\n    const index = componentRef.current ? map.get(componentRef.current) : null;\n    if (index != null) {\n      setIndex(index);\n    }\n  }, [map]);\n  return React.useMemo(() => ({\n    ref,\n    index: index == null ? -1 : index\n  }), [index, ref]);\n}\n\nconst FOCUSABLE_ATTRIBUTE = 'data-floating-ui-focusable';\nconst ACTIVE_KEY = 'active';\nconst SELECTED_KEY = 'selected';\nconst ARROW_LEFT = 'ArrowLeft';\nconst ARROW_RIGHT = 'ArrowRight';\nconst ARROW_UP = 'ArrowUp';\nconst ARROW_DOWN = 'ArrowDown';\n\nfunction renderJsx(render, computedProps) {\n  if (typeof render === 'function') {\n    return render(computedProps);\n  }\n  if (render) {\n    return /*#__PURE__*/React.cloneElement(render, computedProps);\n  }\n  return /*#__PURE__*/jsx(\"div\", {\n    ...computedProps\n  });\n}\nconst CompositeContext = /*#__PURE__*/React.createContext({\n  activeIndex: 0,\n  onNavigate: () => {}\n});\nconst horizontalKeys = [ARROW_LEFT, ARROW_RIGHT];\nconst verticalKeys = [ARROW_UP, ARROW_DOWN];\nconst allKeys = [...horizontalKeys, ...verticalKeys];\n\n/**\n * Creates a single tab stop whose items are navigated by arrow keys, which\n * provides list navigation outside of floating element contexts.\n *\n * This is useful to enable navigation of a list of items that aren’t part of a\n * floating element. A menubar is an example of a composite, with each reference\n * element being an item.\n * @see https://floating-ui.com/docs/Composite\n */\nconst Composite = /*#__PURE__*/React.forwardRef(function Composite(props, forwardedRef) {\n  const {\n    render,\n    orientation = 'both',\n    loop = true,\n    rtl = false,\n    cols = 1,\n    disabledIndices,\n    activeIndex: externalActiveIndex,\n    onNavigate: externalSetActiveIndex,\n    itemSizes,\n    dense = false,\n    ...domProps\n  } = props;\n  const [internalActiveIndex, internalSetActiveIndex] = React.useState(0);\n  const activeIndex = externalActiveIndex != null ? externalActiveIndex : internalActiveIndex;\n  const onNavigate = useEffectEvent(externalSetActiveIndex != null ? externalSetActiveIndex : internalSetActiveIndex);\n  const elementsRef = React.useRef([]);\n  const renderElementProps = render && typeof render !== 'function' ? render.props : {};\n  const contextValue = React.useMemo(() => ({\n    activeIndex,\n    onNavigate\n  }), [activeIndex, onNavigate]);\n  const isGrid = cols > 1;\n  function handleKeyDown(event) {\n    if (!allKeys.includes(event.key)) return;\n    let nextIndex = activeIndex;\n    const minIndex = getMinListIndex(elementsRef, disabledIndices);\n    const maxIndex = getMaxListIndex(elementsRef, disabledIndices);\n    const horizontalEndKey = rtl ? ARROW_LEFT : ARROW_RIGHT;\n    const horizontalStartKey = rtl ? ARROW_RIGHT : ARROW_LEFT;\n    if (isGrid) {\n      const sizes = itemSizes || Array.from({\n        length: elementsRef.current.length\n      }, () => ({\n        width: 1,\n        height: 1\n      }));\n      // To calculate movements on the grid, we use hypothetical cell indices\n      // as if every item was 1x1, then convert back to real indices.\n      const cellMap = createGridCellMap(sizes, cols, dense);\n      const minGridIndex = cellMap.findIndex(index => index != null && !isListIndexDisabled(elementsRef, index, disabledIndices));\n      // last enabled index\n      const maxGridIndex = cellMap.reduce((foundIndex, index, cellIndex) => index != null && !isListIndexDisabled(elementsRef, index, disabledIndices) ? cellIndex : foundIndex, -1);\n      const maybeNextIndex = cellMap[getGridNavigatedIndex({\n        current: cellMap.map(itemIndex => itemIndex ? elementsRef.current[itemIndex] : null)\n      }, {\n        event,\n        orientation,\n        loop,\n        rtl,\n        cols,\n        // treat undefined (empty grid spaces) as disabled indices so we\n        // don't end up in them\n        disabledIndices: getGridCellIndices([...((typeof disabledIndices !== 'function' ? disabledIndices : null) || elementsRef.current.map((_, index) => isListIndexDisabled(elementsRef, index, disabledIndices) ? index : undefined)), undefined], cellMap),\n        minIndex: minGridIndex,\n        maxIndex: maxGridIndex,\n        prevIndex: getGridCellIndexOfCorner(activeIndex > maxIndex ? minIndex : activeIndex, sizes, cellMap, cols,\n        // use a corner matching the edge closest to the direction we're\n        // moving in so we don't end up in the same item. Prefer\n        // top/left over bottom/right.\n        event.key === ARROW_DOWN ? 'bl' : event.key === horizontalEndKey ? 'tr' : 'tl')\n      })];\n      if (maybeNextIndex != null) {\n        nextIndex = maybeNextIndex;\n      }\n    }\n    const toEndKeys = {\n      horizontal: [horizontalEndKey],\n      vertical: [ARROW_DOWN],\n      both: [horizontalEndKey, ARROW_DOWN]\n    }[orientation];\n    const toStartKeys = {\n      horizontal: [horizontalStartKey],\n      vertical: [ARROW_UP],\n      both: [horizontalStartKey, ARROW_UP]\n    }[orientation];\n    const preventedKeys = isGrid ? allKeys : {\n      horizontal: horizontalKeys,\n      vertical: verticalKeys,\n      both: allKeys\n    }[orientation];\n    if (nextIndex === activeIndex && [...toEndKeys, ...toStartKeys].includes(event.key)) {\n      if (loop && nextIndex === maxIndex && toEndKeys.includes(event.key)) {\n        nextIndex = minIndex;\n      } else if (loop && nextIndex === minIndex && toStartKeys.includes(event.key)) {\n        nextIndex = maxIndex;\n      } else {\n        nextIndex = findNonDisabledListIndex(elementsRef, {\n          startingIndex: nextIndex,\n          decrement: toStartKeys.includes(event.key),\n          disabledIndices\n        });\n      }\n    }\n    if (nextIndex !== activeIndex && !isIndexOutOfListBounds(elementsRef, nextIndex)) {\n      var _elementsRef$current$;\n      event.stopPropagation();\n      if (preventedKeys.includes(event.key)) {\n        event.preventDefault();\n      }\n      onNavigate(nextIndex);\n      (_elementsRef$current$ = elementsRef.current[nextIndex]) == null || _elementsRef$current$.focus();\n    }\n  }\n  const computedProps = {\n    ...domProps,\n    ...renderElementProps,\n    ref: forwardedRef,\n    'aria-orientation': orientation === 'both' ? undefined : orientation,\n    onKeyDown(e) {\n      domProps.onKeyDown == null || domProps.onKeyDown(e);\n      renderElementProps.onKeyDown == null || renderElementProps.onKeyDown(e);\n      handleKeyDown(e);\n    }\n  };\n  return /*#__PURE__*/jsx(CompositeContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/jsx(FloatingList, {\n      elementsRef: elementsRef,\n      children: renderJsx(render, computedProps)\n    })\n  });\n});\n/**\n * @see https://floating-ui.com/docs/Composite\n */\nconst CompositeItem = /*#__PURE__*/React.forwardRef(function CompositeItem(props, forwardedRef) {\n  const {\n    render,\n    ...domProps\n  } = props;\n  const renderElementProps = render && typeof render !== 'function' ? render.props : {};\n  const {\n    activeIndex,\n    onNavigate\n  } = React.useContext(CompositeContext);\n  const {\n    ref,\n    index\n  } = useListItem();\n  const mergedRef = useMergeRefs([ref, forwardedRef, renderElementProps.ref]);\n  const isActive = activeIndex === index;\n  const computedProps = {\n    ...domProps,\n    ...renderElementProps,\n    ref: mergedRef,\n    tabIndex: isActive ? 0 : -1,\n    'data-active': isActive ? '' : undefined,\n    onFocus(e) {\n      domProps.onFocus == null || domProps.onFocus(e);\n      renderElementProps.onFocus == null || renderElementProps.onFocus(e);\n      onNavigate(index);\n    }\n  };\n  return renderJsx(render, computedProps);\n});\n\n// https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379\nconst SafeReact = {\n  ...React\n};\n\nlet serverHandoffComplete = false;\nlet count = 0;\nconst genId = () => // Ensure the id is unique with multiple independent versions of Floating UI\n// on <React 18\n\"floating-ui-\" + Math.random().toString(36).slice(2, 6) + count++;\nfunction useFloatingId() {\n  const [id, setId] = React.useState(() => serverHandoffComplete ? genId() : undefined);\n  useModernLayoutEffect(() => {\n    if (id == null) {\n      setId(genId());\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  React.useEffect(() => {\n    serverHandoffComplete = true;\n  }, []);\n  return id;\n}\nconst useReactId = SafeReact.useId;\n\n/**\n * Uses React 18's built-in `useId()` when available, or falls back to a\n * slightly less performant (requiring a double render) implementation for\n * earlier React versions.\n * @see https://floating-ui.com/docs/react-utils#useid\n */\nconst useId = useReactId || useFloatingId;\n\nlet devMessageSet;\nif (process.env.NODE_ENV !== \"production\") {\n  devMessageSet = /*#__PURE__*/new Set();\n}\nfunction warn() {\n  var _devMessageSet;\n  for (var _len = arguments.length, messages = new Array(_len), _key = 0; _key < _len; _key++) {\n    messages[_key] = arguments[_key];\n  }\n  const message = \"Floating UI: \" + messages.join(' ');\n  if (!((_devMessageSet = devMessageSet) != null && _devMessageSet.has(message))) {\n    var _devMessageSet2;\n    (_devMessageSet2 = devMessageSet) == null || _devMessageSet2.add(message);\n    console.warn(message);\n  }\n}\nfunction error() {\n  var _devMessageSet3;\n  for (var _len2 = arguments.length, messages = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    messages[_key2] = arguments[_key2];\n  }\n  const message = \"Floating UI: \" + messages.join(' ');\n  if (!((_devMessageSet3 = devMessageSet) != null && _devMessageSet3.has(message))) {\n    var _devMessageSet4;\n    (_devMessageSet4 = devMessageSet) == null || _devMessageSet4.add(message);\n    console.error(message);\n  }\n}\n\n/**\n * Renders a pointing arrow triangle.\n * @see https://floating-ui.com/docs/FloatingArrow\n */\nconst FloatingArrow = /*#__PURE__*/React.forwardRef(function FloatingArrow(props, ref) {\n  const {\n    context: {\n      placement,\n      elements: {\n        floating\n      },\n      middlewareData: {\n        arrow,\n        shift\n      }\n    },\n    width = 14,\n    height = 7,\n    tipRadius = 0,\n    strokeWidth = 0,\n    staticOffset,\n    stroke,\n    d,\n    style: {\n      transform,\n      ...restStyle\n    } = {},\n    ...rest\n  } = props;\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!ref) {\n      warn('The `ref` prop is required for `FloatingArrow`.');\n    }\n  }\n  const clipPathId = useId();\n  const [isRTL, setIsRTL] = React.useState(false);\n\n  // https://github.com/floating-ui/floating-ui/issues/2932\n  useModernLayoutEffect(() => {\n    if (!floating) return;\n    const isRTL = getComputedStyle(floating).direction === 'rtl';\n    if (isRTL) {\n      setIsRTL(true);\n    }\n  }, [floating]);\n  if (!floating) {\n    return null;\n  }\n  const [side, alignment] = placement.split('-');\n  const isVerticalSide = side === 'top' || side === 'bottom';\n  let computedStaticOffset = staticOffset;\n  if (isVerticalSide && shift != null && shift.x || !isVerticalSide && shift != null && shift.y) {\n    computedStaticOffset = null;\n  }\n\n  // Strokes must be double the border width, this ensures the stroke's width\n  // works as you'd expect.\n  const computedStrokeWidth = strokeWidth * 2;\n  const halfStrokeWidth = computedStrokeWidth / 2;\n  const svgX = width / 2 * (tipRadius / -8 + 1);\n  const svgY = height / 2 * tipRadius / 4;\n  const isCustomShape = !!d;\n  const yOffsetProp = computedStaticOffset && alignment === 'end' ? 'bottom' : 'top';\n  let xOffsetProp = computedStaticOffset && alignment === 'end' ? 'right' : 'left';\n  if (computedStaticOffset && isRTL) {\n    xOffsetProp = alignment === 'end' ? 'left' : 'right';\n  }\n  const arrowX = (arrow == null ? void 0 : arrow.x) != null ? computedStaticOffset || arrow.x : '';\n  const arrowY = (arrow == null ? void 0 : arrow.y) != null ? computedStaticOffset || arrow.y : '';\n  const dValue = d || 'M0,0' + (\" H\" + width) + (\" L\" + (width - svgX) + \",\" + (height - svgY)) + (\" Q\" + width / 2 + \",\" + height + \" \" + svgX + \",\" + (height - svgY)) + ' Z';\n  const rotation = {\n    top: isCustomShape ? 'rotate(180deg)' : '',\n    left: isCustomShape ? 'rotate(90deg)' : 'rotate(-90deg)',\n    bottom: isCustomShape ? '' : 'rotate(180deg)',\n    right: isCustomShape ? 'rotate(-90deg)' : 'rotate(90deg)'\n  }[side];\n  return /*#__PURE__*/jsxs(\"svg\", {\n    ...rest,\n    \"aria-hidden\": true,\n    ref: ref,\n    width: isCustomShape ? width : width + computedStrokeWidth,\n    height: width,\n    viewBox: \"0 0 \" + width + \" \" + (height > width ? height : width),\n    style: {\n      position: 'absolute',\n      pointerEvents: 'none',\n      [xOffsetProp]: arrowX,\n      [yOffsetProp]: arrowY,\n      [side]: isVerticalSide || isCustomShape ? '100%' : \"calc(100% - \" + computedStrokeWidth / 2 + \"px)\",\n      transform: [rotation, transform].filter(t => !!t).join(' '),\n      ...restStyle\n    },\n    children: [computedStrokeWidth > 0 && /*#__PURE__*/jsx(\"path\", {\n      clipPath: \"url(#\" + clipPathId + \")\",\n      fill: \"none\",\n      stroke: stroke\n      // Account for the stroke on the fill path rendered below.\n      ,\n      strokeWidth: computedStrokeWidth + (d ? 0 : 1),\n      d: dValue\n    }), /*#__PURE__*/jsx(\"path\", {\n      stroke: computedStrokeWidth && !d ? rest.fill : 'none',\n      d: dValue\n    }), /*#__PURE__*/jsx(\"clipPath\", {\n      id: clipPathId,\n      children: /*#__PURE__*/jsx(\"rect\", {\n        x: -halfStrokeWidth,\n        y: halfStrokeWidth * (isCustomShape ? -1 : 1),\n        width: width + computedStrokeWidth,\n        height: width\n      })\n    })]\n  });\n});\n\nfunction createEventEmitter() {\n  const map = new Map();\n  return {\n    emit(event, data) {\n      var _map$get;\n      (_map$get = map.get(event)) == null || _map$get.forEach(listener => listener(data));\n    },\n    on(event, listener) {\n      if (!map.has(event)) {\n        map.set(event, new Set());\n      }\n      map.get(event).add(listener);\n    },\n    off(event, listener) {\n      var _map$get2;\n      (_map$get2 = map.get(event)) == null || _map$get2.delete(listener);\n    }\n  };\n}\n\nconst FloatingNodeContext = /*#__PURE__*/React.createContext(null);\nconst FloatingTreeContext = /*#__PURE__*/React.createContext(null);\n\n/**\n * Returns the parent node id for nested floating elements, if available.\n * Returns `null` for top-level floating elements.\n */\nconst useFloatingParentNodeId = () => {\n  var _React$useContext;\n  return ((_React$useContext = React.useContext(FloatingNodeContext)) == null ? void 0 : _React$useContext.id) || null;\n};\n\n/**\n * Returns the nearest floating tree context, if available.\n */\nconst useFloatingTree = () => React.useContext(FloatingTreeContext);\n\n/**\n * Registers a node into the `FloatingTree`, returning its id.\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction useFloatingNodeId(customParentId) {\n  const id = useId();\n  const tree = useFloatingTree();\n  const reactParentId = useFloatingParentNodeId();\n  const parentId = customParentId || reactParentId;\n  useModernLayoutEffect(() => {\n    if (!id) return;\n    const node = {\n      id,\n      parentId\n    };\n    tree == null || tree.addNode(node);\n    return () => {\n      tree == null || tree.removeNode(node);\n    };\n  }, [tree, id, parentId]);\n  return id;\n}\n/**\n * Provides parent node context for nested floating elements.\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction FloatingNode(props) {\n  const {\n    children,\n    id\n  } = props;\n  const parentId = useFloatingParentNodeId();\n  return /*#__PURE__*/jsx(FloatingNodeContext.Provider, {\n    value: React.useMemo(() => ({\n      id,\n      parentId\n    }), [id, parentId]),\n    children: children\n  });\n}\n/**\n * Provides context for nested floating elements when they are not children of\n * each other on the DOM.\n * This is not necessary in all cases, except when there must be explicit communication between parent and child floating elements. It is necessary for:\n * - The `bubbles` option in the `useDismiss()` Hook\n * - Nested virtual list navigation\n * - Nested floating elements that each open on hover\n * - Custom communication between parent and child floating elements\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction FloatingTree(props) {\n  const {\n    children\n  } = props;\n  const nodesRef = React.useRef([]);\n  const addNode = React.useCallback(node => {\n    nodesRef.current = [...nodesRef.current, node];\n  }, []);\n  const removeNode = React.useCallback(node => {\n    nodesRef.current = nodesRef.current.filter(n => n !== node);\n  }, []);\n  const [events] = React.useState(() => createEventEmitter());\n  return /*#__PURE__*/jsx(FloatingTreeContext.Provider, {\n    value: React.useMemo(() => ({\n      nodesRef,\n      addNode,\n      removeNode,\n      events\n    }), [addNode, removeNode, events]),\n    children: children\n  });\n}\n\nfunction createAttribute(name) {\n  return \"data-floating-ui-\" + name;\n}\n\nfunction clearTimeoutIfSet(timeoutRef) {\n  if (timeoutRef.current !== -1) {\n    clearTimeout(timeoutRef.current);\n    timeoutRef.current = -1;\n  }\n}\n\nconst safePolygonIdentifier = /*#__PURE__*/createAttribute('safe-polygon');\nfunction getDelay(value, prop, pointerType) {\n  if (pointerType && !isMouseLikePointerType(pointerType)) {\n    return 0;\n  }\n  if (typeof value === 'number') {\n    return value;\n  }\n  if (typeof value === 'function') {\n    const result = value();\n    if (typeof result === 'number') {\n      return result;\n    }\n    return result == null ? void 0 : result[prop];\n  }\n  return value == null ? void 0 : value[prop];\n}\nfunction getRestMs(value) {\n  if (typeof value === 'function') {\n    return value();\n  }\n  return value;\n}\n/**\n * Opens the floating element while hovering over the reference element, like\n * CSS `:hover`.\n * @see https://floating-ui.com/docs/useHover\n */\nfunction useHover(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    dataRef,\n    events,\n    elements\n  } = context;\n  const {\n    enabled = true,\n    delay = 0,\n    handleClose = null,\n    mouseOnly = false,\n    restMs = 0,\n    move = true\n  } = props;\n  const tree = useFloatingTree();\n  const parentId = useFloatingParentNodeId();\n  const handleCloseRef = useLatestRef(handleClose);\n  const delayRef = useLatestRef(delay);\n  const openRef = useLatestRef(open);\n  const restMsRef = useLatestRef(restMs);\n  const pointerTypeRef = React.useRef();\n  const timeoutRef = React.useRef(-1);\n  const handlerRef = React.useRef();\n  const restTimeoutRef = React.useRef(-1);\n  const blockMouseMoveRef = React.useRef(true);\n  const performedPointerEventsMutationRef = React.useRef(false);\n  const unbindMouseMoveRef = React.useRef(() => {});\n  const restTimeoutPendingRef = React.useRef(false);\n  const isHoverOpen = useEffectEvent(() => {\n    var _dataRef$current$open;\n    const type = (_dataRef$current$open = dataRef.current.openEvent) == null ? void 0 : _dataRef$current$open.type;\n    return (type == null ? void 0 : type.includes('mouse')) && type !== 'mousedown';\n  });\n\n  // When closing before opening, clear the delay timeouts to cancel it\n  // from showing.\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onOpenChange(_ref) {\n      let {\n        open\n      } = _ref;\n      if (!open) {\n        clearTimeoutIfSet(timeoutRef);\n        clearTimeoutIfSet(restTimeoutRef);\n        blockMouseMoveRef.current = true;\n        restTimeoutPendingRef.current = false;\n      }\n    }\n    events.on('openchange', onOpenChange);\n    return () => {\n      events.off('openchange', onOpenChange);\n    };\n  }, [enabled, events]);\n  React.useEffect(() => {\n    if (!enabled) return;\n    if (!handleCloseRef.current) return;\n    if (!open) return;\n    function onLeave(event) {\n      if (isHoverOpen()) {\n        onOpenChange(false, event, 'hover');\n      }\n    }\n    const html = getDocument$1(elements.floating).documentElement;\n    html.addEventListener('mouseleave', onLeave);\n    return () => {\n      html.removeEventListener('mouseleave', onLeave);\n    };\n  }, [elements.floating, open, onOpenChange, enabled, handleCloseRef, isHoverOpen]);\n  const closeWithDelay = React.useCallback(function (event, runElseBranch, reason) {\n    if (runElseBranch === void 0) {\n      runElseBranch = true;\n    }\n    if (reason === void 0) {\n      reason = 'hover';\n    }\n    const closeDelay = getDelay(delayRef.current, 'close', pointerTypeRef.current);\n    if (closeDelay && !handlerRef.current) {\n      clearTimeoutIfSet(timeoutRef);\n      timeoutRef.current = window.setTimeout(() => onOpenChange(false, event, reason), closeDelay);\n    } else if (runElseBranch) {\n      clearTimeoutIfSet(timeoutRef);\n      onOpenChange(false, event, reason);\n    }\n  }, [delayRef, onOpenChange]);\n  const cleanupMouseMoveHandler = useEffectEvent(() => {\n    unbindMouseMoveRef.current();\n    handlerRef.current = undefined;\n  });\n  const clearPointerEvents = useEffectEvent(() => {\n    if (performedPointerEventsMutationRef.current) {\n      const body = getDocument$1(elements.floating).body;\n      body.style.pointerEvents = '';\n      body.removeAttribute(safePolygonIdentifier);\n      performedPointerEventsMutationRef.current = false;\n    }\n  });\n  const isClickLikeOpenEvent = useEffectEvent(() => {\n    return dataRef.current.openEvent ? ['click', 'mousedown'].includes(dataRef.current.openEvent.type) : false;\n  });\n\n  // Registering the mouse events on the reference directly to bypass React's\n  // delegation system. If the cursor was on a disabled element and then entered\n  // the reference (no gap), `mouseenter` doesn't fire in the delegation system.\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onReferenceMouseEnter(event) {\n      clearTimeoutIfSet(timeoutRef);\n      blockMouseMoveRef.current = false;\n      if (mouseOnly && !isMouseLikePointerType(pointerTypeRef.current) || getRestMs(restMsRef.current) > 0 && !getDelay(delayRef.current, 'open')) {\n        return;\n      }\n      const openDelay = getDelay(delayRef.current, 'open', pointerTypeRef.current);\n      if (openDelay) {\n        timeoutRef.current = window.setTimeout(() => {\n          if (!openRef.current) {\n            onOpenChange(true, event, 'hover');\n          }\n        }, openDelay);\n      } else if (!open) {\n        onOpenChange(true, event, 'hover');\n      }\n    }\n    function onReferenceMouseLeave(event) {\n      if (isClickLikeOpenEvent()) {\n        clearPointerEvents();\n        return;\n      }\n      unbindMouseMoveRef.current();\n      const doc = getDocument$1(elements.floating);\n      clearTimeoutIfSet(restTimeoutRef);\n      restTimeoutPendingRef.current = false;\n      if (handleCloseRef.current && dataRef.current.floatingContext) {\n        // Prevent clearing `onScrollMouseLeave` timeout.\n        if (!open) {\n          clearTimeoutIfSet(timeoutRef);\n        }\n        handlerRef.current = handleCloseRef.current({\n          ...dataRef.current.floatingContext,\n          tree,\n          x: event.clientX,\n          y: event.clientY,\n          onClose() {\n            clearPointerEvents();\n            cleanupMouseMoveHandler();\n            if (!isClickLikeOpenEvent()) {\n              closeWithDelay(event, true, 'safe-polygon');\n            }\n          }\n        });\n        const handler = handlerRef.current;\n        doc.addEventListener('mousemove', handler);\n        unbindMouseMoveRef.current = () => {\n          doc.removeEventListener('mousemove', handler);\n        };\n        return;\n      }\n\n      // Allow interactivity without `safePolygon` on touch devices. With a\n      // pointer, a short close delay is an alternative, so it should work\n      // consistently.\n      const shouldClose = pointerTypeRef.current === 'touch' ? !contains$1(elements.floating, event.relatedTarget) : true;\n      if (shouldClose) {\n        closeWithDelay(event);\n      }\n    }\n\n    // Ensure the floating element closes after scrolling even if the pointer\n    // did not move.\n    // https://github.com/floating-ui/floating-ui/discussions/1692\n    function onScrollMouseLeave(event) {\n      if (isClickLikeOpenEvent()) return;\n      if (!dataRef.current.floatingContext) return;\n      handleCloseRef.current == null || handleCloseRef.current({\n        ...dataRef.current.floatingContext,\n        tree,\n        x: event.clientX,\n        y: event.clientY,\n        onClose() {\n          clearPointerEvents();\n          cleanupMouseMoveHandler();\n          if (!isClickLikeOpenEvent()) {\n            closeWithDelay(event);\n          }\n        }\n      })(event);\n    }\n    function onFloatingMouseEnter() {\n      clearTimeoutIfSet(timeoutRef);\n    }\n    function onFloatingMouseLeave(event) {\n      if (!isClickLikeOpenEvent()) {\n        closeWithDelay(event, false);\n      }\n    }\n    if (isElement(elements.domReference)) {\n      const reference = elements.domReference;\n      const floating = elements.floating;\n      if (open) {\n        reference.addEventListener('mouseleave', onScrollMouseLeave);\n      }\n      if (move) {\n        reference.addEventListener('mousemove', onReferenceMouseEnter, {\n          once: true\n        });\n      }\n      reference.addEventListener('mouseenter', onReferenceMouseEnter);\n      reference.addEventListener('mouseleave', onReferenceMouseLeave);\n      if (floating) {\n        floating.addEventListener('mouseleave', onScrollMouseLeave);\n        floating.addEventListener('mouseenter', onFloatingMouseEnter);\n        floating.addEventListener('mouseleave', onFloatingMouseLeave);\n      }\n      return () => {\n        if (open) {\n          reference.removeEventListener('mouseleave', onScrollMouseLeave);\n        }\n        if (move) {\n          reference.removeEventListener('mousemove', onReferenceMouseEnter);\n        }\n        reference.removeEventListener('mouseenter', onReferenceMouseEnter);\n        reference.removeEventListener('mouseleave', onReferenceMouseLeave);\n        if (floating) {\n          floating.removeEventListener('mouseleave', onScrollMouseLeave);\n          floating.removeEventListener('mouseenter', onFloatingMouseEnter);\n          floating.removeEventListener('mouseleave', onFloatingMouseLeave);\n        }\n      };\n    }\n  }, [elements, enabled, context, mouseOnly, move, closeWithDelay, cleanupMouseMoveHandler, clearPointerEvents, onOpenChange, open, openRef, tree, delayRef, handleCloseRef, dataRef, isClickLikeOpenEvent, restMsRef]);\n\n  // Block pointer-events of every element other than the reference and floating\n  // while the floating element is open and has a `handleClose` handler. Also\n  // handles nested floating elements.\n  // https://github.com/floating-ui/floating-ui/issues/1722\n  useModernLayoutEffect(() => {\n    var _handleCloseRef$curre;\n    if (!enabled) return;\n    if (open && (_handleCloseRef$curre = handleCloseRef.current) != null && (_handleCloseRef$curre = _handleCloseRef$curre.__options) != null && _handleCloseRef$curre.blockPointerEvents && isHoverOpen()) {\n      performedPointerEventsMutationRef.current = true;\n      const floatingEl = elements.floating;\n      if (isElement(elements.domReference) && floatingEl) {\n        var _tree$nodesRef$curren;\n        const body = getDocument$1(elements.floating).body;\n        body.setAttribute(safePolygonIdentifier, '');\n        const ref = elements.domReference;\n        const parentFloating = tree == null || (_tree$nodesRef$curren = tree.nodesRef.current.find(node => node.id === parentId)) == null || (_tree$nodesRef$curren = _tree$nodesRef$curren.context) == null ? void 0 : _tree$nodesRef$curren.elements.floating;\n        if (parentFloating) {\n          parentFloating.style.pointerEvents = '';\n        }\n        body.style.pointerEvents = 'none';\n        ref.style.pointerEvents = 'auto';\n        floatingEl.style.pointerEvents = 'auto';\n        return () => {\n          body.style.pointerEvents = '';\n          ref.style.pointerEvents = '';\n          floatingEl.style.pointerEvents = '';\n        };\n      }\n    }\n  }, [enabled, open, parentId, elements, tree, handleCloseRef, isHoverOpen]);\n  useModernLayoutEffect(() => {\n    if (!open) {\n      pointerTypeRef.current = undefined;\n      restTimeoutPendingRef.current = false;\n      cleanupMouseMoveHandler();\n      clearPointerEvents();\n    }\n  }, [open, cleanupMouseMoveHandler, clearPointerEvents]);\n  React.useEffect(() => {\n    return () => {\n      cleanupMouseMoveHandler();\n      clearTimeoutIfSet(timeoutRef);\n      clearTimeoutIfSet(restTimeoutRef);\n      clearPointerEvents();\n    };\n  }, [enabled, elements.domReference, cleanupMouseMoveHandler, clearPointerEvents]);\n  const reference = React.useMemo(() => {\n    function setPointerRef(event) {\n      pointerTypeRef.current = event.pointerType;\n    }\n    return {\n      onPointerDown: setPointerRef,\n      onPointerEnter: setPointerRef,\n      onMouseMove(event) {\n        const {\n          nativeEvent\n        } = event;\n        function handleMouseMove() {\n          if (!blockMouseMoveRef.current && !openRef.current) {\n            onOpenChange(true, nativeEvent, 'hover');\n          }\n        }\n        if (mouseOnly && !isMouseLikePointerType(pointerTypeRef.current)) {\n          return;\n        }\n        if (open || getRestMs(restMsRef.current) === 0) {\n          return;\n        }\n\n        // Ignore insignificant movements to account for tremors.\n        if (restTimeoutPendingRef.current && event.movementX ** 2 + event.movementY ** 2 < 2) {\n          return;\n        }\n        clearTimeoutIfSet(restTimeoutRef);\n        if (pointerTypeRef.current === 'touch') {\n          handleMouseMove();\n        } else {\n          restTimeoutPendingRef.current = true;\n          restTimeoutRef.current = window.setTimeout(handleMouseMove, getRestMs(restMsRef.current));\n        }\n      }\n    };\n  }, [mouseOnly, onOpenChange, open, openRef, restMsRef]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nconst NOOP = () => {};\nconst FloatingDelayGroupContext = /*#__PURE__*/React.createContext({\n  delay: 0,\n  initialDelay: 0,\n  timeoutMs: 0,\n  currentId: null,\n  setCurrentId: NOOP,\n  setState: NOOP,\n  isInstantPhase: false\n});\n\n/**\n * @deprecated\n * Use the return value of `useDelayGroup()` instead.\n */\nconst useDelayGroupContext = () => React.useContext(FloatingDelayGroupContext);\n/**\n * Provides context for a group of floating elements that should share a\n * `delay`.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nfunction FloatingDelayGroup(props) {\n  const {\n    children,\n    delay,\n    timeoutMs = 0\n  } = props;\n  const [state, setState] = React.useReducer((prev, next) => ({\n    ...prev,\n    ...next\n  }), {\n    delay,\n    timeoutMs,\n    initialDelay: delay,\n    currentId: null,\n    isInstantPhase: false\n  });\n  const initialCurrentIdRef = React.useRef(null);\n  const setCurrentId = React.useCallback(currentId => {\n    setState({\n      currentId\n    });\n  }, []);\n  useModernLayoutEffect(() => {\n    if (state.currentId) {\n      if (initialCurrentIdRef.current === null) {\n        initialCurrentIdRef.current = state.currentId;\n      } else if (!state.isInstantPhase) {\n        setState({\n          isInstantPhase: true\n        });\n      }\n    } else {\n      if (state.isInstantPhase) {\n        setState({\n          isInstantPhase: false\n        });\n      }\n      initialCurrentIdRef.current = null;\n    }\n  }, [state.currentId, state.isInstantPhase]);\n  return /*#__PURE__*/jsx(FloatingDelayGroupContext.Provider, {\n    value: React.useMemo(() => ({\n      ...state,\n      setState,\n      setCurrentId\n    }), [state, setCurrentId]),\n    children: children\n  });\n}\n/**\n * Enables grouping when called inside a component that's a child of a\n * `FloatingDelayGroup`.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nfunction useDelayGroup(context, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    floatingId\n  } = context;\n  const {\n    id: optionId,\n    enabled = true\n  } = options;\n  const id = optionId != null ? optionId : floatingId;\n  const groupContext = useDelayGroupContext();\n  const {\n    currentId,\n    setCurrentId,\n    initialDelay,\n    setState,\n    timeoutMs\n  } = groupContext;\n  useModernLayoutEffect(() => {\n    if (!enabled) return;\n    if (!currentId) return;\n    setState({\n      delay: {\n        open: 1,\n        close: getDelay(initialDelay, 'close')\n      }\n    });\n    if (currentId !== id) {\n      onOpenChange(false);\n    }\n  }, [enabled, id, onOpenChange, setState, currentId, initialDelay]);\n  useModernLayoutEffect(() => {\n    function unset() {\n      onOpenChange(false);\n      setState({\n        delay: initialDelay,\n        currentId: null\n      });\n    }\n    if (!enabled) return;\n    if (!currentId) return;\n    if (!open && currentId === id) {\n      if (timeoutMs) {\n        const timeout = window.setTimeout(unset, timeoutMs);\n        return () => {\n          clearTimeout(timeout);\n        };\n      }\n      unset();\n    }\n  }, [enabled, open, setState, currentId, id, onOpenChange, initialDelay, timeoutMs]);\n  useModernLayoutEffect(() => {\n    if (!enabled) return;\n    if (setCurrentId === NOOP || !open) return;\n    setCurrentId(id);\n  }, [enabled, open, setCurrentId, id]);\n  return groupContext;\n}\n\nconst NextFloatingDelayGroupContext = /*#__PURE__*/React.createContext({\n  hasProvider: false,\n  timeoutMs: 0,\n  delayRef: {\n    current: 0\n  },\n  initialDelayRef: {\n    current: 0\n  },\n  timeoutIdRef: {\n    current: -1\n  },\n  currentIdRef: {\n    current: null\n  },\n  currentContextRef: {\n    current: null\n  }\n});\n/**\n * Experimental next version of `FloatingDelayGroup` to become the default\n * in the future. This component is not yet stable.\n * Provides context for a group of floating elements that should share a\n * `delay`. Unlike `FloatingDelayGroup`, `useNextDelayGroup` with this\n * component does not cause a re-render of unrelated consumers of the\n * context when the delay changes.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nfunction NextFloatingDelayGroup(props) {\n  const {\n    children,\n    delay,\n    timeoutMs = 0\n  } = props;\n  const delayRef = React.useRef(delay);\n  const initialDelayRef = React.useRef(delay);\n  const currentIdRef = React.useRef(null);\n  const currentContextRef = React.useRef(null);\n  const timeoutIdRef = React.useRef(-1);\n  return /*#__PURE__*/jsx(NextFloatingDelayGroupContext.Provider, {\n    value: React.useMemo(() => ({\n      hasProvider: true,\n      delayRef,\n      initialDelayRef,\n      currentIdRef,\n      timeoutMs,\n      currentContextRef,\n      timeoutIdRef\n    }), [timeoutMs]),\n    children: children\n  });\n}\n/**\n * Enables grouping when called inside a component that's a child of a\n * `NextFloatingDelayGroup`.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nfunction useNextDelayGroup(context, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    floatingId\n  } = context;\n  const {\n    enabled = true\n  } = options;\n  const groupContext = React.useContext(NextFloatingDelayGroupContext);\n  const {\n    currentIdRef,\n    delayRef,\n    timeoutMs,\n    initialDelayRef,\n    currentContextRef,\n    hasProvider,\n    timeoutIdRef\n  } = groupContext;\n  const [isInstantPhase, setIsInstantPhase] = React.useState(false);\n  useModernLayoutEffect(() => {\n    function unset() {\n      var _currentContextRef$cu;\n      setIsInstantPhase(false);\n      (_currentContextRef$cu = currentContextRef.current) == null || _currentContextRef$cu.setIsInstantPhase(false);\n      currentIdRef.current = null;\n      currentContextRef.current = null;\n      delayRef.current = initialDelayRef.current;\n    }\n    if (!enabled) return;\n    if (!currentIdRef.current) return;\n    if (!open && currentIdRef.current === floatingId) {\n      setIsInstantPhase(false);\n      if (timeoutMs) {\n        timeoutIdRef.current = window.setTimeout(unset, timeoutMs);\n        return () => {\n          clearTimeout(timeoutIdRef.current);\n        };\n      }\n      unset();\n    }\n  }, [enabled, open, floatingId, currentIdRef, delayRef, timeoutMs, initialDelayRef, currentContextRef, timeoutIdRef]);\n  useModernLayoutEffect(() => {\n    if (!enabled) return;\n    if (!open) return;\n    const prevContext = currentContextRef.current;\n    const prevId = currentIdRef.current;\n    currentContextRef.current = {\n      onOpenChange,\n      setIsInstantPhase\n    };\n    currentIdRef.current = floatingId;\n    delayRef.current = {\n      open: 0,\n      close: getDelay(initialDelayRef.current, 'close')\n    };\n    if (prevId !== null && prevId !== floatingId) {\n      clearTimeoutIfSet(timeoutIdRef);\n      setIsInstantPhase(true);\n      prevContext == null || prevContext.setIsInstantPhase(true);\n      prevContext == null || prevContext.onOpenChange(false);\n    } else {\n      setIsInstantPhase(false);\n      prevContext == null || prevContext.setIsInstantPhase(false);\n    }\n  }, [enabled, open, floatingId, onOpenChange, currentIdRef, delayRef, timeoutMs, initialDelayRef, currentContextRef, timeoutIdRef]);\n  useModernLayoutEffect(() => {\n    return () => {\n      currentContextRef.current = null;\n    };\n  }, [currentContextRef]);\n  return React.useMemo(() => ({\n    hasProvider,\n    delayRef,\n    isInstantPhase\n  }), [hasProvider, delayRef, isInstantPhase]);\n}\n\nlet rafId = 0;\nfunction enqueueFocus(el, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    preventScroll = false,\n    cancelPrevious = true,\n    sync = false\n  } = options;\n  cancelPrevious && cancelAnimationFrame(rafId);\n  const exec = () => el == null ? void 0 : el.focus({\n    preventScroll\n  });\n  if (sync) {\n    exec();\n  } else {\n    rafId = requestAnimationFrame(exec);\n  }\n}\n\nfunction contains(parent, child) {\n  if (!parent || !child) {\n    return false;\n  }\n  const rootNode = child.getRootNode == null ? void 0 : child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n\n  // then fallback to custom implementation with Shadow DOM support\n  if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    while (next) {\n      if (parent === next) {\n        return true;\n      }\n      // @ts-ignore\n      next = next.parentNode || next.host;\n    }\n  }\n\n  // Give up, the result is false\n  return false;\n}\nfunction getTarget(event) {\n  if ('composedPath' in event) {\n    return event.composedPath()[0];\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support\n  // `composedPath()`, but browsers without shadow DOM don't.\n  return event.target;\n}\nfunction getDocument(node) {\n  return (node == null ? void 0 : node.ownerDocument) || document;\n}\n\n// Modified to add conditional `aria-hidden` support:\n// https://github.com/theKashey/aria-hidden/blob/9220c8f4a4fd35f63bee5510a9f41a37264382d4/src/index.ts\nconst counters = {\n  inert: /*#__PURE__*/new WeakMap(),\n  'aria-hidden': /*#__PURE__*/new WeakMap(),\n  none: /*#__PURE__*/new WeakMap()\n};\nfunction getCounterMap(control) {\n  if (control === 'inert') return counters.inert;\n  if (control === 'aria-hidden') return counters['aria-hidden'];\n  return counters.none;\n}\nlet uncontrolledElementsSet = /*#__PURE__*/new WeakSet();\nlet markerMap = {};\nlet lockCount$1 = 0;\nconst supportsInert = () => typeof HTMLElement !== 'undefined' && 'inert' in HTMLElement.prototype;\nconst unwrapHost = node => node && (node.host || unwrapHost(node.parentNode));\nconst correctElements = (parent, targets) => targets.map(target => {\n  if (parent.contains(target)) {\n    return target;\n  }\n  const correctedTarget = unwrapHost(target);\n  if (parent.contains(correctedTarget)) {\n    return correctedTarget;\n  }\n  return null;\n}).filter(x => x != null);\nfunction applyAttributeToOthers(uncorrectedAvoidElements, body, ariaHidden, inert) {\n  const markerName = 'data-floating-ui-inert';\n  const controlAttribute = inert ? 'inert' : ariaHidden ? 'aria-hidden' : null;\n  const avoidElements = correctElements(body, uncorrectedAvoidElements);\n  const elementsToKeep = new Set();\n  const elementsToStop = new Set(avoidElements);\n  const hiddenElements = [];\n  if (!markerMap[markerName]) {\n    markerMap[markerName] = new WeakMap();\n  }\n  const markerCounter = markerMap[markerName];\n  avoidElements.forEach(keep);\n  deep(body);\n  elementsToKeep.clear();\n  function keep(el) {\n    if (!el || elementsToKeep.has(el)) {\n      return;\n    }\n    elementsToKeep.add(el);\n    el.parentNode && keep(el.parentNode);\n  }\n  function deep(parent) {\n    if (!parent || elementsToStop.has(parent)) {\n      return;\n    }\n    [].forEach.call(parent.children, node => {\n      if (getNodeName(node) === 'script') return;\n      if (elementsToKeep.has(node)) {\n        deep(node);\n      } else {\n        const attr = controlAttribute ? node.getAttribute(controlAttribute) : null;\n        const alreadyHidden = attr !== null && attr !== 'false';\n        const counterMap = getCounterMap(controlAttribute);\n        const counterValue = (counterMap.get(node) || 0) + 1;\n        const markerValue = (markerCounter.get(node) || 0) + 1;\n        counterMap.set(node, counterValue);\n        markerCounter.set(node, markerValue);\n        hiddenElements.push(node);\n        if (counterValue === 1 && alreadyHidden) {\n          uncontrolledElementsSet.add(node);\n        }\n        if (markerValue === 1) {\n          node.setAttribute(markerName, '');\n        }\n        if (!alreadyHidden && controlAttribute) {\n          node.setAttribute(controlAttribute, controlAttribute === 'inert' ? '' : 'true');\n        }\n      }\n    });\n  }\n  lockCount$1++;\n  return () => {\n    hiddenElements.forEach(element => {\n      const counterMap = getCounterMap(controlAttribute);\n      const currentCounterValue = counterMap.get(element) || 0;\n      const counterValue = currentCounterValue - 1;\n      const markerValue = (markerCounter.get(element) || 0) - 1;\n      counterMap.set(element, counterValue);\n      markerCounter.set(element, markerValue);\n      if (!counterValue) {\n        if (!uncontrolledElementsSet.has(element) && controlAttribute) {\n          element.removeAttribute(controlAttribute);\n        }\n        uncontrolledElementsSet.delete(element);\n      }\n      if (!markerValue) {\n        element.removeAttribute(markerName);\n      }\n    });\n    lockCount$1--;\n    if (!lockCount$1) {\n      counters.inert = new WeakMap();\n      counters['aria-hidden'] = new WeakMap();\n      counters.none = new WeakMap();\n      uncontrolledElementsSet = new WeakSet();\n      markerMap = {};\n    }\n  };\n}\nfunction markOthers(avoidElements, ariaHidden, inert) {\n  if (ariaHidden === void 0) {\n    ariaHidden = false;\n  }\n  if (inert === void 0) {\n    inert = false;\n  }\n  const body = getDocument(avoidElements[0]).body;\n  return applyAttributeToOthers(avoidElements.concat(Array.from(body.querySelectorAll('[aria-live],[role=\"status\"],output'))), body, ariaHidden, inert);\n}\n\nconst HIDDEN_STYLES = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'fixed',\n  whiteSpace: 'nowrap',\n  width: '1px',\n  top: 0,\n  left: 0\n};\nconst FocusGuard = /*#__PURE__*/React.forwardRef(function FocusGuard(props, ref) {\n  const [role, setRole] = React.useState();\n  useModernLayoutEffect(() => {\n    if (isSafari()) {\n      // Unlike other screen readers such as NVDA and JAWS, the virtual cursor\n      // on VoiceOver does trigger the onFocus event, so we can use the focus\n      // trap element. On Safari, only buttons trigger the onFocus event.\n      // NB: \"group\" role in the Sandbox no longer appears to work, must be a\n      // button role.\n      setRole('button');\n    }\n  }, []);\n  const restProps = {\n    ref,\n    tabIndex: 0,\n    // Role is only for VoiceOver\n    role,\n    'aria-hidden': role ? undefined : true,\n    [createAttribute('focus-guard')]: '',\n    style: HIDDEN_STYLES\n  };\n  return /*#__PURE__*/jsx(\"span\", {\n    ...props,\n    ...restProps\n  });\n});\n\nconst PortalContext = /*#__PURE__*/React.createContext(null);\nconst attr = /*#__PURE__*/createAttribute('portal');\n/**\n * @see https://floating-ui.com/docs/FloatingPortal#usefloatingportalnode\n */\nfunction useFloatingPortalNode(props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    id,\n    root\n  } = props;\n  const uniqueId = useId();\n  const portalContext = usePortalContext();\n  const [portalNode, setPortalNode] = React.useState(null);\n  const portalNodeRef = React.useRef(null);\n  useModernLayoutEffect(() => {\n    return () => {\n      portalNode == null || portalNode.remove();\n      // Allow the subsequent layout effects to create a new node on updates.\n      // The portal node will still be cleaned up on unmount.\n      // https://github.com/floating-ui/floating-ui/issues/2454\n      queueMicrotask(() => {\n        portalNodeRef.current = null;\n      });\n    };\n  }, [portalNode]);\n  useModernLayoutEffect(() => {\n    // Wait for the uniqueId to be generated before creating the portal node in\n    // React <18 (using `useFloatingId` instead of the native `useId`).\n    // https://github.com/floating-ui/floating-ui/issues/2778\n    if (!uniqueId) return;\n    if (portalNodeRef.current) return;\n    const existingIdRoot = id ? document.getElementById(id) : null;\n    if (!existingIdRoot) return;\n    const subRoot = document.createElement('div');\n    subRoot.id = uniqueId;\n    subRoot.setAttribute(attr, '');\n    existingIdRoot.appendChild(subRoot);\n    portalNodeRef.current = subRoot;\n    setPortalNode(subRoot);\n  }, [id, uniqueId]);\n  useModernLayoutEffect(() => {\n    // Wait for the root to exist before creating the portal node. The root must\n    // be stored in state, not a ref, for this to work reactively.\n    if (root === null) return;\n    if (!uniqueId) return;\n    if (portalNodeRef.current) return;\n    let container = root || (portalContext == null ? void 0 : portalContext.portalNode);\n    if (container && !isElement(container)) container = container.current;\n    container = container || document.body;\n    let idWrapper = null;\n    if (id) {\n      idWrapper = document.createElement('div');\n      idWrapper.id = id;\n      container.appendChild(idWrapper);\n    }\n    const subRoot = document.createElement('div');\n    subRoot.id = uniqueId;\n    subRoot.setAttribute(attr, '');\n    container = idWrapper || container;\n    container.appendChild(subRoot);\n    portalNodeRef.current = subRoot;\n    setPortalNode(subRoot);\n  }, [id, root, uniqueId, portalContext]);\n  return portalNode;\n}\n/**\n * Portals the floating element into a given container element — by default,\n * outside of the app root and into the body.\n * This is necessary to ensure the floating element can appear outside any\n * potential parent containers that cause clipping (such as `overflow: hidden`),\n * while retaining its location in the React tree.\n * @see https://floating-ui.com/docs/FloatingPortal\n */\nfunction FloatingPortal(props) {\n  const {\n    children,\n    id,\n    root,\n    preserveTabOrder = true\n  } = props;\n  const portalNode = useFloatingPortalNode({\n    id,\n    root\n  });\n  const [focusManagerState, setFocusManagerState] = React.useState(null);\n  const beforeOutsideRef = React.useRef(null);\n  const afterOutsideRef = React.useRef(null);\n  const beforeInsideRef = React.useRef(null);\n  const afterInsideRef = React.useRef(null);\n  const modal = focusManagerState == null ? void 0 : focusManagerState.modal;\n  const open = focusManagerState == null ? void 0 : focusManagerState.open;\n  const shouldRenderGuards =\n  // The FocusManager and therefore floating element are currently open/\n  // rendered.\n  !!focusManagerState &&\n  // Guards are only for non-modal focus management.\n  !focusManagerState.modal &&\n  // Don't render if unmount is transitioning.\n  focusManagerState.open && preserveTabOrder && !!(root || portalNode);\n\n  // https://codesandbox.io/s/tabbable-portal-f4tng?file=/src/TabbablePortal.tsx\n  React.useEffect(() => {\n    if (!portalNode || !preserveTabOrder || modal) {\n      return;\n    }\n\n    // Make sure elements inside the portal element are tabbable only when the\n    // portal has already been focused, either by tabbing into a focus trap\n    // element outside or using the mouse.\n    function onFocus(event) {\n      if (portalNode && isOutsideEvent(event)) {\n        const focusing = event.type === 'focusin';\n        const manageFocus = focusing ? enableFocusInside : disableFocusInside;\n        manageFocus(portalNode);\n      }\n    }\n    // Listen to the event on the capture phase so they run before the focus\n    // trap elements onFocus prop is called.\n    portalNode.addEventListener('focusin', onFocus, true);\n    portalNode.addEventListener('focusout', onFocus, true);\n    return () => {\n      portalNode.removeEventListener('focusin', onFocus, true);\n      portalNode.removeEventListener('focusout', onFocus, true);\n    };\n  }, [portalNode, preserveTabOrder, modal]);\n  React.useEffect(() => {\n    if (!portalNode) return;\n    if (open) return;\n    enableFocusInside(portalNode);\n  }, [open, portalNode]);\n  return /*#__PURE__*/jsxs(PortalContext.Provider, {\n    value: React.useMemo(() => ({\n      preserveTabOrder,\n      beforeOutsideRef,\n      afterOutsideRef,\n      beforeInsideRef,\n      afterInsideRef,\n      portalNode,\n      setFocusManagerState\n    }), [preserveTabOrder, portalNode]),\n    children: [shouldRenderGuards && portalNode && /*#__PURE__*/jsx(FocusGuard, {\n      \"data-type\": \"outside\",\n      ref: beforeOutsideRef,\n      onFocus: event => {\n        if (isOutsideEvent(event, portalNode)) {\n          var _beforeInsideRef$curr;\n          (_beforeInsideRef$curr = beforeInsideRef.current) == null || _beforeInsideRef$curr.focus();\n        } else {\n          const domReference = focusManagerState ? focusManagerState.domReference : null;\n          const prevTabbable = getPreviousTabbable(domReference);\n          prevTabbable == null || prevTabbable.focus();\n        }\n      }\n    }), shouldRenderGuards && portalNode && /*#__PURE__*/jsx(\"span\", {\n      \"aria-owns\": portalNode.id,\n      style: HIDDEN_STYLES\n    }), portalNode && /*#__PURE__*/ReactDOM.createPortal(children, portalNode), shouldRenderGuards && portalNode && /*#__PURE__*/jsx(FocusGuard, {\n      \"data-type\": \"outside\",\n      ref: afterOutsideRef,\n      onFocus: event => {\n        if (isOutsideEvent(event, portalNode)) {\n          var _afterInsideRef$curre;\n          (_afterInsideRef$curre = afterInsideRef.current) == null || _afterInsideRef$curre.focus();\n        } else {\n          const domReference = focusManagerState ? focusManagerState.domReference : null;\n          const nextTabbable = getNextTabbable(domReference);\n          nextTabbable == null || nextTabbable.focus();\n          (focusManagerState == null ? void 0 : focusManagerState.closeOnFocusOut) && (focusManagerState == null ? void 0 : focusManagerState.onOpenChange(false, event.nativeEvent, 'focus-out'));\n        }\n      }\n    })]\n  });\n}\nconst usePortalContext = () => React.useContext(PortalContext);\n\nfunction useLiteMergeRefs(refs) {\n  return React.useMemo(() => {\n    return value => {\n      refs.forEach(ref => {\n        if (ref) {\n          ref.current = value;\n        }\n      });\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}\n\nconst LIST_LIMIT = 20;\nlet previouslyFocusedElements = [];\nfunction addPreviouslyFocusedElement(element) {\n  previouslyFocusedElements = previouslyFocusedElements.filter(el => el.isConnected);\n  if (element && getNodeName(element) !== 'body') {\n    previouslyFocusedElements.push(element);\n    if (previouslyFocusedElements.length > LIST_LIMIT) {\n      previouslyFocusedElements = previouslyFocusedElements.slice(-20);\n    }\n  }\n}\nfunction getPreviouslyFocusedElement() {\n  return previouslyFocusedElements.slice().reverse().find(el => el.isConnected);\n}\nfunction getFirstTabbableElement(container) {\n  const tabbableOptions = getTabbableOptions();\n  if (isTabbable(container, tabbableOptions)) {\n    return container;\n  }\n  return tabbable(container, tabbableOptions)[0] || container;\n}\nfunction handleTabIndex(floatingFocusElement, orderRef) {\n  var _floatingFocusElement;\n  if (!orderRef.current.includes('floating') && !((_floatingFocusElement = floatingFocusElement.getAttribute('role')) != null && _floatingFocusElement.includes('dialog'))) {\n    return;\n  }\n  const options = getTabbableOptions();\n  const focusableElements = focusable(floatingFocusElement, options);\n  const tabbableContent = focusableElements.filter(element => {\n    const dataTabIndex = element.getAttribute('data-tabindex') || '';\n    return isTabbable(element, options) || element.hasAttribute('data-tabindex') && !dataTabIndex.startsWith('-');\n  });\n  const tabIndex = floatingFocusElement.getAttribute('tabindex');\n  if (orderRef.current.includes('floating') || tabbableContent.length === 0) {\n    if (tabIndex !== '0') {\n      floatingFocusElement.setAttribute('tabindex', '0');\n    }\n  } else if (tabIndex !== '-1' || floatingFocusElement.hasAttribute('data-tabindex') && floatingFocusElement.getAttribute('data-tabindex') !== '-1') {\n    floatingFocusElement.setAttribute('tabindex', '-1');\n    floatingFocusElement.setAttribute('data-tabindex', '-1');\n  }\n}\nconst VisuallyHiddenDismiss = /*#__PURE__*/React.forwardRef(function VisuallyHiddenDismiss(props, ref) {\n  return /*#__PURE__*/jsx(\"button\", {\n    ...props,\n    type: \"button\",\n    ref: ref,\n    tabIndex: -1,\n    style: HIDDEN_STYLES\n  });\n});\n/**\n * Provides focus management for the floating element.\n * @see https://floating-ui.com/docs/FloatingFocusManager\n */\nfunction FloatingFocusManager(props) {\n  const {\n    context,\n    children,\n    disabled = false,\n    order = ['content'],\n    guards: _guards = true,\n    initialFocus = 0,\n    returnFocus = true,\n    restoreFocus = false,\n    modal = true,\n    visuallyHiddenDismiss = false,\n    closeOnFocusOut = true,\n    outsideElementsInert = false,\n    getInsideElements: _getInsideElements = () => []\n  } = props;\n  const {\n    open,\n    onOpenChange,\n    events,\n    dataRef,\n    elements: {\n      domReference,\n      floating\n    }\n  } = context;\n  const getNodeId = useEffectEvent(() => {\n    var _dataRef$current$floa;\n    return (_dataRef$current$floa = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa.nodeId;\n  });\n  const getInsideElements = useEffectEvent(_getInsideElements);\n  const ignoreInitialFocus = typeof initialFocus === 'number' && initialFocus < 0;\n  // If the reference is a combobox and is typeable (e.g. input/textarea),\n  // there are different focus semantics. The guards should not be rendered, but\n  // aria-hidden should be applied to all nodes still. Further, the visually\n  // hidden dismiss button should only appear at the end of the list, not the\n  // start.\n  const isUntrappedTypeableCombobox = isTypeableCombobox(domReference) && ignoreInitialFocus;\n\n  // Force the guards to be rendered if the `inert` attribute is not supported.\n  const inertSupported = supportsInert();\n  const guards = inertSupported ? _guards : true;\n  const useInert = !guards || inertSupported && outsideElementsInert;\n  const orderRef = useLatestRef(order);\n  const initialFocusRef = useLatestRef(initialFocus);\n  const returnFocusRef = useLatestRef(returnFocus);\n  const tree = useFloatingTree();\n  const portalContext = usePortalContext();\n  const startDismissButtonRef = React.useRef(null);\n  const endDismissButtonRef = React.useRef(null);\n  const preventReturnFocusRef = React.useRef(false);\n  const isPointerDownRef = React.useRef(false);\n  const tabbableIndexRef = React.useRef(-1);\n  const isInsidePortal = portalContext != null;\n  const floatingFocusElement = getFloatingFocusElement(floating);\n  const getTabbableContent = useEffectEvent(function (container) {\n    if (container === void 0) {\n      container = floatingFocusElement;\n    }\n    return container ? tabbable(container, getTabbableOptions()) : [];\n  });\n  const getTabbableElements = useEffectEvent(container => {\n    const content = getTabbableContent(container);\n    return orderRef.current.map(type => {\n      if (domReference && type === 'reference') {\n        return domReference;\n      }\n      if (floatingFocusElement && type === 'floating') {\n        return floatingFocusElement;\n      }\n      return content;\n    }).filter(Boolean).flat();\n  });\n  React.useEffect(() => {\n    if (disabled) return;\n    if (!modal) return;\n    function onKeyDown(event) {\n      if (event.key === 'Tab') {\n        // The focus guards have nothing to focus, so we need to stop the event.\n        if (contains$1(floatingFocusElement, activeElement(getDocument$1(floatingFocusElement))) && getTabbableContent().length === 0 && !isUntrappedTypeableCombobox) {\n          stopEvent(event);\n        }\n        const els = getTabbableElements();\n        const target = getTarget$1(event);\n        if (orderRef.current[0] === 'reference' && target === domReference) {\n          stopEvent(event);\n          if (event.shiftKey) {\n            enqueueFocus(els[els.length - 1]);\n          } else {\n            enqueueFocus(els[1]);\n          }\n        }\n        if (orderRef.current[1] === 'floating' && target === floatingFocusElement && event.shiftKey) {\n          stopEvent(event);\n          enqueueFocus(els[0]);\n        }\n      }\n    }\n    const doc = getDocument$1(floatingFocusElement);\n    doc.addEventListener('keydown', onKeyDown);\n    return () => {\n      doc.removeEventListener('keydown', onKeyDown);\n    };\n  }, [disabled, domReference, floatingFocusElement, modal, orderRef, isUntrappedTypeableCombobox, getTabbableContent, getTabbableElements]);\n  React.useEffect(() => {\n    if (disabled) return;\n    if (!floating) return;\n    function handleFocusIn(event) {\n      const target = getTarget$1(event);\n      const tabbableContent = getTabbableContent();\n      const tabbableIndex = tabbableContent.indexOf(target);\n      if (tabbableIndex !== -1) {\n        tabbableIndexRef.current = tabbableIndex;\n      }\n    }\n    floating.addEventListener('focusin', handleFocusIn);\n    return () => {\n      floating.removeEventListener('focusin', handleFocusIn);\n    };\n  }, [disabled, floating, getTabbableContent]);\n  React.useEffect(() => {\n    if (disabled) return;\n    if (!closeOnFocusOut) return;\n\n    // In Safari, buttons lose focus when pressing them.\n    function handlePointerDown() {\n      isPointerDownRef.current = true;\n      setTimeout(() => {\n        isPointerDownRef.current = false;\n      });\n    }\n    function handleFocusOutside(event) {\n      const relatedTarget = event.relatedTarget;\n      const currentTarget = event.currentTarget;\n      const target = getTarget$1(event);\n      queueMicrotask(() => {\n        const nodeId = getNodeId();\n        const movedToUnrelatedNode = !(contains$1(domReference, relatedTarget) || contains$1(floating, relatedTarget) || contains$1(relatedTarget, floating) || contains$1(portalContext == null ? void 0 : portalContext.portalNode, relatedTarget) || relatedTarget != null && relatedTarget.hasAttribute(createAttribute('focus-guard')) || tree && (getNodeChildren$1(tree.nodesRef.current, nodeId).find(node => {\n          var _node$context, _node$context2;\n          return contains$1((_node$context = node.context) == null ? void 0 : _node$context.elements.floating, relatedTarget) || contains$1((_node$context2 = node.context) == null ? void 0 : _node$context2.elements.domReference, relatedTarget);\n        }) || getNodeAncestors(tree.nodesRef.current, nodeId).find(node => {\n          var _node$context3, _node$context4, _node$context5;\n          return [(_node$context3 = node.context) == null ? void 0 : _node$context3.elements.floating, getFloatingFocusElement((_node$context4 = node.context) == null ? void 0 : _node$context4.elements.floating)].includes(relatedTarget) || ((_node$context5 = node.context) == null ? void 0 : _node$context5.elements.domReference) === relatedTarget;\n        })));\n        if (currentTarget === domReference && floatingFocusElement) {\n          handleTabIndex(floatingFocusElement, orderRef);\n        }\n\n        // Restore focus to the previous tabbable element index to prevent\n        // focus from being lost outside the floating tree.\n        if (restoreFocus && currentTarget !== domReference && !(target != null && target.isConnected) && activeElement(getDocument$1(floatingFocusElement)) === getDocument$1(floatingFocusElement).body) {\n          // Let `FloatingPortal` effect knows that focus is still inside the\n          // floating tree.\n          if (isHTMLElement(floatingFocusElement)) {\n            floatingFocusElement.focus();\n          }\n          const prevTabbableIndex = tabbableIndexRef.current;\n          const tabbableContent = getTabbableContent();\n          const nodeToFocus = tabbableContent[prevTabbableIndex] || tabbableContent[tabbableContent.length - 1] || floatingFocusElement;\n          if (isHTMLElement(nodeToFocus)) {\n            nodeToFocus.focus();\n          }\n        }\n\n        // https://github.com/floating-ui/floating-ui/issues/3060\n        if (dataRef.current.insideReactTree) {\n          dataRef.current.insideReactTree = false;\n          return;\n        }\n\n        // Focus did not move inside the floating tree, and there are no tabbable\n        // portal guards to handle closing.\n        if ((isUntrappedTypeableCombobox ? true : !modal) && relatedTarget && movedToUnrelatedNode && !isPointerDownRef.current &&\n        // Fix React 18 Strict Mode returnFocus due to double rendering.\n        relatedTarget !== getPreviouslyFocusedElement()) {\n          preventReturnFocusRef.current = true;\n          onOpenChange(false, event, 'focus-out');\n        }\n      });\n    }\n    if (floating && isHTMLElement(domReference)) {\n      domReference.addEventListener('focusout', handleFocusOutside);\n      domReference.addEventListener('pointerdown', handlePointerDown);\n      floating.addEventListener('focusout', handleFocusOutside);\n      return () => {\n        domReference.removeEventListener('focusout', handleFocusOutside);\n        domReference.removeEventListener('pointerdown', handlePointerDown);\n        floating.removeEventListener('focusout', handleFocusOutside);\n      };\n    }\n  }, [disabled, domReference, floating, floatingFocusElement, modal, tree, portalContext, onOpenChange, closeOnFocusOut, restoreFocus, getTabbableContent, isUntrappedTypeableCombobox, getNodeId, orderRef, dataRef]);\n  const beforeGuardRef = React.useRef(null);\n  const afterGuardRef = React.useRef(null);\n  const mergedBeforeGuardRef = useLiteMergeRefs([beforeGuardRef, portalContext == null ? void 0 : portalContext.beforeInsideRef]);\n  const mergedAfterGuardRef = useLiteMergeRefs([afterGuardRef, portalContext == null ? void 0 : portalContext.afterInsideRef]);\n  React.useEffect(() => {\n    var _portalContext$portal, _ancestors$find;\n    if (disabled) return;\n    if (!floating) return;\n\n    // Don't hide portals nested within the parent portal.\n    const portalNodes = Array.from((portalContext == null || (_portalContext$portal = portalContext.portalNode) == null ? void 0 : _portalContext$portal.querySelectorAll(\"[\" + createAttribute('portal') + \"]\")) || []);\n    const ancestors = tree ? getNodeAncestors(tree.nodesRef.current, getNodeId()) : [];\n    const ancestorFloatingNodes = tree && !modal ? ancestors.map(node => {\n      var _node$context6;\n      return (_node$context6 = node.context) == null ? void 0 : _node$context6.elements.floating;\n    }) : [];\n    const rootAncestorComboboxDomReference = (_ancestors$find = ancestors.find(node => {\n      var _node$context7;\n      return isTypeableCombobox(((_node$context7 = node.context) == null ? void 0 : _node$context7.elements.domReference) || null);\n    })) == null || (_ancestors$find = _ancestors$find.context) == null ? void 0 : _ancestors$find.elements.domReference;\n    const insideElements = [floating, rootAncestorComboboxDomReference, ...portalNodes, ...ancestorFloatingNodes, ...getInsideElements(), startDismissButtonRef.current, endDismissButtonRef.current, beforeGuardRef.current, afterGuardRef.current, portalContext == null ? void 0 : portalContext.beforeOutsideRef.current, portalContext == null ? void 0 : portalContext.afterOutsideRef.current, orderRef.current.includes('reference') || isUntrappedTypeableCombobox ? domReference : null].filter(x => x != null);\n    const cleanup = modal || isUntrappedTypeableCombobox ? markOthers(insideElements, !useInert, useInert) : markOthers(insideElements);\n    return () => {\n      cleanup();\n    };\n  }, [disabled, domReference, floating, modal, orderRef, portalContext, isUntrappedTypeableCombobox, guards, useInert, tree, getNodeId, getInsideElements]);\n  useModernLayoutEffect(() => {\n    if (disabled || !isHTMLElement(floatingFocusElement)) return;\n    const doc = getDocument$1(floatingFocusElement);\n    const previouslyFocusedElement = activeElement(doc);\n\n    // Wait for any layout effect state setters to execute to set `tabIndex`.\n    queueMicrotask(() => {\n      const focusableElements = getTabbableElements(floatingFocusElement);\n      const initialFocusValue = initialFocusRef.current;\n      const elToFocus = (typeof initialFocusValue === 'number' ? focusableElements[initialFocusValue] : initialFocusValue.current) || floatingFocusElement;\n      const focusAlreadyInsideFloatingEl = contains$1(floatingFocusElement, previouslyFocusedElement);\n      if (!ignoreInitialFocus && !focusAlreadyInsideFloatingEl && open) {\n        enqueueFocus(elToFocus, {\n          preventScroll: elToFocus === floatingFocusElement\n        });\n      }\n    });\n  }, [disabled, open, floatingFocusElement, ignoreInitialFocus, getTabbableElements, initialFocusRef]);\n  useModernLayoutEffect(() => {\n    if (disabled || !floatingFocusElement) return;\n    const doc = getDocument$1(floatingFocusElement);\n    const previouslyFocusedElement = activeElement(doc);\n    addPreviouslyFocusedElement(previouslyFocusedElement);\n\n    // Dismissing via outside press should always ignore `returnFocus` to\n    // prevent unwanted scrolling.\n    function onOpenChange(_ref) {\n      let {\n        reason,\n        event,\n        nested\n      } = _ref;\n      if (['hover', 'safe-polygon'].includes(reason) && event.type === 'mouseleave') {\n        preventReturnFocusRef.current = true;\n      }\n      if (reason !== 'outside-press') return;\n      if (nested) {\n        preventReturnFocusRef.current = false;\n      } else if (isVirtualClick(event) || isVirtualPointerEvent(event)) {\n        preventReturnFocusRef.current = false;\n      } else {\n        let isPreventScrollSupported = false;\n        document.createElement('div').focus({\n          get preventScroll() {\n            isPreventScrollSupported = true;\n            return false;\n          }\n        });\n        if (isPreventScrollSupported) {\n          preventReturnFocusRef.current = false;\n        } else {\n          preventReturnFocusRef.current = true;\n        }\n      }\n    }\n    events.on('openchange', onOpenChange);\n    const fallbackEl = doc.createElement('span');\n    fallbackEl.setAttribute('tabindex', '-1');\n    fallbackEl.setAttribute('aria-hidden', 'true');\n    Object.assign(fallbackEl.style, HIDDEN_STYLES);\n    if (isInsidePortal && domReference) {\n      domReference.insertAdjacentElement('afterend', fallbackEl);\n    }\n    function getReturnElement() {\n      if (typeof returnFocusRef.current === 'boolean') {\n        const el = domReference || getPreviouslyFocusedElement();\n        return el && el.isConnected ? el : fallbackEl;\n      }\n      return returnFocusRef.current.current || fallbackEl;\n    }\n    return () => {\n      events.off('openchange', onOpenChange);\n      const activeEl = activeElement(doc);\n      const isFocusInsideFloatingTree = contains$1(floating, activeEl) || tree && getNodeChildren$1(tree.nodesRef.current, getNodeId(), false).some(node => {\n        var _node$context8;\n        return contains$1((_node$context8 = node.context) == null ? void 0 : _node$context8.elements.floating, activeEl);\n      });\n      const returnElement = getReturnElement();\n      queueMicrotask(() => {\n        // This is `returnElement`, if it's tabbable, or its first tabbable child.\n        const tabbableReturnElement = getFirstTabbableElement(returnElement);\n        if (\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        returnFocusRef.current && !preventReturnFocusRef.current && isHTMLElement(tabbableReturnElement) && (\n        // If the focus moved somewhere else after mount, avoid returning focus\n        // since it likely entered a different element which should be\n        // respected: https://github.com/floating-ui/floating-ui/issues/2607\n        tabbableReturnElement !== activeEl && activeEl !== doc.body ? isFocusInsideFloatingTree : true)) {\n          tabbableReturnElement.focus({\n            preventScroll: true\n          });\n        }\n        fallbackEl.remove();\n      });\n    };\n  }, [disabled, floating, floatingFocusElement, returnFocusRef, dataRef, events, tree, isInsidePortal, domReference, getNodeId]);\n  React.useEffect(() => {\n    // The `returnFocus` cleanup behavior is inside a microtask; ensure we\n    // wait for it to complete before resetting the flag.\n    queueMicrotask(() => {\n      preventReturnFocusRef.current = false;\n    });\n  }, [disabled]);\n\n  // Synchronize the `context` & `modal` value to the FloatingPortal context.\n  // It will decide whether or not it needs to render its own guards.\n  useModernLayoutEffect(() => {\n    if (disabled) return;\n    if (!portalContext) return;\n    portalContext.setFocusManagerState({\n      modal,\n      closeOnFocusOut,\n      open,\n      onOpenChange,\n      domReference\n    });\n    return () => {\n      portalContext.setFocusManagerState(null);\n    };\n  }, [disabled, portalContext, modal, open, onOpenChange, closeOnFocusOut, domReference]);\n  useModernLayoutEffect(() => {\n    if (disabled) return;\n    if (!floatingFocusElement) return;\n    handleTabIndex(floatingFocusElement, orderRef);\n  }, [disabled, floatingFocusElement, orderRef]);\n  function renderDismissButton(location) {\n    if (disabled || !visuallyHiddenDismiss || !modal) {\n      return null;\n    }\n    return /*#__PURE__*/jsx(VisuallyHiddenDismiss, {\n      ref: location === 'start' ? startDismissButtonRef : endDismissButtonRef,\n      onClick: event => onOpenChange(false, event.nativeEvent),\n      children: typeof visuallyHiddenDismiss === 'string' ? visuallyHiddenDismiss : 'Dismiss'\n    });\n  }\n  const shouldRenderGuards = !disabled && guards && (modal ? !isUntrappedTypeableCombobox : true) && (isInsidePortal || modal);\n  return /*#__PURE__*/jsxs(Fragment, {\n    children: [shouldRenderGuards && /*#__PURE__*/jsx(FocusGuard, {\n      \"data-type\": \"inside\",\n      ref: mergedBeforeGuardRef,\n      onFocus: event => {\n        if (modal) {\n          const els = getTabbableElements();\n          enqueueFocus(order[0] === 'reference' ? els[0] : els[els.length - 1]);\n        } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {\n          preventReturnFocusRef.current = false;\n          if (isOutsideEvent(event, portalContext.portalNode)) {\n            const nextTabbable = getNextTabbable(domReference);\n            nextTabbable == null || nextTabbable.focus();\n          } else {\n            var _portalContext$before;\n            (_portalContext$before = portalContext.beforeOutsideRef.current) == null || _portalContext$before.focus();\n          }\n        }\n      }\n    }), !isUntrappedTypeableCombobox && renderDismissButton('start'), children, renderDismissButton('end'), shouldRenderGuards && /*#__PURE__*/jsx(FocusGuard, {\n      \"data-type\": \"inside\",\n      ref: mergedAfterGuardRef,\n      onFocus: event => {\n        if (modal) {\n          enqueueFocus(getTabbableElements()[0]);\n        } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {\n          if (closeOnFocusOut) {\n            preventReturnFocusRef.current = true;\n          }\n          if (isOutsideEvent(event, portalContext.portalNode)) {\n            const prevTabbable = getPreviousTabbable(domReference);\n            prevTabbable == null || prevTabbable.focus();\n          } else {\n            var _portalContext$afterO;\n            (_portalContext$afterO = portalContext.afterOutsideRef.current) == null || _portalContext$afterO.focus();\n          }\n        }\n      }\n    })]\n  });\n}\n\nlet lockCount = 0;\nconst scrollbarProperty = '--floating-ui-scrollbar-width';\nfunction enableScrollLock() {\n  const platform = getPlatform();\n  const isIOS = /iP(hone|ad|od)|iOS/.test(platform) ||\n  // iPads can claim to be MacIntel\n  platform === 'MacIntel' && navigator.maxTouchPoints > 1;\n  const bodyStyle = document.body.style;\n  // RTL <body> scrollbar\n  const scrollbarX = Math.round(document.documentElement.getBoundingClientRect().left) + document.documentElement.scrollLeft;\n  const paddingProp = scrollbarX ? 'paddingLeft' : 'paddingRight';\n  const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;\n  const scrollX = bodyStyle.left ? parseFloat(bodyStyle.left) : window.scrollX;\n  const scrollY = bodyStyle.top ? parseFloat(bodyStyle.top) : window.scrollY;\n  bodyStyle.overflow = 'hidden';\n  bodyStyle.setProperty(scrollbarProperty, scrollbarWidth + \"px\");\n  if (scrollbarWidth) {\n    bodyStyle[paddingProp] = scrollbarWidth + \"px\";\n  }\n\n  // Only iOS doesn't respect `overflow: hidden` on document.body, and this\n  // technique has fewer side effects.\n  if (isIOS) {\n    var _window$visualViewpor, _window$visualViewpor2;\n    // iOS 12 does not support `visualViewport`.\n    const offsetLeft = ((_window$visualViewpor = window.visualViewport) == null ? void 0 : _window$visualViewpor.offsetLeft) || 0;\n    const offsetTop = ((_window$visualViewpor2 = window.visualViewport) == null ? void 0 : _window$visualViewpor2.offsetTop) || 0;\n    Object.assign(bodyStyle, {\n      position: 'fixed',\n      top: -(scrollY - Math.floor(offsetTop)) + \"px\",\n      left: -(scrollX - Math.floor(offsetLeft)) + \"px\",\n      right: '0'\n    });\n  }\n  return () => {\n    Object.assign(bodyStyle, {\n      overflow: '',\n      [paddingProp]: ''\n    });\n    bodyStyle.removeProperty(scrollbarProperty);\n    if (isIOS) {\n      Object.assign(bodyStyle, {\n        position: '',\n        top: '',\n        left: '',\n        right: ''\n      });\n      window.scrollTo(scrollX, scrollY);\n    }\n  };\n}\nlet cleanup = () => {};\n\n/**\n * Provides base styling for a fixed overlay element to dim content or block\n * pointer events behind a floating element.\n * It's a regular `<div>`, so it can be styled via any CSS solution you prefer.\n * @see https://floating-ui.com/docs/FloatingOverlay\n */\nconst FloatingOverlay = /*#__PURE__*/React.forwardRef(function FloatingOverlay(props, ref) {\n  const {\n    lockScroll = false,\n    ...rest\n  } = props;\n  useModernLayoutEffect(() => {\n    if (!lockScroll) return;\n    lockCount++;\n    if (lockCount === 1) {\n      cleanup = enableScrollLock();\n    }\n    return () => {\n      lockCount--;\n      if (lockCount === 0) {\n        cleanup();\n      }\n    };\n  }, [lockScroll]);\n  return /*#__PURE__*/jsx(\"div\", {\n    ref: ref,\n    ...rest,\n    style: {\n      position: 'fixed',\n      overflow: 'auto',\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0,\n      ...rest.style\n    }\n  });\n});\n\nfunction isButtonTarget(event) {\n  return isHTMLElement(event.target) && event.target.tagName === 'BUTTON';\n}\nfunction isAnchorTarget(event) {\n  return isHTMLElement(event.target) && event.target.tagName === 'A';\n}\nfunction isSpaceIgnored(element) {\n  return isTypeableElement(element);\n}\n/**\n * Opens or closes the floating element when clicking the reference element.\n * @see https://floating-ui.com/docs/useClick\n */\nfunction useClick(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    dataRef,\n    elements: {\n      domReference\n    }\n  } = context;\n  const {\n    enabled = true,\n    event: eventOption = 'click',\n    toggle = true,\n    ignoreMouse = false,\n    keyboardHandlers = true,\n    stickIfOpen = true\n  } = props;\n  const pointerTypeRef = React.useRef();\n  const didKeyDownRef = React.useRef(false);\n  const reference = React.useMemo(() => ({\n    onPointerDown(event) {\n      pointerTypeRef.current = event.pointerType;\n    },\n    onMouseDown(event) {\n      const pointerType = pointerTypeRef.current;\n\n      // Ignore all buttons except for the \"main\" button.\n      // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/button\n      if (event.button !== 0) return;\n      if (eventOption === 'click') return;\n      if (isMouseLikePointerType(pointerType, true) && ignoreMouse) return;\n      if (open && toggle && (dataRef.current.openEvent && stickIfOpen ? dataRef.current.openEvent.type === 'mousedown' : true)) {\n        onOpenChange(false, event.nativeEvent, 'click');\n      } else {\n        // Prevent stealing focus from the floating element\n        event.preventDefault();\n        onOpenChange(true, event.nativeEvent, 'click');\n      }\n    },\n    onClick(event) {\n      const pointerType = pointerTypeRef.current;\n      if (eventOption === 'mousedown' && pointerTypeRef.current) {\n        pointerTypeRef.current = undefined;\n        return;\n      }\n      if (isMouseLikePointerType(pointerType, true) && ignoreMouse) return;\n      if (open && toggle && (dataRef.current.openEvent && stickIfOpen ? dataRef.current.openEvent.type === 'click' : true)) {\n        onOpenChange(false, event.nativeEvent, 'click');\n      } else {\n        onOpenChange(true, event.nativeEvent, 'click');\n      }\n    },\n    onKeyDown(event) {\n      pointerTypeRef.current = undefined;\n      if (event.defaultPrevented || !keyboardHandlers || isButtonTarget(event)) {\n        return;\n      }\n      if (event.key === ' ' && !isSpaceIgnored(domReference)) {\n        // Prevent scrolling\n        event.preventDefault();\n        didKeyDownRef.current = true;\n      }\n      if (isAnchorTarget(event)) {\n        return;\n      }\n      if (event.key === 'Enter') {\n        if (open && toggle) {\n          onOpenChange(false, event.nativeEvent, 'click');\n        } else {\n          onOpenChange(true, event.nativeEvent, 'click');\n        }\n      }\n    },\n    onKeyUp(event) {\n      if (event.defaultPrevented || !keyboardHandlers || isButtonTarget(event) || isSpaceIgnored(domReference)) {\n        return;\n      }\n      if (event.key === ' ' && didKeyDownRef.current) {\n        didKeyDownRef.current = false;\n        if (open && toggle) {\n          onOpenChange(false, event.nativeEvent, 'click');\n        } else {\n          onOpenChange(true, event.nativeEvent, 'click');\n        }\n      }\n    }\n  }), [dataRef, domReference, eventOption, ignoreMouse, keyboardHandlers, onOpenChange, open, stickIfOpen, toggle]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nfunction createVirtualElement(domElement, data) {\n  let offsetX = null;\n  let offsetY = null;\n  let isAutoUpdateEvent = false;\n  return {\n    contextElement: domElement || undefined,\n    getBoundingClientRect() {\n      var _data$dataRef$current;\n      const domRect = (domElement == null ? void 0 : domElement.getBoundingClientRect()) || {\n        width: 0,\n        height: 0,\n        x: 0,\n        y: 0\n      };\n      const isXAxis = data.axis === 'x' || data.axis === 'both';\n      const isYAxis = data.axis === 'y' || data.axis === 'both';\n      const canTrackCursorOnAutoUpdate = ['mouseenter', 'mousemove'].includes(((_data$dataRef$current = data.dataRef.current.openEvent) == null ? void 0 : _data$dataRef$current.type) || '') && data.pointerType !== 'touch';\n      let width = domRect.width;\n      let height = domRect.height;\n      let x = domRect.x;\n      let y = domRect.y;\n      if (offsetX == null && data.x && isXAxis) {\n        offsetX = domRect.x - data.x;\n      }\n      if (offsetY == null && data.y && isYAxis) {\n        offsetY = domRect.y - data.y;\n      }\n      x -= offsetX || 0;\n      y -= offsetY || 0;\n      width = 0;\n      height = 0;\n      if (!isAutoUpdateEvent || canTrackCursorOnAutoUpdate) {\n        width = data.axis === 'y' ? domRect.width : 0;\n        height = data.axis === 'x' ? domRect.height : 0;\n        x = isXAxis && data.x != null ? data.x : x;\n        y = isYAxis && data.y != null ? data.y : y;\n      } else if (isAutoUpdateEvent && !canTrackCursorOnAutoUpdate) {\n        height = data.axis === 'x' ? domRect.height : height;\n        width = data.axis === 'y' ? domRect.width : width;\n      }\n      isAutoUpdateEvent = true;\n      return {\n        width,\n        height,\n        x,\n        y,\n        top: y,\n        right: x + width,\n        bottom: y + height,\n        left: x\n      };\n    }\n  };\n}\nfunction isMouseBasedEvent(event) {\n  return event != null && event.clientX != null;\n}\n/**\n * Positions the floating element relative to a client point (in the viewport),\n * such as the mouse position. By default, it follows the mouse cursor.\n * @see https://floating-ui.com/docs/useClientPoint\n */\nfunction useClientPoint(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    dataRef,\n    elements: {\n      floating,\n      domReference\n    },\n    refs\n  } = context;\n  const {\n    enabled = true,\n    axis = 'both',\n    x = null,\n    y = null\n  } = props;\n  const initialRef = React.useRef(false);\n  const cleanupListenerRef = React.useRef(null);\n  const [pointerType, setPointerType] = React.useState();\n  const [reactive, setReactive] = React.useState([]);\n  const setReference = useEffectEvent((x, y) => {\n    if (initialRef.current) return;\n\n    // Prevent setting if the open event was not a mouse-like one\n    // (e.g. focus to open, then hover over the reference element).\n    // Only apply if the event exists.\n    if (dataRef.current.openEvent && !isMouseBasedEvent(dataRef.current.openEvent)) {\n      return;\n    }\n    refs.setPositionReference(createVirtualElement(domReference, {\n      x,\n      y,\n      axis,\n      dataRef,\n      pointerType\n    }));\n  });\n  const handleReferenceEnterOrMove = useEffectEvent(event => {\n    if (x != null || y != null) return;\n    if (!open) {\n      setReference(event.clientX, event.clientY);\n    } else if (!cleanupListenerRef.current) {\n      // If there's no cleanup, there's no listener, but we want to ensure\n      // we add the listener if the cursor landed on the floating element and\n      // then back on the reference (i.e. it's interactive).\n      setReactive([]);\n    }\n  });\n\n  // If the pointer is a mouse-like pointer, we want to continue following the\n  // mouse even if the floating element is transitioning out. On touch\n  // devices, this is undesirable because the floating element will move to\n  // the dismissal touch point.\n  const openCheck = isMouseLikePointerType(pointerType) ? floating : open;\n  const addListener = React.useCallback(() => {\n    // Explicitly specified `x`/`y` coordinates shouldn't add a listener.\n    if (!openCheck || !enabled || x != null || y != null) return;\n    const win = getWindow(floating);\n    function handleMouseMove(event) {\n      const target = getTarget$1(event);\n      if (!contains$1(floating, target)) {\n        setReference(event.clientX, event.clientY);\n      } else {\n        win.removeEventListener('mousemove', handleMouseMove);\n        cleanupListenerRef.current = null;\n      }\n    }\n    if (!dataRef.current.openEvent || isMouseBasedEvent(dataRef.current.openEvent)) {\n      win.addEventListener('mousemove', handleMouseMove);\n      const cleanup = () => {\n        win.removeEventListener('mousemove', handleMouseMove);\n        cleanupListenerRef.current = null;\n      };\n      cleanupListenerRef.current = cleanup;\n      return cleanup;\n    }\n    refs.setPositionReference(domReference);\n  }, [openCheck, enabled, x, y, floating, dataRef, refs, domReference, setReference]);\n  React.useEffect(() => {\n    return addListener();\n  }, [addListener, reactive]);\n  React.useEffect(() => {\n    if (enabled && !floating) {\n      initialRef.current = false;\n    }\n  }, [enabled, floating]);\n  React.useEffect(() => {\n    if (!enabled && open) {\n      initialRef.current = true;\n    }\n  }, [enabled, open]);\n  useModernLayoutEffect(() => {\n    if (enabled && (x != null || y != null)) {\n      initialRef.current = false;\n      setReference(x, y);\n    }\n  }, [enabled, x, y, setReference]);\n  const reference = React.useMemo(() => {\n    function setPointerTypeRef(_ref) {\n      let {\n        pointerType\n      } = _ref;\n      setPointerType(pointerType);\n    }\n    return {\n      onPointerDown: setPointerTypeRef,\n      onPointerEnter: setPointerTypeRef,\n      onMouseMove: handleReferenceEnterOrMove,\n      onMouseEnter: handleReferenceEnterOrMove\n    };\n  }, [handleReferenceEnterOrMove]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nconst bubbleHandlerKeys = {\n  pointerdown: 'onPointerDown',\n  mousedown: 'onMouseDown',\n  click: 'onClick'\n};\nconst captureHandlerKeys = {\n  pointerdown: 'onPointerDownCapture',\n  mousedown: 'onMouseDownCapture',\n  click: 'onClickCapture'\n};\nconst normalizeProp = normalizable => {\n  var _normalizable$escapeK, _normalizable$outside;\n  return {\n    escapeKey: typeof normalizable === 'boolean' ? normalizable : (_normalizable$escapeK = normalizable == null ? void 0 : normalizable.escapeKey) != null ? _normalizable$escapeK : false,\n    outsidePress: typeof normalizable === 'boolean' ? normalizable : (_normalizable$outside = normalizable == null ? void 0 : normalizable.outsidePress) != null ? _normalizable$outside : true\n  };\n};\n/**\n * Closes the floating element when a dismissal is requested — by default, when\n * the user presses the `escape` key or outside of the floating element.\n * @see https://floating-ui.com/docs/useDismiss\n */\nfunction useDismiss(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    elements,\n    dataRef\n  } = context;\n  const {\n    enabled = true,\n    escapeKey = true,\n    outsidePress: unstable_outsidePress = true,\n    outsidePressEvent = 'pointerdown',\n    referencePress = false,\n    referencePressEvent = 'pointerdown',\n    ancestorScroll = false,\n    bubbles,\n    capture\n  } = props;\n  const tree = useFloatingTree();\n  const outsidePressFn = useEffectEvent(typeof unstable_outsidePress === 'function' ? unstable_outsidePress : () => false);\n  const outsidePress = typeof unstable_outsidePress === 'function' ? outsidePressFn : unstable_outsidePress;\n  const endedOrStartedInsideRef = React.useRef(false);\n  const {\n    escapeKey: escapeKeyBubbles,\n    outsidePress: outsidePressBubbles\n  } = normalizeProp(bubbles);\n  const {\n    escapeKey: escapeKeyCapture,\n    outsidePress: outsidePressCapture\n  } = normalizeProp(capture);\n  const isComposingRef = React.useRef(false);\n  const blurTimeoutRef = React.useRef(-1);\n  const closeOnEscapeKeyDown = useEffectEvent(event => {\n    var _dataRef$current$floa;\n    if (!open || !enabled || !escapeKey || event.key !== 'Escape') {\n      return;\n    }\n\n    // Wait until IME is settled. Pressing `Escape` while composing should\n    // close the compose menu, but not the floating element.\n    if (isComposingRef.current) {\n      return;\n    }\n    const nodeId = (_dataRef$current$floa = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa.nodeId;\n    const children = tree ? getNodeChildren$1(tree.nodesRef.current, nodeId) : [];\n    if (!escapeKeyBubbles) {\n      event.stopPropagation();\n      if (children.length > 0) {\n        let shouldDismiss = true;\n        children.forEach(child => {\n          var _child$context;\n          if ((_child$context = child.context) != null && _child$context.open && !child.context.dataRef.current.__escapeKeyBubbles) {\n            shouldDismiss = false;\n            return;\n          }\n        });\n        if (!shouldDismiss) {\n          return;\n        }\n      }\n    }\n    onOpenChange(false, isReactEvent(event) ? event.nativeEvent : event, 'escape-key');\n  });\n  const closeOnEscapeKeyDownCapture = useEffectEvent(event => {\n    var _getTarget2;\n    const callback = () => {\n      var _getTarget;\n      closeOnEscapeKeyDown(event);\n      (_getTarget = getTarget$1(event)) == null || _getTarget.removeEventListener('keydown', callback);\n    };\n    (_getTarget2 = getTarget$1(event)) == null || _getTarget2.addEventListener('keydown', callback);\n  });\n  const closeOnPressOutside = useEffectEvent(event => {\n    var _dataRef$current$floa2;\n    // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n    const insideReactTree = dataRef.current.insideReactTree;\n    dataRef.current.insideReactTree = false;\n\n    // When click outside is lazy (`click` event), handle dragging.\n    // Don't close if:\n    // - The click started inside the floating element.\n    // - The click ended inside the floating element.\n    const endedOrStartedInside = endedOrStartedInsideRef.current;\n    endedOrStartedInsideRef.current = false;\n    if (outsidePressEvent === 'click' && endedOrStartedInside) {\n      return;\n    }\n    if (insideReactTree) {\n      return;\n    }\n    if (typeof outsidePress === 'function' && !outsidePress(event)) {\n      return;\n    }\n    const target = getTarget$1(event);\n    const inertSelector = \"[\" + createAttribute('inert') + \"]\";\n    const markers = getDocument$1(elements.floating).querySelectorAll(inertSelector);\n    let targetRootAncestor = isElement(target) ? target : null;\n    while (targetRootAncestor && !isLastTraversableNode(targetRootAncestor)) {\n      const nextParent = getParentNode(targetRootAncestor);\n      if (isLastTraversableNode(nextParent) || !isElement(nextParent)) {\n        break;\n      }\n      targetRootAncestor = nextParent;\n    }\n\n    // Check if the click occurred on a third-party element injected after the\n    // floating element rendered.\n    if (markers.length && isElement(target) && !isRootElement(target) &&\n    // Clicked on a direct ancestor (e.g. FloatingOverlay).\n    !contains$1(target, elements.floating) &&\n    // If the target root element contains none of the markers, then the\n    // element was injected after the floating element rendered.\n    Array.from(markers).every(marker => !contains$1(targetRootAncestor, marker))) {\n      return;\n    }\n\n    // Check if the click occurred on the scrollbar\n    if (isHTMLElement(target) && floating) {\n      const lastTraversableNode = isLastTraversableNode(target);\n      const style = getComputedStyle(target);\n      const scrollRe = /auto|scroll/;\n      const isScrollableX = lastTraversableNode || scrollRe.test(style.overflowX);\n      const isScrollableY = lastTraversableNode || scrollRe.test(style.overflowY);\n      const canScrollX = isScrollableX && target.clientWidth > 0 && target.scrollWidth > target.clientWidth;\n      const canScrollY = isScrollableY && target.clientHeight > 0 && target.scrollHeight > target.clientHeight;\n      const isRTL = style.direction === 'rtl';\n\n      // Check click position relative to scrollbar.\n      // In some browsers it is possible to change the <body> (or window)\n      // scrollbar to the left side, but is very rare and is difficult to\n      // check for. Plus, for modal dialogs with backdrops, it is more\n      // important that the backdrop is checked but not so much the window.\n      const pressedVerticalScrollbar = canScrollY && (isRTL ? event.offsetX <= target.offsetWidth - target.clientWidth : event.offsetX > target.clientWidth);\n      const pressedHorizontalScrollbar = canScrollX && event.offsetY > target.clientHeight;\n      if (pressedVerticalScrollbar || pressedHorizontalScrollbar) {\n        return;\n      }\n    }\n    const nodeId = (_dataRef$current$floa2 = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa2.nodeId;\n    const targetIsInsideChildren = tree && getNodeChildren$1(tree.nodesRef.current, nodeId).some(node => {\n      var _node$context;\n      return isEventTargetWithin(event, (_node$context = node.context) == null ? void 0 : _node$context.elements.floating);\n    });\n    if (isEventTargetWithin(event, elements.floating) || isEventTargetWithin(event, elements.domReference) || targetIsInsideChildren) {\n      return;\n    }\n    const children = tree ? getNodeChildren$1(tree.nodesRef.current, nodeId) : [];\n    if (children.length > 0) {\n      let shouldDismiss = true;\n      children.forEach(child => {\n        var _child$context2;\n        if ((_child$context2 = child.context) != null && _child$context2.open && !child.context.dataRef.current.__outsidePressBubbles) {\n          shouldDismiss = false;\n          return;\n        }\n      });\n      if (!shouldDismiss) {\n        return;\n      }\n    }\n    onOpenChange(false, event, 'outside-press');\n  });\n  const closeOnPressOutsideCapture = useEffectEvent(event => {\n    var _getTarget4;\n    const callback = () => {\n      var _getTarget3;\n      closeOnPressOutside(event);\n      (_getTarget3 = getTarget$1(event)) == null || _getTarget3.removeEventListener(outsidePressEvent, callback);\n    };\n    (_getTarget4 = getTarget$1(event)) == null || _getTarget4.addEventListener(outsidePressEvent, callback);\n  });\n  React.useEffect(() => {\n    if (!open || !enabled) {\n      return;\n    }\n    dataRef.current.__escapeKeyBubbles = escapeKeyBubbles;\n    dataRef.current.__outsidePressBubbles = outsidePressBubbles;\n    let compositionTimeout = -1;\n    function onScroll(event) {\n      onOpenChange(false, event, 'ancestor-scroll');\n    }\n    function handleCompositionStart() {\n      window.clearTimeout(compositionTimeout);\n      isComposingRef.current = true;\n    }\n    function handleCompositionEnd() {\n      // Safari fires `compositionend` before `keydown`, so we need to wait\n      // until the next tick to set `isComposing` to `false`.\n      // https://bugs.webkit.org/show_bug.cgi?id=165004\n      compositionTimeout = window.setTimeout(() => {\n        isComposingRef.current = false;\n      },\n      // 0ms or 1ms don't work in Safari. 5ms appears to consistently work.\n      // Only apply to WebKit for the test to remain 0ms.\n      isWebKit() ? 5 : 0);\n    }\n    const doc = getDocument$1(elements.floating);\n    if (escapeKey) {\n      doc.addEventListener('keydown', escapeKeyCapture ? closeOnEscapeKeyDownCapture : closeOnEscapeKeyDown, escapeKeyCapture);\n      doc.addEventListener('compositionstart', handleCompositionStart);\n      doc.addEventListener('compositionend', handleCompositionEnd);\n    }\n    outsidePress && doc.addEventListener(outsidePressEvent, outsidePressCapture ? closeOnPressOutsideCapture : closeOnPressOutside, outsidePressCapture);\n    let ancestors = [];\n    if (ancestorScroll) {\n      if (isElement(elements.domReference)) {\n        ancestors = getOverflowAncestors(elements.domReference);\n      }\n      if (isElement(elements.floating)) {\n        ancestors = ancestors.concat(getOverflowAncestors(elements.floating));\n      }\n      if (!isElement(elements.reference) && elements.reference && elements.reference.contextElement) {\n        ancestors = ancestors.concat(getOverflowAncestors(elements.reference.contextElement));\n      }\n    }\n\n    // Ignore the visual viewport for scrolling dismissal (allow pinch-zoom)\n    ancestors = ancestors.filter(ancestor => {\n      var _doc$defaultView;\n      return ancestor !== ((_doc$defaultView = doc.defaultView) == null ? void 0 : _doc$defaultView.visualViewport);\n    });\n    ancestors.forEach(ancestor => {\n      ancestor.addEventListener('scroll', onScroll, {\n        passive: true\n      });\n    });\n    return () => {\n      if (escapeKey) {\n        doc.removeEventListener('keydown', escapeKeyCapture ? closeOnEscapeKeyDownCapture : closeOnEscapeKeyDown, escapeKeyCapture);\n        doc.removeEventListener('compositionstart', handleCompositionStart);\n        doc.removeEventListener('compositionend', handleCompositionEnd);\n      }\n      outsidePress && doc.removeEventListener(outsidePressEvent, outsidePressCapture ? closeOnPressOutsideCapture : closeOnPressOutside, outsidePressCapture);\n      ancestors.forEach(ancestor => {\n        ancestor.removeEventListener('scroll', onScroll);\n      });\n      window.clearTimeout(compositionTimeout);\n    };\n  }, [dataRef, elements, escapeKey, outsidePress, outsidePressEvent, open, onOpenChange, ancestorScroll, enabled, escapeKeyBubbles, outsidePressBubbles, closeOnEscapeKeyDown, escapeKeyCapture, closeOnEscapeKeyDownCapture, closeOnPressOutside, outsidePressCapture, closeOnPressOutsideCapture]);\n  React.useEffect(() => {\n    dataRef.current.insideReactTree = false;\n  }, [dataRef, outsidePress, outsidePressEvent]);\n  const reference = React.useMemo(() => ({\n    onKeyDown: closeOnEscapeKeyDown,\n    ...(referencePress && {\n      [bubbleHandlerKeys[referencePressEvent]]: event => {\n        onOpenChange(false, event.nativeEvent, 'reference-press');\n      },\n      ...(referencePressEvent !== 'click' && {\n        onClick(event) {\n          onOpenChange(false, event.nativeEvent, 'reference-press');\n        }\n      })\n    })\n  }), [closeOnEscapeKeyDown, onOpenChange, referencePress, referencePressEvent]);\n  const floating = React.useMemo(() => ({\n    onKeyDown: closeOnEscapeKeyDown,\n    onMouseDown() {\n      endedOrStartedInsideRef.current = true;\n    },\n    onMouseUp() {\n      endedOrStartedInsideRef.current = true;\n    },\n    [captureHandlerKeys[outsidePressEvent]]: () => {\n      dataRef.current.insideReactTree = true;\n    },\n    onBlurCapture() {\n      if (tree) return;\n      clearTimeoutIfSet(blurTimeoutRef);\n      dataRef.current.insideReactTree = true;\n      blurTimeoutRef.current = window.setTimeout(() => {\n        dataRef.current.insideReactTree = false;\n      });\n    }\n  }), [closeOnEscapeKeyDown, outsidePressEvent, dataRef, tree]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating\n  } : {}, [enabled, reference, floating]);\n}\n\nfunction useFloatingRootContext(options) {\n  const {\n    open = false,\n    onOpenChange: onOpenChangeProp,\n    elements: elementsProp\n  } = options;\n  const floatingId = useId();\n  const dataRef = React.useRef({});\n  const [events] = React.useState(() => createEventEmitter());\n  const nested = useFloatingParentNodeId() != null;\n  if (process.env.NODE_ENV !== \"production\") {\n    const optionDomReference = elementsProp.reference;\n    if (optionDomReference && !isElement(optionDomReference)) {\n      error('Cannot pass a virtual element to the `elements.reference` option,', 'as it must be a real DOM element. Use `refs.setPositionReference()`', 'instead.');\n    }\n  }\n  const [positionReference, setPositionReference] = React.useState(elementsProp.reference);\n  const onOpenChange = useEffectEvent((open, event, reason) => {\n    dataRef.current.openEvent = open ? event : undefined;\n    events.emit('openchange', {\n      open,\n      event,\n      reason,\n      nested\n    });\n    onOpenChangeProp == null || onOpenChangeProp(open, event, reason);\n  });\n  const refs = React.useMemo(() => ({\n    setPositionReference\n  }), []);\n  const elements = React.useMemo(() => ({\n    reference: positionReference || elementsProp.reference || null,\n    floating: elementsProp.floating || null,\n    domReference: elementsProp.reference\n  }), [positionReference, elementsProp.reference, elementsProp.floating]);\n  return React.useMemo(() => ({\n    dataRef,\n    open,\n    onOpenChange,\n    elements,\n    events,\n    floatingId,\n    refs\n  }), [open, onOpenChange, elements, events, floatingId, refs]);\n}\n\n/**\n * Provides data to position a floating element and context to add interactions.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    nodeId\n  } = options;\n  const internalRootContext = useFloatingRootContext({\n    ...options,\n    elements: {\n      reference: null,\n      floating: null,\n      ...options.elements\n    }\n  });\n  const rootContext = options.rootContext || internalRootContext;\n  const computedElements = rootContext.elements;\n  const [_domReference, setDomReference] = React.useState(null);\n  const [positionReference, _setPositionReference] = React.useState(null);\n  const optionDomReference = computedElements == null ? void 0 : computedElements.domReference;\n  const domReference = optionDomReference || _domReference;\n  const domReferenceRef = React.useRef(null);\n  const tree = useFloatingTree();\n  useModernLayoutEffect(() => {\n    if (domReference) {\n      domReferenceRef.current = domReference;\n    }\n  }, [domReference]);\n  const position = useFloating$1({\n    ...options,\n    elements: {\n      ...computedElements,\n      ...(positionReference && {\n        reference: positionReference\n      })\n    }\n  });\n  const setPositionReference = React.useCallback(node => {\n    const computedPositionReference = isElement(node) ? {\n      getBoundingClientRect: () => node.getBoundingClientRect(),\n      getClientRects: () => node.getClientRects(),\n      contextElement: node\n    } : node;\n    // Store the positionReference in state if the DOM reference is specified externally via the\n    // `elements.reference` option. This ensures that it won't be overridden on future renders.\n    _setPositionReference(computedPositionReference);\n    position.refs.setReference(computedPositionReference);\n  }, [position.refs]);\n  const setReference = React.useCallback(node => {\n    if (isElement(node) || node === null) {\n      domReferenceRef.current = node;\n      setDomReference(node);\n    }\n\n    // Backwards-compatibility for passing a virtual element to `reference`\n    // after it has set the DOM reference.\n    if (isElement(position.refs.reference.current) || position.refs.reference.current === null ||\n    // Don't allow setting virtual elements using the old technique back to\n    // `null` to support `positionReference` + an unstable `reference`\n    // callback ref.\n    node !== null && !isElement(node)) {\n      position.refs.setReference(node);\n    }\n  }, [position.refs]);\n  const refs = React.useMemo(() => ({\n    ...position.refs,\n    setReference,\n    setPositionReference,\n    domReference: domReferenceRef\n  }), [position.refs, setReference, setPositionReference]);\n  const elements = React.useMemo(() => ({\n    ...position.elements,\n    domReference: domReference\n  }), [position.elements, domReference]);\n  const context = React.useMemo(() => ({\n    ...position,\n    ...rootContext,\n    refs,\n    elements,\n    nodeId\n  }), [position, refs, elements, nodeId, rootContext]);\n  useModernLayoutEffect(() => {\n    rootContext.dataRef.current.floatingContext = context;\n    const node = tree == null ? void 0 : tree.nodesRef.current.find(node => node.id === nodeId);\n    if (node) {\n      node.context = context;\n    }\n  });\n  return React.useMemo(() => ({\n    ...position,\n    context,\n    refs,\n    elements\n  }), [position, refs, elements, context]);\n}\n\nfunction isMacSafari() {\n  return isMac() && isSafari();\n}\n/**\n * Opens the floating element while the reference element has focus, like CSS\n * `:focus`.\n * @see https://floating-ui.com/docs/useFocus\n */\nfunction useFocus(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    events,\n    dataRef,\n    elements\n  } = context;\n  const {\n    enabled = true,\n    visibleOnly = true\n  } = props;\n  const blockFocusRef = React.useRef(false);\n  const timeoutRef = React.useRef(-1);\n  const keyboardModalityRef = React.useRef(true);\n  React.useEffect(() => {\n    if (!enabled) return;\n    const win = getWindow(elements.domReference);\n\n    // If the reference was focused and the user left the tab/window, and the\n    // floating element was not open, the focus should be blocked when they\n    // return to the tab/window.\n    function onBlur() {\n      if (!open && isHTMLElement(elements.domReference) && elements.domReference === activeElement(getDocument$1(elements.domReference))) {\n        blockFocusRef.current = true;\n      }\n    }\n    function onKeyDown() {\n      keyboardModalityRef.current = true;\n    }\n    function onPointerDown() {\n      keyboardModalityRef.current = false;\n    }\n    win.addEventListener('blur', onBlur);\n    if (isMacSafari()) {\n      win.addEventListener('keydown', onKeyDown, true);\n      win.addEventListener('pointerdown', onPointerDown, true);\n    }\n    return () => {\n      win.removeEventListener('blur', onBlur);\n      if (isMacSafari()) {\n        win.removeEventListener('keydown', onKeyDown, true);\n        win.removeEventListener('pointerdown', onPointerDown, true);\n      }\n    };\n  }, [elements.domReference, open, enabled]);\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onOpenChange(_ref) {\n      let {\n        reason\n      } = _ref;\n      if (reason === 'reference-press' || reason === 'escape-key') {\n        blockFocusRef.current = true;\n      }\n    }\n    events.on('openchange', onOpenChange);\n    return () => {\n      events.off('openchange', onOpenChange);\n    };\n  }, [events, enabled]);\n  React.useEffect(() => {\n    return () => {\n      clearTimeoutIfSet(timeoutRef);\n    };\n  }, []);\n  const reference = React.useMemo(() => ({\n    onMouseLeave() {\n      blockFocusRef.current = false;\n    },\n    onFocus(event) {\n      if (blockFocusRef.current) return;\n      const target = getTarget$1(event.nativeEvent);\n      if (visibleOnly && isElement(target)) {\n        // Safari fails to match `:focus-visible` if focus was initially\n        // outside the document.\n        if (isMacSafari() && !event.relatedTarget) {\n          if (!keyboardModalityRef.current && !isTypeableElement(target)) {\n            return;\n          }\n        } else if (!matchesFocusVisible(target)) {\n          return;\n        }\n      }\n      onOpenChange(true, event.nativeEvent, 'focus');\n    },\n    onBlur(event) {\n      blockFocusRef.current = false;\n      const relatedTarget = event.relatedTarget;\n      const nativeEvent = event.nativeEvent;\n\n      // Hit the non-modal focus management portal guard. Focus will be\n      // moved into the floating element immediately after.\n      const movedToFocusGuard = isElement(relatedTarget) && relatedTarget.hasAttribute(createAttribute('focus-guard')) && relatedTarget.getAttribute('data-type') === 'outside';\n\n      // Wait for the window blur listener to fire.\n      timeoutRef.current = window.setTimeout(() => {\n        var _dataRef$current$floa;\n        const activeEl = activeElement(elements.domReference ? elements.domReference.ownerDocument : document);\n\n        // Focus left the page, keep it open.\n        if (!relatedTarget && activeEl === elements.domReference) return;\n\n        // When focusing the reference element (e.g. regular click), then\n        // clicking into the floating element, prevent it from hiding.\n        // Note: it must be focusable, e.g. `tabindex=\"-1\"`.\n        // We can not rely on relatedTarget to point to the correct element\n        // as it will only point to the shadow host of the newly focused element\n        // and not the element that actually has received focus if it is located\n        // inside a shadow root.\n        if (contains$1((_dataRef$current$floa = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa.refs.floating.current, activeEl) || contains$1(elements.domReference, activeEl) || movedToFocusGuard) {\n          return;\n        }\n        onOpenChange(false, nativeEvent, 'focus');\n      });\n    }\n  }), [dataRef, elements.domReference, onOpenChange, visibleOnly]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nfunction mergeProps(userProps, propsList, elementKey) {\n  const map = new Map();\n  const isItem = elementKey === 'item';\n  let domUserProps = userProps;\n  if (isItem && userProps) {\n    const {\n      [ACTIVE_KEY]: _,\n      [SELECTED_KEY]: __,\n      ...validProps\n    } = userProps;\n    domUserProps = validProps;\n  }\n  return {\n    ...(elementKey === 'floating' && {\n      tabIndex: -1,\n      [FOCUSABLE_ATTRIBUTE]: ''\n    }),\n    ...domUserProps,\n    ...propsList.map(value => {\n      const propsOrGetProps = value ? value[elementKey] : null;\n      if (typeof propsOrGetProps === 'function') {\n        return userProps ? propsOrGetProps(userProps) : null;\n      }\n      return propsOrGetProps;\n    }).concat(userProps).reduce((acc, props) => {\n      if (!props) {\n        return acc;\n      }\n      Object.entries(props).forEach(_ref => {\n        let [key, value] = _ref;\n        if (isItem && [ACTIVE_KEY, SELECTED_KEY].includes(key)) {\n          return;\n        }\n        if (key.indexOf('on') === 0) {\n          if (!map.has(key)) {\n            map.set(key, []);\n          }\n          if (typeof value === 'function') {\n            var _map$get;\n            (_map$get = map.get(key)) == null || _map$get.push(value);\n            acc[key] = function () {\n              var _map$get2;\n              for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n                args[_key] = arguments[_key];\n              }\n              return (_map$get2 = map.get(key)) == null ? void 0 : _map$get2.map(fn => fn(...args)).find(val => val !== undefined);\n            };\n          }\n        } else {\n          acc[key] = value;\n        }\n      });\n      return acc;\n    }, {})\n  };\n}\n/**\n * Merges an array of interaction hooks' props into prop getters, allowing\n * event handler functions to be composed together without overwriting one\n * another.\n * @see https://floating-ui.com/docs/useInteractions\n */\nfunction useInteractions(propsList) {\n  if (propsList === void 0) {\n    propsList = [];\n  }\n  const referenceDeps = propsList.map(key => key == null ? void 0 : key.reference);\n  const floatingDeps = propsList.map(key => key == null ? void 0 : key.floating);\n  const itemDeps = propsList.map(key => key == null ? void 0 : key.item);\n  const getReferenceProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'reference'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  referenceDeps);\n  const getFloatingProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'floating'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  floatingDeps);\n  const getItemProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'item'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  itemDeps);\n  return React.useMemo(() => ({\n    getReferenceProps,\n    getFloatingProps,\n    getItemProps\n  }), [getReferenceProps, getFloatingProps, getItemProps]);\n}\n\nconst ESCAPE = 'Escape';\nfunction doSwitch(orientation, vertical, horizontal) {\n  switch (orientation) {\n    case 'vertical':\n      return vertical;\n    case 'horizontal':\n      return horizontal;\n    default:\n      return vertical || horizontal;\n  }\n}\nfunction isMainOrientationKey(key, orientation) {\n  const vertical = key === ARROW_UP || key === ARROW_DOWN;\n  const horizontal = key === ARROW_LEFT || key === ARROW_RIGHT;\n  return doSwitch(orientation, vertical, horizontal);\n}\nfunction isMainOrientationToEndKey(key, orientation, rtl) {\n  const vertical = key === ARROW_DOWN;\n  const horizontal = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;\n  return doSwitch(orientation, vertical, horizontal) || key === 'Enter' || key === ' ' || key === '';\n}\nfunction isCrossOrientationOpenKey(key, orientation, rtl) {\n  const vertical = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;\n  const horizontal = key === ARROW_DOWN;\n  return doSwitch(orientation, vertical, horizontal);\n}\nfunction isCrossOrientationCloseKey(key, orientation, rtl, cols) {\n  const vertical = rtl ? key === ARROW_RIGHT : key === ARROW_LEFT;\n  const horizontal = key === ARROW_UP;\n  if (orientation === 'both' || orientation === 'horizontal' && cols && cols > 1) {\n    return key === ESCAPE;\n  }\n  return doSwitch(orientation, vertical, horizontal);\n}\n/**\n * Adds arrow key-based navigation of a list of items, either using real DOM\n * focus or virtual focus.\n * @see https://floating-ui.com/docs/useListNavigation\n */\nfunction useListNavigation(context, props) {\n  const {\n    open,\n    onOpenChange,\n    elements,\n    floatingId\n  } = context;\n  const {\n    listRef,\n    activeIndex,\n    onNavigate: unstable_onNavigate = () => {},\n    enabled = true,\n    selectedIndex = null,\n    allowEscape = false,\n    loop = false,\n    nested = false,\n    rtl = false,\n    virtual = false,\n    focusItemOnOpen = 'auto',\n    focusItemOnHover = true,\n    openOnArrowKeyDown = true,\n    disabledIndices = undefined,\n    orientation = 'vertical',\n    parentOrientation,\n    cols = 1,\n    scrollItemIntoView = true,\n    virtualItemRef,\n    itemSizes,\n    dense = false\n  } = props;\n  if (process.env.NODE_ENV !== \"production\") {\n    if (allowEscape) {\n      if (!loop) {\n        warn('`useListNavigation` looping must be enabled to allow escaping.');\n      }\n      if (!virtual) {\n        warn('`useListNavigation` must be virtual to allow escaping.');\n      }\n    }\n    if (orientation === 'vertical' && cols > 1) {\n      warn('In grid list navigation mode (`cols` > 1), the `orientation` should', 'be either \"horizontal\" or \"both\".');\n    }\n  }\n  const floatingFocusElement = getFloatingFocusElement(elements.floating);\n  const floatingFocusElementRef = useLatestRef(floatingFocusElement);\n  const parentId = useFloatingParentNodeId();\n  const tree = useFloatingTree();\n  useModernLayoutEffect(() => {\n    context.dataRef.current.orientation = orientation;\n  }, [context, orientation]);\n  const onNavigate = useEffectEvent(() => {\n    unstable_onNavigate(indexRef.current === -1 ? null : indexRef.current);\n  });\n  const typeableComboboxReference = isTypeableCombobox(elements.domReference);\n  const focusItemOnOpenRef = React.useRef(focusItemOnOpen);\n  const indexRef = React.useRef(selectedIndex != null ? selectedIndex : -1);\n  const keyRef = React.useRef(null);\n  const isPointerModalityRef = React.useRef(true);\n  const previousOnNavigateRef = React.useRef(onNavigate);\n  const previousMountedRef = React.useRef(!!elements.floating);\n  const previousOpenRef = React.useRef(open);\n  const forceSyncFocusRef = React.useRef(false);\n  const forceScrollIntoViewRef = React.useRef(false);\n  const disabledIndicesRef = useLatestRef(disabledIndices);\n  const latestOpenRef = useLatestRef(open);\n  const scrollItemIntoViewRef = useLatestRef(scrollItemIntoView);\n  const selectedIndexRef = useLatestRef(selectedIndex);\n  const [activeId, setActiveId] = React.useState();\n  const [virtualId, setVirtualId] = React.useState();\n  const focusItem = useEffectEvent(() => {\n    function runFocus(item) {\n      if (virtual) {\n        var _item$id;\n        if ((_item$id = item.id) != null && _item$id.endsWith('-fui-option')) {\n          item.id = floatingId + \"-\" + Math.random().toString(16).slice(2, 10);\n        }\n        setActiveId(item.id);\n        tree == null || tree.events.emit('virtualfocus', item);\n        if (virtualItemRef) {\n          virtualItemRef.current = item;\n        }\n      } else {\n        enqueueFocus(item, {\n          sync: forceSyncFocusRef.current,\n          preventScroll: true\n        });\n      }\n    }\n    const initialItem = listRef.current[indexRef.current];\n    const forceScrollIntoView = forceScrollIntoViewRef.current;\n    if (initialItem) {\n      runFocus(initialItem);\n    }\n    const scheduler = forceSyncFocusRef.current ? v => v() : requestAnimationFrame;\n    scheduler(() => {\n      const waitedItem = listRef.current[indexRef.current] || initialItem;\n      if (!waitedItem) return;\n      if (!initialItem) {\n        runFocus(waitedItem);\n      }\n      const scrollIntoViewOptions = scrollItemIntoViewRef.current;\n      const shouldScrollIntoView = scrollIntoViewOptions && item && (forceScrollIntoView || !isPointerModalityRef.current);\n      if (shouldScrollIntoView) {\n        // JSDOM doesn't support `.scrollIntoView()` but it's widely supported\n        // by all browsers.\n        waitedItem.scrollIntoView == null || waitedItem.scrollIntoView(typeof scrollIntoViewOptions === 'boolean' ? {\n          block: 'nearest',\n          inline: 'nearest'\n        } : scrollIntoViewOptions);\n      }\n    });\n  });\n\n  // Sync `selectedIndex` to be the `activeIndex` upon opening the floating\n  // element. Also, reset `activeIndex` upon closing the floating element.\n  useModernLayoutEffect(() => {\n    if (!enabled) return;\n    if (open && elements.floating) {\n      if (focusItemOnOpenRef.current && selectedIndex != null) {\n        // Regardless of the pointer modality, we want to ensure the selected\n        // item comes into view when the floating element is opened.\n        forceScrollIntoViewRef.current = true;\n        indexRef.current = selectedIndex;\n        onNavigate();\n      }\n    } else if (previousMountedRef.current) {\n      // Since the user can specify `onNavigate` conditionally\n      // (onNavigate: open ? setActiveIndex : setSelectedIndex),\n      // we store and call the previous function.\n      indexRef.current = -1;\n      previousOnNavigateRef.current();\n    }\n  }, [enabled, open, elements.floating, selectedIndex, onNavigate]);\n\n  // Sync `activeIndex` to be the focused item while the floating element is\n  // open.\n  useModernLayoutEffect(() => {\n    if (!enabled) return;\n    if (!open) return;\n    if (!elements.floating) return;\n    if (activeIndex == null) {\n      forceSyncFocusRef.current = false;\n      if (selectedIndexRef.current != null) {\n        return;\n      }\n\n      // Reset while the floating element was open (e.g. the list changed).\n      if (previousMountedRef.current) {\n        indexRef.current = -1;\n        focusItem();\n      }\n\n      // Initial sync.\n      if ((!previousOpenRef.current || !previousMountedRef.current) && focusItemOnOpenRef.current && (keyRef.current != null || focusItemOnOpenRef.current === true && keyRef.current == null)) {\n        let runs = 0;\n        const waitForListPopulated = () => {\n          if (listRef.current[0] == null) {\n            // Avoid letting the browser paint if possible on the first try,\n            // otherwise use rAF. Don't try more than twice, since something\n            // is wrong otherwise.\n            if (runs < 2) {\n              const scheduler = runs ? requestAnimationFrame : queueMicrotask;\n              scheduler(waitForListPopulated);\n            }\n            runs++;\n          } else {\n            indexRef.current = keyRef.current == null || isMainOrientationToEndKey(keyRef.current, orientation, rtl) || nested ? getMinListIndex(listRef, disabledIndicesRef.current) : getMaxListIndex(listRef, disabledIndicesRef.current);\n            keyRef.current = null;\n            onNavigate();\n          }\n        };\n        waitForListPopulated();\n      }\n    } else if (!isIndexOutOfListBounds(listRef, activeIndex)) {\n      indexRef.current = activeIndex;\n      focusItem();\n      forceScrollIntoViewRef.current = false;\n    }\n  }, [enabled, open, elements.floating, activeIndex, selectedIndexRef, nested, listRef, orientation, rtl, onNavigate, focusItem, disabledIndicesRef]);\n\n  // Ensure the parent floating element has focus when a nested child closes\n  // to allow arrow key navigation to work after the pointer leaves the child.\n  useModernLayoutEffect(() => {\n    var _nodes$find;\n    if (!enabled || elements.floating || !tree || virtual || !previousMountedRef.current) {\n      return;\n    }\n    const nodes = tree.nodesRef.current;\n    const parent = (_nodes$find = nodes.find(node => node.id === parentId)) == null || (_nodes$find = _nodes$find.context) == null ? void 0 : _nodes$find.elements.floating;\n    const activeEl = activeElement(getDocument$1(elements.floating));\n    const treeContainsActiveEl = nodes.some(node => node.context && contains$1(node.context.elements.floating, activeEl));\n    if (parent && !treeContainsActiveEl && isPointerModalityRef.current) {\n      parent.focus({\n        preventScroll: true\n      });\n    }\n  }, [enabled, elements.floating, tree, parentId, virtual]);\n  useModernLayoutEffect(() => {\n    if (!enabled) return;\n    if (!tree) return;\n    if (!virtual) return;\n    if (parentId) return;\n    function handleVirtualFocus(item) {\n      setVirtualId(item.id);\n      if (virtualItemRef) {\n        virtualItemRef.current = item;\n      }\n    }\n    tree.events.on('virtualfocus', handleVirtualFocus);\n    return () => {\n      tree.events.off('virtualfocus', handleVirtualFocus);\n    };\n  }, [enabled, tree, virtual, parentId, virtualItemRef]);\n  useModernLayoutEffect(() => {\n    previousOnNavigateRef.current = onNavigate;\n    previousOpenRef.current = open;\n    previousMountedRef.current = !!elements.floating;\n  });\n  useModernLayoutEffect(() => {\n    if (!open) {\n      keyRef.current = null;\n      focusItemOnOpenRef.current = focusItemOnOpen;\n    }\n  }, [open, focusItemOnOpen]);\n  const hasActiveIndex = activeIndex != null;\n  const item = React.useMemo(() => {\n    function syncCurrentTarget(currentTarget) {\n      if (!latestOpenRef.current) return;\n      const index = listRef.current.indexOf(currentTarget);\n      if (index !== -1 && indexRef.current !== index) {\n        indexRef.current = index;\n        onNavigate();\n      }\n    }\n    const props = {\n      onFocus(_ref) {\n        let {\n          currentTarget\n        } = _ref;\n        forceSyncFocusRef.current = true;\n        syncCurrentTarget(currentTarget);\n      },\n      onClick: _ref2 => {\n        let {\n          currentTarget\n        } = _ref2;\n        return currentTarget.focus({\n          preventScroll: true\n        });\n      },\n      // Safari\n      ...(focusItemOnHover && {\n        onMouseMove(_ref3) {\n          let {\n            currentTarget\n          } = _ref3;\n          forceSyncFocusRef.current = true;\n          forceScrollIntoViewRef.current = false;\n          syncCurrentTarget(currentTarget);\n        },\n        onPointerLeave(_ref4) {\n          let {\n            pointerType\n          } = _ref4;\n          if (!isPointerModalityRef.current || pointerType === 'touch') {\n            return;\n          }\n          forceSyncFocusRef.current = true;\n          indexRef.current = -1;\n          onNavigate();\n          if (!virtual) {\n            var _floatingFocusElement;\n            (_floatingFocusElement = floatingFocusElementRef.current) == null || _floatingFocusElement.focus({\n              preventScroll: true\n            });\n          }\n        }\n      })\n    };\n    return props;\n  }, [latestOpenRef, floatingFocusElementRef, focusItemOnHover, listRef, onNavigate, virtual]);\n  const getParentOrientation = React.useCallback(() => {\n    var _tree$nodesRef$curren;\n    return parentOrientation != null ? parentOrientation : tree == null || (_tree$nodesRef$curren = tree.nodesRef.current.find(node => node.id === parentId)) == null || (_tree$nodesRef$curren = _tree$nodesRef$curren.context) == null || (_tree$nodesRef$curren = _tree$nodesRef$curren.dataRef) == null ? void 0 : _tree$nodesRef$curren.current.orientation;\n  }, [parentId, tree, parentOrientation]);\n  const commonOnKeyDown = useEffectEvent(event => {\n    isPointerModalityRef.current = false;\n    forceSyncFocusRef.current = true;\n\n    // When composing a character, Chrome fires ArrowDown twice. Firefox/Safari\n    // don't appear to suffer from this. `event.isComposing` is avoided due to\n    // Safari not supporting it properly (although it's not needed in the first\n    // place for Safari, just avoiding any possible issues).\n    if (event.which === 229) {\n      return;\n    }\n\n    // If the floating element is animating out, ignore navigation. Otherwise,\n    // the `activeIndex` gets set to 0 despite not being open so the next time\n    // the user ArrowDowns, the first item won't be focused.\n    if (!latestOpenRef.current && event.currentTarget === floatingFocusElementRef.current) {\n      return;\n    }\n    if (nested && isCrossOrientationCloseKey(event.key, orientation, rtl, cols)) {\n      // If the nested list's close key is also the parent navigation key,\n      // let the parent navigate. Otherwise, stop propagating the event.\n      if (!isMainOrientationKey(event.key, getParentOrientation())) {\n        stopEvent(event);\n      }\n      onOpenChange(false, event.nativeEvent, 'list-navigation');\n      if (isHTMLElement(elements.domReference)) {\n        if (virtual) {\n          tree == null || tree.events.emit('virtualfocus', elements.domReference);\n        } else {\n          elements.domReference.focus();\n        }\n      }\n      return;\n    }\n    const currentIndex = indexRef.current;\n    const minIndex = getMinListIndex(listRef, disabledIndices);\n    const maxIndex = getMaxListIndex(listRef, disabledIndices);\n    if (!typeableComboboxReference) {\n      if (event.key === 'Home') {\n        stopEvent(event);\n        indexRef.current = minIndex;\n        onNavigate();\n      }\n      if (event.key === 'End') {\n        stopEvent(event);\n        indexRef.current = maxIndex;\n        onNavigate();\n      }\n    }\n\n    // Grid navigation.\n    if (cols > 1) {\n      const sizes = itemSizes || Array.from({\n        length: listRef.current.length\n      }, () => ({\n        width: 1,\n        height: 1\n      }));\n      // To calculate movements on the grid, we use hypothetical cell indices\n      // as if every item was 1x1, then convert back to real indices.\n      const cellMap = createGridCellMap(sizes, cols, dense);\n      const minGridIndex = cellMap.findIndex(index => index != null && !isListIndexDisabled(listRef, index, disabledIndices));\n      // last enabled index\n      const maxGridIndex = cellMap.reduce((foundIndex, index, cellIndex) => index != null && !isListIndexDisabled(listRef, index, disabledIndices) ? cellIndex : foundIndex, -1);\n      const index = cellMap[getGridNavigatedIndex({\n        current: cellMap.map(itemIndex => itemIndex != null ? listRef.current[itemIndex] : null)\n      }, {\n        event,\n        orientation,\n        loop,\n        rtl,\n        cols,\n        // treat undefined (empty grid spaces) as disabled indices so we\n        // don't end up in them\n        disabledIndices: getGridCellIndices([...((typeof disabledIndices !== 'function' ? disabledIndices : null) || listRef.current.map((_, index) => isListIndexDisabled(listRef, index, disabledIndices) ? index : undefined)), undefined], cellMap),\n        minIndex: minGridIndex,\n        maxIndex: maxGridIndex,\n        prevIndex: getGridCellIndexOfCorner(indexRef.current > maxIndex ? minIndex : indexRef.current, sizes, cellMap, cols,\n        // use a corner matching the edge closest to the direction\n        // we're moving in so we don't end up in the same item. Prefer\n        // top/left over bottom/right.\n        event.key === ARROW_DOWN ? 'bl' : event.key === (rtl ? ARROW_LEFT : ARROW_RIGHT) ? 'tr' : 'tl'),\n        stopEvent: true\n      })];\n      if (index != null) {\n        indexRef.current = index;\n        onNavigate();\n      }\n      if (orientation === 'both') {\n        return;\n      }\n    }\n    if (isMainOrientationKey(event.key, orientation)) {\n      stopEvent(event);\n\n      // Reset the index if no item is focused.\n      if (open && !virtual && activeElement(event.currentTarget.ownerDocument) === event.currentTarget) {\n        indexRef.current = isMainOrientationToEndKey(event.key, orientation, rtl) ? minIndex : maxIndex;\n        onNavigate();\n        return;\n      }\n      if (isMainOrientationToEndKey(event.key, orientation, rtl)) {\n        if (loop) {\n          indexRef.current = currentIndex >= maxIndex ? allowEscape && currentIndex !== listRef.current.length ? -1 : minIndex : findNonDisabledListIndex(listRef, {\n            startingIndex: currentIndex,\n            disabledIndices\n          });\n        } else {\n          indexRef.current = Math.min(maxIndex, findNonDisabledListIndex(listRef, {\n            startingIndex: currentIndex,\n            disabledIndices\n          }));\n        }\n      } else {\n        if (loop) {\n          indexRef.current = currentIndex <= minIndex ? allowEscape && currentIndex !== -1 ? listRef.current.length : maxIndex : findNonDisabledListIndex(listRef, {\n            startingIndex: currentIndex,\n            decrement: true,\n            disabledIndices\n          });\n        } else {\n          indexRef.current = Math.max(minIndex, findNonDisabledListIndex(listRef, {\n            startingIndex: currentIndex,\n            decrement: true,\n            disabledIndices\n          }));\n        }\n      }\n      if (isIndexOutOfListBounds(listRef, indexRef.current)) {\n        indexRef.current = -1;\n      }\n      onNavigate();\n    }\n  });\n  const ariaActiveDescendantProp = React.useMemo(() => {\n    return virtual && open && hasActiveIndex && {\n      'aria-activedescendant': virtualId || activeId\n    };\n  }, [virtual, open, hasActiveIndex, virtualId, activeId]);\n  const floating = React.useMemo(() => {\n    return {\n      'aria-orientation': orientation === 'both' ? undefined : orientation,\n      ...(!typeableComboboxReference ? ariaActiveDescendantProp : {}),\n      onKeyDown: commonOnKeyDown,\n      onPointerMove() {\n        isPointerModalityRef.current = true;\n      }\n    };\n  }, [ariaActiveDescendantProp, commonOnKeyDown, orientation, typeableComboboxReference]);\n  const reference = React.useMemo(() => {\n    function checkVirtualMouse(event) {\n      if (focusItemOnOpen === 'auto' && isVirtualClick(event.nativeEvent)) {\n        focusItemOnOpenRef.current = true;\n      }\n    }\n    function checkVirtualPointer(event) {\n      // `pointerdown` fires first, reset the state then perform the checks.\n      focusItemOnOpenRef.current = focusItemOnOpen;\n      if (focusItemOnOpen === 'auto' && isVirtualPointerEvent(event.nativeEvent)) {\n        focusItemOnOpenRef.current = true;\n      }\n    }\n    return {\n      ...ariaActiveDescendantProp,\n      onKeyDown(event) {\n        isPointerModalityRef.current = false;\n        const isArrowKey = event.key.startsWith('Arrow');\n        const isHomeOrEndKey = ['Home', 'End'].includes(event.key);\n        const isMoveKey = isArrowKey || isHomeOrEndKey;\n        const isCrossOpenKey = isCrossOrientationOpenKey(event.key, orientation, rtl);\n        const isCrossCloseKey = isCrossOrientationCloseKey(event.key, orientation, rtl, cols);\n        const isParentCrossOpenKey = isCrossOrientationOpenKey(event.key, getParentOrientation(), rtl);\n        const isMainKey = isMainOrientationKey(event.key, orientation);\n        const isNavigationKey = (nested ? isParentCrossOpenKey : isMainKey) || event.key === 'Enter' || event.key.trim() === '';\n        if (virtual && open) {\n          const rootNode = tree == null ? void 0 : tree.nodesRef.current.find(node => node.parentId == null);\n          const deepestNode = tree && rootNode ? getDeepestNode(tree.nodesRef.current, rootNode.id) : null;\n          if (isMoveKey && deepestNode && virtualItemRef) {\n            const eventObject = new KeyboardEvent('keydown', {\n              key: event.key,\n              bubbles: true\n            });\n            if (isCrossOpenKey || isCrossCloseKey) {\n              var _deepestNode$context, _deepestNode$context2;\n              const isCurrentTarget = ((_deepestNode$context = deepestNode.context) == null ? void 0 : _deepestNode$context.elements.domReference) === event.currentTarget;\n              const dispatchItem = isCrossCloseKey && !isCurrentTarget ? (_deepestNode$context2 = deepestNode.context) == null ? void 0 : _deepestNode$context2.elements.domReference : isCrossOpenKey ? listRef.current.find(item => (item == null ? void 0 : item.id) === activeId) : null;\n              if (dispatchItem) {\n                stopEvent(event);\n                dispatchItem.dispatchEvent(eventObject);\n                setVirtualId(undefined);\n              }\n            }\n            if ((isMainKey || isHomeOrEndKey) && deepestNode.context) {\n              if (deepestNode.context.open && deepestNode.parentId && event.currentTarget !== deepestNode.context.elements.domReference) {\n                var _deepestNode$context$;\n                stopEvent(event);\n                (_deepestNode$context$ = deepestNode.context.elements.domReference) == null || _deepestNode$context$.dispatchEvent(eventObject);\n                return;\n              }\n            }\n          }\n          return commonOnKeyDown(event);\n        }\n        // If a floating element should not open on arrow key down, avoid\n        // setting `activeIndex` while it's closed.\n        if (!open && !openOnArrowKeyDown && isArrowKey) {\n          return;\n        }\n        if (isNavigationKey) {\n          const isParentMainKey = isMainOrientationKey(event.key, getParentOrientation());\n          keyRef.current = nested && isParentMainKey ? null : event.key;\n        }\n        if (nested) {\n          if (isParentCrossOpenKey) {\n            stopEvent(event);\n            if (open) {\n              indexRef.current = getMinListIndex(listRef, disabledIndicesRef.current);\n              onNavigate();\n            } else {\n              onOpenChange(true, event.nativeEvent, 'list-navigation');\n            }\n          }\n          return;\n        }\n        if (isMainKey) {\n          if (selectedIndex != null) {\n            indexRef.current = selectedIndex;\n          }\n          stopEvent(event);\n          if (!open && openOnArrowKeyDown) {\n            onOpenChange(true, event.nativeEvent, 'list-navigation');\n          } else {\n            commonOnKeyDown(event);\n          }\n          if (open) {\n            onNavigate();\n          }\n        }\n      },\n      onFocus() {\n        if (open && !virtual) {\n          indexRef.current = -1;\n          onNavigate();\n        }\n      },\n      onPointerDown: checkVirtualPointer,\n      onPointerEnter: checkVirtualPointer,\n      onMouseDown: checkVirtualMouse,\n      onClick: checkVirtualMouse\n    };\n  }, [activeId, ariaActiveDescendantProp, cols, commonOnKeyDown, disabledIndicesRef, focusItemOnOpen, listRef, nested, onNavigate, onOpenChange, open, openOnArrowKeyDown, orientation, getParentOrientation, rtl, selectedIndex, tree, virtual, virtualItemRef]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating,\n    item\n  } : {}, [enabled, reference, floating, item]);\n}\n\nconst componentRoleToAriaRoleMap = /*#__PURE__*/new Map([['select', 'listbox'], ['combobox', 'listbox'], ['label', false]]);\n\n/**\n * Adds base screen reader props to the reference and floating elements for a\n * given floating element `role`.\n * @see https://floating-ui.com/docs/useRole\n */\nfunction useRole(context, props) {\n  var _elements$domReferenc, _componentRoleToAriaR;\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    elements,\n    floatingId: defaultFloatingId\n  } = context;\n  const {\n    enabled = true,\n    role = 'dialog'\n  } = props;\n  const defaultReferenceId = useId();\n  const referenceId = ((_elements$domReferenc = elements.domReference) == null ? void 0 : _elements$domReferenc.id) || defaultReferenceId;\n  const floatingId = React.useMemo(() => {\n    var _getFloatingFocusElem;\n    return ((_getFloatingFocusElem = getFloatingFocusElement(elements.floating)) == null ? void 0 : _getFloatingFocusElem.id) || defaultFloatingId;\n  }, [elements.floating, defaultFloatingId]);\n  const ariaRole = (_componentRoleToAriaR = componentRoleToAriaRoleMap.get(role)) != null ? _componentRoleToAriaR : role;\n  const parentId = useFloatingParentNodeId();\n  const isNested = parentId != null;\n  const reference = React.useMemo(() => {\n    if (ariaRole === 'tooltip' || role === 'label') {\n      return {\n        [\"aria-\" + (role === 'label' ? 'labelledby' : 'describedby')]: open ? floatingId : undefined\n      };\n    }\n    return {\n      'aria-expanded': open ? 'true' : 'false',\n      'aria-haspopup': ariaRole === 'alertdialog' ? 'dialog' : ariaRole,\n      'aria-controls': open ? floatingId : undefined,\n      ...(ariaRole === 'listbox' && {\n        role: 'combobox'\n      }),\n      ...(ariaRole === 'menu' && {\n        id: referenceId\n      }),\n      ...(ariaRole === 'menu' && isNested && {\n        role: 'menuitem'\n      }),\n      ...(role === 'select' && {\n        'aria-autocomplete': 'none'\n      }),\n      ...(role === 'combobox' && {\n        'aria-autocomplete': 'list'\n      })\n    };\n  }, [ariaRole, floatingId, isNested, open, referenceId, role]);\n  const floating = React.useMemo(() => {\n    const floatingProps = {\n      id: floatingId,\n      ...(ariaRole && {\n        role: ariaRole\n      })\n    };\n    if (ariaRole === 'tooltip' || role === 'label') {\n      return floatingProps;\n    }\n    return {\n      ...floatingProps,\n      ...(ariaRole === 'menu' && {\n        'aria-labelledby': referenceId\n      })\n    };\n  }, [ariaRole, floatingId, referenceId, role]);\n  const item = React.useCallback(_ref => {\n    let {\n      active,\n      selected\n    } = _ref;\n    const commonProps = {\n      role: 'option',\n      ...(active && {\n        id: floatingId + \"-fui-option\"\n      })\n    };\n\n    // For `menu`, we are unable to tell if the item is a `menuitemradio`\n    // or `menuitemcheckbox`. For backwards-compatibility reasons, also\n    // avoid defaulting to `menuitem` as it may overwrite custom role props.\n    switch (role) {\n      case 'select':\n        return {\n          ...commonProps,\n          'aria-selected': active && selected\n        };\n      case 'combobox':\n        {\n          return {\n            ...commonProps,\n            'aria-selected': selected\n          };\n        }\n    }\n    return {};\n  }, [floatingId, role]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating,\n    item\n  } : {}, [enabled, reference, floating, item]);\n}\n\n// Converts a JS style key like `backgroundColor` to a CSS transition-property\n// like `background-color`.\nconst camelCaseToKebabCase = str => str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, ($, ofs) => (ofs ? '-' : '') + $.toLowerCase());\nfunction execWithArgsOrReturn(valueOrFn, args) {\n  return typeof valueOrFn === 'function' ? valueOrFn(args) : valueOrFn;\n}\nfunction useDelayUnmount(open, durationMs) {\n  const [isMounted, setIsMounted] = React.useState(open);\n  if (open && !isMounted) {\n    setIsMounted(true);\n  }\n  React.useEffect(() => {\n    if (!open && isMounted) {\n      const timeout = setTimeout(() => setIsMounted(false), durationMs);\n      return () => clearTimeout(timeout);\n    }\n  }, [open, isMounted, durationMs]);\n  return isMounted;\n}\n/**\n * Provides a status string to apply CSS transitions to a floating element,\n * correctly handling placement-aware transitions.\n * @see https://floating-ui.com/docs/useTransition#usetransitionstatus\n */\nfunction useTransitionStatus(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    elements: {\n      floating\n    }\n  } = context;\n  const {\n    duration = 250\n  } = props;\n  const isNumberDuration = typeof duration === 'number';\n  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;\n  const [status, setStatus] = React.useState('unmounted');\n  const isMounted = useDelayUnmount(open, closeDuration);\n  if (!isMounted && status === 'close') {\n    setStatus('unmounted');\n  }\n  useModernLayoutEffect(() => {\n    if (!floating) return;\n    if (open) {\n      setStatus('initial');\n      const frame = requestAnimationFrame(() => {\n        // Ensure it opens before paint. With `FloatingDelayGroup`,\n        // this avoids a flicker when moving between floating elements\n        // to ensure one is always open with no missing frames.\n        ReactDOM.flushSync(() => {\n          setStatus('open');\n        });\n      });\n      return () => {\n        cancelAnimationFrame(frame);\n      };\n    }\n    setStatus('close');\n  }, [open, floating]);\n  return {\n    isMounted,\n    status\n  };\n}\n/**\n * Provides styles to apply CSS transitions to a floating element, correctly\n * handling placement-aware transitions. Wrapper around `useTransitionStatus`.\n * @see https://floating-ui.com/docs/useTransition#usetransitionstyles\n */\nfunction useTransitionStyles(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    initial: unstable_initial = {\n      opacity: 0\n    },\n    open: unstable_open,\n    close: unstable_close,\n    common: unstable_common,\n    duration = 250\n  } = props;\n  const placement = context.placement;\n  const side = placement.split('-')[0];\n  const fnArgs = React.useMemo(() => ({\n    side,\n    placement\n  }), [side, placement]);\n  const isNumberDuration = typeof duration === 'number';\n  const openDuration = (isNumberDuration ? duration : duration.open) || 0;\n  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;\n  const [styles, setStyles] = React.useState(() => ({\n    ...execWithArgsOrReturn(unstable_common, fnArgs),\n    ...execWithArgsOrReturn(unstable_initial, fnArgs)\n  }));\n  const {\n    isMounted,\n    status\n  } = useTransitionStatus(context, {\n    duration\n  });\n  const initialRef = useLatestRef(unstable_initial);\n  const openRef = useLatestRef(unstable_open);\n  const closeRef = useLatestRef(unstable_close);\n  const commonRef = useLatestRef(unstable_common);\n  useModernLayoutEffect(() => {\n    const initialStyles = execWithArgsOrReturn(initialRef.current, fnArgs);\n    const closeStyles = execWithArgsOrReturn(closeRef.current, fnArgs);\n    const commonStyles = execWithArgsOrReturn(commonRef.current, fnArgs);\n    const openStyles = execWithArgsOrReturn(openRef.current, fnArgs) || Object.keys(initialStyles).reduce((acc, key) => {\n      acc[key] = '';\n      return acc;\n    }, {});\n    if (status === 'initial') {\n      setStyles(styles => ({\n        transitionProperty: styles.transitionProperty,\n        ...commonStyles,\n        ...initialStyles\n      }));\n    }\n    if (status === 'open') {\n      setStyles({\n        transitionProperty: Object.keys(openStyles).map(camelCaseToKebabCase).join(','),\n        transitionDuration: openDuration + \"ms\",\n        ...commonStyles,\n        ...openStyles\n      });\n    }\n    if (status === 'close') {\n      const styles = closeStyles || initialStyles;\n      setStyles({\n        transitionProperty: Object.keys(styles).map(camelCaseToKebabCase).join(','),\n        transitionDuration: closeDuration + \"ms\",\n        ...commonStyles,\n        ...styles\n      });\n    }\n  }, [closeDuration, closeRef, initialRef, openRef, commonRef, openDuration, status, fnArgs]);\n  return {\n    isMounted,\n    styles\n  };\n}\n\n/**\n * Provides a matching callback that can be used to focus an item as the user\n * types, often used in tandem with `useListNavigation()`.\n * @see https://floating-ui.com/docs/useTypeahead\n */\nfunction useTypeahead(context, props) {\n  var _ref;\n  const {\n    open,\n    dataRef\n  } = context;\n  const {\n    listRef,\n    activeIndex,\n    onMatch: unstable_onMatch,\n    onTypingChange: unstable_onTypingChange,\n    enabled = true,\n    findMatch = null,\n    resetMs = 750,\n    ignoreKeys = [],\n    selectedIndex = null\n  } = props;\n  const timeoutIdRef = React.useRef(-1);\n  const stringRef = React.useRef('');\n  const prevIndexRef = React.useRef((_ref = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref : -1);\n  const matchIndexRef = React.useRef(null);\n  const onMatch = useEffectEvent(unstable_onMatch);\n  const onTypingChange = useEffectEvent(unstable_onTypingChange);\n  const findMatchRef = useLatestRef(findMatch);\n  const ignoreKeysRef = useLatestRef(ignoreKeys);\n  useModernLayoutEffect(() => {\n    if (open) {\n      clearTimeoutIfSet(timeoutIdRef);\n      matchIndexRef.current = null;\n      stringRef.current = '';\n    }\n  }, [open]);\n  useModernLayoutEffect(() => {\n    // Sync arrow key navigation but not typeahead navigation.\n    if (open && stringRef.current === '') {\n      var _ref2;\n      prevIndexRef.current = (_ref2 = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref2 : -1;\n    }\n  }, [open, selectedIndex, activeIndex]);\n  const setTypingChange = useEffectEvent(value => {\n    if (value) {\n      if (!dataRef.current.typing) {\n        dataRef.current.typing = value;\n        onTypingChange(value);\n      }\n    } else {\n      if (dataRef.current.typing) {\n        dataRef.current.typing = value;\n        onTypingChange(value);\n      }\n    }\n  });\n  const onKeyDown = useEffectEvent(event => {\n    function getMatchingIndex(list, orderedList, string) {\n      const str = findMatchRef.current ? findMatchRef.current(orderedList, string) : orderedList.find(text => (text == null ? void 0 : text.toLocaleLowerCase().indexOf(string.toLocaleLowerCase())) === 0);\n      return str ? list.indexOf(str) : -1;\n    }\n    const listContent = listRef.current;\n    if (stringRef.current.length > 0 && stringRef.current[0] !== ' ') {\n      if (getMatchingIndex(listContent, listContent, stringRef.current) === -1) {\n        setTypingChange(false);\n      } else if (event.key === ' ') {\n        stopEvent(event);\n      }\n    }\n    if (listContent == null || ignoreKeysRef.current.includes(event.key) ||\n    // Character key.\n    event.key.length !== 1 ||\n    // Modifier key.\n    event.ctrlKey || event.metaKey || event.altKey) {\n      return;\n    }\n    if (open && event.key !== ' ') {\n      stopEvent(event);\n      setTypingChange(true);\n    }\n\n    // Bail out if the list contains a word like \"llama\" or \"aaron\". TODO:\n    // allow it in this case, too.\n    const allowRapidSuccessionOfFirstLetter = listContent.every(text => {\n      var _text$, _text$2;\n      return text ? ((_text$ = text[0]) == null ? void 0 : _text$.toLocaleLowerCase()) !== ((_text$2 = text[1]) == null ? void 0 : _text$2.toLocaleLowerCase()) : true;\n    });\n\n    // Allows the user to cycle through items that start with the same letter\n    // in rapid succession.\n    if (allowRapidSuccessionOfFirstLetter && stringRef.current === event.key) {\n      stringRef.current = '';\n      prevIndexRef.current = matchIndexRef.current;\n    }\n    stringRef.current += event.key;\n    clearTimeoutIfSet(timeoutIdRef);\n    timeoutIdRef.current = window.setTimeout(() => {\n      stringRef.current = '';\n      prevIndexRef.current = matchIndexRef.current;\n      setTypingChange(false);\n    }, resetMs);\n    const prevIndex = prevIndexRef.current;\n    const index = getMatchingIndex(listContent, [...listContent.slice((prevIndex || 0) + 1), ...listContent.slice(0, (prevIndex || 0) + 1)], stringRef.current);\n    if (index !== -1) {\n      onMatch(index);\n      matchIndexRef.current = index;\n    } else if (event.key !== ' ') {\n      stringRef.current = '';\n      setTypingChange(false);\n    }\n  });\n  const reference = React.useMemo(() => ({\n    onKeyDown\n  }), [onKeyDown]);\n  const floating = React.useMemo(() => {\n    return {\n      onKeyDown,\n      onKeyUp(event) {\n        if (event.key === ' ') {\n          setTypingChange(false);\n        }\n      }\n    };\n  }, [onKeyDown, setTypingChange]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating\n  } : {}, [enabled, reference, floating]);\n}\n\nfunction getArgsWithCustomFloatingHeight(state, height) {\n  return {\n    ...state,\n    rects: {\n      ...state.rects,\n      floating: {\n        ...state.rects.floating,\n        height\n      }\n    }\n  };\n}\n/**\n * Positions the floating element such that an inner element inside of it is\n * anchored to the reference element.\n * @see https://floating-ui.com/docs/inner\n * @deprecated\n */\nconst inner = props => ({\n  name: 'inner',\n  options: props,\n  async fn(state) {\n    const {\n      listRef,\n      overflowRef,\n      onFallbackChange,\n      offset: innerOffset = 0,\n      index = 0,\n      minItemsVisible = 4,\n      referenceOverflowThreshold = 0,\n      scrollRef,\n      ...detectOverflowOptions\n    } = evaluate(props, state);\n    const {\n      rects,\n      elements: {\n        floating\n      }\n    } = state;\n    const item = listRef.current[index];\n    const scrollEl = (scrollRef == null ? void 0 : scrollRef.current) || floating;\n\n    // Valid combinations:\n    // 1. Floating element is the scrollRef and has a border (default)\n    // 2. Floating element is not the scrollRef, floating element has a border\n    // 3. Floating element is not the scrollRef, scrollRef has a border\n    // Floating > {...getFloatingProps()} wrapper > scrollRef > items is not\n    // allowed as VoiceOver doesn't work.\n    const clientTop = floating.clientTop || scrollEl.clientTop;\n    const floatingIsBordered = floating.clientTop !== 0;\n    const scrollElIsBordered = scrollEl.clientTop !== 0;\n    const floatingIsScrollEl = floating === scrollEl;\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!state.placement.startsWith('bottom')) {\n        warn('`placement` side must be \"bottom\" when using the `inner`', 'middleware.');\n      }\n    }\n    if (!item) {\n      return {};\n    }\n    const nextArgs = {\n      ...state,\n      ...(await offset(-item.offsetTop - floating.clientTop - rects.reference.height / 2 - item.offsetHeight / 2 - innerOffset).fn(state))\n    };\n    const overflow = await detectOverflow(getArgsWithCustomFloatingHeight(nextArgs, scrollEl.scrollHeight + clientTop + floating.clientTop), detectOverflowOptions);\n    const refOverflow = await detectOverflow(nextArgs, {\n      ...detectOverflowOptions,\n      elementContext: 'reference'\n    });\n    const diffY = max(0, overflow.top);\n    const nextY = nextArgs.y + diffY;\n    const isScrollable = scrollEl.scrollHeight > scrollEl.clientHeight;\n    const rounder = isScrollable ? v => v : round;\n    const maxHeight = rounder(max(0, scrollEl.scrollHeight + (floatingIsBordered && floatingIsScrollEl || scrollElIsBordered ? clientTop * 2 : 0) - diffY - max(0, overflow.bottom)));\n    scrollEl.style.maxHeight = maxHeight + \"px\";\n    scrollEl.scrollTop = diffY;\n\n    // There is not enough space, fallback to standard anchored positioning\n    if (onFallbackChange) {\n      const shouldFallback = scrollEl.offsetHeight < item.offsetHeight * min(minItemsVisible, listRef.current.length) - 1 || refOverflow.top >= -referenceOverflowThreshold || refOverflow.bottom >= -referenceOverflowThreshold;\n      ReactDOM.flushSync(() => onFallbackChange(shouldFallback));\n    }\n    if (overflowRef) {\n      overflowRef.current = await detectOverflow(getArgsWithCustomFloatingHeight({\n        ...nextArgs,\n        y: nextY\n      }, scrollEl.offsetHeight + clientTop + floating.clientTop), detectOverflowOptions);\n    }\n    return {\n      y: nextY\n    };\n  }\n});\n/**\n * Changes the `inner` middleware's `offset` upon a `wheel` event to\n * expand the floating element's height, revealing more list items.\n * @see https://floating-ui.com/docs/inner\n * @deprecated\n */\nfunction useInnerOffset(context, props) {\n  const {\n    open,\n    elements\n  } = context;\n  const {\n    enabled = true,\n    overflowRef,\n    scrollRef,\n    onChange: unstable_onChange\n  } = props;\n  const onChange = useEffectEvent(unstable_onChange);\n  const controlledScrollingRef = React.useRef(false);\n  const prevScrollTopRef = React.useRef(null);\n  const initialOverflowRef = React.useRef(null);\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onWheel(e) {\n      if (e.ctrlKey || !el || overflowRef.current == null) {\n        return;\n      }\n      const dY = e.deltaY;\n      const isAtTop = overflowRef.current.top >= -0.5;\n      const isAtBottom = overflowRef.current.bottom >= -0.5;\n      const remainingScroll = el.scrollHeight - el.clientHeight;\n      const sign = dY < 0 ? -1 : 1;\n      const method = dY < 0 ? 'max' : 'min';\n      if (el.scrollHeight <= el.clientHeight) {\n        return;\n      }\n      if (!isAtTop && dY > 0 || !isAtBottom && dY < 0) {\n        e.preventDefault();\n        ReactDOM.flushSync(() => {\n          onChange(d => d + Math[method](dY, remainingScroll * sign));\n        });\n      } else if (/firefox/i.test(getUserAgent())) {\n        // Needed to propagate scrolling during momentum scrolling phase once\n        // it gets limited by the boundary. UX improvement, not critical.\n        el.scrollTop += dY;\n      }\n    }\n    const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;\n    if (open && el) {\n      el.addEventListener('wheel', onWheel);\n\n      // Wait for the position to be ready.\n      requestAnimationFrame(() => {\n        prevScrollTopRef.current = el.scrollTop;\n        if (overflowRef.current != null) {\n          initialOverflowRef.current = {\n            ...overflowRef.current\n          };\n        }\n      });\n      return () => {\n        prevScrollTopRef.current = null;\n        initialOverflowRef.current = null;\n        el.removeEventListener('wheel', onWheel);\n      };\n    }\n  }, [enabled, open, elements.floating, overflowRef, scrollRef, onChange]);\n  const floating = React.useMemo(() => ({\n    onKeyDown() {\n      controlledScrollingRef.current = true;\n    },\n    onWheel() {\n      controlledScrollingRef.current = false;\n    },\n    onPointerMove() {\n      controlledScrollingRef.current = false;\n    },\n    onScroll() {\n      const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;\n      if (!overflowRef.current || !el || !controlledScrollingRef.current) {\n        return;\n      }\n      if (prevScrollTopRef.current !== null) {\n        const scrollDiff = el.scrollTop - prevScrollTopRef.current;\n        if (overflowRef.current.bottom < -0.5 && scrollDiff < -1 || overflowRef.current.top < -0.5 && scrollDiff > 1) {\n          ReactDOM.flushSync(() => onChange(d => d + scrollDiff));\n        }\n      }\n\n      // [Firefox] Wait for the height change to have been applied.\n      requestAnimationFrame(() => {\n        prevScrollTopRef.current = el.scrollTop;\n      });\n    }\n  }), [elements.floating, onChange, overflowRef, scrollRef]);\n  return React.useMemo(() => enabled ? {\n    floating\n  } : {}, [enabled, floating]);\n}\n\nfunction getNodeChildren(nodes, id, onlyOpenChildren) {\n  if (onlyOpenChildren === void 0) {\n    onlyOpenChildren = true;\n  }\n  const directChildren = nodes.filter(node => {\n    var _node$context;\n    return node.parentId === id && (!onlyOpenChildren || ((_node$context = node.context) == null ? void 0 : _node$context.open));\n  });\n  return directChildren.flatMap(child => [child, ...getNodeChildren(nodes, child.id, onlyOpenChildren)]);\n}\n\nfunction isPointInPolygon(point, polygon) {\n  const [x, y] = point;\n  let isInside = false;\n  const length = polygon.length;\n  for (let i = 0, j = length - 1; i < length; j = i++) {\n    const [xi, yi] = polygon[i] || [0, 0];\n    const [xj, yj] = polygon[j] || [0, 0];\n    const intersect = yi >= y !== yj >= y && x <= (xj - xi) * (y - yi) / (yj - yi) + xi;\n    if (intersect) {\n      isInside = !isInside;\n    }\n  }\n  return isInside;\n}\nfunction isInside(point, rect) {\n  return point[0] >= rect.x && point[0] <= rect.x + rect.width && point[1] >= rect.y && point[1] <= rect.y + rect.height;\n}\n/**\n * Generates a safe polygon area that the user can traverse without closing the\n * floating element once leaving the reference element.\n * @see https://floating-ui.com/docs/useHover#safepolygon\n */\nfunction safePolygon(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    buffer = 0.5,\n    blockPointerEvents = false,\n    requireIntent = true\n  } = options;\n  const timeoutRef = {\n    current: -1\n  };\n  let hasLanded = false;\n  let lastX = null;\n  let lastY = null;\n  let lastCursorTime = performance.now();\n  function getCursorSpeed(x, y) {\n    const currentTime = performance.now();\n    const elapsedTime = currentTime - lastCursorTime;\n    if (lastX === null || lastY === null || elapsedTime === 0) {\n      lastX = x;\n      lastY = y;\n      lastCursorTime = currentTime;\n      return null;\n    }\n    const deltaX = x - lastX;\n    const deltaY = y - lastY;\n    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n    const speed = distance / elapsedTime; // px / ms\n\n    lastX = x;\n    lastY = y;\n    lastCursorTime = currentTime;\n    return speed;\n  }\n  const fn = _ref => {\n    let {\n      x,\n      y,\n      placement,\n      elements,\n      onClose,\n      nodeId,\n      tree\n    } = _ref;\n    return function onMouseMove(event) {\n      function close() {\n        clearTimeoutIfSet(timeoutRef);\n        onClose();\n      }\n      clearTimeoutIfSet(timeoutRef);\n      if (!elements.domReference || !elements.floating || placement == null || x == null || y == null) {\n        return;\n      }\n      const {\n        clientX,\n        clientY\n      } = event;\n      const clientPoint = [clientX, clientY];\n      const target = getTarget(event);\n      const isLeave = event.type === 'mouseleave';\n      const isOverFloatingEl = contains(elements.floating, target);\n      const isOverReferenceEl = contains(elements.domReference, target);\n      const refRect = elements.domReference.getBoundingClientRect();\n      const rect = elements.floating.getBoundingClientRect();\n      const side = placement.split('-')[0];\n      const cursorLeaveFromRight = x > rect.right - rect.width / 2;\n      const cursorLeaveFromBottom = y > rect.bottom - rect.height / 2;\n      const isOverReferenceRect = isInside(clientPoint, refRect);\n      const isFloatingWider = rect.width > refRect.width;\n      const isFloatingTaller = rect.height > refRect.height;\n      const left = (isFloatingWider ? refRect : rect).left;\n      const right = (isFloatingWider ? refRect : rect).right;\n      const top = (isFloatingTaller ? refRect : rect).top;\n      const bottom = (isFloatingTaller ? refRect : rect).bottom;\n      if (isOverFloatingEl) {\n        hasLanded = true;\n        if (!isLeave) {\n          return;\n        }\n      }\n      if (isOverReferenceEl) {\n        hasLanded = false;\n      }\n      if (isOverReferenceEl && !isLeave) {\n        hasLanded = true;\n        return;\n      }\n\n      // Prevent overlapping floating element from being stuck in an open-close\n      // loop: https://github.com/floating-ui/floating-ui/issues/1910\n      if (isLeave && isElement(event.relatedTarget) && contains(elements.floating, event.relatedTarget)) {\n        return;\n      }\n\n      // If any nested child is open, abort.\n      if (tree && getNodeChildren(tree.nodesRef.current, nodeId).length) {\n        return;\n      }\n\n      // If the pointer is leaving from the opposite side, the \"buffer\" logic\n      // creates a point where the floating element remains open, but should be\n      // ignored.\n      // A constant of 1 handles floating point rounding errors.\n      if (side === 'top' && y >= refRect.bottom - 1 || side === 'bottom' && y <= refRect.top + 1 || side === 'left' && x >= refRect.right - 1 || side === 'right' && x <= refRect.left + 1) {\n        return close();\n      }\n\n      // Ignore when the cursor is within the rectangular trough between the\n      // two elements. Since the triangle is created from the cursor point,\n      // which can start beyond the ref element's edge, traversing back and\n      // forth from the ref to the floating element can cause it to close. This\n      // ensures it always remains open in that case.\n      let rectPoly = [];\n      switch (side) {\n        case 'top':\n          rectPoly = [[left, refRect.top + 1], [left, rect.bottom - 1], [right, rect.bottom - 1], [right, refRect.top + 1]];\n          break;\n        case 'bottom':\n          rectPoly = [[left, rect.top + 1], [left, refRect.bottom - 1], [right, refRect.bottom - 1], [right, rect.top + 1]];\n          break;\n        case 'left':\n          rectPoly = [[rect.right - 1, bottom], [rect.right - 1, top], [refRect.left + 1, top], [refRect.left + 1, bottom]];\n          break;\n        case 'right':\n          rectPoly = [[refRect.right - 1, bottom], [refRect.right - 1, top], [rect.left + 1, top], [rect.left + 1, bottom]];\n          break;\n      }\n      function getPolygon(_ref2) {\n        let [x, y] = _ref2;\n        switch (side) {\n          case 'top':\n            {\n              const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];\n              const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];\n              const commonPoints = [[rect.left, cursorLeaveFromRight ? rect.bottom - buffer : isFloatingWider ? rect.bottom - buffer : rect.top], [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.bottom - buffer : rect.top : rect.bottom - buffer]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n          case 'bottom':\n            {\n              const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];\n              const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];\n              const commonPoints = [[rect.left, cursorLeaveFromRight ? rect.top + buffer : isFloatingWider ? rect.top + buffer : rect.bottom], [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.top + buffer : rect.bottom : rect.top + buffer]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n          case 'left':\n            {\n              const cursorPointOne = [x + buffer + 1, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const cursorPointTwo = [x + buffer + 1, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const commonPoints = [[cursorLeaveFromBottom ? rect.right - buffer : isFloatingTaller ? rect.right - buffer : rect.left, rect.top], [cursorLeaveFromBottom ? isFloatingTaller ? rect.right - buffer : rect.left : rect.right - buffer, rect.bottom]];\n              return [...commonPoints, cursorPointOne, cursorPointTwo];\n            }\n          case 'right':\n            {\n              const cursorPointOne = [x - buffer, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const cursorPointTwo = [x - buffer, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const commonPoints = [[cursorLeaveFromBottom ? rect.left + buffer : isFloatingTaller ? rect.left + buffer : rect.right, rect.top], [cursorLeaveFromBottom ? isFloatingTaller ? rect.left + buffer : rect.right : rect.left + buffer, rect.bottom]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n        }\n      }\n      if (isPointInPolygon([clientX, clientY], rectPoly)) {\n        return;\n      }\n      if (hasLanded && !isOverReferenceRect) {\n        return close();\n      }\n      if (!isLeave && requireIntent) {\n        const cursorSpeed = getCursorSpeed(event.clientX, event.clientY);\n        const cursorSpeedThreshold = 0.1;\n        if (cursorSpeed !== null && cursorSpeed < cursorSpeedThreshold) {\n          return close();\n        }\n      }\n      if (!isPointInPolygon([clientX, clientY], getPolygon([x, y]))) {\n        close();\n      } else if (!hasLanded && requireIntent) {\n        timeoutRef.current = window.setTimeout(close, 40);\n      }\n    };\n  };\n  fn.__options = {\n    blockPointerEvents\n  };\n  return fn;\n}\n\nexport { Composite, CompositeItem, FloatingArrow, FloatingDelayGroup, FloatingFocusManager, FloatingList, FloatingNode, FloatingOverlay, FloatingPortal, FloatingTree, NextFloatingDelayGroup, inner, safePolygon, useClick, useClientPoint, useDelayGroup, useDelayGroupContext, useDismiss, useFloating, useFloatingNodeId, useFloatingParentNodeId, useFloatingPortalNode, useFloatingRootContext, useFloatingTree, useFocus, useHover, useId, useInnerOffset, useInteractions, useListItem, useListNavigation, useMergeRefs, useNextDelayGroup, useRole, useTransitionStatus, useTransitionStyles, useTypeahead };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;;;;;;;;;;AAEA;;;CAGC,GACD,SAAS,aAAa,IAAI;IACxB,MAAM,aAAa,6WAAM,MAAM,CAAC;IAChC,MAAM,YAAY,6WAAM,WAAW,CAAC,CAAA;QAClC,MAAM,WAAW,KAAK,GAAG,CAAC,CAAA;YACxB,IAAI,OAAO,MAAM;gBACf;YACF;YACA,IAAI,OAAO,QAAQ,YAAY;gBAC7B,MAAM,cAAc;gBACpB,MAAM,aAAa,YAAY;gBAC/B,OAAO,OAAO,eAAe,aAAa,aAAa;oBACrD,YAAY;gBACd;YACF;YACA,IAAI,OAAO,GAAG;YACd,OAAO;gBACL,IAAI,OAAO,GAAG;YAChB;QACF;QACA,OAAO;YACL,SAAS,OAAO,CAAC,CAAA,aAAc,cAAc,OAAO,KAAK,IAAI;QAC/D;IACA,uDAAuD;IACzD,GAAG;IACH,OAAO,6WAAM,OAAO,CAAC;QACnB,IAAI,KAAK,KAAK,CAAC,CAAA,MAAO,OAAO,OAAO;YAClC,OAAO;QACT;QACA,OAAO,CAAA;YACL,IAAI,WAAW,OAAO,EAAE;gBACtB,WAAW,OAAO;gBAClB,WAAW,OAAO,GAAG;YACvB;YACA,IAAI,SAAS,MAAM;gBACjB,WAAW,OAAO,GAAG,UAAU;YACjC;QACF;IACA,uDAAuD;IACzD,GAAG;AACL;AAEA,SAAS,uBAAuB,CAAC,EAAE,CAAC;IAClC,MAAM,WAAW,EAAE,uBAAuB,CAAC;IAC3C,IAAI,WAAW,KAAK,2BAA2B,IAAI,WAAW,KAAK,8BAA8B,EAAE;QACjG,OAAO,CAAC;IACV;IACA,IAAI,WAAW,KAAK,2BAA2B,IAAI,WAAW,KAAK,0BAA0B,EAAE;QAC7F,OAAO;IACT;IACA,OAAO;AACT;AACA,MAAM,sBAAsB,WAAW,GAAE,6WAAM,aAAa,CAAC;IAC3D,UAAU,KAAO;IACjB,YAAY,KAAO;IACnB,KAAK,WAAW,GAAE,IAAI;IACtB,aAAa;QACX,SAAS,EAAE;IACb;AACF;AACA;;;CAGC,GACD,SAAS,aAAa,KAAK;IACzB,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,SAAS,EACV,GAAG;IACJ,MAAM,CAAC,OAAO,SAAS,GAAG,6WAAM,QAAQ,CAAC,IAAM,IAAI;IACnD,MAAM,WAAW,6WAAM,WAAW,CAAC,CAAA;QACjC,SAAS,CAAA,UAAW,IAAI,IAAI,SAAS,GAAG,CAAC;IAC3C,GAAG,EAAE;IACL,MAAM,aAAa,6WAAM,WAAW,CAAC,CAAA;QACnC,SAAS,CAAA;YACP,MAAM,MAAM,IAAI,IAAI;YACpB,IAAI,MAAM,CAAC;YACX,OAAO;QACT;IACF,GAAG,EAAE;IACL,MAAM,MAAM,6WAAM,OAAO,CAAC;QACxB,MAAM,SAAS,IAAI;QACnB,MAAM,cAAc,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC;QAClD,YAAY,OAAO,CAAC,CAAC,MAAM;YACzB,OAAO,GAAG,CAAC,MAAM;QACnB;QACA,OAAO;IACT,GAAG;QAAC;KAAM;IACV,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,oBAAoB,QAAQ,EAAE;QACpD,OAAO,6WAAM,OAAO,CAAC,IAAM,CAAC;gBAC1B;gBACA;gBACA;gBACA;gBACA;YACF,CAAC,GAAG;YAAC;YAAU;YAAY;YAAK;YAAa;SAAU;QACvD,UAAU;IACZ;AACF;AACA;;;;CAIC,GACD,SAAS,YAAY,KAAK;IACxB,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,KAAK,EACN,GAAG;IACJ,MAAM,EACJ,QAAQ,EACR,UAAU,EACV,GAAG,EACH,WAAW,EACX,SAAS,EACV,GAAG,6WAAM,UAAU,CAAC;IACrB,MAAM,CAAC,OAAO,SAAS,GAAG,6WAAM,QAAQ,CAAC;IACzC,MAAM,eAAe,6WAAM,MAAM,CAAC;IAClC,MAAM,MAAM,6WAAM,WAAW,CAAC,CAAA;QAC5B,aAAa,OAAO,GAAG;QACvB,IAAI,UAAU,MAAM;YAClB,YAAY,OAAO,CAAC,MAAM,GAAG;YAC7B,IAAI,WAAW;gBACb,IAAI;gBACJ,MAAM,iBAAiB,UAAU;gBACjC,UAAU,OAAO,CAAC,MAAM,GAAG,iBAAiB,QAAQ,CAAC,oBAAoB,QAAQ,OAAO,KAAK,IAAI,KAAK,WAAW,KAAK,OAAO,oBAAoB;YACnJ;QACF;IACF,GAAG;QAAC;QAAO;QAAa;QAAW;KAAM;IACzC,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,MAAM,OAAO,aAAa,OAAO;QACjC,IAAI,MAAM;YACR,SAAS;YACT,OAAO;gBACL,WAAW;YACb;QACF;IACF,GAAG;QAAC;QAAU;KAAW;IACzB,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,MAAM,QAAQ,aAAa,OAAO,GAAG,IAAI,GAAG,CAAC,aAAa,OAAO,IAAI;QACrE,IAAI,SAAS,MAAM;YACjB,SAAS;QACX;IACF,GAAG;QAAC;KAAI;IACR,OAAO,6WAAM,OAAO,CAAC,IAAM,CAAC;YAC1B;YACA,OAAO,SAAS,OAAO,CAAC,IAAI;QAC9B,CAAC,GAAG;QAAC;QAAO;KAAI;AAClB;AAEA,MAAM,sBAAsB;AAC5B,MAAM,aAAa;AACnB,MAAM,eAAe;AACrB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,WAAW;AACjB,MAAM,aAAa;AAEnB,SAAS,UAAU,MAAM,EAAE,aAAa;IACtC,IAAI,OAAO,WAAW,YAAY;QAChC,OAAO,OAAO;IAChB;IACA,IAAI,QAAQ;QACV,OAAO,WAAW,GAAE,6WAAM,YAAY,CAAC,QAAQ;IACjD;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAC7B,GAAG,aAAa;IAClB;AACF;AACA,MAAM,mBAAmB,WAAW,GAAE,6WAAM,aAAa,CAAC;IACxD,aAAa;IACb,YAAY,KAAO;AACrB;AACA,MAAM,iBAAiB;IAAC;IAAY;CAAY;AAChD,MAAM,eAAe;IAAC;IAAU;CAAW;AAC3C,MAAM,UAAU;OAAI;OAAmB;CAAa;AAEpD;;;;;;;;CAQC,GACD,MAAM,YAAY,WAAW,GAAE,6WAAM,UAAU,CAAC,SAAS,UAAU,KAAK,EAAE,YAAY;IACpF,MAAM,EACJ,MAAM,EACN,cAAc,MAAM,EACpB,OAAO,IAAI,EACX,MAAM,KAAK,EACX,OAAO,CAAC,EACR,eAAe,EACf,aAAa,mBAAmB,EAChC,YAAY,sBAAsB,EAClC,SAAS,EACT,QAAQ,KAAK,EACb,GAAG,UACJ,GAAG;IACJ,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,6WAAM,QAAQ,CAAC;IACrE,MAAM,cAAc,uBAAuB,OAAO,sBAAsB;IACxE,MAAM,aAAa,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,0BAA0B,OAAO,yBAAyB;IAC5F,MAAM,cAAc,6WAAM,MAAM,CAAC,EAAE;IACnC,MAAM,qBAAqB,UAAU,OAAO,WAAW,aAAa,OAAO,KAAK,GAAG,CAAC;IACpF,MAAM,eAAe,6WAAM,OAAO,CAAC,IAAM,CAAC;YACxC;YACA;QACF,CAAC,GAAG;QAAC;QAAa;KAAW;IAC7B,MAAM,SAAS,OAAO;IACtB,SAAS,cAAc,KAAK;QAC1B,IAAI,CAAC,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;QAClC,IAAI,YAAY;QAChB,MAAM,WAAW,CAAA,GAAA,2UAAA,CAAA,kBAAe,AAAD,EAAE,aAAa;QAC9C,MAAM,WAAW,CAAA,GAAA,2UAAA,CAAA,kBAAe,AAAD,EAAE,aAAa;QAC9C,MAAM,mBAAmB,MAAM,aAAa;QAC5C,MAAM,qBAAqB,MAAM,cAAc;QAC/C,IAAI,QAAQ;YACV,MAAM,QAAQ,aAAa,MAAM,IAAI,CAAC;gBACpC,QAAQ,YAAY,OAAO,CAAC,MAAM;YACpC,GAAG,IAAM,CAAC;oBACR,OAAO;oBACP,QAAQ;gBACV,CAAC;YACD,uEAAuE;YACvE,+DAA+D;YAC/D,MAAM,UAAU,CAAA,GAAA,2UAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,MAAM;YAC/C,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAA,QAAS,SAAS,QAAQ,CAAC,CAAA,GAAA,2UAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,OAAO;YAC1G,qBAAqB;YACrB,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,YAAY,OAAO,YAAc,SAAS,QAAQ,CAAC,CAAA,GAAA,2UAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,OAAO,mBAAmB,YAAY,YAAY,CAAC;YAC5K,MAAM,iBAAiB,OAAO,CAAC,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;gBACnD,SAAS,QAAQ,GAAG,CAAC,CAAA,YAAa,YAAY,YAAY,OAAO,CAAC,UAAU,GAAG;YACjF,GAAG;gBACD;gBACA;gBACA;gBACA;gBACA;gBACA,gEAAgE;gBAChE,uBAAuB;gBACvB,iBAAiB,CAAA,GAAA,2UAAA,CAAA,qBAAkB,AAAD,EAAE;uBAAK,CAAC,OAAO,oBAAoB,aAAa,kBAAkB,IAAI,KAAK,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,QAAU,CAAA,GAAA,2UAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,OAAO,mBAAmB,QAAQ;oBAAa;iBAAU,EAAE;gBAC/O,UAAU;gBACV,UAAU;gBACV,WAAW,CAAA,GAAA,2UAAA,CAAA,2BAAwB,AAAD,EAAE,cAAc,WAAW,WAAW,aAAa,OAAO,SAAS,MACrG,gEAAgE;gBAChE,wDAAwD;gBACxD,8BAA8B;gBAC9B,MAAM,GAAG,KAAK,aAAa,OAAO,MAAM,GAAG,KAAK,mBAAmB,OAAO;YAC5E,GAAG;YACH,IAAI,kBAAkB,MAAM;gBAC1B,YAAY;YACd;QACF;QACA,MAAM,YAAY;YAChB,YAAY;gBAAC;aAAiB;YAC9B,UAAU;gBAAC;aAAW;YACtB,MAAM;gBAAC;gBAAkB;aAAW;QACtC,CAAC,CAAC,YAAY;QACd,MAAM,cAAc;YAClB,YAAY;gBAAC;aAAmB;YAChC,UAAU;gBAAC;aAAS;YACpB,MAAM;gBAAC;gBAAoB;aAAS;QACtC,CAAC,CAAC,YAAY;QACd,MAAM,gBAAgB,SAAS,UAAU,CAAA;YACvC,YAAY;YACZ,UAAU;YACV,MAAM;QACR,CAAA,CAAC,CAAC,YAAY;QACd,IAAI,cAAc,eAAe;eAAI;eAAc;SAAY,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnF,IAAI,QAAQ,cAAc,YAAY,UAAU,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACnE,YAAY;YACd,OAAO,IAAI,QAAQ,cAAc,YAAY,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC5E,YAAY;YACd,OAAO;gBACL,YAAY,CAAA,GAAA,2UAAA,CAAA,2BAAwB,AAAD,EAAE,aAAa;oBAChD,eAAe;oBACf,WAAW,YAAY,QAAQ,CAAC,MAAM,GAAG;oBACzC;gBACF;YACF;QACF;QACA,IAAI,cAAc,eAAe,CAAC,CAAA,GAAA,2UAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,YAAY;YAChF,IAAI;YACJ,MAAM,eAAe;YACrB,IAAI,cAAc,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACrC,MAAM,cAAc;YACtB;YACA,WAAW;YACX,CAAC,wBAAwB,YAAY,OAAO,CAAC,UAAU,KAAK,QAAQ,sBAAsB,KAAK;QACjG;IACF;IACA,MAAM,gBAAgB;QACpB,GAAG,QAAQ;QACX,GAAG,kBAAkB;QACrB,KAAK;QACL,oBAAoB,gBAAgB,SAAS,YAAY;QACzD,WAAU,CAAC;YACT,SAAS,SAAS,IAAI,QAAQ,SAAS,SAAS,CAAC;YACjD,mBAAmB,SAAS,IAAI,QAAQ,mBAAmB,SAAS,CAAC;YACrE,cAAc;QAChB;IACF;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,iBAAiB,QAAQ,EAAE;QACjD,OAAO;QACP,UAAU,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,cAAc;YACvC,aAAa;YACb,UAAU,UAAU,QAAQ;QAC9B;IACF;AACF;AACA;;CAEC,GACD,MAAM,gBAAgB,WAAW,GAAE,6WAAM,UAAU,CAAC,SAAS,cAAc,KAAK,EAAE,YAAY;IAC5F,MAAM,EACJ,MAAM,EACN,GAAG,UACJ,GAAG;IACJ,MAAM,qBAAqB,UAAU,OAAO,WAAW,aAAa,OAAO,KAAK,GAAG,CAAC;IACpF,MAAM,EACJ,WAAW,EACX,UAAU,EACX,GAAG,6WAAM,UAAU,CAAC;IACrB,MAAM,EACJ,GAAG,EACH,KAAK,EACN,GAAG;IACJ,MAAM,YAAY,aAAa;QAAC;QAAK;QAAc,mBAAmB,GAAG;KAAC;IAC1E,MAAM,WAAW,gBAAgB;IACjC,MAAM,gBAAgB;QACpB,GAAG,QAAQ;QACX,GAAG,kBAAkB;QACrB,KAAK;QACL,UAAU,WAAW,IAAI,CAAC;QAC1B,eAAe,WAAW,KAAK;QAC/B,SAAQ,CAAC;YACP,SAAS,OAAO,IAAI,QAAQ,SAAS,OAAO,CAAC;YAC7C,mBAAmB,OAAO,IAAI,QAAQ,mBAAmB,OAAO,CAAC;YACjE,WAAW;QACb;IACF;IACA,OAAO,UAAU,QAAQ;AAC3B;AAEA,0EAA0E;AAC1E,MAAM,YAAY;IAChB,GAAG,4WAAK;AACV;AAEA,IAAI,wBAAwB;AAC5B,IAAI,QAAQ;AACZ,MAAM,QAAQ,IACd,eAAe;IACf,iBAAiB,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK;AAC1D,SAAS;IACP,MAAM,CAAC,IAAI,MAAM,GAAG,6WAAM,QAAQ,CAAC,IAAM,wBAAwB,UAAU;IAC3E,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,MAAM,MAAM;YACd,MAAM;QACR;IACA,uDAAuD;IACzD,GAAG,EAAE;IACL,6WAAM,SAAS,CAAC;QACd,wBAAwB;IAC1B,GAAG,EAAE;IACL,OAAO;AACT;AACA,MAAM,aAAa,UAAU,KAAK;AAElC;;;;;CAKC,GACD,MAAM,QAAQ,cAAc;AAE5B,IAAI;AACJ,wCAA2C;IACzC,gBAAgB,WAAW,GAAE,IAAI;AACnC;AACA,SAAS;IACP,IAAI;IACJ,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,WAAW,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;QAC3F,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IAClC;IACA,MAAM,UAAU,kBAAkB,SAAS,IAAI,CAAC;IAChD,IAAI,CAAC,CAAC,CAAC,iBAAiB,aAAa,KAAK,QAAQ,eAAe,GAAG,CAAC,QAAQ,GAAG;QAC9E,IAAI;QACJ,CAAC,kBAAkB,aAAa,KAAK,QAAQ,gBAAgB,GAAG,CAAC;QACjE,QAAQ,IAAI,CAAC;IACf;AACF;AACA,SAAS;IACP,IAAI;IACJ,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,WAAW,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;QACjG,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;IACpC;IACA,MAAM,UAAU,kBAAkB,SAAS,IAAI,CAAC;IAChD,IAAI,CAAC,CAAC,CAAC,kBAAkB,aAAa,KAAK,QAAQ,gBAAgB,GAAG,CAAC,QAAQ,GAAG;QAChF,IAAI;QACJ,CAAC,kBAAkB,aAAa,KAAK,QAAQ,gBAAgB,GAAG,CAAC;QACjE,QAAQ,KAAK,CAAC;IAChB;AACF;AAEA;;;CAGC,GACD,MAAM,gBAAgB,WAAW,GAAE,6WAAM,UAAU,CAAC,SAAS,cAAc,KAAK,EAAE,GAAG;IACnF,MAAM,EACJ,SAAS,EACP,SAAS,EACT,UAAU,EACR,QAAQ,EACT,EACD,gBAAgB,EACd,KAAK,EACL,KAAK,EACN,EACF,EACD,QAAQ,EAAE,EACV,SAAS,CAAC,EACV,YAAY,CAAC,EACb,cAAc,CAAC,EACf,YAAY,EACZ,MAAM,EACN,CAAC,EACD,OAAO,EACL,SAAS,EACT,GAAG,WACJ,GAAG,CAAC,CAAC,EACN,GAAG,MACJ,GAAG;IACJ,wCAA2C;QACzC,IAAI,CAAC,KAAK;YACR,KAAK;QACP;IACF;IACA,MAAM,aAAa;IACnB,MAAM,CAAC,OAAO,SAAS,GAAG,6WAAM,QAAQ,CAAC;IAEzC,yDAAyD;IACzD,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,CAAC,UAAU;QACf,MAAM,QAAQ,CAAA,GAAA,2PAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,SAAS,KAAK;QACvD,IAAI,OAAO;YACT,SAAS;QACX;IACF,GAAG;QAAC;KAAS;IACb,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IACA,MAAM,CAAC,MAAM,UAAU,GAAG,UAAU,KAAK,CAAC;IAC1C,MAAM,iBAAiB,SAAS,SAAS,SAAS;IAClD,IAAI,uBAAuB;IAC3B,IAAI,kBAAkB,SAAS,QAAQ,MAAM,CAAC,IAAI,CAAC,kBAAkB,SAAS,QAAQ,MAAM,CAAC,EAAE;QAC7F,uBAAuB;IACzB;IAEA,2EAA2E;IAC3E,yBAAyB;IACzB,MAAM,sBAAsB,cAAc;IAC1C,MAAM,kBAAkB,sBAAsB;IAC9C,MAAM,OAAO,QAAQ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;IAC5C,MAAM,OAAO,SAAS,IAAI,YAAY;IACtC,MAAM,gBAAgB,CAAC,CAAC;IACxB,MAAM,cAAc,wBAAwB,cAAc,QAAQ,WAAW;IAC7E,IAAI,cAAc,wBAAwB,cAAc,QAAQ,UAAU;IAC1E,IAAI,wBAAwB,OAAO;QACjC,cAAc,cAAc,QAAQ,SAAS;IAC/C;IACA,MAAM,SAAS,CAAC,SAAS,OAAO,KAAK,IAAI,MAAM,CAAC,KAAK,OAAO,wBAAwB,MAAM,CAAC,GAAG;IAC9F,MAAM,SAAS,CAAC,SAAS,OAAO,KAAK,IAAI,MAAM,CAAC,KAAK,OAAO,wBAAwB,MAAM,CAAC,GAAG;IAC9F,MAAM,SAAS,KAAK,SAAS,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,QAAQ,IAAI,MAAM,SAAS,MAAM,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI;IACzK,MAAM,WAAW;QACf,KAAK,gBAAgB,mBAAmB;QACxC,MAAM,gBAAgB,kBAAkB;QACxC,QAAQ,gBAAgB,KAAK;QAC7B,OAAO,gBAAgB,mBAAmB;IAC5C,CAAC,CAAC,KAAK;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAC9B,GAAG,IAAI;QACP,eAAe;QACf,KAAK;QACL,OAAO,gBAAgB,QAAQ,QAAQ;QACvC,QAAQ;QACR,SAAS,SAAS,QAAQ,MAAM,CAAC,SAAS,QAAQ,SAAS,KAAK;QAChE,OAAO;YACL,UAAU;YACV,eAAe;YACf,CAAC,YAAY,EAAE;YACf,CAAC,YAAY,EAAE;YACf,CAAC,KAAK,EAAE,kBAAkB,gBAAgB,SAAS,iBAAiB,sBAAsB,IAAI;YAC9F,WAAW;gBAAC;gBAAU;aAAU,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,GAAG,IAAI,CAAC;YACvD,GAAG,SAAS;QACd;QACA,UAAU;YAAC,sBAAsB,KAAK,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAC7D,UAAU,UAAU,aAAa;gBACjC,MAAM;gBACN,QAAQ;gBAGR,aAAa,sBAAsB,CAAC,IAAI,IAAI,CAAC;gBAC7C,GAAG;YACL;YAAI,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAC3B,QAAQ,uBAAuB,CAAC,IAAI,KAAK,IAAI,GAAG;gBAChD,GAAG;YACL;YAAI,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,YAAY;gBAC/B,IAAI;gBACJ,UAAU,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;oBACjC,GAAG,CAAC;oBACJ,GAAG,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC;oBAC5C,OAAO,QAAQ;oBACf,QAAQ;gBACV;YACF;SAAG;IACL;AACF;AAEA,SAAS;IACP,MAAM,MAAM,IAAI;IAChB,OAAO;QACL,MAAK,KAAK,EAAE,IAAI;YACd,IAAI;YACJ,CAAC,WAAW,IAAI,GAAG,CAAC,MAAM,KAAK,QAAQ,SAAS,OAAO,CAAC,CAAA,WAAY,SAAS;QAC/E;QACA,IAAG,KAAK,EAAE,QAAQ;YAChB,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ;gBACnB,IAAI,GAAG,CAAC,OAAO,IAAI;YACrB;YACA,IAAI,GAAG,CAAC,OAAO,GAAG,CAAC;QACrB;QACA,KAAI,KAAK,EAAE,QAAQ;YACjB,IAAI;YACJ,CAAC,YAAY,IAAI,GAAG,CAAC,MAAM,KAAK,QAAQ,UAAU,MAAM,CAAC;QAC3D;IACF;AACF;AAEA,MAAM,sBAAsB,WAAW,GAAE,6WAAM,aAAa,CAAC;AAC7D,MAAM,sBAAsB,WAAW,GAAE,6WAAM,aAAa,CAAC;AAE7D;;;CAGC,GACD,MAAM,0BAA0B;IAC9B,IAAI;IACJ,OAAO,CAAC,CAAC,oBAAoB,6WAAM,UAAU,CAAC,oBAAoB,KAAK,OAAO,KAAK,IAAI,kBAAkB,EAAE,KAAK;AAClH;AAEA;;CAEC,GACD,MAAM,kBAAkB,IAAM,6WAAM,UAAU,CAAC;AAE/C;;;CAGC,GACD,SAAS,kBAAkB,cAAc;IACvC,MAAM,KAAK;IACX,MAAM,OAAO;IACb,MAAM,gBAAgB;IACtB,MAAM,WAAW,kBAAkB;IACnC,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,CAAC,IAAI;QACT,MAAM,OAAO;YACX;YACA;QACF;QACA,QAAQ,QAAQ,KAAK,OAAO,CAAC;QAC7B,OAAO;YACL,QAAQ,QAAQ,KAAK,UAAU,CAAC;QAClC;IACF,GAAG;QAAC;QAAM;QAAI;KAAS;IACvB,OAAO;AACT;AACA;;;CAGC,GACD,SAAS,aAAa,KAAK;IACzB,MAAM,EACJ,QAAQ,EACR,EAAE,EACH,GAAG;IACJ,MAAM,WAAW;IACjB,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,oBAAoB,QAAQ,EAAE;QACpD,OAAO,6WAAM,OAAO,CAAC,IAAM,CAAC;gBAC1B;gBACA;YACF,CAAC,GAAG;YAAC;YAAI;SAAS;QAClB,UAAU;IACZ;AACF;AACA;;;;;;;;;CASC,GACD,SAAS,aAAa,KAAK;IACzB,MAAM,EACJ,QAAQ,EACT,GAAG;IACJ,MAAM,WAAW,6WAAM,MAAM,CAAC,EAAE;IAChC,MAAM,UAAU,6WAAM,WAAW,CAAC,CAAA;QAChC,SAAS,OAAO,GAAG;eAAI,SAAS,OAAO;YAAE;SAAK;IAChD,GAAG,EAAE;IACL,MAAM,aAAa,6WAAM,WAAW,CAAC,CAAA;QACnC,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;IACxD,GAAG,EAAE;IACL,MAAM,CAAC,OAAO,GAAG,6WAAM,QAAQ,CAAC,IAAM;IACtC,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,oBAAoB,QAAQ,EAAE;QACpD,OAAO,6WAAM,OAAO,CAAC,IAAM,CAAC;gBAC1B;gBACA;gBACA;gBACA;YACF,CAAC,GAAG;YAAC;YAAS;YAAY;SAAO;QACjC,UAAU;IACZ;AACF;AAEA,SAAS,gBAAgB,IAAI;IAC3B,OAAO,sBAAsB;AAC/B;AAEA,SAAS,kBAAkB,UAAU;IACnC,IAAI,WAAW,OAAO,KAAK,CAAC,GAAG;QAC7B,aAAa,WAAW,OAAO;QAC/B,WAAW,OAAO,GAAG,CAAC;IACxB;AACF;AAEA,MAAM,wBAAwB,WAAW,GAAE,gBAAgB;AAC3D,SAAS,SAAS,KAAK,EAAE,IAAI,EAAE,WAAW;IACxC,IAAI,eAAe,CAAC,CAAA,GAAA,2UAAA,CAAA,yBAAsB,AAAD,EAAE,cAAc;QACvD,OAAO;IACT;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,IAAI,OAAO,UAAU,YAAY;QAC/B,MAAM,SAAS;QACf,IAAI,OAAO,WAAW,UAAU;YAC9B,OAAO;QACT;QACA,OAAO,UAAU,OAAO,KAAK,IAAI,MAAM,CAAC,KAAK;IAC/C;IACA,OAAO,SAAS,OAAO,KAAK,IAAI,KAAK,CAAC,KAAK;AAC7C;AACA,SAAS,UAAU,KAAK;IACtB,IAAI,OAAO,UAAU,YAAY;QAC/B,OAAO;IACT;IACA,OAAO;AACT;AACA;;;;CAIC,GACD,SAAS,SAAS,OAAO,EAAE,KAAK;IAC9B,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACT,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,QAAQ,CAAC,EACT,cAAc,IAAI,EAClB,YAAY,KAAK,EACjB,SAAS,CAAC,EACV,OAAO,IAAI,EACZ,GAAG;IACJ,MAAM,OAAO;IACb,MAAM,WAAW;IACjB,MAAM,iBAAiB,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IACpC,MAAM,WAAW,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IAC9B,MAAM,UAAU,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IAC7B,MAAM,YAAY,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IAC/B,MAAM,iBAAiB,6WAAM,MAAM;IACnC,MAAM,aAAa,6WAAM,MAAM,CAAC,CAAC;IACjC,MAAM,aAAa,6WAAM,MAAM;IAC/B,MAAM,iBAAiB,6WAAM,MAAM,CAAC,CAAC;IACrC,MAAM,oBAAoB,6WAAM,MAAM,CAAC;IACvC,MAAM,oCAAoC,6WAAM,MAAM,CAAC;IACvD,MAAM,qBAAqB,6WAAM,MAAM,CAAC,KAAO;IAC/C,MAAM,wBAAwB,6WAAM,MAAM,CAAC;IAC3C,MAAM,cAAc,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE;QACjC,IAAI;QACJ,MAAM,OAAO,CAAC,wBAAwB,QAAQ,OAAO,CAAC,SAAS,KAAK,OAAO,KAAK,IAAI,sBAAsB,IAAI;QAC9G,OAAO,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,SAAS;IACtE;IAEA,qEAAqE;IACrE,gBAAgB;IAChB,6WAAM,SAAS,CAAC;QACd,IAAI,CAAC,SAAS;QACd,SAAS,aAAa,IAAI;YACxB,IAAI,EACF,IAAI,EACL,GAAG;YACJ,IAAI,CAAC,MAAM;gBACT,kBAAkB;gBAClB,kBAAkB;gBAClB,kBAAkB,OAAO,GAAG;gBAC5B,sBAAsB,OAAO,GAAG;YAClC;QACF;QACA,OAAO,EAAE,CAAC,cAAc;QACxB,OAAO;YACL,OAAO,GAAG,CAAC,cAAc;QAC3B;IACF,GAAG;QAAC;QAAS;KAAO;IACpB,6WAAM,SAAS,CAAC;QACd,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,eAAe,OAAO,EAAE;QAC7B,IAAI,CAAC,MAAM;QACX,SAAS,QAAQ,KAAK;YACpB,IAAI,eAAe;gBACjB,aAAa,OAAO,OAAO;YAC7B;QACF;QACA,MAAM,OAAO,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE,SAAS,QAAQ,EAAE,eAAe;QAC7D,KAAK,gBAAgB,CAAC,cAAc;QACpC,OAAO;YACL,KAAK,mBAAmB,CAAC,cAAc;QACzC;IACF,GAAG;QAAC,SAAS,QAAQ;QAAE;QAAM;QAAc;QAAS;QAAgB;KAAY;IAChF,MAAM,iBAAiB,6WAAM,WAAW,CAAC,SAAU,KAAK,EAAE,aAAa,EAAE,MAAM;QAC7E,IAAI,kBAAkB,KAAK,GAAG;YAC5B,gBAAgB;QAClB;QACA,IAAI,WAAW,KAAK,GAAG;YACrB,SAAS;QACX;QACA,MAAM,aAAa,SAAS,SAAS,OAAO,EAAE,SAAS,eAAe,OAAO;QAC7E,IAAI,cAAc,CAAC,WAAW,OAAO,EAAE;YACrC,kBAAkB;YAClB,WAAW,OAAO,GAAG,OAAO,UAAU,CAAC,IAAM,aAAa,OAAO,OAAO,SAAS;QACnF,OAAO,IAAI,eAAe;YACxB,kBAAkB;YAClB,aAAa,OAAO,OAAO;QAC7B;IACF,GAAG;QAAC;QAAU;KAAa;IAC3B,MAAM,0BAA0B,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE;QAC7C,mBAAmB,OAAO;QAC1B,WAAW,OAAO,GAAG;IACvB;IACA,MAAM,qBAAqB,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE;QACxC,IAAI,kCAAkC,OAAO,EAAE;YAC7C,MAAM,OAAO,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE,SAAS,QAAQ,EAAE,IAAI;YAClD,KAAK,KAAK,CAAC,aAAa,GAAG;YAC3B,KAAK,eAAe,CAAC;YACrB,kCAAkC,OAAO,GAAG;QAC9C;IACF;IACA,MAAM,uBAAuB,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE;QAC1C,OAAO,QAAQ,OAAO,CAAC,SAAS,GAAG;YAAC;YAAS;SAAY,CAAC,QAAQ,CAAC,QAAQ,OAAO,CAAC,SAAS,CAAC,IAAI,IAAI;IACvG;IAEA,2EAA2E;IAC3E,8EAA8E;IAC9E,8EAA8E;IAC9E,6WAAM,SAAS,CAAC;QACd,IAAI,CAAC,SAAS;QACd,SAAS,sBAAsB,KAAK;YAClC,kBAAkB;YAClB,kBAAkB,OAAO,GAAG;YAC5B,IAAI,aAAa,CAAC,CAAA,GAAA,2UAAA,CAAA,yBAAsB,AAAD,EAAE,eAAe,OAAO,KAAK,UAAU,UAAU,OAAO,IAAI,KAAK,CAAC,SAAS,SAAS,OAAO,EAAE,SAAS;gBAC3I;YACF;YACA,MAAM,YAAY,SAAS,SAAS,OAAO,EAAE,QAAQ,eAAe,OAAO;YAC3E,IAAI,WAAW;gBACb,WAAW,OAAO,GAAG,OAAO,UAAU,CAAC;oBACrC,IAAI,CAAC,QAAQ,OAAO,EAAE;wBACpB,aAAa,MAAM,OAAO;oBAC5B;gBACF,GAAG;YACL,OAAO,IAAI,CAAC,MAAM;gBAChB,aAAa,MAAM,OAAO;YAC5B;QACF;QACA,SAAS,sBAAsB,KAAK;YAClC,IAAI,wBAAwB;gBAC1B;gBACA;YACF;YACA,mBAAmB,OAAO;YAC1B,MAAM,MAAM,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE,SAAS,QAAQ;YAC3C,kBAAkB;YAClB,sBAAsB,OAAO,GAAG;YAChC,IAAI,eAAe,OAAO,IAAI,QAAQ,OAAO,CAAC,eAAe,EAAE;gBAC7D,iDAAiD;gBACjD,IAAI,CAAC,MAAM;oBACT,kBAAkB;gBACpB;gBACA,WAAW,OAAO,GAAG,eAAe,OAAO,CAAC;oBAC1C,GAAG,QAAQ,OAAO,CAAC,eAAe;oBAClC;oBACA,GAAG,MAAM,OAAO;oBAChB,GAAG,MAAM,OAAO;oBAChB;wBACE;wBACA;wBACA,IAAI,CAAC,wBAAwB;4BAC3B,eAAe,OAAO,MAAM;wBAC9B;oBACF;gBACF;gBACA,MAAM,UAAU,WAAW,OAAO;gBAClC,IAAI,gBAAgB,CAAC,aAAa;gBAClC,mBAAmB,OAAO,GAAG;oBAC3B,IAAI,mBAAmB,CAAC,aAAa;gBACvC;gBACA;YACF;YAEA,qEAAqE;YACrE,oEAAoE;YACpE,gBAAgB;YAChB,MAAM,cAAc,eAAe,OAAO,KAAK,UAAU,CAAC,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,SAAS,QAAQ,EAAE,MAAM,aAAa,IAAI;YAC/G,IAAI,aAAa;gBACf,eAAe;YACjB;QACF;QAEA,yEAAyE;QACzE,gBAAgB;QAChB,8DAA8D;QAC9D,SAAS,mBAAmB,KAAK;YAC/B,IAAI,wBAAwB;YAC5B,IAAI,CAAC,QAAQ,OAAO,CAAC,eAAe,EAAE;YACtC,eAAe,OAAO,IAAI,QAAQ,eAAe,OAAO,CAAC;gBACvD,GAAG,QAAQ,OAAO,CAAC,eAAe;gBAClC;gBACA,GAAG,MAAM,OAAO;gBAChB,GAAG,MAAM,OAAO;gBAChB;oBACE;oBACA;oBACA,IAAI,CAAC,wBAAwB;wBAC3B,eAAe;oBACjB;gBACF;YACF,GAAG;QACL;QACA,SAAS;YACP,kBAAkB;QACpB;QACA,SAAS,qBAAqB,KAAK;YACjC,IAAI,CAAC,wBAAwB;gBAC3B,eAAe,OAAO;YACxB;QACF;QACA,IAAI,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,SAAS,YAAY,GAAG;YACpC,MAAM,YAAY,SAAS,YAAY;YACvC,MAAM,WAAW,SAAS,QAAQ;YAClC,IAAI,MAAM;gBACR,UAAU,gBAAgB,CAAC,cAAc;YAC3C;YACA,IAAI,MAAM;gBACR,UAAU,gBAAgB,CAAC,aAAa,uBAAuB;oBAC7D,MAAM;gBACR;YACF;YACA,UAAU,gBAAgB,CAAC,cAAc;YACzC,UAAU,gBAAgB,CAAC,cAAc;YACzC,IAAI,UAAU;gBACZ,SAAS,gBAAgB,CAAC,cAAc;gBACxC,SAAS,gBAAgB,CAAC,cAAc;gBACxC,SAAS,gBAAgB,CAAC,cAAc;YAC1C;YACA,OAAO;gBACL,IAAI,MAAM;oBACR,UAAU,mBAAmB,CAAC,cAAc;gBAC9C;gBACA,IAAI,MAAM;oBACR,UAAU,mBAAmB,CAAC,aAAa;gBAC7C;gBACA,UAAU,mBAAmB,CAAC,cAAc;gBAC5C,UAAU,mBAAmB,CAAC,cAAc;gBAC5C,IAAI,UAAU;oBACZ,SAAS,mBAAmB,CAAC,cAAc;oBAC3C,SAAS,mBAAmB,CAAC,cAAc;oBAC3C,SAAS,mBAAmB,CAAC,cAAc;gBAC7C;YACF;QACF;IACF,GAAG;QAAC;QAAU;QAAS;QAAS;QAAW;QAAM;QAAgB;QAAyB;QAAoB;QAAc;QAAM;QAAS;QAAM;QAAU;QAAgB;QAAS;QAAsB;KAAU;IAEpN,8EAA8E;IAC9E,2EAA2E;IAC3E,oCAAoC;IACpC,yDAAyD;IACzD,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI;QACJ,IAAI,CAAC,SAAS;QACd,IAAI,QAAQ,CAAC,wBAAwB,eAAe,OAAO,KAAK,QAAQ,CAAC,wBAAwB,sBAAsB,SAAS,KAAK,QAAQ,sBAAsB,kBAAkB,IAAI,eAAe;YACtM,kCAAkC,OAAO,GAAG;YAC5C,MAAM,aAAa,SAAS,QAAQ;YACpC,IAAI,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,SAAS,YAAY,KAAK,YAAY;gBAClD,IAAI;gBACJ,MAAM,OAAO,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE,SAAS,QAAQ,EAAE,IAAI;gBAClD,KAAK,YAAY,CAAC,uBAAuB;gBACzC,MAAM,MAAM,SAAS,YAAY;gBACjC,MAAM,iBAAiB,QAAQ,QAAQ,CAAC,wBAAwB,KAAK,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,KAAK,QAAQ,CAAC,wBAAwB,sBAAsB,OAAO,KAAK,OAAO,KAAK,IAAI,sBAAsB,QAAQ,CAAC,QAAQ;gBACvP,IAAI,gBAAgB;oBAClB,eAAe,KAAK,CAAC,aAAa,GAAG;gBACvC;gBACA,KAAK,KAAK,CAAC,aAAa,GAAG;gBAC3B,IAAI,KAAK,CAAC,aAAa,GAAG;gBAC1B,WAAW,KAAK,CAAC,aAAa,GAAG;gBACjC,OAAO;oBACL,KAAK,KAAK,CAAC,aAAa,GAAG;oBAC3B,IAAI,KAAK,CAAC,aAAa,GAAG;oBAC1B,WAAW,KAAK,CAAC,aAAa,GAAG;gBACnC;YACF;QACF;IACF,GAAG;QAAC;QAAS;QAAM;QAAU;QAAU;QAAM;QAAgB;KAAY;IACzE,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,CAAC,MAAM;YACT,eAAe,OAAO,GAAG;YACzB,sBAAsB,OAAO,GAAG;YAChC;YACA;QACF;IACF,GAAG;QAAC;QAAM;QAAyB;KAAmB;IACtD,6WAAM,SAAS,CAAC;QACd,OAAO;YACL;YACA,kBAAkB;YAClB,kBAAkB;YAClB;QACF;IACF,GAAG;QAAC;QAAS,SAAS,YAAY;QAAE;QAAyB;KAAmB;IAChF,MAAM,YAAY,6WAAM,OAAO,CAAC;QAC9B,SAAS,cAAc,KAAK;YAC1B,eAAe,OAAO,GAAG,MAAM,WAAW;QAC5C;QACA,OAAO;YACL,eAAe;YACf,gBAAgB;YAChB,aAAY,KAAK;gBACf,MAAM,EACJ,WAAW,EACZ,GAAG;gBACJ,SAAS;oBACP,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE;wBAClD,aAAa,MAAM,aAAa;oBAClC;gBACF;gBACA,IAAI,aAAa,CAAC,CAAA,GAAA,2UAAA,CAAA,yBAAsB,AAAD,EAAE,eAAe,OAAO,GAAG;oBAChE;gBACF;gBACA,IAAI,QAAQ,UAAU,UAAU,OAAO,MAAM,GAAG;oBAC9C;gBACF;gBAEA,yDAAyD;gBACzD,IAAI,sBAAsB,OAAO,IAAI,MAAM,SAAS,IAAI,IAAI,MAAM,SAAS,IAAI,IAAI,GAAG;oBACpF;gBACF;gBACA,kBAAkB;gBAClB,IAAI,eAAe,OAAO,KAAK,SAAS;oBACtC;gBACF,OAAO;oBACL,sBAAsB,OAAO,GAAG;oBAChC,eAAe,OAAO,GAAG,OAAO,UAAU,CAAC,iBAAiB,UAAU,UAAU,OAAO;gBACzF;YACF;QACF;IACF,GAAG;QAAC;QAAW;QAAc;QAAM;QAAS;KAAU;IACtD,OAAO,6WAAM,OAAO,CAAC,IAAM,UAAU;YACnC;QACF,IAAI,CAAC,GAAG;QAAC;QAAS;KAAU;AAC9B;AAEA,MAAM,OAAO,KAAO;AACpB,MAAM,4BAA4B,WAAW,GAAE,6WAAM,aAAa,CAAC;IACjE,OAAO;IACP,cAAc;IACd,WAAW;IACX,WAAW;IACX,cAAc;IACd,UAAU;IACV,gBAAgB;AAClB;AAEA;;;CAGC,GACD,MAAM,uBAAuB,IAAM,6WAAM,UAAU,CAAC;AACpD;;;;CAIC,GACD,SAAS,mBAAmB,KAAK;IAC/B,MAAM,EACJ,QAAQ,EACR,KAAK,EACL,YAAY,CAAC,EACd,GAAG;IACJ,MAAM,CAAC,OAAO,SAAS,GAAG,6WAAM,UAAU,CAAC,CAAC,MAAM,OAAS,CAAC;YAC1D,GAAG,IAAI;YACP,GAAG,IAAI;QACT,CAAC,GAAG;QACF;QACA;QACA,cAAc;QACd,WAAW;QACX,gBAAgB;IAClB;IACA,MAAM,sBAAsB,6WAAM,MAAM,CAAC;IACzC,MAAM,eAAe,6WAAM,WAAW,CAAC,CAAA;QACrC,SAAS;YACP;QACF;IACF,GAAG,EAAE;IACL,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,MAAM,SAAS,EAAE;YACnB,IAAI,oBAAoB,OAAO,KAAK,MAAM;gBACxC,oBAAoB,OAAO,GAAG,MAAM,SAAS;YAC/C,OAAO,IAAI,CAAC,MAAM,cAAc,EAAE;gBAChC,SAAS;oBACP,gBAAgB;gBAClB;YACF;QACF,OAAO;YACL,IAAI,MAAM,cAAc,EAAE;gBACxB,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,oBAAoB,OAAO,GAAG;QAChC;IACF,GAAG;QAAC,MAAM,SAAS;QAAE,MAAM,cAAc;KAAC;IAC1C,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,0BAA0B,QAAQ,EAAE;QAC1D,OAAO,6WAAM,OAAO,CAAC,IAAM,CAAC;gBAC1B,GAAG,KAAK;gBACR;gBACA;YACF,CAAC,GAAG;YAAC;YAAO;SAAa;QACzB,UAAU;IACZ;AACF;AACA;;;;CAIC,GACD,SAAS,cAAc,OAAO,EAAE,OAAO;IACrC,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,UAAU,EACX,GAAG;IACJ,MAAM,EACJ,IAAI,QAAQ,EACZ,UAAU,IAAI,EACf,GAAG;IACJ,MAAM,KAAK,YAAY,OAAO,WAAW;IACzC,MAAM,eAAe;IACrB,MAAM,EACJ,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,QAAQ,EACR,SAAS,EACV,GAAG;IACJ,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,WAAW;QAChB,SAAS;YACP,OAAO;gBACL,MAAM;gBACN,OAAO,SAAS,cAAc;YAChC;QACF;QACA,IAAI,cAAc,IAAI;YACpB,aAAa;QACf;IACF,GAAG;QAAC;QAAS;QAAI;QAAc;QAAU;QAAW;KAAa;IACjE,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,SAAS;YACP,aAAa;YACb,SAAS;gBACP,OAAO;gBACP,WAAW;YACb;QACF;QACA,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,WAAW;QAChB,IAAI,CAAC,QAAQ,cAAc,IAAI;YAC7B,IAAI,WAAW;gBACb,MAAM,UAAU,OAAO,UAAU,CAAC,OAAO;gBACzC,OAAO;oBACL,aAAa;gBACf;YACF;YACA;QACF;IACF,GAAG;QAAC;QAAS;QAAM;QAAU;QAAW;QAAI;QAAc;QAAc;KAAU;IAClF,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,CAAC,SAAS;QACd,IAAI,iBAAiB,QAAQ,CAAC,MAAM;QACpC,aAAa;IACf,GAAG;QAAC;QAAS;QAAM;QAAc;KAAG;IACpC,OAAO;AACT;AAEA,MAAM,gCAAgC,WAAW,GAAE,6WAAM,aAAa,CAAC;IACrE,aAAa;IACb,WAAW;IACX,UAAU;QACR,SAAS;IACX;IACA,iBAAiB;QACf,SAAS;IACX;IACA,cAAc;QACZ,SAAS,CAAC;IACZ;IACA,cAAc;QACZ,SAAS;IACX;IACA,mBAAmB;QACjB,SAAS;IACX;AACF;AACA;;;;;;;;CAQC,GACD,SAAS,uBAAuB,KAAK;IACnC,MAAM,EACJ,QAAQ,EACR,KAAK,EACL,YAAY,CAAC,EACd,GAAG;IACJ,MAAM,WAAW,6WAAM,MAAM,CAAC;IAC9B,MAAM,kBAAkB,6WAAM,MAAM,CAAC;IACrC,MAAM,eAAe,6WAAM,MAAM,CAAC;IAClC,MAAM,oBAAoB,6WAAM,MAAM,CAAC;IACvC,MAAM,eAAe,6WAAM,MAAM,CAAC,CAAC;IACnC,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,8BAA8B,QAAQ,EAAE;QAC9D,OAAO,6WAAM,OAAO,CAAC,IAAM,CAAC;gBAC1B,aAAa;gBACb;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC,GAAG;YAAC;SAAU;QACf,UAAU;IACZ;AACF;AACA;;;;CAIC,GACD,SAAS,kBAAkB,OAAO,EAAE,OAAO;IACzC,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,UAAU,EACX,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACf,GAAG;IACJ,MAAM,eAAe,6WAAM,UAAU,CAAC;IACtC,MAAM,EACJ,YAAY,EACZ,QAAQ,EACR,SAAS,EACT,eAAe,EACf,iBAAiB,EACjB,WAAW,EACX,YAAY,EACb,GAAG;IACJ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,6WAAM,QAAQ,CAAC;IAC3D,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,SAAS;YACP,IAAI;YACJ,kBAAkB;YAClB,CAAC,wBAAwB,kBAAkB,OAAO,KAAK,QAAQ,sBAAsB,iBAAiB,CAAC;YACvG,aAAa,OAAO,GAAG;YACvB,kBAAkB,OAAO,GAAG;YAC5B,SAAS,OAAO,GAAG,gBAAgB,OAAO;QAC5C;QACA,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,aAAa,OAAO,EAAE;QAC3B,IAAI,CAAC,QAAQ,aAAa,OAAO,KAAK,YAAY;YAChD,kBAAkB;YAClB,IAAI,WAAW;gBACb,aAAa,OAAO,GAAG,OAAO,UAAU,CAAC,OAAO;gBAChD,OAAO;oBACL,aAAa,aAAa,OAAO;gBACnC;YACF;YACA;QACF;IACF,GAAG;QAAC;QAAS;QAAM;QAAY;QAAc;QAAU;QAAW;QAAiB;QAAmB;KAAa;IACnH,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,MAAM;QACX,MAAM,cAAc,kBAAkB,OAAO;QAC7C,MAAM,SAAS,aAAa,OAAO;QACnC,kBAAkB,OAAO,GAAG;YAC1B;YACA;QACF;QACA,aAAa,OAAO,GAAG;QACvB,SAAS,OAAO,GAAG;YACjB,MAAM;YACN,OAAO,SAAS,gBAAgB,OAAO,EAAE;QAC3C;QACA,IAAI,WAAW,QAAQ,WAAW,YAAY;YAC5C,kBAAkB;YAClB,kBAAkB;YAClB,eAAe,QAAQ,YAAY,iBAAiB,CAAC;YACrD,eAAe,QAAQ,YAAY,YAAY,CAAC;QAClD,OAAO;YACL,kBAAkB;YAClB,eAAe,QAAQ,YAAY,iBAAiB,CAAC;QACvD;IACF,GAAG;QAAC;QAAS;QAAM;QAAY;QAAc;QAAc;QAAU;QAAW;QAAiB;QAAmB;KAAa;IACjI,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,OAAO;YACL,kBAAkB,OAAO,GAAG;QAC9B;IACF,GAAG;QAAC;KAAkB;IACtB,OAAO,6WAAM,OAAO,CAAC,IAAM,CAAC;YAC1B;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAa;QAAU;KAAe;AAC7C;AAEA,IAAI,QAAQ;AACZ,SAAS,aAAa,EAAE,EAAE,OAAO;IAC/B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,gBAAgB,KAAK,EACrB,iBAAiB,IAAI,EACrB,OAAO,KAAK,EACb,GAAG;IACJ,kBAAkB,qBAAqB;IACvC,MAAM,OAAO,IAAM,MAAM,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC;YAChD;QACF;IACA,IAAI,MAAM;QACR;IACF,OAAO;QACL,QAAQ,sBAAsB;IAChC;AACF;AAEA,SAAS,SAAS,MAAM,EAAE,KAAK;IAC7B,IAAI,CAAC,UAAU,CAAC,OAAO;QACrB,OAAO;IACT;IACA,MAAM,WAAW,MAAM,WAAW,IAAI,OAAO,KAAK,IAAI,MAAM,WAAW;IAEvE,2CAA2C;IAC3C,IAAI,OAAO,QAAQ,CAAC,QAAQ;QAC1B,OAAO;IACT;IAEA,iEAAiE;IACjE,IAAI,YAAY,CAAA,GAAA,2PAAA,CAAA,eAAY,AAAD,EAAE,WAAW;QACtC,IAAI,OAAO;QACX,MAAO,KAAM;YACX,IAAI,WAAW,MAAM;gBACnB,OAAO;YACT;YACA,aAAa;YACb,OAAO,KAAK,UAAU,IAAI,KAAK,IAAI;QACrC;IACF;IAEA,+BAA+B;IAC/B,OAAO;AACT;AACA,SAAS,UAAU,KAAK;IACtB,IAAI,kBAAkB,OAAO;QAC3B,OAAO,MAAM,YAAY,EAAE,CAAC,EAAE;IAChC;IAEA,wEAAwE;IACxE,2DAA2D;IAC3D,OAAO,MAAM,MAAM;AACrB;AACA,SAAS,YAAY,IAAI;IACvB,OAAO,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,aAAa,KAAK;AACzD;AAEA,qDAAqD;AACrD,sGAAsG;AACtG,MAAM,WAAW;IACf,OAAO,WAAW,GAAE,IAAI;IACxB,eAAe,WAAW,GAAE,IAAI;IAChC,MAAM,WAAW,GAAE,IAAI;AACzB;AACA,SAAS,cAAc,OAAO;IAC5B,IAAI,YAAY,SAAS,OAAO,SAAS,KAAK;IAC9C,IAAI,YAAY,eAAe,OAAO,QAAQ,CAAC,cAAc;IAC7D,OAAO,SAAS,IAAI;AACtB;AACA,IAAI,0BAA0B,WAAW,GAAE,IAAI;AAC/C,IAAI,YAAY,CAAC;AACjB,IAAI,cAAc;AAClB,MAAM,gBAAgB,IAAM,OAAO,gBAAgB,eAAe,WAAW,YAAY,SAAS;AAClG,MAAM,aAAa,CAAA,OAAQ,QAAQ,CAAC,KAAK,IAAI,IAAI,WAAW,KAAK,UAAU,CAAC;AAC5E,MAAM,kBAAkB,CAAC,QAAQ,UAAY,QAAQ,GAAG,CAAC,CAAA;QACvD,IAAI,OAAO,QAAQ,CAAC,SAAS;YAC3B,OAAO;QACT;QACA,MAAM,kBAAkB,WAAW;QACnC,IAAI,OAAO,QAAQ,CAAC,kBAAkB;YACpC,OAAO;QACT;QACA,OAAO;IACT,GAAG,MAAM,CAAC,CAAA,IAAK,KAAK;AACpB,SAAS,uBAAuB,wBAAwB,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK;IAC/E,MAAM,aAAa;IACnB,MAAM,mBAAmB,QAAQ,UAAU,aAAa,gBAAgB;IACxE,MAAM,gBAAgB,gBAAgB,MAAM;IAC5C,MAAM,iBAAiB,IAAI;IAC3B,MAAM,iBAAiB,IAAI,IAAI;IAC/B,MAAM,iBAAiB,EAAE;IACzB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;QAC1B,SAAS,CAAC,WAAW,GAAG,IAAI;IAC9B;IACA,MAAM,gBAAgB,SAAS,CAAC,WAAW;IAC3C,cAAc,OAAO,CAAC;IACtB,KAAK;IACL,eAAe,KAAK;IACpB,SAAS,KAAK,EAAE;QACd,IAAI,CAAC,MAAM,eAAe,GAAG,CAAC,KAAK;YACjC;QACF;QACA,eAAe,GAAG,CAAC;QACnB,GAAG,UAAU,IAAI,KAAK,GAAG,UAAU;IACrC;IACA,SAAS,KAAK,MAAM;QAClB,IAAI,CAAC,UAAU,eAAe,GAAG,CAAC,SAAS;YACzC;QACF;QACA,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,QAAQ,EAAE,CAAA;YAC/B,IAAI,CAAA,GAAA,2PAAA,CAAA,cAAW,AAAD,EAAE,UAAU,UAAU;YACpC,IAAI,eAAe,GAAG,CAAC,OAAO;gBAC5B,KAAK;YACP,OAAO;gBACL,MAAM,OAAO,mBAAmB,KAAK,YAAY,CAAC,oBAAoB;gBACtE,MAAM,gBAAgB,SAAS,QAAQ,SAAS;gBAChD,MAAM,aAAa,cAAc;gBACjC,MAAM,eAAe,CAAC,WAAW,GAAG,CAAC,SAAS,CAAC,IAAI;gBACnD,MAAM,cAAc,CAAC,cAAc,GAAG,CAAC,SAAS,CAAC,IAAI;gBACrD,WAAW,GAAG,CAAC,MAAM;gBACrB,cAAc,GAAG,CAAC,MAAM;gBACxB,eAAe,IAAI,CAAC;gBACpB,IAAI,iBAAiB,KAAK,eAAe;oBACvC,wBAAwB,GAAG,CAAC;gBAC9B;gBACA,IAAI,gBAAgB,GAAG;oBACrB,KAAK,YAAY,CAAC,YAAY;gBAChC;gBACA,IAAI,CAAC,iBAAiB,kBAAkB;oBACtC,KAAK,YAAY,CAAC,kBAAkB,qBAAqB,UAAU,KAAK;gBAC1E;YACF;QACF;IACF;IACA;IACA,OAAO;QACL,eAAe,OAAO,CAAC,CAAA;YACrB,MAAM,aAAa,cAAc;YACjC,MAAM,sBAAsB,WAAW,GAAG,CAAC,YAAY;YACvD,MAAM,eAAe,sBAAsB;YAC3C,MAAM,cAAc,CAAC,cAAc,GAAG,CAAC,YAAY,CAAC,IAAI;YACxD,WAAW,GAAG,CAAC,SAAS;YACxB,cAAc,GAAG,CAAC,SAAS;YAC3B,IAAI,CAAC,cAAc;gBACjB,IAAI,CAAC,wBAAwB,GAAG,CAAC,YAAY,kBAAkB;oBAC7D,QAAQ,eAAe,CAAC;gBAC1B;gBACA,wBAAwB,MAAM,CAAC;YACjC;YACA,IAAI,CAAC,aAAa;gBAChB,QAAQ,eAAe,CAAC;YAC1B;QACF;QACA;QACA,IAAI,CAAC,aAAa;YAChB,SAAS,KAAK,GAAG,IAAI;YACrB,QAAQ,CAAC,cAAc,GAAG,IAAI;YAC9B,SAAS,IAAI,GAAG,IAAI;YACpB,0BAA0B,IAAI;YAC9B,YAAY,CAAC;QACf;IACF;AACF;AACA,SAAS,WAAW,aAAa,EAAE,UAAU,EAAE,KAAK;IAClD,IAAI,eAAe,KAAK,GAAG;QACzB,aAAa;IACf;IACA,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ;IACV;IACA,MAAM,OAAO,YAAY,aAAa,CAAC,EAAE,EAAE,IAAI;IAC/C,OAAO,uBAAuB,cAAc,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,gBAAgB,CAAC,yCAAyC,MAAM,YAAY;AACjJ;AAEA,MAAM,gBAAgB;IACpB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,SAAS;IACT,UAAU;IACV,YAAY;IACZ,OAAO;IACP,KAAK;IACL,MAAM;AACR;AACA,MAAM,aAAa,WAAW,GAAE,6WAAM,UAAU,CAAC,SAAS,WAAW,KAAK,EAAE,GAAG;IAC7E,MAAM,CAAC,MAAM,QAAQ,GAAG,6WAAM,QAAQ;IACtC,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,CAAA,GAAA,2UAAA,CAAA,WAAQ,AAAD,KAAK;YACd,wEAAwE;YACxE,uEAAuE;YACvE,mEAAmE;YACnE,uEAAuE;YACvE,eAAe;YACf,QAAQ;QACV;IACF,GAAG,EAAE;IACL,MAAM,YAAY;QAChB;QACA,UAAU;QACV,6BAA6B;QAC7B;QACA,eAAe,OAAO,YAAY;QAClC,CAAC,gBAAgB,eAAe,EAAE;QAClC,OAAO;IACT;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;QAC9B,GAAG,KAAK;QACR,GAAG,SAAS;IACd;AACF;AAEA,MAAM,gBAAgB,WAAW,GAAE,6WAAM,aAAa,CAAC;AACvD,MAAM,OAAO,WAAW,GAAE,gBAAgB;AAC1C;;CAEC,GACD,SAAS,sBAAsB,KAAK;IAClC,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,EAAE,EACF,IAAI,EACL,GAAG;IACJ,MAAM,WAAW;IACjB,MAAM,gBAAgB;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,6WAAM,QAAQ,CAAC;IACnD,MAAM,gBAAgB,6WAAM,MAAM,CAAC;IACnC,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,OAAO;YACL,cAAc,QAAQ,WAAW,MAAM;YACvC,uEAAuE;YACvE,uDAAuD;YACvD,yDAAyD;YACzD,eAAe;gBACb,cAAc,OAAO,GAAG;YAC1B;QACF;IACF,GAAG;QAAC;KAAW;IACf,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,2EAA2E;QAC3E,mEAAmE;QACnE,yDAAyD;QACzD,IAAI,CAAC,UAAU;QACf,IAAI,cAAc,OAAO,EAAE;QAC3B,MAAM,iBAAiB,KAAK,SAAS,cAAc,CAAC,MAAM;QAC1D,IAAI,CAAC,gBAAgB;QACrB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,EAAE,GAAG;QACb,QAAQ,YAAY,CAAC,MAAM;QAC3B,eAAe,WAAW,CAAC;QAC3B,cAAc,OAAO,GAAG;QACxB,cAAc;IAChB,GAAG;QAAC;QAAI;KAAS;IACjB,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,4EAA4E;QAC5E,8DAA8D;QAC9D,IAAI,SAAS,MAAM;QACnB,IAAI,CAAC,UAAU;QACf,IAAI,cAAc,OAAO,EAAE;QAC3B,IAAI,YAAY,QAAQ,CAAC,iBAAiB,OAAO,KAAK,IAAI,cAAc,UAAU;QAClF,IAAI,aAAa,CAAC,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,YAAY,YAAY,UAAU,OAAO;QACrE,YAAY,aAAa,SAAS,IAAI;QACtC,IAAI,YAAY;QAChB,IAAI,IAAI;YACN,YAAY,SAAS,aAAa,CAAC;YACnC,UAAU,EAAE,GAAG;YACf,UAAU,WAAW,CAAC;QACxB;QACA,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,EAAE,GAAG;QACb,QAAQ,YAAY,CAAC,MAAM;QAC3B,YAAY,aAAa;QACzB,UAAU,WAAW,CAAC;QACtB,cAAc,OAAO,GAAG;QACxB,cAAc;IAChB,GAAG;QAAC;QAAI;QAAM;QAAU;KAAc;IACtC,OAAO;AACT;AACA;;;;;;;CAOC,GACD,SAAS,eAAe,KAAK;IAC3B,MAAM,EACJ,QAAQ,EACR,EAAE,EACF,IAAI,EACJ,mBAAmB,IAAI,EACxB,GAAG;IACJ,MAAM,aAAa,sBAAsB;QACvC;QACA;IACF;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,6WAAM,QAAQ,CAAC;IACjE,MAAM,mBAAmB,6WAAM,MAAM,CAAC;IACtC,MAAM,kBAAkB,6WAAM,MAAM,CAAC;IACrC,MAAM,kBAAkB,6WAAM,MAAM,CAAC;IACrC,MAAM,iBAAiB,6WAAM,MAAM,CAAC;IACpC,MAAM,QAAQ,qBAAqB,OAAO,KAAK,IAAI,kBAAkB,KAAK;IAC1E,MAAM,OAAO,qBAAqB,OAAO,KAAK,IAAI,kBAAkB,IAAI;IACxE,MAAM,qBACN,sEAAsE;IACtE,YAAY;IACZ,CAAC,CAAC,qBACF,kDAAkD;IAClD,CAAC,kBAAkB,KAAK,IACxB,4CAA4C;IAC5C,kBAAkB,IAAI,IAAI,oBAAoB,CAAC,CAAC,CAAC,QAAQ,UAAU;IAEnE,8EAA8E;IAC9E,6WAAM,SAAS,CAAC;QACd,IAAI,CAAC,cAAc,CAAC,oBAAoB,OAAO;YAC7C;QACF;QAEA,0EAA0E;QAC1E,uEAAuE;QACvE,sCAAsC;QACtC,SAAS,QAAQ,KAAK;YACpB,IAAI,cAAc,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;gBACvC,MAAM,WAAW,MAAM,IAAI,KAAK;gBAChC,MAAM,cAAc,WAAW,2UAAA,CAAA,oBAAiB,GAAG,2UAAA,CAAA,qBAAkB;gBACrE,YAAY;YACd;QACF;QACA,wEAAwE;QACxE,wCAAwC;QACxC,WAAW,gBAAgB,CAAC,WAAW,SAAS;QAChD,WAAW,gBAAgB,CAAC,YAAY,SAAS;QACjD,OAAO;YACL,WAAW,mBAAmB,CAAC,WAAW,SAAS;YACnD,WAAW,mBAAmB,CAAC,YAAY,SAAS;QACtD;IACF,GAAG;QAAC;QAAY;QAAkB;KAAM;IACxC,6WAAM,SAAS,CAAC;QACd,IAAI,CAAC,YAAY;QACjB,IAAI,MAAM;QACV,CAAA,GAAA,2UAAA,CAAA,oBAAiB,AAAD,EAAE;IACpB,GAAG;QAAC;QAAM;KAAW;IACrB,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,OAAI,AAAD,EAAE,cAAc,QAAQ,EAAE;QAC/C,OAAO,6WAAM,OAAO,CAAC,IAAM,CAAC;gBAC1B;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC,GAAG;YAAC;YAAkB;SAAW;QAClC,UAAU;YAAC,sBAAsB,cAAc,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,YAAY;gBAC1E,aAAa;gBACb,KAAK;gBACL,SAAS,CAAA;oBACP,IAAI,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,aAAa;wBACrC,IAAI;wBACJ,CAAC,wBAAwB,gBAAgB,OAAO,KAAK,QAAQ,sBAAsB,KAAK;oBAC1F,OAAO;wBACL,MAAM,eAAe,oBAAoB,kBAAkB,YAAY,GAAG;wBAC1E,MAAM,eAAe,CAAA,GAAA,2UAAA,CAAA,sBAAmB,AAAD,EAAE;wBACzC,gBAAgB,QAAQ,aAAa,KAAK;oBAC5C;gBACF;YACF;YAAI,sBAAsB,cAAc,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAC/D,aAAa,WAAW,EAAE;gBAC1B,OAAO;YACT;YAAI,cAAc,WAAW,GAAE,CAAA,GAAA,mXAAA,CAAA,eAAqB,AAAD,EAAE,UAAU;YAAa,sBAAsB,cAAc,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,YAAY;gBAC3I,aAAa;gBACb,KAAK;gBACL,SAAS,CAAA;oBACP,IAAI,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,aAAa;wBACrC,IAAI;wBACJ,CAAC,wBAAwB,eAAe,OAAO,KAAK,QAAQ,sBAAsB,KAAK;oBACzF,OAAO;wBACL,MAAM,eAAe,oBAAoB,kBAAkB,YAAY,GAAG;wBAC1E,MAAM,eAAe,CAAA,GAAA,2UAAA,CAAA,kBAAe,AAAD,EAAE;wBACrC,gBAAgB,QAAQ,aAAa,KAAK;wBAC1C,CAAC,qBAAqB,OAAO,KAAK,IAAI,kBAAkB,eAAe,KAAK,CAAC,qBAAqB,OAAO,KAAK,IAAI,kBAAkB,YAAY,CAAC,OAAO,MAAM,WAAW,EAAE,YAAY;oBACzL;gBACF;YACF;SAAG;IACL;AACF;AACA,MAAM,mBAAmB,IAAM,6WAAM,UAAU,CAAC;AAEhD,SAAS,iBAAiB,IAAI;IAC5B,OAAO,6WAAM,OAAO,CAAC;QACnB,OAAO,CAAA;YACL,KAAK,OAAO,CAAC,CAAA;gBACX,IAAI,KAAK;oBACP,IAAI,OAAO,GAAG;gBAChB;YACF;QACF;IACA,uDAAuD;IACzD,GAAG;AACL;AAEA,MAAM,aAAa;AACnB,IAAI,4BAA4B,EAAE;AAClC,SAAS,4BAA4B,OAAO;IAC1C,4BAA4B,0BAA0B,MAAM,CAAC,CAAA,KAAM,GAAG,WAAW;IACjF,IAAI,WAAW,CAAA,GAAA,2PAAA,CAAA,cAAW,AAAD,EAAE,aAAa,QAAQ;QAC9C,0BAA0B,IAAI,CAAC;QAC/B,IAAI,0BAA0B,MAAM,GAAG,YAAY;YACjD,4BAA4B,0BAA0B,KAAK,CAAC,CAAC;QAC/D;IACF;AACF;AACA,SAAS;IACP,OAAO,0BAA0B,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,CAAA,KAAM,GAAG,WAAW;AAC9E;AACA,SAAS,wBAAwB,SAAS;IACxC,MAAM,kBAAkB,CAAA,GAAA,2UAAA,CAAA,qBAAkB,AAAD;IACzC,IAAI,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,WAAW,kBAAkB;QAC1C,OAAO;IACT;IACA,OAAO,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,gBAAgB,CAAC,EAAE,IAAI;AACpD;AACA,SAAS,eAAe,oBAAoB,EAAE,QAAQ;IACpD,IAAI;IACJ,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,wBAAwB,qBAAqB,YAAY,CAAC,OAAO,KAAK,QAAQ,sBAAsB,QAAQ,CAAC,SAAS,GAAG;QACxK;IACF;IACA,MAAM,UAAU,CAAA,GAAA,2UAAA,CAAA,qBAAkB,AAAD;IACjC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE,sBAAsB;IAC1D,MAAM,kBAAkB,kBAAkB,MAAM,CAAC,CAAA;QAC/C,MAAM,eAAe,QAAQ,YAAY,CAAC,oBAAoB;QAC9D,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,SAAS,YAAY,QAAQ,YAAY,CAAC,oBAAoB,CAAC,aAAa,UAAU,CAAC;IAC3G;IACA,MAAM,WAAW,qBAAqB,YAAY,CAAC;IACnD,IAAI,SAAS,OAAO,CAAC,QAAQ,CAAC,eAAe,gBAAgB,MAAM,KAAK,GAAG;QACzE,IAAI,aAAa,KAAK;YACpB,qBAAqB,YAAY,CAAC,YAAY;QAChD;IACF,OAAO,IAAI,aAAa,QAAQ,qBAAqB,YAAY,CAAC,oBAAoB,qBAAqB,YAAY,CAAC,qBAAqB,MAAM;QACjJ,qBAAqB,YAAY,CAAC,YAAY;QAC9C,qBAAqB,YAAY,CAAC,iBAAiB;IACrD;AACF;AACA,MAAM,wBAAwB,WAAW,GAAE,6WAAM,UAAU,CAAC,SAAS,sBAAsB,KAAK,EAAE,GAAG;IACnG,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,UAAU;QAChC,GAAG,KAAK;QACR,MAAM;QACN,KAAK;QACL,UAAU,CAAC;QACX,OAAO;IACT;AACF;AACA;;;CAGC,GACD,SAAS,qBAAqB,KAAK;IACjC,MAAM,EACJ,OAAO,EACP,QAAQ,EACR,WAAW,KAAK,EAChB,QAAQ;QAAC;KAAU,EACnB,QAAQ,UAAU,IAAI,EACtB,eAAe,CAAC,EAChB,cAAc,IAAI,EAClB,eAAe,KAAK,EACpB,QAAQ,IAAI,EACZ,wBAAwB,KAAK,EAC7B,kBAAkB,IAAI,EACtB,uBAAuB,KAAK,EAC5B,mBAAmB,qBAAqB,IAAM,EAAE,EACjD,GAAG;IACJ,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,MAAM,EACN,OAAO,EACP,UAAU,EACR,YAAY,EACZ,QAAQ,EACT,EACF,GAAG;IACJ,MAAM,YAAY,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE;QAC/B,IAAI;QACJ,OAAO,CAAC,wBAAwB,QAAQ,OAAO,CAAC,eAAe,KAAK,OAAO,KAAK,IAAI,sBAAsB,MAAM;IAClH;IACA,MAAM,oBAAoB,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE;IACzC,MAAM,qBAAqB,OAAO,iBAAiB,YAAY,eAAe;IAC9E,wEAAwE;IACxE,8EAA8E;IAC9E,0EAA0E;IAC1E,2EAA2E;IAC3E,SAAS;IACT,MAAM,8BAA8B,CAAA,GAAA,2UAAA,CAAA,qBAAkB,AAAD,EAAE,iBAAiB;IAExE,6EAA6E;IAC7E,MAAM,iBAAiB;IACvB,MAAM,SAAS,iBAAiB,UAAU;IAC1C,MAAM,WAAW,CAAC,UAAU,kBAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IAC9B,MAAM,kBAAkB,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IACrC,MAAM,iBAAiB,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IACpC,MAAM,OAAO;IACb,MAAM,gBAAgB;IACtB,MAAM,wBAAwB,6WAAM,MAAM,CAAC;IAC3C,MAAM,sBAAsB,6WAAM,MAAM,CAAC;IACzC,MAAM,wBAAwB,6WAAM,MAAM,CAAC;IAC3C,MAAM,mBAAmB,6WAAM,MAAM,CAAC;IACtC,MAAM,mBAAmB,6WAAM,MAAM,CAAC,CAAC;IACvC,MAAM,iBAAiB,iBAAiB;IACxC,MAAM,uBAAuB,CAAA,GAAA,2UAAA,CAAA,0BAAuB,AAAD,EAAE;IACrD,MAAM,qBAAqB,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,SAAU,SAAS;QAC3D,IAAI,cAAc,KAAK,GAAG;YACxB,YAAY;QACd;QACA,OAAO,YAAY,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,CAAA,GAAA,2UAAA,CAAA,qBAAkB,AAAD,OAAO,EAAE;IACnE;IACA,MAAM,sBAAsB,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,CAAA;QACzC,MAAM,UAAU,mBAAmB;QACnC,OAAO,SAAS,OAAO,CAAC,GAAG,CAAC,CAAA;YAC1B,IAAI,gBAAgB,SAAS,aAAa;gBACxC,OAAO;YACT;YACA,IAAI,wBAAwB,SAAS,YAAY;gBAC/C,OAAO;YACT;YACA,OAAO;QACT,GAAG,MAAM,CAAC,SAAS,IAAI;IACzB;IACA,6WAAM,SAAS,CAAC;QACd,IAAI,UAAU;QACd,IAAI,CAAC,OAAO;QACZ,SAAS,UAAU,KAAK;YACtB,IAAI,MAAM,GAAG,KAAK,OAAO;gBACvB,wEAAwE;gBACxE,IAAI,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,sBAAsB,CAAA,GAAA,2UAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE,2BAA2B,qBAAqB,MAAM,KAAK,KAAK,CAAC,6BAA6B;oBAC7J,CAAA,GAAA,2UAAA,CAAA,YAAS,AAAD,EAAE;gBACZ;gBACA,MAAM,MAAM;gBACZ,MAAM,SAAS,CAAA,GAAA,2UAAA,CAAA,YAAW,AAAD,EAAE;gBAC3B,IAAI,SAAS,OAAO,CAAC,EAAE,KAAK,eAAe,WAAW,cAAc;oBAClE,CAAA,GAAA,2UAAA,CAAA,YAAS,AAAD,EAAE;oBACV,IAAI,MAAM,QAAQ,EAAE;wBAClB,aAAa,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;oBAClC,OAAO;wBACL,aAAa,GAAG,CAAC,EAAE;oBACrB;gBACF;gBACA,IAAI,SAAS,OAAO,CAAC,EAAE,KAAK,cAAc,WAAW,wBAAwB,MAAM,QAAQ,EAAE;oBAC3F,CAAA,GAAA,2UAAA,CAAA,YAAS,AAAD,EAAE;oBACV,aAAa,GAAG,CAAC,EAAE;gBACrB;YACF;QACF;QACA,MAAM,MAAM,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE;QAC1B,IAAI,gBAAgB,CAAC,WAAW;QAChC,OAAO;YACL,IAAI,mBAAmB,CAAC,WAAW;QACrC;IACF,GAAG;QAAC;QAAU;QAAc;QAAsB;QAAO;QAAU;QAA6B;QAAoB;KAAoB;IACxI,6WAAM,SAAS,CAAC;QACd,IAAI,UAAU;QACd,IAAI,CAAC,UAAU;QACf,SAAS,cAAc,KAAK;YAC1B,MAAM,SAAS,CAAA,GAAA,2UAAA,CAAA,YAAW,AAAD,EAAE;YAC3B,MAAM,kBAAkB;YACxB,MAAM,gBAAgB,gBAAgB,OAAO,CAAC;YAC9C,IAAI,kBAAkB,CAAC,GAAG;gBACxB,iBAAiB,OAAO,GAAG;YAC7B;QACF;QACA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;QAAU;QAAU;KAAmB;IAC3C,6WAAM,SAAS,CAAC;QACd,IAAI,UAAU;QACd,IAAI,CAAC,iBAAiB;QAEtB,oDAAoD;QACpD,SAAS;YACP,iBAAiB,OAAO,GAAG;YAC3B,WAAW;gBACT,iBAAiB,OAAO,GAAG;YAC7B;QACF;QACA,SAAS,mBAAmB,KAAK;YAC/B,MAAM,gBAAgB,MAAM,aAAa;YACzC,MAAM,gBAAgB,MAAM,aAAa;YACzC,MAAM,SAAS,CAAA,GAAA,2UAAA,CAAA,YAAW,AAAD,EAAE;YAC3B,eAAe;gBACb,MAAM,SAAS;gBACf,MAAM,uBAAuB,CAAC,CAAC,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,cAAc,kBAAkB,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,UAAU,kBAAkB,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,eAAe,aAAa,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,iBAAiB,OAAO,KAAK,IAAI,cAAc,UAAU,EAAE,kBAAkB,iBAAiB,QAAQ,cAAc,YAAY,CAAC,gBAAgB,mBAAmB,QAAQ,CAAC,CAAA,GAAA,2UAAA,CAAA,kBAAiB,AAAD,EAAE,KAAK,QAAQ,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAA;oBACpY,IAAI,eAAe;oBACnB,OAAO,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,CAAC,gBAAgB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,cAAc,QAAQ,CAAC,QAAQ,EAAE,kBAAkB,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,QAAQ,CAAC,YAAY,EAAE;gBAC7N,MAAM,CAAA,GAAA,2UAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,QAAQ,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAA;oBACzD,IAAI,gBAAgB,gBAAgB;oBACpC,OAAO;wBAAC,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,QAAQ,CAAC,QAAQ;wBAAE,CAAA,GAAA,2UAAA,CAAA,0BAAuB,AAAD,EAAE,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,QAAQ,CAAC,QAAQ;qBAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,QAAQ,CAAC,YAAY,MAAM;gBACtU,EAAE,CAAC;gBACH,IAAI,kBAAkB,gBAAgB,sBAAsB;oBAC1D,eAAe,sBAAsB;gBACvC;gBAEA,kEAAkE;gBAClE,mDAAmD;gBACnD,IAAI,gBAAgB,kBAAkB,gBAAgB,CAAC,CAAC,UAAU,QAAQ,OAAO,WAAW,KAAK,CAAA,GAAA,2UAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE,2BAA2B,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE,sBAAsB,IAAI,EAAE;oBAChM,mEAAmE;oBACnE,iBAAiB;oBACjB,IAAI,CAAA,GAAA,2PAAA,CAAA,gBAAa,AAAD,EAAE,uBAAuB;wBACvC,qBAAqB,KAAK;oBAC5B;oBACA,MAAM,oBAAoB,iBAAiB,OAAO;oBAClD,MAAM,kBAAkB;oBACxB,MAAM,cAAc,eAAe,CAAC,kBAAkB,IAAI,eAAe,CAAC,gBAAgB,MAAM,GAAG,EAAE,IAAI;oBACzG,IAAI,CAAA,GAAA,2PAAA,CAAA,gBAAa,AAAD,EAAE,cAAc;wBAC9B,YAAY,KAAK;oBACnB;gBACF;gBAEA,yDAAyD;gBACzD,IAAI,QAAQ,OAAO,CAAC,eAAe,EAAE;oBACnC,QAAQ,OAAO,CAAC,eAAe,GAAG;oBAClC;gBACF;gBAEA,yEAAyE;gBACzE,mCAAmC;gBACnC,IAAI,CAAC,8BAA8B,OAAO,CAAC,KAAK,KAAK,iBAAiB,wBAAwB,CAAC,iBAAiB,OAAO,IACvH,gEAAgE;gBAChE,kBAAkB,+BAA+B;oBAC/C,sBAAsB,OAAO,GAAG;oBAChC,aAAa,OAAO,OAAO;gBAC7B;YACF;QACF;QACA,IAAI,YAAY,CAAA,GAAA,2PAAA,CAAA,gBAAa,AAAD,EAAE,eAAe;YAC3C,aAAa,gBAAgB,CAAC,YAAY;YAC1C,aAAa,gBAAgB,CAAC,eAAe;YAC7C,SAAS,gBAAgB,CAAC,YAAY;YACtC,OAAO;gBACL,aAAa,mBAAmB,CAAC,YAAY;gBAC7C,aAAa,mBAAmB,CAAC,eAAe;gBAChD,SAAS,mBAAmB,CAAC,YAAY;YAC3C;QACF;IACF,GAAG;QAAC;QAAU;QAAc;QAAU;QAAsB;QAAO;QAAM;QAAe;QAAc;QAAiB;QAAc;QAAoB;QAA6B;QAAW;QAAU;KAAQ;IACnN,MAAM,iBAAiB,6WAAM,MAAM,CAAC;IACpC,MAAM,gBAAgB,6WAAM,MAAM,CAAC;IACnC,MAAM,uBAAuB,iBAAiB;QAAC;QAAgB,iBAAiB,OAAO,KAAK,IAAI,cAAc,eAAe;KAAC;IAC9H,MAAM,sBAAsB,iBAAiB;QAAC;QAAe,iBAAiB,OAAO,KAAK,IAAI,cAAc,cAAc;KAAC;IAC3H,6WAAM,SAAS,CAAC;QACd,IAAI,uBAAuB;QAC3B,IAAI,UAAU;QACd,IAAI,CAAC,UAAU;QAEf,sDAAsD;QACtD,MAAM,cAAc,MAAM,IAAI,CAAC,CAAC,iBAAiB,QAAQ,CAAC,wBAAwB,cAAc,UAAU,KAAK,OAAO,KAAK,IAAI,sBAAsB,gBAAgB,CAAC,MAAM,gBAAgB,YAAY,IAAI,KAAK,EAAE;QACnN,MAAM,YAAY,OAAO,CAAA,GAAA,2UAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,QAAQ,CAAC,OAAO,EAAE,eAAe,EAAE;QAClF,MAAM,wBAAwB,QAAQ,CAAC,QAAQ,UAAU,GAAG,CAAC,CAAA;YAC3D,IAAI;YACJ,OAAO,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,QAAQ,CAAC,QAAQ;QAC5F,KAAK,EAAE;QACP,MAAM,mCAAmC,CAAC,kBAAkB,UAAU,IAAI,CAAC,CAAA;YACzE,IAAI;YACJ,OAAO,CAAA,GAAA,2UAAA,CAAA,qBAAkB,AAAD,EAAE,CAAC,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,QAAQ,CAAC,YAAY,KAAK;QACzH,EAAE,KAAK,QAAQ,CAAC,kBAAkB,gBAAgB,OAAO,KAAK,OAAO,KAAK,IAAI,gBAAgB,QAAQ,CAAC,YAAY;QACnH,MAAM,iBAAiB;YAAC;YAAU;eAAqC;eAAgB;eAA0B;YAAqB,sBAAsB,OAAO;YAAE,oBAAoB,OAAO;YAAE,eAAe,OAAO;YAAE,cAAc,OAAO;YAAE,iBAAiB,OAAO,KAAK,IAAI,cAAc,gBAAgB,CAAC,OAAO;YAAE,iBAAiB,OAAO,KAAK,IAAI,cAAc,eAAe,CAAC,OAAO;YAAE,SAAS,OAAO,CAAC,QAAQ,CAAC,gBAAgB,8BAA8B,eAAe;SAAK,CAAC,MAAM,CAAC,CAAA,IAAK,KAAK;QAChf,MAAM,UAAU,SAAS,8BAA8B,WAAW,gBAAgB,CAAC,UAAU,YAAY,WAAW;QACpH,OAAO;YACL;QACF;IACF,GAAG;QAAC;QAAU;QAAc;QAAU;QAAO;QAAU;QAAe;QAA6B;QAAQ;QAAU;QAAM;QAAW;KAAkB;IACxJ,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,YAAY,CAAC,CAAA,GAAA,2PAAA,CAAA,gBAAa,AAAD,EAAE,uBAAuB;QACtD,MAAM,MAAM,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE;QAC1B,MAAM,2BAA2B,CAAA,GAAA,2UAAA,CAAA,gBAAa,AAAD,EAAE;QAE/C,yEAAyE;QACzE,eAAe;YACb,MAAM,oBAAoB,oBAAoB;YAC9C,MAAM,oBAAoB,gBAAgB,OAAO;YACjD,MAAM,YAAY,CAAC,OAAO,sBAAsB,WAAW,iBAAiB,CAAC,kBAAkB,GAAG,kBAAkB,OAAO,KAAK;YAChI,MAAM,+BAA+B,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,sBAAsB;YACtE,IAAI,CAAC,sBAAsB,CAAC,gCAAgC,MAAM;gBAChE,aAAa,WAAW;oBACtB,eAAe,cAAc;gBAC/B;YACF;QACF;IACF,GAAG;QAAC;QAAU;QAAM;QAAsB;QAAoB;QAAqB;KAAgB;IACnG,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,YAAY,CAAC,sBAAsB;QACvC,MAAM,MAAM,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE;QAC1B,MAAM,2BAA2B,CAAA,GAAA,2UAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C,4BAA4B;QAE5B,qEAAqE;QACrE,8BAA8B;QAC9B,SAAS,aAAa,IAAI;YACxB,IAAI,EACF,MAAM,EACN,KAAK,EACL,MAAM,EACP,GAAG;YACJ,IAAI;gBAAC;gBAAS;aAAe,CAAC,QAAQ,CAAC,WAAW,MAAM,IAAI,KAAK,cAAc;gBAC7E,sBAAsB,OAAO,GAAG;YAClC;YACA,IAAI,WAAW,iBAAiB;YAChC,IAAI,QAAQ;gBACV,sBAAsB,OAAO,GAAG;YAClC,OAAO,IAAI,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ;gBAChE,sBAAsB,OAAO,GAAG;YAClC,OAAO;gBACL,IAAI,2BAA2B;gBAC/B,SAAS,aAAa,CAAC,OAAO,KAAK,CAAC;oBAClC,IAAI,iBAAgB;wBAClB,2BAA2B;wBAC3B,OAAO;oBACT;gBACF;gBACA,IAAI,0BAA0B;oBAC5B,sBAAsB,OAAO,GAAG;gBAClC,OAAO;oBACL,sBAAsB,OAAO,GAAG;gBAClC;YACF;QACF;QACA,OAAO,EAAE,CAAC,cAAc;QACxB,MAAM,aAAa,IAAI,aAAa,CAAC;QACrC,WAAW,YAAY,CAAC,YAAY;QACpC,WAAW,YAAY,CAAC,eAAe;QACvC,OAAO,MAAM,CAAC,WAAW,KAAK,EAAE;QAChC,IAAI,kBAAkB,cAAc;YAClC,aAAa,qBAAqB,CAAC,YAAY;QACjD;QACA,SAAS;YACP,IAAI,OAAO,eAAe,OAAO,KAAK,WAAW;gBAC/C,MAAM,KAAK,gBAAgB;gBAC3B,OAAO,MAAM,GAAG,WAAW,GAAG,KAAK;YACrC;YACA,OAAO,eAAe,OAAO,CAAC,OAAO,IAAI;QAC3C;QACA,OAAO;YACL,OAAO,GAAG,CAAC,cAAc;YACzB,MAAM,WAAW,CAAA,GAAA,2UAAA,CAAA,gBAAa,AAAD,EAAE;YAC/B,MAAM,4BAA4B,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,UAAU,aAAa,QAAQ,CAAA,GAAA,2UAAA,CAAA,kBAAiB,AAAD,EAAE,KAAK,QAAQ,CAAC,OAAO,EAAE,aAAa,OAAO,IAAI,CAAC,CAAA;gBAC5I,IAAI;gBACJ,OAAO,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,QAAQ,CAAC,QAAQ,EAAE;YACzG;YACA,MAAM,gBAAgB;YACtB,eAAe;gBACb,0EAA0E;gBAC1E,MAAM,wBAAwB,wBAAwB;gBACtD,IACA,uDAAuD;gBACvD,eAAe,OAAO,IAAI,CAAC,sBAAsB,OAAO,IAAI,CAAA,GAAA,2PAAA,CAAA,gBAAa,AAAD,EAAE,0BAA0B,CACpG,uEAAuE;gBACvE,8DAA8D;gBAC9D,oEAAoE;gBACpE,0BAA0B,YAAY,aAAa,IAAI,IAAI,GAAG,4BAA4B,IAAI,GAAG;oBAC/F,sBAAsB,KAAK,CAAC;wBAC1B,eAAe;oBACjB;gBACF;gBACA,WAAW,MAAM;YACnB;QACF;IACF,GAAG;QAAC;QAAU;QAAU;QAAsB;QAAgB;QAAS;QAAQ;QAAM;QAAgB;QAAc;KAAU;IAC7H,6WAAM,SAAS,CAAC;QACd,sEAAsE;QACtE,qDAAqD;QACrD,eAAe;YACb,sBAAsB,OAAO,GAAG;QAClC;IACF,GAAG;QAAC;KAAS;IAEb,2EAA2E;IAC3E,mEAAmE;IACnE,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,UAAU;QACd,IAAI,CAAC,eAAe;QACpB,cAAc,oBAAoB,CAAC;YACjC;YACA;YACA;YACA;YACA;QACF;QACA,OAAO;YACL,cAAc,oBAAoB,CAAC;QACrC;IACF,GAAG;QAAC;QAAU;QAAe;QAAO;QAAM;QAAc;QAAiB;KAAa;IACtF,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,UAAU;QACd,IAAI,CAAC,sBAAsB;QAC3B,eAAe,sBAAsB;IACvC,GAAG;QAAC;QAAU;QAAsB;KAAS;IAC7C,SAAS,oBAAoB,QAAQ;QACnC,IAAI,YAAY,CAAC,yBAAyB,CAAC,OAAO;YAChD,OAAO;QACT;QACA,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,uBAAuB;YAC7C,KAAK,aAAa,UAAU,wBAAwB;YACpD,SAAS,CAAA,QAAS,aAAa,OAAO,MAAM,WAAW;YACvD,UAAU,OAAO,0BAA0B,WAAW,wBAAwB;QAChF;IACF;IACA,MAAM,qBAAqB,CAAC,YAAY,UAAU,CAAC,QAAQ,CAAC,8BAA8B,IAAI,KAAK,CAAC,kBAAkB,KAAK;IAC3H,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,OAAI,AAAD,EAAE,8XAAA,CAAA,WAAQ,EAAE;QACjC,UAAU;YAAC,sBAAsB,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,YAAY;gBAC5D,aAAa;gBACb,KAAK;gBACL,SAAS,CAAA;oBACP,IAAI,OAAO;wBACT,MAAM,MAAM;wBACZ,aAAa,KAAK,CAAC,EAAE,KAAK,cAAc,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;oBACtE,OAAO,IAAI,iBAAiB,QAAQ,cAAc,gBAAgB,IAAI,cAAc,UAAU,EAAE;wBAC9F,sBAAsB,OAAO,GAAG;wBAChC,IAAI,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,cAAc,UAAU,GAAG;4BACnD,MAAM,eAAe,CAAA,GAAA,2UAAA,CAAA,kBAAe,AAAD,EAAE;4BACrC,gBAAgB,QAAQ,aAAa,KAAK;wBAC5C,OAAO;4BACL,IAAI;4BACJ,CAAC,wBAAwB,cAAc,gBAAgB,CAAC,OAAO,KAAK,QAAQ,sBAAsB,KAAK;wBACzG;oBACF;gBACF;YACF;YAAI,CAAC,+BAA+B,oBAAoB;YAAU;YAAU,oBAAoB;YAAQ,sBAAsB,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,YAAY;gBACzJ,aAAa;gBACb,KAAK;gBACL,SAAS,CAAA;oBACP,IAAI,OAAO;wBACT,aAAa,qBAAqB,CAAC,EAAE;oBACvC,OAAO,IAAI,iBAAiB,QAAQ,cAAc,gBAAgB,IAAI,cAAc,UAAU,EAAE;wBAC9F,IAAI,iBAAiB;4BACnB,sBAAsB,OAAO,GAAG;wBAClC;wBACA,IAAI,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,cAAc,UAAU,GAAG;4BACnD,MAAM,eAAe,CAAA,GAAA,2UAAA,CAAA,sBAAmB,AAAD,EAAE;4BACzC,gBAAgB,QAAQ,aAAa,KAAK;wBAC5C,OAAO;4BACL,IAAI;4BACJ,CAAC,wBAAwB,cAAc,eAAe,CAAC,OAAO,KAAK,QAAQ,sBAAsB,KAAK;wBACxG;oBACF;gBACF;YACF;SAAG;IACL;AACF;AAEA,IAAI,YAAY;AAChB,MAAM,oBAAoB;AAC1B,SAAS;IACP,MAAM,WAAW,CAAA,GAAA,2UAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,QAAQ,qBAAqB,IAAI,CAAC,aACxC,iCAAiC;IACjC,aAAa,cAAc,UAAU,cAAc,GAAG;IACtD,MAAM,YAAY,SAAS,IAAI,CAAC,KAAK;IACrC,uBAAuB;IACvB,MAAM,aAAa,KAAK,KAAK,CAAC,SAAS,eAAe,CAAC,qBAAqB,GAAG,IAAI,IAAI,SAAS,eAAe,CAAC,UAAU;IAC1H,MAAM,cAAc,aAAa,gBAAgB;IACjD,MAAM,iBAAiB,OAAO,UAAU,GAAG,SAAS,eAAe,CAAC,WAAW;IAC/E,MAAM,UAAU,UAAU,IAAI,GAAG,WAAW,UAAU,IAAI,IAAI,OAAO,OAAO;IAC5E,MAAM,UAAU,UAAU,GAAG,GAAG,WAAW,UAAU,GAAG,IAAI,OAAO,OAAO;IAC1E,UAAU,QAAQ,GAAG;IACrB,UAAU,WAAW,CAAC,mBAAmB,iBAAiB;IAC1D,IAAI,gBAAgB;QAClB,SAAS,CAAC,YAAY,GAAG,iBAAiB;IAC5C;IAEA,yEAAyE;IACzE,oCAAoC;IACpC,IAAI,OAAO;QACT,IAAI,uBAAuB;QAC3B,4CAA4C;QAC5C,MAAM,aAAa,CAAC,CAAC,wBAAwB,OAAO,cAAc,KAAK,OAAO,KAAK,IAAI,sBAAsB,UAAU,KAAK;QAC5H,MAAM,YAAY,CAAC,CAAC,yBAAyB,OAAO,cAAc,KAAK,OAAO,KAAK,IAAI,uBAAuB,SAAS,KAAK;QAC5H,OAAO,MAAM,CAAC,WAAW;YACvB,UAAU;YACV,KAAK,CAAC,CAAC,UAAU,KAAK,KAAK,CAAC,UAAU,IAAI;YAC1C,MAAM,CAAC,CAAC,UAAU,KAAK,KAAK,CAAC,WAAW,IAAI;YAC5C,OAAO;QACT;IACF;IACA,OAAO;QACL,OAAO,MAAM,CAAC,WAAW;YACvB,UAAU;YACV,CAAC,YAAY,EAAE;QACjB;QACA,UAAU,cAAc,CAAC;QACzB,IAAI,OAAO;YACT,OAAO,MAAM,CAAC,WAAW;gBACvB,UAAU;gBACV,KAAK;gBACL,MAAM;gBACN,OAAO;YACT;YACA,OAAO,QAAQ,CAAC,SAAS;QAC3B;IACF;AACF;AACA,IAAI,UAAU,KAAO;AAErB;;;;;CAKC,GACD,MAAM,kBAAkB,WAAW,GAAE,6WAAM,UAAU,CAAC,SAAS,gBAAgB,KAAK,EAAE,GAAG;IACvF,MAAM,EACJ,aAAa,KAAK,EAClB,GAAG,MACJ,GAAG;IACJ,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,CAAC,YAAY;QACjB;QACA,IAAI,cAAc,GAAG;YACnB,UAAU;QACZ;QACA,OAAO;YACL;YACA,IAAI,cAAc,GAAG;gBACnB;YACF;QACF;IACF,GAAG;QAAC;KAAW;IACf,OAAO,WAAW,GAAE,CAAA,GAAA,8XAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAC7B,KAAK;QACL,GAAG,IAAI;QACP,OAAO;YACL,UAAU;YACV,UAAU;YACV,KAAK;YACL,OAAO;YACP,QAAQ;YACR,MAAM;YACN,GAAG,KAAK,KAAK;QACf;IACF;AACF;AAEA,SAAS,eAAe,KAAK;IAC3B,OAAO,CAAA,GAAA,2PAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,MAAM,KAAK,MAAM,MAAM,CAAC,OAAO,KAAK;AACjE;AACA,SAAS,eAAe,KAAK;IAC3B,OAAO,CAAA,GAAA,2PAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,MAAM,KAAK,MAAM,MAAM,CAAC,OAAO,KAAK;AACjE;AACA,SAAS,eAAe,OAAO;IAC7B,OAAO,CAAA,GAAA,2UAAA,CAAA,oBAAiB,AAAD,EAAE;AAC3B;AACA;;;CAGC,GACD,SAAS,SAAS,OAAO,EAAE,KAAK;IAC9B,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,OAAO,EACP,UAAU,EACR,YAAY,EACb,EACF,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,OAAO,cAAc,OAAO,EAC5B,SAAS,IAAI,EACb,cAAc,KAAK,EACnB,mBAAmB,IAAI,EACvB,cAAc,IAAI,EACnB,GAAG;IACJ,MAAM,iBAAiB,6WAAM,MAAM;IACnC,MAAM,gBAAgB,6WAAM,MAAM,CAAC;IACnC,MAAM,YAAY,6WAAM,OAAO,CAAC,IAAM,CAAC;YACrC,eAAc,KAAK;gBACjB,eAAe,OAAO,GAAG,MAAM,WAAW;YAC5C;YACA,aAAY,KAAK;gBACf,MAAM,cAAc,eAAe,OAAO;gBAE1C,mDAAmD;gBACnD,qEAAqE;gBACrE,IAAI,MAAM,MAAM,KAAK,GAAG;gBACxB,IAAI,gBAAgB,SAAS;gBAC7B,IAAI,CAAA,GAAA,2UAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS,aAAa;gBAC9D,IAAI,QAAQ,UAAU,CAAC,QAAQ,OAAO,CAAC,SAAS,IAAI,cAAc,QAAQ,OAAO,CAAC,SAAS,CAAC,IAAI,KAAK,cAAc,IAAI,GAAG;oBACxH,aAAa,OAAO,MAAM,WAAW,EAAE;gBACzC,OAAO;oBACL,mDAAmD;oBACnD,MAAM,cAAc;oBACpB,aAAa,MAAM,MAAM,WAAW,EAAE;gBACxC;YACF;YACA,SAAQ,KAAK;gBACX,MAAM,cAAc,eAAe,OAAO;gBAC1C,IAAI,gBAAgB,eAAe,eAAe,OAAO,EAAE;oBACzD,eAAe,OAAO,GAAG;oBACzB;gBACF;gBACA,IAAI,CAAA,GAAA,2UAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS,aAAa;gBAC9D,IAAI,QAAQ,UAAU,CAAC,QAAQ,OAAO,CAAC,SAAS,IAAI,cAAc,QAAQ,OAAO,CAAC,SAAS,CAAC,IAAI,KAAK,UAAU,IAAI,GAAG;oBACpH,aAAa,OAAO,MAAM,WAAW,EAAE;gBACzC,OAAO;oBACL,aAAa,MAAM,MAAM,WAAW,EAAE;gBACxC;YACF;YACA,WAAU,KAAK;gBACb,eAAe,OAAO,GAAG;gBACzB,IAAI,MAAM,gBAAgB,IAAI,CAAC,oBAAoB,eAAe,QAAQ;oBACxE;gBACF;gBACA,IAAI,MAAM,GAAG,KAAK,OAAO,CAAC,eAAe,eAAe;oBACtD,oBAAoB;oBACpB,MAAM,cAAc;oBACpB,cAAc,OAAO,GAAG;gBAC1B;gBACA,IAAI,eAAe,QAAQ;oBACzB;gBACF;gBACA,IAAI,MAAM,GAAG,KAAK,SAAS;oBACzB,IAAI,QAAQ,QAAQ;wBAClB,aAAa,OAAO,MAAM,WAAW,EAAE;oBACzC,OAAO;wBACL,aAAa,MAAM,MAAM,WAAW,EAAE;oBACxC;gBACF;YACF;YACA,SAAQ,KAAK;gBACX,IAAI,MAAM,gBAAgB,IAAI,CAAC,oBAAoB,eAAe,UAAU,eAAe,eAAe;oBACxG;gBACF;gBACA,IAAI,MAAM,GAAG,KAAK,OAAO,cAAc,OAAO,EAAE;oBAC9C,cAAc,OAAO,GAAG;oBACxB,IAAI,QAAQ,QAAQ;wBAClB,aAAa,OAAO,MAAM,WAAW,EAAE;oBACzC,OAAO;wBACL,aAAa,MAAM,MAAM,WAAW,EAAE;oBACxC;gBACF;YACF;QACF,CAAC,GAAG;QAAC;QAAS;QAAc;QAAa;QAAa;QAAkB;QAAc;QAAM;QAAa;KAAO;IAChH,OAAO,6WAAM,OAAO,CAAC,IAAM,UAAU;YACnC;QACF,IAAI,CAAC,GAAG;QAAC;QAAS;KAAU;AAC9B;AAEA,SAAS,qBAAqB,UAAU,EAAE,IAAI;IAC5C,IAAI,UAAU;IACd,IAAI,UAAU;IACd,IAAI,oBAAoB;IACxB,OAAO;QACL,gBAAgB,cAAc;QAC9B;YACE,IAAI;YACJ,MAAM,UAAU,CAAC,cAAc,OAAO,KAAK,IAAI,WAAW,qBAAqB,EAAE,KAAK;gBACpF,OAAO;gBACP,QAAQ;gBACR,GAAG;gBACH,GAAG;YACL;YACA,MAAM,UAAU,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK;YACnD,MAAM,UAAU,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK;YACnD,MAAM,6BAA6B;gBAAC;gBAAc;aAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB,KAAK,OAAO,CAAC,OAAO,CAAC,SAAS,KAAK,OAAO,KAAK,IAAI,sBAAsB,IAAI,KAAK,OAAO,KAAK,WAAW,KAAK;YAChN,IAAI,QAAQ,QAAQ,KAAK;YACzB,IAAI,SAAS,QAAQ,MAAM;YAC3B,IAAI,IAAI,QAAQ,CAAC;YACjB,IAAI,IAAI,QAAQ,CAAC;YACjB,IAAI,WAAW,QAAQ,KAAK,CAAC,IAAI,SAAS;gBACxC,UAAU,QAAQ,CAAC,GAAG,KAAK,CAAC;YAC9B;YACA,IAAI,WAAW,QAAQ,KAAK,CAAC,IAAI,SAAS;gBACxC,UAAU,QAAQ,CAAC,GAAG,KAAK,CAAC;YAC9B;YACA,KAAK,WAAW;YAChB,KAAK,WAAW;YAChB,QAAQ;YACR,SAAS;YACT,IAAI,CAAC,qBAAqB,4BAA4B;gBACpD,QAAQ,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK,GAAG;gBAC5C,SAAS,KAAK,IAAI,KAAK,MAAM,QAAQ,MAAM,GAAG;gBAC9C,IAAI,WAAW,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,GAAG;gBACzC,IAAI,WAAW,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,GAAG;YAC3C,OAAO,IAAI,qBAAqB,CAAC,4BAA4B;gBAC3D,SAAS,KAAK,IAAI,KAAK,MAAM,QAAQ,MAAM,GAAG;gBAC9C,QAAQ,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK,GAAG;YAC9C;YACA,oBAAoB;YACpB,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA,KAAK;gBACL,OAAO,IAAI;gBACX,QAAQ,IAAI;gBACZ,MAAM;YACR;QACF;IACF;AACF;AACA,SAAS,kBAAkB,KAAK;IAC9B,OAAO,SAAS,QAAQ,MAAM,OAAO,IAAI;AAC3C;AACA;;;;CAIC,GACD,SAAS,eAAe,OAAO,EAAE,KAAK;IACpC,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,UAAU,EACR,QAAQ,EACR,YAAY,EACb,EACD,IAAI,EACL,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,OAAO,MAAM,EACb,IAAI,IAAI,EACR,IAAI,IAAI,EACT,GAAG;IACJ,MAAM,aAAa,6WAAM,MAAM,CAAC;IAChC,MAAM,qBAAqB,6WAAM,MAAM,CAAC;IACxC,MAAM,CAAC,aAAa,eAAe,GAAG,6WAAM,QAAQ;IACpD,MAAM,CAAC,UAAU,YAAY,GAAG,6WAAM,QAAQ,CAAC,EAAE;IACjD,MAAM,eAAe,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG;QACtC,IAAI,WAAW,OAAO,EAAE;QAExB,6DAA6D;QAC7D,+DAA+D;QAC/D,kCAAkC;QAClC,IAAI,QAAQ,OAAO,CAAC,SAAS,IAAI,CAAC,kBAAkB,QAAQ,OAAO,CAAC,SAAS,GAAG;YAC9E;QACF;QACA,KAAK,oBAAoB,CAAC,qBAAqB,cAAc;YAC3D;YACA;YACA;YACA;YACA;QACF;IACF;IACA,MAAM,6BAA6B,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,CAAA;QAChD,IAAI,KAAK,QAAQ,KAAK,MAAM;QAC5B,IAAI,CAAC,MAAM;YACT,aAAa,MAAM,OAAO,EAAE,MAAM,OAAO;QAC3C,OAAO,IAAI,CAAC,mBAAmB,OAAO,EAAE;YACtC,oEAAoE;YACpE,uEAAuE;YACvE,sDAAsD;YACtD,YAAY,EAAE;QAChB;IACF;IAEA,4EAA4E;IAC5E,oEAAoE;IACpE,yEAAyE;IACzE,6BAA6B;IAC7B,MAAM,YAAY,CAAA,GAAA,2UAAA,CAAA,yBAAsB,AAAD,EAAE,eAAe,WAAW;IACnE,MAAM,cAAc,6WAAM,WAAW,CAAC;QACpC,qEAAqE;QACrE,IAAI,CAAC,aAAa,CAAC,WAAW,KAAK,QAAQ,KAAK,MAAM;QACtD,MAAM,MAAM,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE;QACtB,SAAS,gBAAgB,KAAK;YAC5B,MAAM,SAAS,CAAA,GAAA,2UAAA,CAAA,YAAW,AAAD,EAAE;YAC3B,IAAI,CAAC,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,UAAU,SAAS;gBACjC,aAAa,MAAM,OAAO,EAAE,MAAM,OAAO;YAC3C,OAAO;gBACL,IAAI,mBAAmB,CAAC,aAAa;gBACrC,mBAAmB,OAAO,GAAG;YAC/B;QACF;QACA,IAAI,CAAC,QAAQ,OAAO,CAAC,SAAS,IAAI,kBAAkB,QAAQ,OAAO,CAAC,SAAS,GAAG;YAC9E,IAAI,gBAAgB,CAAC,aAAa;YAClC,MAAM,UAAU;gBACd,IAAI,mBAAmB,CAAC,aAAa;gBACrC,mBAAmB,OAAO,GAAG;YAC/B;YACA,mBAAmB,OAAO,GAAG;YAC7B,OAAO;QACT;QACA,KAAK,oBAAoB,CAAC;IAC5B,GAAG;QAAC;QAAW;QAAS;QAAG;QAAG;QAAU;QAAS;QAAM;QAAc;KAAa;IAClF,6WAAM,SAAS,CAAC;QACd,OAAO;IACT,GAAG;QAAC;QAAa;KAAS;IAC1B,6WAAM,SAAS,CAAC;QACd,IAAI,WAAW,CAAC,UAAU;YACxB,WAAW,OAAO,GAAG;QACvB;IACF,GAAG;QAAC;QAAS;KAAS;IACtB,6WAAM,SAAS,CAAC;QACd,IAAI,CAAC,WAAW,MAAM;YACpB,WAAW,OAAO,GAAG;QACvB;IACF,GAAG;QAAC;QAAS;KAAK;IAClB,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,WAAW,CAAC,KAAK,QAAQ,KAAK,IAAI,GAAG;YACvC,WAAW,OAAO,GAAG;YACrB,aAAa,GAAG;QAClB;IACF,GAAG;QAAC;QAAS;QAAG;QAAG;KAAa;IAChC,MAAM,YAAY,6WAAM,OAAO,CAAC;QAC9B,SAAS,kBAAkB,IAAI;YAC7B,IAAI,EACF,WAAW,EACZ,GAAG;YACJ,eAAe;QACjB;QACA,OAAO;YACL,eAAe;YACf,gBAAgB;YAChB,aAAa;YACb,cAAc;QAChB;IACF,GAAG;QAAC;KAA2B;IAC/B,OAAO,6WAAM,OAAO,CAAC,IAAM,UAAU;YACnC;QACF,IAAI,CAAC,GAAG;QAAC;QAAS;KAAU;AAC9B;AAEA,MAAM,oBAAoB;IACxB,aAAa;IACb,WAAW;IACX,OAAO;AACT;AACA,MAAM,qBAAqB;IACzB,aAAa;IACb,WAAW;IACX,OAAO;AACT;AACA,MAAM,gBAAgB,CAAA;IACpB,IAAI,uBAAuB;IAC3B,OAAO;QACL,WAAW,OAAO,iBAAiB,YAAY,eAAe,CAAC,wBAAwB,gBAAgB,OAAO,KAAK,IAAI,aAAa,SAAS,KAAK,OAAO,wBAAwB;QACjL,cAAc,OAAO,iBAAiB,YAAY,eAAe,CAAC,wBAAwB,gBAAgB,OAAO,KAAK,IAAI,aAAa,YAAY,KAAK,OAAO,wBAAwB;IACzL;AACF;AACA;;;;CAIC,GACD,SAAS,WAAW,OAAO,EAAE,KAAK;IAChC,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,OAAO,EACR,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,YAAY,IAAI,EAChB,cAAc,wBAAwB,IAAI,EAC1C,oBAAoB,aAAa,EACjC,iBAAiB,KAAK,EACtB,sBAAsB,aAAa,EACnC,iBAAiB,KAAK,EACtB,OAAO,EACP,OAAO,EACR,GAAG;IACJ,MAAM,OAAO;IACb,MAAM,iBAAiB,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,0BAA0B,aAAa,wBAAwB,IAAM;IAClH,MAAM,eAAe,OAAO,0BAA0B,aAAa,iBAAiB;IACpF,MAAM,0BAA0B,6WAAM,MAAM,CAAC;IAC7C,MAAM,EACJ,WAAW,gBAAgB,EAC3B,cAAc,mBAAmB,EAClC,GAAG,cAAc;IAClB,MAAM,EACJ,WAAW,gBAAgB,EAC3B,cAAc,mBAAmB,EAClC,GAAG,cAAc;IAClB,MAAM,iBAAiB,6WAAM,MAAM,CAAC;IACpC,MAAM,iBAAiB,6WAAM,MAAM,CAAC,CAAC;IACrC,MAAM,uBAAuB,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,CAAA;QAC1C,IAAI;QACJ,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,aAAa,MAAM,GAAG,KAAK,UAAU;YAC7D;QACF;QAEA,sEAAsE;QACtE,wDAAwD;QACxD,IAAI,eAAe,OAAO,EAAE;YAC1B;QACF;QACA,MAAM,SAAS,CAAC,wBAAwB,QAAQ,OAAO,CAAC,eAAe,KAAK,OAAO,KAAK,IAAI,sBAAsB,MAAM;QACxH,MAAM,WAAW,OAAO,CAAA,GAAA,2UAAA,CAAA,kBAAiB,AAAD,EAAE,KAAK,QAAQ,CAAC,OAAO,EAAE,UAAU,EAAE;QAC7E,IAAI,CAAC,kBAAkB;YACrB,MAAM,eAAe;YACrB,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,IAAI,gBAAgB;gBACpB,SAAS,OAAO,CAAC,CAAA;oBACf,IAAI;oBACJ,IAAI,CAAC,iBAAiB,MAAM,OAAO,KAAK,QAAQ,eAAe,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE;wBACxH,gBAAgB;wBAChB;oBACF;gBACF;gBACA,IAAI,CAAC,eAAe;oBAClB;gBACF;YACF;QACF;QACA,aAAa,OAAO,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE,SAAS,MAAM,WAAW,GAAG,OAAO;IACvE;IACA,MAAM,8BAA8B,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,CAAA;QACjD,IAAI;QACJ,MAAM,WAAW;YACf,IAAI;YACJ,qBAAqB;YACrB,CAAC,aAAa,CAAA,GAAA,2UAAA,CAAA,YAAW,AAAD,EAAE,MAAM,KAAK,QAAQ,WAAW,mBAAmB,CAAC,WAAW;QACzF;QACA,CAAC,cAAc,CAAA,GAAA,2UAAA,CAAA,YAAW,AAAD,EAAE,MAAM,KAAK,QAAQ,YAAY,gBAAgB,CAAC,WAAW;IACxF;IACA,MAAM,sBAAsB,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,CAAA;QACzC,IAAI;QACJ,oEAAoE;QACpE,kDAAkD;QAClD,MAAM,kBAAkB,QAAQ,OAAO,CAAC,eAAe;QACvD,QAAQ,OAAO,CAAC,eAAe,GAAG;QAElC,+DAA+D;QAC/D,kBAAkB;QAClB,mDAAmD;QACnD,iDAAiD;QACjD,MAAM,uBAAuB,wBAAwB,OAAO;QAC5D,wBAAwB,OAAO,GAAG;QAClC,IAAI,sBAAsB,WAAW,sBAAsB;YACzD;QACF;QACA,IAAI,iBAAiB;YACnB;QACF;QACA,IAAI,OAAO,iBAAiB,cAAc,CAAC,aAAa,QAAQ;YAC9D;QACF;QACA,MAAM,SAAS,CAAA,GAAA,2UAAA,CAAA,YAAW,AAAD,EAAE;QAC3B,MAAM,gBAAgB,MAAM,gBAAgB,WAAW;QACvD,MAAM,UAAU,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE,SAAS,QAAQ,EAAE,gBAAgB,CAAC;QAClE,IAAI,qBAAqB,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,UAAU,SAAS;QACtD,MAAO,sBAAsB,CAAC,CAAA,GAAA,2PAAA,CAAA,wBAAqB,AAAD,EAAE,oBAAqB;YACvE,MAAM,aAAa,CAAA,GAAA,2PAAA,CAAA,gBAAa,AAAD,EAAE;YACjC,IAAI,CAAA,GAAA,2PAAA,CAAA,wBAAqB,AAAD,EAAE,eAAe,CAAC,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,aAAa;gBAC/D;YACF;YACA,qBAAqB;QACvB;QAEA,0EAA0E;QAC1E,6BAA6B;QAC7B,IAAI,QAAQ,MAAM,IAAI,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,WAAW,CAAC,CAAA,GAAA,2UAAA,CAAA,gBAAa,AAAD,EAAE,WAC1D,uDAAuD;QACvD,CAAC,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,QAAQ,SAAS,QAAQ,KACrC,oEAAoE;QACpE,4DAA4D;QAC5D,MAAM,IAAI,CAAC,SAAS,KAAK,CAAC,CAAA,SAAU,CAAC,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,oBAAoB,UAAU;YAC5E;QACF;QAEA,+CAA+C;QAC/C,IAAI,CAAA,GAAA,2PAAA,CAAA,gBAAa,AAAD,EAAE,WAAW,UAAU;YACrC,MAAM,sBAAsB,CAAA,GAAA,2PAAA,CAAA,wBAAqB,AAAD,EAAE;YAClD,MAAM,QAAQ,CAAA,GAAA,2PAAA,CAAA,mBAAgB,AAAD,EAAE;YAC/B,MAAM,WAAW;YACjB,MAAM,gBAAgB,uBAAuB,SAAS,IAAI,CAAC,MAAM,SAAS;YAC1E,MAAM,gBAAgB,uBAAuB,SAAS,IAAI,CAAC,MAAM,SAAS;YAC1E,MAAM,aAAa,iBAAiB,OAAO,WAAW,GAAG,KAAK,OAAO,WAAW,GAAG,OAAO,WAAW;YACrG,MAAM,aAAa,iBAAiB,OAAO,YAAY,GAAG,KAAK,OAAO,YAAY,GAAG,OAAO,YAAY;YACxG,MAAM,QAAQ,MAAM,SAAS,KAAK;YAElC,8CAA8C;YAC9C,mEAAmE;YACnE,mEAAmE;YACnE,gEAAgE;YAChE,qEAAqE;YACrE,MAAM,2BAA2B,cAAc,CAAC,QAAQ,MAAM,OAAO,IAAI,OAAO,WAAW,GAAG,OAAO,WAAW,GAAG,MAAM,OAAO,GAAG,OAAO,WAAW;YACrJ,MAAM,6BAA6B,cAAc,MAAM,OAAO,GAAG,OAAO,YAAY;YACpF,IAAI,4BAA4B,4BAA4B;gBAC1D;YACF;QACF;QACA,MAAM,SAAS,CAAC,yBAAyB,QAAQ,OAAO,CAAC,eAAe,KAAK,OAAO,KAAK,IAAI,uBAAuB,MAAM;QAC1H,MAAM,yBAAyB,QAAQ,CAAA,GAAA,2UAAA,CAAA,kBAAiB,AAAD,EAAE,KAAK,QAAQ,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAA;YAC3F,IAAI;YACJ,OAAO,CAAA,GAAA,2UAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,CAAC,gBAAgB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,cAAc,QAAQ,CAAC,QAAQ;QACrH;QACA,IAAI,CAAA,GAAA,2UAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,QAAQ,KAAK,CAAA,GAAA,2UAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,YAAY,KAAK,wBAAwB;YAChI;QACF;QACA,MAAM,WAAW,OAAO,CAAA,GAAA,2UAAA,CAAA,kBAAiB,AAAD,EAAE,KAAK,QAAQ,CAAC,OAAO,EAAE,UAAU,EAAE;QAC7E,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,IAAI,gBAAgB;YACpB,SAAS,OAAO,CAAC,CAAA;gBACf,IAAI;gBACJ,IAAI,CAAC,kBAAkB,MAAM,OAAO,KAAK,QAAQ,gBAAgB,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE;oBAC7H,gBAAgB;oBAChB;gBACF;YACF;YACA,IAAI,CAAC,eAAe;gBAClB;YACF;QACF;QACA,aAAa,OAAO,OAAO;IAC7B;IACA,MAAM,6BAA6B,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,CAAA;QAChD,IAAI;QACJ,MAAM,WAAW;YACf,IAAI;YACJ,oBAAoB;YACpB,CAAC,cAAc,CAAA,GAAA,2UAAA,CAAA,YAAW,AAAD,EAAE,MAAM,KAAK,QAAQ,YAAY,mBAAmB,CAAC,mBAAmB;QACnG;QACA,CAAC,cAAc,CAAA,GAAA,2UAAA,CAAA,YAAW,AAAD,EAAE,MAAM,KAAK,QAAQ,YAAY,gBAAgB,CAAC,mBAAmB;IAChG;IACA,6WAAM,SAAS,CAAC;QACd,IAAI,CAAC,QAAQ,CAAC,SAAS;YACrB;QACF;QACA,QAAQ,OAAO,CAAC,kBAAkB,GAAG;QACrC,QAAQ,OAAO,CAAC,qBAAqB,GAAG;QACxC,IAAI,qBAAqB,CAAC;QAC1B,SAAS,SAAS,KAAK;YACrB,aAAa,OAAO,OAAO;QAC7B;QACA,SAAS;YACP,OAAO,YAAY,CAAC;YACpB,eAAe,OAAO,GAAG;QAC3B;QACA,SAAS;YACP,qEAAqE;YACrE,uDAAuD;YACvD,iDAAiD;YACjD,qBAAqB,OAAO,UAAU,CAAC;gBACrC,eAAe,OAAO,GAAG;YAC3B,GACA,qEAAqE;YACrE,mDAAmD;YACnD,CAAA,GAAA,2PAAA,CAAA,WAAQ,AAAD,MAAM,IAAI;QACnB;QACA,MAAM,MAAM,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE,SAAS,QAAQ;QAC3C,IAAI,WAAW;YACb,IAAI,gBAAgB,CAAC,WAAW,mBAAmB,8BAA8B,sBAAsB;YACvG,IAAI,gBAAgB,CAAC,oBAAoB;YACzC,IAAI,gBAAgB,CAAC,kBAAkB;QACzC;QACA,gBAAgB,IAAI,gBAAgB,CAAC,mBAAmB,sBAAsB,6BAA6B,qBAAqB;QAChI,IAAI,YAAY,EAAE;QAClB,IAAI,gBAAgB;YAClB,IAAI,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,SAAS,YAAY,GAAG;gBACpC,YAAY,CAAA,GAAA,2PAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,YAAY;YACxD;YACA,IAAI,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,SAAS,QAAQ,GAAG;gBAChC,YAAY,UAAU,MAAM,CAAC,CAAA,GAAA,2PAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,QAAQ;YACrE;YACA,IAAI,CAAC,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,SAAS,SAAS,KAAK,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,cAAc,EAAE;gBAC7F,YAAY,UAAU,MAAM,CAAC,CAAA,GAAA,2PAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,SAAS,CAAC,cAAc;YACrF;QACF;QAEA,wEAAwE;QACxE,YAAY,UAAU,MAAM,CAAC,CAAA;YAC3B,IAAI;YACJ,OAAO,aAAa,CAAC,CAAC,mBAAmB,IAAI,WAAW,KAAK,OAAO,KAAK,IAAI,iBAAiB,cAAc;QAC9G;QACA,UAAU,OAAO,CAAC,CAAA;YAChB,SAAS,gBAAgB,CAAC,UAAU,UAAU;gBAC5C,SAAS;YACX;QACF;QACA,OAAO;YACL,IAAI,WAAW;gBACb,IAAI,mBAAmB,CAAC,WAAW,mBAAmB,8BAA8B,sBAAsB;gBAC1G,IAAI,mBAAmB,CAAC,oBAAoB;gBAC5C,IAAI,mBAAmB,CAAC,kBAAkB;YAC5C;YACA,gBAAgB,IAAI,mBAAmB,CAAC,mBAAmB,sBAAsB,6BAA6B,qBAAqB;YACnI,UAAU,OAAO,CAAC,CAAA;gBAChB,SAAS,mBAAmB,CAAC,UAAU;YACzC;YACA,OAAO,YAAY,CAAC;QACtB;IACF,GAAG;QAAC;QAAS;QAAU;QAAW;QAAc;QAAmB;QAAM;QAAc;QAAgB;QAAS;QAAkB;QAAqB;QAAsB;QAAkB;QAA6B;QAAqB;QAAqB;KAA2B;IACjS,6WAAM,SAAS,CAAC;QACd,QAAQ,OAAO,CAAC,eAAe,GAAG;IACpC,GAAG;QAAC;QAAS;QAAc;KAAkB;IAC7C,MAAM,YAAY,6WAAM,OAAO,CAAC,IAAM,CAAC;YACrC,WAAW;YACX,GAAI,kBAAkB;gBACpB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,EAAE,CAAA;oBACxC,aAAa,OAAO,MAAM,WAAW,EAAE;gBACzC;gBACA,GAAI,wBAAwB,WAAW;oBACrC,SAAQ,KAAK;wBACX,aAAa,OAAO,MAAM,WAAW,EAAE;oBACzC;gBACF,CAAC;YACH,CAAC;QACH,CAAC,GAAG;QAAC;QAAsB;QAAc;QAAgB;KAAoB;IAC7E,MAAM,WAAW,6WAAM,OAAO,CAAC,IAAM,CAAC;YACpC,WAAW;YACX;gBACE,wBAAwB,OAAO,GAAG;YACpC;YACA;gBACE,wBAAwB,OAAO,GAAG;YACpC;YACA,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,EAAE;gBACvC,QAAQ,OAAO,CAAC,eAAe,GAAG;YACpC;YACA;gBACE,IAAI,MAAM;gBACV,kBAAkB;gBAClB,QAAQ,OAAO,CAAC,eAAe,GAAG;gBAClC,eAAe,OAAO,GAAG,OAAO,UAAU,CAAC;oBACzC,QAAQ,OAAO,CAAC,eAAe,GAAG;gBACpC;YACF;QACF,CAAC,GAAG;QAAC;QAAsB;QAAmB;QAAS;KAAK;IAC5D,OAAO,6WAAM,OAAO,CAAC,IAAM,UAAU;YACnC;YACA;QACF,IAAI,CAAC,GAAG;QAAC;QAAS;QAAW;KAAS;AACxC;AAEA,SAAS,uBAAuB,OAAO;IACrC,MAAM,EACJ,OAAO,KAAK,EACZ,cAAc,gBAAgB,EAC9B,UAAU,YAAY,EACvB,GAAG;IACJ,MAAM,aAAa;IACnB,MAAM,UAAU,6WAAM,MAAM,CAAC,CAAC;IAC9B,MAAM,CAAC,OAAO,GAAG,6WAAM,QAAQ,CAAC,IAAM;IACtC,MAAM,SAAS,6BAA6B;IAC5C,wCAA2C;QACzC,MAAM,qBAAqB,aAAa,SAAS;QACjD,IAAI,sBAAsB,CAAC,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,qBAAqB;YACxD,MAAM,qEAAqE,uEAAuE;QACpJ;IACF;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,6WAAM,QAAQ,CAAC,aAAa,SAAS;IACvF,MAAM,eAAe,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,MAAM,OAAO;QAChD,QAAQ,OAAO,CAAC,SAAS,GAAG,OAAO,QAAQ;QAC3C,OAAO,IAAI,CAAC,cAAc;YACxB;YACA;YACA;YACA;QACF;QACA,oBAAoB,QAAQ,iBAAiB,MAAM,OAAO;IAC5D;IACA,MAAM,OAAO,6WAAM,OAAO,CAAC,IAAM,CAAC;YAChC;QACF,CAAC,GAAG,EAAE;IACN,MAAM,WAAW,6WAAM,OAAO,CAAC,IAAM,CAAC;YACpC,WAAW,qBAAqB,aAAa,SAAS,IAAI;YAC1D,UAAU,aAAa,QAAQ,IAAI;YACnC,cAAc,aAAa,SAAS;QACtC,CAAC,GAAG;QAAC;QAAmB,aAAa,SAAS;QAAE,aAAa,QAAQ;KAAC;IACtE,OAAO,6WAAM,OAAO,CAAC,IAAM,CAAC;YAC1B;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAM;QAAc;QAAU;QAAQ;QAAY;KAAK;AAC9D;AAEA;;;CAGC,GACD,SAAS,YAAY,OAAO;IAC1B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,MAAM,EACP,GAAG;IACJ,MAAM,sBAAsB,uBAAuB;QACjD,GAAG,OAAO;QACV,UAAU;YACR,WAAW;YACX,UAAU;YACV,GAAG,QAAQ,QAAQ;QACrB;IACF;IACA,MAAM,cAAc,QAAQ,WAAW,IAAI;IAC3C,MAAM,mBAAmB,YAAY,QAAQ;IAC7C,MAAM,CAAC,eAAe,gBAAgB,GAAG,6WAAM,QAAQ,CAAC;IACxD,MAAM,CAAC,mBAAmB,sBAAsB,GAAG,6WAAM,QAAQ,CAAC;IAClE,MAAM,qBAAqB,oBAAoB,OAAO,KAAK,IAAI,iBAAiB,YAAY;IAC5F,MAAM,eAAe,sBAAsB;IAC3C,MAAM,kBAAkB,6WAAM,MAAM,CAAC;IACrC,MAAM,OAAO;IACb,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,cAAc;YAChB,gBAAgB,OAAO,GAAG;QAC5B;IACF,GAAG;QAAC;KAAa;IACjB,MAAM,WAAW,CAAA,GAAA,qWAAA,CAAA,cAAa,AAAD,EAAE;QAC7B,GAAG,OAAO;QACV,UAAU;YACR,GAAG,gBAAgB;YACnB,GAAI,qBAAqB;gBACvB,WAAW;YACb,CAAC;QACH;IACF;IACA,MAAM,uBAAuB,6WAAM,WAAW,CAAC,CAAA;QAC7C,MAAM,4BAA4B,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YAClD,uBAAuB,IAAM,KAAK,qBAAqB;YACvD,gBAAgB,IAAM,KAAK,cAAc;YACzC,gBAAgB;QAClB,IAAI;QACJ,4FAA4F;QAC5F,2FAA2F;QAC3F,sBAAsB;QACtB,SAAS,IAAI,CAAC,YAAY,CAAC;IAC7B,GAAG;QAAC,SAAS,IAAI;KAAC;IAClB,MAAM,eAAe,6WAAM,WAAW,CAAC,CAAA;QACrC,IAAI,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,SAAS,SAAS,MAAM;YACpC,gBAAgB,OAAO,GAAG;YAC1B,gBAAgB;QAClB;QAEA,uEAAuE;QACvE,sCAAsC;QACtC,IAAI,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,QACtF,uEAAuE;QACvE,kEAAkE;QAClE,gBAAgB;QAChB,SAAS,QAAQ,CAAC,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,OAAO;YACjC,SAAS,IAAI,CAAC,YAAY,CAAC;QAC7B;IACF,GAAG;QAAC,SAAS,IAAI;KAAC;IAClB,MAAM,OAAO,6WAAM,OAAO,CAAC,IAAM,CAAC;YAChC,GAAG,SAAS,IAAI;YAChB;YACA;YACA,cAAc;QAChB,CAAC,GAAG;QAAC,SAAS,IAAI;QAAE;QAAc;KAAqB;IACvD,MAAM,WAAW,6WAAM,OAAO,CAAC,IAAM,CAAC;YACpC,GAAG,SAAS,QAAQ;YACpB,cAAc;QAChB,CAAC,GAAG;QAAC,SAAS,QAAQ;QAAE;KAAa;IACrC,MAAM,UAAU,6WAAM,OAAO,CAAC,IAAM,CAAC;YACnC,GAAG,QAAQ;YACX,GAAG,WAAW;YACd;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAU;QAAM;QAAU;QAAQ;KAAY;IACnD,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,YAAY,OAAO,CAAC,OAAO,CAAC,eAAe,GAAG;QAC9C,MAAM,OAAO,QAAQ,OAAO,KAAK,IAAI,KAAK,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACpF,IAAI,MAAM;YACR,KAAK,OAAO,GAAG;QACjB;IACF;IACA,OAAO,6WAAM,OAAO,CAAC,IAAM,CAAC;YAC1B,GAAG,QAAQ;YACX;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAU;QAAM;QAAU;KAAQ;AACzC;AAEA,SAAS;IACP,OAAO,CAAA,GAAA,2UAAA,CAAA,QAAK,AAAD,OAAO,CAAA,GAAA,2UAAA,CAAA,WAAQ,AAAD;AAC3B;AACA;;;;CAIC,GACD,SAAS,SAAS,OAAO,EAAE,KAAK;IAC9B,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,MAAM,EACN,OAAO,EACP,QAAQ,EACT,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,cAAc,IAAI,EACnB,GAAG;IACJ,MAAM,gBAAgB,6WAAM,MAAM,CAAC;IACnC,MAAM,aAAa,6WAAM,MAAM,CAAC,CAAC;IACjC,MAAM,sBAAsB,6WAAM,MAAM,CAAC;IACzC,6WAAM,SAAS,CAAC;QACd,IAAI,CAAC,SAAS;QACd,MAAM,MAAM,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,SAAS,YAAY;QAE3C,yEAAyE;QACzE,uEAAuE;QACvE,4BAA4B;QAC5B,SAAS;YACP,IAAI,CAAC,QAAQ,CAAA,GAAA,2PAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,YAAY,KAAK,SAAS,YAAY,KAAK,CAAA,GAAA,2UAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE,SAAS,YAAY,IAAI;gBAClI,cAAc,OAAO,GAAG;YAC1B;QACF;QACA,SAAS;YACP,oBAAoB,OAAO,GAAG;QAChC;QACA,SAAS;YACP,oBAAoB,OAAO,GAAG;QAChC;QACA,IAAI,gBAAgB,CAAC,QAAQ;QAC7B,IAAI,eAAe;YACjB,IAAI,gBAAgB,CAAC,WAAW,WAAW;YAC3C,IAAI,gBAAgB,CAAC,eAAe,eAAe;QACrD;QACA,OAAO;YACL,IAAI,mBAAmB,CAAC,QAAQ;YAChC,IAAI,eAAe;gBACjB,IAAI,mBAAmB,CAAC,WAAW,WAAW;gBAC9C,IAAI,mBAAmB,CAAC,eAAe,eAAe;YACxD;QACF;IACF,GAAG;QAAC,SAAS,YAAY;QAAE;QAAM;KAAQ;IACzC,6WAAM,SAAS,CAAC;QACd,IAAI,CAAC,SAAS;QACd,SAAS,aAAa,IAAI;YACxB,IAAI,EACF,MAAM,EACP,GAAG;YACJ,IAAI,WAAW,qBAAqB,WAAW,cAAc;gBAC3D,cAAc,OAAO,GAAG;YAC1B;QACF;QACA,OAAO,EAAE,CAAC,cAAc;QACxB,OAAO;YACL,OAAO,GAAG,CAAC,cAAc;QAC3B;IACF,GAAG;QAAC;QAAQ;KAAQ;IACpB,6WAAM,SAAS,CAAC;QACd,OAAO;YACL,kBAAkB;QACpB;IACF,GAAG,EAAE;IACL,MAAM,YAAY,6WAAM,OAAO,CAAC,IAAM,CAAC;YACrC;gBACE,cAAc,OAAO,GAAG;YAC1B;YACA,SAAQ,KAAK;gBACX,IAAI,cAAc,OAAO,EAAE;gBAC3B,MAAM,SAAS,CAAA,GAAA,2UAAA,CAAA,YAAW,AAAD,EAAE,MAAM,WAAW;gBAC5C,IAAI,eAAe,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,SAAS;oBACpC,gEAAgE;oBAChE,wBAAwB;oBACxB,IAAI,iBAAiB,CAAC,MAAM,aAAa,EAAE;wBACzC,IAAI,CAAC,oBAAoB,OAAO,IAAI,CAAC,CAAA,GAAA,2UAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;4BAC9D;wBACF;oBACF,OAAO,IAAI,CAAC,CAAA,GAAA,2UAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS;wBACvC;oBACF;gBACF;gBACA,aAAa,MAAM,MAAM,WAAW,EAAE;YACxC;YACA,QAAO,KAAK;gBACV,cAAc,OAAO,GAAG;gBACxB,MAAM,gBAAgB,MAAM,aAAa;gBACzC,MAAM,cAAc,MAAM,WAAW;gBAErC,iEAAiE;gBACjE,qDAAqD;gBACrD,MAAM,oBAAoB,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB,cAAc,YAAY,CAAC,gBAAgB,mBAAmB,cAAc,YAAY,CAAC,iBAAiB;gBAEhK,6CAA6C;gBAC7C,WAAW,OAAO,GAAG,OAAO,UAAU,CAAC;oBACrC,IAAI;oBACJ,MAAM,WAAW,CAAA,GAAA,2UAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,YAAY,GAAG,SAAS,YAAY,CAAC,aAAa,GAAG;oBAE7F,qCAAqC;oBACrC,IAAI,CAAC,iBAAiB,aAAa,SAAS,YAAY,EAAE;oBAE1D,iEAAiE;oBACjE,8DAA8D;oBAC9D,oDAAoD;oBACpD,mEAAmE;oBACnE,wEAAwE;oBACxE,wEAAwE;oBACxE,wBAAwB;oBACxB,IAAI,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,CAAC,wBAAwB,QAAQ,OAAO,CAAC,eAAe,KAAK,OAAO,KAAK,IAAI,sBAAsB,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,aAAa,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,SAAS,YAAY,EAAE,aAAa,mBAAmB;wBACtN;oBACF;oBACA,aAAa,OAAO,aAAa;gBACnC;YACF;QACF,CAAC,GAAG;QAAC;QAAS,SAAS,YAAY;QAAE;QAAc;KAAY;IAC/D,OAAO,6WAAM,OAAO,CAAC,IAAM,UAAU;YACnC;QACF,IAAI,CAAC,GAAG;QAAC;QAAS;KAAU;AAC9B;AAEA,SAAS,WAAW,SAAS,EAAE,SAAS,EAAE,UAAU;IAClD,MAAM,MAAM,IAAI;IAChB,MAAM,SAAS,eAAe;IAC9B,IAAI,eAAe;IACnB,IAAI,UAAU,WAAW;QACvB,MAAM,EACJ,CAAC,WAAW,EAAE,CAAC,EACf,CAAC,aAAa,EAAE,EAAE,EAClB,GAAG,YACJ,GAAG;QACJ,eAAe;IACjB;IACA,OAAO;QACL,GAAI,eAAe,cAAc;YAC/B,UAAU,CAAC;YACX,CAAC,oBAAoB,EAAE;QACzB,CAAC;QACD,GAAG,YAAY;QACf,GAAG,UAAU,GAAG,CAAC,CAAA;YACf,MAAM,kBAAkB,QAAQ,KAAK,CAAC,WAAW,GAAG;YACpD,IAAI,OAAO,oBAAoB,YAAY;gBACzC,OAAO,YAAY,gBAAgB,aAAa;YAClD;YACA,OAAO;QACT,GAAG,MAAM,CAAC,WAAW,MAAM,CAAC,CAAC,KAAK;YAChC,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YACA,OAAO,OAAO,CAAC,OAAO,OAAO,CAAC,CAAA;gBAC5B,IAAI,CAAC,KAAK,MAAM,GAAG;gBACnB,IAAI,UAAU;oBAAC;oBAAY;iBAAa,CAAC,QAAQ,CAAC,MAAM;oBACtD;gBACF;gBACA,IAAI,IAAI,OAAO,CAAC,UAAU,GAAG;oBAC3B,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM;wBACjB,IAAI,GAAG,CAAC,KAAK,EAAE;oBACjB;oBACA,IAAI,OAAO,UAAU,YAAY;wBAC/B,IAAI;wBACJ,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,SAAS,IAAI,CAAC;wBACnD,GAAG,CAAC,IAAI,GAAG;4BACT,IAAI;4BACJ,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;gCACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;4BAC9B;4BACA,OAAO,CAAC,YAAY,IAAI,GAAG,CAAC,IAAI,KAAK,OAAO,KAAK,IAAI,UAAU,GAAG,CAAC,CAAA,KAAM,MAAM,OAAO,IAAI,CAAC,CAAA,MAAO,QAAQ;wBAC5G;oBACF;gBACF,OAAO;oBACL,GAAG,CAAC,IAAI,GAAG;gBACb;YACF;YACA,OAAO;QACT,GAAG,CAAC,EAAE;IACR;AACF;AACA;;;;;CAKC,GACD,SAAS,gBAAgB,SAAS;IAChC,IAAI,cAAc,KAAK,GAAG;QACxB,YAAY,EAAE;IAChB;IACA,MAAM,gBAAgB,UAAU,GAAG,CAAC,CAAA,MAAO,OAAO,OAAO,KAAK,IAAI,IAAI,SAAS;IAC/E,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,MAAO,OAAO,OAAO,KAAK,IAAI,IAAI,QAAQ;IAC7E,MAAM,WAAW,UAAU,GAAG,CAAC,CAAA,MAAO,OAAO,OAAO,KAAK,IAAI,IAAI,IAAI;IACrE,MAAM,oBAAoB,6WAAM,WAAW,CAAC,CAAA,YAAa,WAAW,WAAW,WAAW,cAC1F,uDAAuD;IACvD;IACA,MAAM,mBAAmB,6WAAM,WAAW,CAAC,CAAA,YAAa,WAAW,WAAW,WAAW,aACzF,uDAAuD;IACvD;IACA,MAAM,eAAe,6WAAM,WAAW,CAAC,CAAA,YAAa,WAAW,WAAW,WAAW,SACrF,uDAAuD;IACvD;IACA,OAAO,6WAAM,OAAO,CAAC,IAAM,CAAC;YAC1B;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAmB;QAAkB;KAAa;AACzD;AAEA,MAAM,SAAS;AACf,SAAS,SAAS,WAAW,EAAE,QAAQ,EAAE,UAAU;IACjD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,YAAY;IACvB;AACF;AACA,SAAS,qBAAqB,GAAG,EAAE,WAAW;IAC5C,MAAM,WAAW,QAAQ,YAAY,QAAQ;IAC7C,MAAM,aAAa,QAAQ,cAAc,QAAQ;IACjD,OAAO,SAAS,aAAa,UAAU;AACzC;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW,EAAE,GAAG;IACtD,MAAM,WAAW,QAAQ;IACzB,MAAM,aAAa,MAAM,QAAQ,aAAa,QAAQ;IACtD,OAAO,SAAS,aAAa,UAAU,eAAe,QAAQ,WAAW,QAAQ,OAAO,QAAQ;AAClG;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW,EAAE,GAAG;IACtD,MAAM,WAAW,MAAM,QAAQ,aAAa,QAAQ;IACpD,MAAM,aAAa,QAAQ;IAC3B,OAAO,SAAS,aAAa,UAAU;AACzC;AACA,SAAS,2BAA2B,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE,IAAI;IAC7D,MAAM,WAAW,MAAM,QAAQ,cAAc,QAAQ;IACrD,MAAM,aAAa,QAAQ;IAC3B,IAAI,gBAAgB,UAAU,gBAAgB,gBAAgB,QAAQ,OAAO,GAAG;QAC9E,OAAO,QAAQ;IACjB;IACA,OAAO,SAAS,aAAa,UAAU;AACzC;AACA;;;;CAIC,GACD,SAAS,kBAAkB,OAAO,EAAE,KAAK;IACvC,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,UAAU,EACX,GAAG;IACJ,MAAM,EACJ,OAAO,EACP,WAAW,EACX,YAAY,sBAAsB,KAAO,CAAC,EAC1C,UAAU,IAAI,EACd,gBAAgB,IAAI,EACpB,cAAc,KAAK,EACnB,OAAO,KAAK,EACZ,SAAS,KAAK,EACd,MAAM,KAAK,EACX,UAAU,KAAK,EACf,kBAAkB,MAAM,EACxB,mBAAmB,IAAI,EACvB,qBAAqB,IAAI,EACzB,kBAAkB,SAAS,EAC3B,cAAc,UAAU,EACxB,iBAAiB,EACjB,OAAO,CAAC,EACR,qBAAqB,IAAI,EACzB,cAAc,EACd,SAAS,EACT,QAAQ,KAAK,EACd,GAAG;IACJ,wCAA2C;QACzC,IAAI,aAAa;YACf,IAAI,CAAC,MAAM;gBACT,KAAK;YACP;YACA,IAAI,CAAC,SAAS;gBACZ,KAAK;YACP;QACF;QACA,IAAI,gBAAgB,cAAc,OAAO,GAAG;YAC1C,KAAK,uEAAuE;QAC9E;IACF;IACA,MAAM,uBAAuB,CAAA,GAAA,2UAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,QAAQ;IACtE,MAAM,0BAA0B,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IAC7C,MAAM,WAAW;IACjB,MAAM,OAAO;IACb,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,QAAQ,OAAO,CAAC,OAAO,CAAC,WAAW,GAAG;IACxC,GAAG;QAAC;QAAS;KAAY;IACzB,MAAM,aAAa,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE;QAChC,oBAAoB,SAAS,OAAO,KAAK,CAAC,IAAI,OAAO,SAAS,OAAO;IACvE;IACA,MAAM,4BAA4B,CAAA,GAAA,2UAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,YAAY;IAC1E,MAAM,qBAAqB,6WAAM,MAAM,CAAC;IACxC,MAAM,WAAW,6WAAM,MAAM,CAAC,iBAAiB,OAAO,gBAAgB,CAAC;IACvE,MAAM,SAAS,6WAAM,MAAM,CAAC;IAC5B,MAAM,uBAAuB,6WAAM,MAAM,CAAC;IAC1C,MAAM,wBAAwB,6WAAM,MAAM,CAAC;IAC3C,MAAM,qBAAqB,6WAAM,MAAM,CAAC,CAAC,CAAC,SAAS,QAAQ;IAC3D,MAAM,kBAAkB,6WAAM,MAAM,CAAC;IACrC,MAAM,oBAAoB,6WAAM,MAAM,CAAC;IACvC,MAAM,yBAAyB,6WAAM,MAAM,CAAC;IAC5C,MAAM,qBAAqB,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IACxC,MAAM,gBAAgB,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IACnC,MAAM,wBAAwB,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IAC3C,MAAM,mBAAmB,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IACtC,MAAM,CAAC,UAAU,YAAY,GAAG,6WAAM,QAAQ;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,6WAAM,QAAQ;IAChD,MAAM,YAAY,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE;QAC/B,SAAS,SAAS,IAAI;YACpB,IAAI,SAAS;gBACX,IAAI;gBACJ,IAAI,CAAC,WAAW,KAAK,EAAE,KAAK,QAAQ,SAAS,QAAQ,CAAC,gBAAgB;oBACpE,KAAK,EAAE,GAAG,aAAa,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG;gBACnE;gBACA,YAAY,KAAK,EAAE;gBACnB,QAAQ,QAAQ,KAAK,MAAM,CAAC,IAAI,CAAC,gBAAgB;gBACjD,IAAI,gBAAgB;oBAClB,eAAe,OAAO,GAAG;gBAC3B;YACF,OAAO;gBACL,aAAa,MAAM;oBACjB,MAAM,kBAAkB,OAAO;oBAC/B,eAAe;gBACjB;YACF;QACF;QACA,MAAM,cAAc,QAAQ,OAAO,CAAC,SAAS,OAAO,CAAC;QACrD,MAAM,sBAAsB,uBAAuB,OAAO;QAC1D,IAAI,aAAa;YACf,SAAS;QACX;QACA,MAAM,YAAY,kBAAkB,OAAO,GAAG,CAAA,IAAK,MAAM;QACzD,UAAU;YACR,MAAM,aAAa,QAAQ,OAAO,CAAC,SAAS,OAAO,CAAC,IAAI;YACxD,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,aAAa;gBAChB,SAAS;YACX;YACA,MAAM,wBAAwB,sBAAsB,OAAO;YAC3D,MAAM,uBAAuB,yBAAyB,QAAQ,CAAC,uBAAuB,CAAC,qBAAqB,OAAO;YACnH,IAAI,sBAAsB;gBACxB,sEAAsE;gBACtE,mBAAmB;gBACnB,WAAW,cAAc,IAAI,QAAQ,WAAW,cAAc,CAAC,OAAO,0BAA0B,YAAY;oBAC1G,OAAO;oBACP,QAAQ;gBACV,IAAI;YACN;QACF;IACF;IAEA,yEAAyE;IACzE,wEAAwE;IACxE,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,CAAC,SAAS;QACd,IAAI,QAAQ,SAAS,QAAQ,EAAE;YAC7B,IAAI,mBAAmB,OAAO,IAAI,iBAAiB,MAAM;gBACvD,qEAAqE;gBACrE,4DAA4D;gBAC5D,uBAAuB,OAAO,GAAG;gBACjC,SAAS,OAAO,GAAG;gBACnB;YACF;QACF,OAAO,IAAI,mBAAmB,OAAO,EAAE;YACrC,wDAAwD;YACxD,0DAA0D;YAC1D,2CAA2C;YAC3C,SAAS,OAAO,GAAG,CAAC;YACpB,sBAAsB,OAAO;QAC/B;IACF,GAAG;QAAC;QAAS;QAAM,SAAS,QAAQ;QAAE;QAAe;KAAW;IAEhE,0EAA0E;IAC1E,QAAQ;IACR,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,MAAM;QACX,IAAI,CAAC,SAAS,QAAQ,EAAE;QACxB,IAAI,eAAe,MAAM;YACvB,kBAAkB,OAAO,GAAG;YAC5B,IAAI,iBAAiB,OAAO,IAAI,MAAM;gBACpC;YACF;YAEA,qEAAqE;YACrE,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,SAAS,OAAO,GAAG,CAAC;gBACpB;YACF;YAEA,gBAAgB;YAChB,IAAI,CAAC,CAAC,gBAAgB,OAAO,IAAI,CAAC,mBAAmB,OAAO,KAAK,mBAAmB,OAAO,IAAI,CAAC,OAAO,OAAO,IAAI,QAAQ,mBAAmB,OAAO,KAAK,QAAQ,OAAO,OAAO,IAAI,IAAI,GAAG;gBACxL,IAAI,OAAO;gBACX,MAAM,uBAAuB;oBAC3B,IAAI,QAAQ,OAAO,CAAC,EAAE,IAAI,MAAM;wBAC9B,gEAAgE;wBAChE,gEAAgE;wBAChE,sBAAsB;wBACtB,IAAI,OAAO,GAAG;4BACZ,MAAM,YAAY,OAAO,wBAAwB;4BACjD,UAAU;wBACZ;wBACA;oBACF,OAAO;wBACL,SAAS,OAAO,GAAG,OAAO,OAAO,IAAI,QAAQ,0BAA0B,OAAO,OAAO,EAAE,aAAa,QAAQ,SAAS,CAAA,GAAA,2UAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,mBAAmB,OAAO,IAAI,CAAA,GAAA,2UAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,mBAAmB,OAAO;wBAC/N,OAAO,OAAO,GAAG;wBACjB;oBACF;gBACF;gBACA;YACF;QACF,OAAO,IAAI,CAAC,CAAA,GAAA,2UAAA,CAAA,yBAAsB,AAAD,EAAE,SAAS,cAAc;YACxD,SAAS,OAAO,GAAG;YACnB;YACA,uBAAuB,OAAO,GAAG;QACnC;IACF,GAAG;QAAC;QAAS;QAAM,SAAS,QAAQ;QAAE;QAAa;QAAkB;QAAQ;QAAS;QAAa;QAAK;QAAY;QAAW;KAAmB;IAElJ,0EAA0E;IAC1E,4EAA4E;IAC5E,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI;QACJ,IAAI,CAAC,WAAW,SAAS,QAAQ,IAAI,CAAC,QAAQ,WAAW,CAAC,mBAAmB,OAAO,EAAE;YACpF;QACF;QACA,MAAM,QAAQ,KAAK,QAAQ,CAAC,OAAO;QACnC,MAAM,SAAS,CAAC,cAAc,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,KAAK,QAAQ,CAAC,cAAc,YAAY,OAAO,KAAK,OAAO,KAAK,IAAI,YAAY,QAAQ,CAAC,QAAQ;QACvK,MAAM,WAAW,CAAA,GAAA,2UAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,2UAAA,CAAA,cAAa,AAAD,EAAE,SAAS,QAAQ;QAC9D,MAAM,uBAAuB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,IAAI,CAAA,GAAA,2UAAA,CAAA,WAAU,AAAD,EAAE,KAAK,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE;QAC3G,IAAI,UAAU,CAAC,wBAAwB,qBAAqB,OAAO,EAAE;YACnE,OAAO,KAAK,CAAC;gBACX,eAAe;YACjB;QACF;IACF,GAAG;QAAC;QAAS,SAAS,QAAQ;QAAE;QAAM;QAAU;KAAQ;IACxD,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,MAAM;QACX,IAAI,CAAC,SAAS;QACd,IAAI,UAAU;QACd,SAAS,mBAAmB,IAAI;YAC9B,aAAa,KAAK,EAAE;YACpB,IAAI,gBAAgB;gBAClB,eAAe,OAAO,GAAG;YAC3B;QACF;QACA,KAAK,MAAM,CAAC,EAAE,CAAC,gBAAgB;QAC/B,OAAO;YACL,KAAK,MAAM,CAAC,GAAG,CAAC,gBAAgB;QAClC;IACF,GAAG;QAAC;QAAS;QAAM;QAAS;QAAU;KAAe;IACrD,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,sBAAsB,OAAO,GAAG;QAChC,gBAAgB,OAAO,GAAG;QAC1B,mBAAmB,OAAO,GAAG,CAAC,CAAC,SAAS,QAAQ;IAClD;IACA,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,CAAC,MAAM;YACT,OAAO,OAAO,GAAG;YACjB,mBAAmB,OAAO,GAAG;QAC/B;IACF,GAAG;QAAC;QAAM;KAAgB;IAC1B,MAAM,iBAAiB,eAAe;IACtC,MAAM,OAAO,6WAAM,OAAO,CAAC;QACzB,SAAS,kBAAkB,aAAa;YACtC,IAAI,CAAC,cAAc,OAAO,EAAE;YAC5B,MAAM,QAAQ,QAAQ,OAAO,CAAC,OAAO,CAAC;YACtC,IAAI,UAAU,CAAC,KAAK,SAAS,OAAO,KAAK,OAAO;gBAC9C,SAAS,OAAO,GAAG;gBACnB;YACF;QACF;QACA,MAAM,QAAQ;YACZ,SAAQ,IAAI;gBACV,IAAI,EACF,aAAa,EACd,GAAG;gBACJ,kBAAkB,OAAO,GAAG;gBAC5B,kBAAkB;YACpB;YACA,SAAS,CAAA;gBACP,IAAI,EACF,aAAa,EACd,GAAG;gBACJ,OAAO,cAAc,KAAK,CAAC;oBACzB,eAAe;gBACjB;YACF;YACA,SAAS;YACT,GAAI,oBAAoB;gBACtB,aAAY,KAAK;oBACf,IAAI,EACF,aAAa,EACd,GAAG;oBACJ,kBAAkB,OAAO,GAAG;oBAC5B,uBAAuB,OAAO,GAAG;oBACjC,kBAAkB;gBACpB;gBACA,gBAAe,KAAK;oBAClB,IAAI,EACF,WAAW,EACZ,GAAG;oBACJ,IAAI,CAAC,qBAAqB,OAAO,IAAI,gBAAgB,SAAS;wBAC5D;oBACF;oBACA,kBAAkB,OAAO,GAAG;oBAC5B,SAAS,OAAO,GAAG,CAAC;oBACpB;oBACA,IAAI,CAAC,SAAS;wBACZ,IAAI;wBACJ,CAAC,wBAAwB,wBAAwB,OAAO,KAAK,QAAQ,sBAAsB,KAAK,CAAC;4BAC/F,eAAe;wBACjB;oBACF;gBACF;YACF,CAAC;QACH;QACA,OAAO;IACT,GAAG;QAAC;QAAe;QAAyB;QAAkB;QAAS;QAAY;KAAQ;IAC3F,MAAM,uBAAuB,6WAAM,WAAW,CAAC;QAC7C,IAAI;QACJ,OAAO,qBAAqB,OAAO,oBAAoB,QAAQ,QAAQ,CAAC,wBAAwB,KAAK,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,KAAK,QAAQ,CAAC,wBAAwB,sBAAsB,OAAO,KAAK,QAAQ,CAAC,wBAAwB,sBAAsB,OAAO,KAAK,OAAO,KAAK,IAAI,sBAAsB,OAAO,CAAC,WAAW;IAC9V,GAAG;QAAC;QAAU;QAAM;KAAkB;IACtC,MAAM,kBAAkB,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,CAAA;QACrC,qBAAqB,OAAO,GAAG;QAC/B,kBAAkB,OAAO,GAAG;QAE5B,2EAA2E;QAC3E,0EAA0E;QAC1E,2EAA2E;QAC3E,wDAAwD;QACxD,IAAI,MAAM,KAAK,KAAK,KAAK;YACvB;QACF;QAEA,0EAA0E;QAC1E,0EAA0E;QAC1E,wDAAwD;QACxD,IAAI,CAAC,cAAc,OAAO,IAAI,MAAM,aAAa,KAAK,wBAAwB,OAAO,EAAE;YACrF;QACF;QACA,IAAI,UAAU,2BAA2B,MAAM,GAAG,EAAE,aAAa,KAAK,OAAO;YAC3E,oEAAoE;YACpE,kEAAkE;YAClE,IAAI,CAAC,qBAAqB,MAAM,GAAG,EAAE,yBAAyB;gBAC5D,CAAA,GAAA,2UAAA,CAAA,YAAS,AAAD,EAAE;YACZ;YACA,aAAa,OAAO,MAAM,WAAW,EAAE;YACvC,IAAI,CAAA,GAAA,2PAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,YAAY,GAAG;gBACxC,IAAI,SAAS;oBACX,QAAQ,QAAQ,KAAK,MAAM,CAAC,IAAI,CAAC,gBAAgB,SAAS,YAAY;gBACxE,OAAO;oBACL,SAAS,YAAY,CAAC,KAAK;gBAC7B;YACF;YACA;QACF;QACA,MAAM,eAAe,SAAS,OAAO;QACrC,MAAM,WAAW,CAAA,GAAA,2UAAA,CAAA,kBAAe,AAAD,EAAE,SAAS;QAC1C,MAAM,WAAW,CAAA,GAAA,2UAAA,CAAA,kBAAe,AAAD,EAAE,SAAS;QAC1C,IAAI,CAAC,2BAA2B;YAC9B,IAAI,MAAM,GAAG,KAAK,QAAQ;gBACxB,CAAA,GAAA,2UAAA,CAAA,YAAS,AAAD,EAAE;gBACV,SAAS,OAAO,GAAG;gBACnB;YACF;YACA,IAAI,MAAM,GAAG,KAAK,OAAO;gBACvB,CAAA,GAAA,2UAAA,CAAA,YAAS,AAAD,EAAE;gBACV,SAAS,OAAO,GAAG;gBACnB;YACF;QACF;QAEA,mBAAmB;QACnB,IAAI,OAAO,GAAG;YACZ,MAAM,QAAQ,aAAa,MAAM,IAAI,CAAC;gBACpC,QAAQ,QAAQ,OAAO,CAAC,MAAM;YAChC,GAAG,IAAM,CAAC;oBACR,OAAO;oBACP,QAAQ;gBACV,CAAC;YACD,uEAAuE;YACvE,+DAA+D;YAC/D,MAAM,UAAU,CAAA,GAAA,2UAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,MAAM;YAC/C,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAA,QAAS,SAAS,QAAQ,CAAC,CAAA,GAAA,2UAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,OAAO;YACtG,qBAAqB;YACrB,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,YAAY,OAAO,YAAc,SAAS,QAAQ,CAAC,CAAA,GAAA,2UAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,OAAO,mBAAmB,YAAY,YAAY,CAAC;YACxK,MAAM,QAAQ,OAAO,CAAC,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;gBAC1C,SAAS,QAAQ,GAAG,CAAC,CAAA,YAAa,aAAa,OAAO,QAAQ,OAAO,CAAC,UAAU,GAAG;YACrF,GAAG;gBACD;gBACA;gBACA;gBACA;gBACA;gBACA,gEAAgE;gBAChE,uBAAuB;gBACvB,iBAAiB,CAAA,GAAA,2UAAA,CAAA,qBAAkB,AAAD,EAAE;uBAAK,CAAC,OAAO,oBAAoB,aAAa,kBAAkB,IAAI,KAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,QAAU,CAAA,GAAA,2UAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,OAAO,mBAAmB,QAAQ;oBAAa;iBAAU,EAAE;gBACvO,UAAU;gBACV,UAAU;gBACV,WAAW,CAAA,GAAA,2UAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS,OAAO,GAAG,WAAW,WAAW,SAAS,OAAO,EAAE,OAAO,SAAS,MAC/G,0DAA0D;gBAC1D,8DAA8D;gBAC9D,8BAA8B;gBAC9B,MAAM,GAAG,KAAK,aAAa,OAAO,MAAM,GAAG,KAAK,CAAC,MAAM,aAAa,WAAW,IAAI,OAAO;gBAC1F,WAAW;YACb,GAAG;YACH,IAAI,SAAS,MAAM;gBACjB,SAAS,OAAO,GAAG;gBACnB;YACF;YACA,IAAI,gBAAgB,QAAQ;gBAC1B;YACF;QACF;QACA,IAAI,qBAAqB,MAAM,GAAG,EAAE,cAAc;YAChD,CAAA,GAAA,2UAAA,CAAA,YAAS,AAAD,EAAE;YAEV,yCAAyC;YACzC,IAAI,QAAQ,CAAC,WAAW,CAAA,GAAA,2UAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,aAAa,CAAC,aAAa,MAAM,MAAM,aAAa,EAAE;gBAChG,SAAS,OAAO,GAAG,0BAA0B,MAAM,GAAG,EAAE,aAAa,OAAO,WAAW;gBACvF;gBACA;YACF;YACA,IAAI,0BAA0B,MAAM,GAAG,EAAE,aAAa,MAAM;gBAC1D,IAAI,MAAM;oBACR,SAAS,OAAO,GAAG,gBAAgB,WAAW,eAAe,iBAAiB,QAAQ,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAA,GAAA,2UAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS;wBACvJ,eAAe;wBACf;oBACF;gBACF,OAAO;oBACL,SAAS,OAAO,GAAG,KAAK,GAAG,CAAC,UAAU,CAAA,GAAA,2UAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS;wBACtE,eAAe;wBACf;oBACF;gBACF;YACF,OAAO;gBACL,IAAI,MAAM;oBACR,SAAS,OAAO,GAAG,gBAAgB,WAAW,eAAe,iBAAiB,CAAC,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,WAAW,CAAA,GAAA,2UAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS;wBACvJ,eAAe;wBACf,WAAW;wBACX;oBACF;gBACF,OAAO;oBACL,SAAS,OAAO,GAAG,KAAK,GAAG,CAAC,UAAU,CAAA,GAAA,2UAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS;wBACtE,eAAe;wBACf,WAAW;wBACX;oBACF;gBACF;YACF;YACA,IAAI,CAAA,GAAA,2UAAA,CAAA,yBAAsB,AAAD,EAAE,SAAS,SAAS,OAAO,GAAG;gBACrD,SAAS,OAAO,GAAG,CAAC;YACtB;YACA;QACF;IACF;IACA,MAAM,2BAA2B,6WAAM,OAAO,CAAC;QAC7C,OAAO,WAAW,QAAQ,kBAAkB;YAC1C,yBAAyB,aAAa;QACxC;IACF,GAAG;QAAC;QAAS;QAAM;QAAgB;QAAW;KAAS;IACvD,MAAM,WAAW,6WAAM,OAAO,CAAC;QAC7B,OAAO;YACL,oBAAoB,gBAAgB,SAAS,YAAY;YACzD,GAAI,CAAC,4BAA4B,2BAA2B,CAAC,CAAC;YAC9D,WAAW;YACX;gBACE,qBAAqB,OAAO,GAAG;YACjC;QACF;IACF,GAAG;QAAC;QAA0B;QAAiB;QAAa;KAA0B;IACtF,MAAM,YAAY,6WAAM,OAAO,CAAC;QAC9B,SAAS,kBAAkB,KAAK;YAC9B,IAAI,oBAAoB,UAAU,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW,GAAG;gBACnE,mBAAmB,OAAO,GAAG;YAC/B;QACF;QACA,SAAS,oBAAoB,KAAK;YAChC,sEAAsE;YACtE,mBAAmB,OAAO,GAAG;YAC7B,IAAI,oBAAoB,UAAU,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,WAAW,GAAG;gBAC1E,mBAAmB,OAAO,GAAG;YAC/B;QACF;QACA,OAAO;YACL,GAAG,wBAAwB;YAC3B,WAAU,KAAK;gBACb,qBAAqB,OAAO,GAAG;gBAC/B,MAAM,aAAa,MAAM,GAAG,CAAC,UAAU,CAAC;gBACxC,MAAM,iBAAiB;oBAAC;oBAAQ;iBAAM,CAAC,QAAQ,CAAC,MAAM,GAAG;gBACzD,MAAM,YAAY,cAAc;gBAChC,MAAM,iBAAiB,0BAA0B,MAAM,GAAG,EAAE,aAAa;gBACzE,MAAM,kBAAkB,2BAA2B,MAAM,GAAG,EAAE,aAAa,KAAK;gBAChF,MAAM,uBAAuB,0BAA0B,MAAM,GAAG,EAAE,wBAAwB;gBAC1F,MAAM,YAAY,qBAAqB,MAAM,GAAG,EAAE;gBAClD,MAAM,kBAAkB,CAAC,SAAS,uBAAuB,SAAS,KAAK,MAAM,GAAG,KAAK,WAAW,MAAM,GAAG,CAAC,IAAI,OAAO;gBACrH,IAAI,WAAW,MAAM;oBACnB,MAAM,WAAW,QAAQ,OAAO,KAAK,IAAI,KAAK,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,IAAI;oBAC7F,MAAM,cAAc,QAAQ,WAAW,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI;oBAC5F,IAAI,aAAa,eAAe,gBAAgB;wBAC9C,MAAM,cAAc,IAAI,cAAc,WAAW;4BAC/C,KAAK,MAAM,GAAG;4BACd,SAAS;wBACX;wBACA,IAAI,kBAAkB,iBAAiB;4BACrC,IAAI,sBAAsB;4BAC1B,MAAM,kBAAkB,CAAC,CAAC,uBAAuB,YAAY,OAAO,KAAK,OAAO,KAAK,IAAI,qBAAqB,QAAQ,CAAC,YAAY,MAAM,MAAM,aAAa;4BAC5J,MAAM,eAAe,mBAAmB,CAAC,kBAAkB,CAAC,wBAAwB,YAAY,OAAO,KAAK,OAAO,KAAK,IAAI,sBAAsB,QAAQ,CAAC,YAAY,GAAG,iBAAiB,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAA,OAAQ,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,MAAM,YAAY;4BAC1Q,IAAI,cAAc;gCAChB,CAAA,GAAA,2UAAA,CAAA,YAAS,AAAD,EAAE;gCACV,aAAa,aAAa,CAAC;gCAC3B,aAAa;4BACf;wBACF;wBACA,IAAI,CAAC,aAAa,cAAc,KAAK,YAAY,OAAO,EAAE;4BACxD,IAAI,YAAY,OAAO,CAAC,IAAI,IAAI,YAAY,QAAQ,IAAI,MAAM,aAAa,KAAK,YAAY,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE;gCACzH,IAAI;gCACJ,CAAA,GAAA,2UAAA,CAAA,YAAS,AAAD,EAAE;gCACV,CAAC,wBAAwB,YAAY,OAAO,CAAC,QAAQ,CAAC,YAAY,KAAK,QAAQ,sBAAsB,aAAa,CAAC;gCACnH;4BACF;wBACF;oBACF;oBACA,OAAO,gBAAgB;gBACzB;gBACA,iEAAiE;gBACjE,2CAA2C;gBAC3C,IAAI,CAAC,QAAQ,CAAC,sBAAsB,YAAY;oBAC9C;gBACF;gBACA,IAAI,iBAAiB;oBACnB,MAAM,kBAAkB,qBAAqB,MAAM,GAAG,EAAE;oBACxD,OAAO,OAAO,GAAG,UAAU,kBAAkB,OAAO,MAAM,GAAG;gBAC/D;gBACA,IAAI,QAAQ;oBACV,IAAI,sBAAsB;wBACxB,CAAA,GAAA,2UAAA,CAAA,YAAS,AAAD,EAAE;wBACV,IAAI,MAAM;4BACR,SAAS,OAAO,GAAG,CAAA,GAAA,2UAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,mBAAmB,OAAO;4BACtE;wBACF,OAAO;4BACL,aAAa,MAAM,MAAM,WAAW,EAAE;wBACxC;oBACF;oBACA;gBACF;gBACA,IAAI,WAAW;oBACb,IAAI,iBAAiB,MAAM;wBACzB,SAAS,OAAO,GAAG;oBACrB;oBACA,CAAA,GAAA,2UAAA,CAAA,YAAS,AAAD,EAAE;oBACV,IAAI,CAAC,QAAQ,oBAAoB;wBAC/B,aAAa,MAAM,MAAM,WAAW,EAAE;oBACxC,OAAO;wBACL,gBAAgB;oBAClB;oBACA,IAAI,MAAM;wBACR;oBACF;gBACF;YACF;YACA;gBACE,IAAI,QAAQ,CAAC,SAAS;oBACpB,SAAS,OAAO,GAAG,CAAC;oBACpB;gBACF;YACF;YACA,eAAe;YACf,gBAAgB;YAChB,aAAa;YACb,SAAS;QACX;IACF,GAAG;QAAC;QAAU;QAA0B;QAAM;QAAiB;QAAoB;QAAiB;QAAS;QAAQ;QAAY;QAAc;QAAM;QAAoB;QAAa;QAAsB;QAAK;QAAe;QAAM;QAAS;KAAe;IAC9P,OAAO,6WAAM,OAAO,CAAC,IAAM,UAAU;YACnC;YACA;YACA;QACF,IAAI,CAAC,GAAG;QAAC;QAAS;QAAW;QAAU;KAAK;AAC9C;AAEA,MAAM,6BAA6B,WAAW,GAAE,IAAI,IAAI;IAAC;QAAC;QAAU;KAAU;IAAE;QAAC;QAAY;KAAU;IAAE;QAAC;QAAS;KAAM;CAAC;AAE1H;;;;CAIC,GACD,SAAS,QAAQ,OAAO,EAAE,KAAK;IAC7B,IAAI,uBAAuB;IAC3B,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,QAAQ,EACR,YAAY,iBAAiB,EAC9B,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,OAAO,QAAQ,EAChB,GAAG;IACJ,MAAM,qBAAqB;IAC3B,MAAM,cAAc,CAAC,CAAC,wBAAwB,SAAS,YAAY,KAAK,OAAO,KAAK,IAAI,sBAAsB,EAAE,KAAK;IACrH,MAAM,aAAa,6WAAM,OAAO,CAAC;QAC/B,IAAI;QACJ,OAAO,CAAC,CAAC,wBAAwB,CAAA,GAAA,2UAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,QAAQ,CAAC,KAAK,OAAO,KAAK,IAAI,sBAAsB,EAAE,KAAK;IAC/H,GAAG;QAAC,SAAS,QAAQ;QAAE;KAAkB;IACzC,MAAM,WAAW,CAAC,wBAAwB,2BAA2B,GAAG,CAAC,KAAK,KAAK,OAAO,wBAAwB;IAClH,MAAM,WAAW;IACjB,MAAM,WAAW,YAAY;IAC7B,MAAM,YAAY,6WAAM,OAAO,CAAC;QAC9B,IAAI,aAAa,aAAa,SAAS,SAAS;YAC9C,OAAO;gBACL,CAAC,UAAU,CAAC,SAAS,UAAU,eAAe,aAAa,EAAE,EAAE,OAAO,aAAa;YACrF;QACF;QACA,OAAO;YACL,iBAAiB,OAAO,SAAS;YACjC,iBAAiB,aAAa,gBAAgB,WAAW;YACzD,iBAAiB,OAAO,aAAa;YACrC,GAAI,aAAa,aAAa;gBAC5B,MAAM;YACR,CAAC;YACD,GAAI,aAAa,UAAU;gBACzB,IAAI;YACN,CAAC;YACD,GAAI,aAAa,UAAU,YAAY;gBACrC,MAAM;YACR,CAAC;YACD,GAAI,SAAS,YAAY;gBACvB,qBAAqB;YACvB,CAAC;YACD,GAAI,SAAS,cAAc;gBACzB,qBAAqB;YACvB,CAAC;QACH;IACF,GAAG;QAAC;QAAU;QAAY;QAAU;QAAM;QAAa;KAAK;IAC5D,MAAM,WAAW,6WAAM,OAAO,CAAC;QAC7B,MAAM,gBAAgB;YACpB,IAAI;YACJ,GAAI,YAAY;gBACd,MAAM;YACR,CAAC;QACH;QACA,IAAI,aAAa,aAAa,SAAS,SAAS;YAC9C,OAAO;QACT;QACA,OAAO;YACL,GAAG,aAAa;YAChB,GAAI,aAAa,UAAU;gBACzB,mBAAmB;YACrB,CAAC;QACH;IACF,GAAG;QAAC;QAAU;QAAY;QAAa;KAAK;IAC5C,MAAM,OAAO,6WAAM,WAAW,CAAC,CAAA;QAC7B,IAAI,EACF,MAAM,EACN,QAAQ,EACT,GAAG;QACJ,MAAM,cAAc;YAClB,MAAM;YACN,GAAI,UAAU;gBACZ,IAAI,aAAa;YACnB,CAAC;QACH;QAEA,qEAAqE;QACrE,mEAAmE;QACnE,wEAAwE;QACxE,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,GAAG,WAAW;oBACd,iBAAiB,UAAU;gBAC7B;YACF,KAAK;gBACH;oBACE,OAAO;wBACL,GAAG,WAAW;wBACd,iBAAiB;oBACnB;gBACF;QACJ;QACA,OAAO,CAAC;IACV,GAAG;QAAC;QAAY;KAAK;IACrB,OAAO,6WAAM,OAAO,CAAC,IAAM,UAAU;YACnC;YACA;YACA;QACF,IAAI,CAAC,GAAG;QAAC;QAAS;QAAW;QAAU;KAAK;AAC9C;AAEA,8EAA8E;AAC9E,2BAA2B;AAC3B,MAAM,uBAAuB,CAAA,MAAO,IAAI,OAAO,CAAC,0BAA0B,CAAC,GAAG,MAAQ,CAAC,MAAM,MAAM,EAAE,IAAI,EAAE,WAAW;AACtH,SAAS,qBAAqB,SAAS,EAAE,IAAI;IAC3C,OAAO,OAAO,cAAc,aAAa,UAAU,QAAQ;AAC7D;AACA,SAAS,gBAAgB,IAAI,EAAE,UAAU;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,6WAAM,QAAQ,CAAC;IACjD,IAAI,QAAQ,CAAC,WAAW;QACtB,aAAa;IACf;IACA,6WAAM,SAAS,CAAC;QACd,IAAI,CAAC,QAAQ,WAAW;YACtB,MAAM,UAAU,WAAW,IAAM,aAAa,QAAQ;YACtD,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAM;QAAW;KAAW;IAChC,OAAO;AACT;AACA;;;;CAIC,GACD,SAAS,oBAAoB,OAAO,EAAE,KAAK;IACzC,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,UAAU,EACR,QAAQ,EACT,EACF,GAAG;IACJ,MAAM,EACJ,WAAW,GAAG,EACf,GAAG;IACJ,MAAM,mBAAmB,OAAO,aAAa;IAC7C,MAAM,gBAAgB,CAAC,mBAAmB,WAAW,SAAS,KAAK,KAAK;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,6WAAM,QAAQ,CAAC;IAC3C,MAAM,YAAY,gBAAgB,MAAM;IACxC,IAAI,CAAC,aAAa,WAAW,SAAS;QACpC,UAAU;IACZ;IACA,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,CAAC,UAAU;QACf,IAAI,MAAM;YACR,UAAU;YACV,MAAM,QAAQ,sBAAsB;gBAClC,2DAA2D;gBAC3D,8DAA8D;gBAC9D,uDAAuD;gBACvD,CAAA,GAAA,mXAAA,CAAA,YAAkB,AAAD,EAAE;oBACjB,UAAU;gBACZ;YACF;YACA,OAAO;gBACL,qBAAqB;YACvB;QACF;QACA,UAAU;IACZ,GAAG;QAAC;QAAM;KAAS;IACnB,OAAO;QACL;QACA;IACF;AACF;AACA;;;;CAIC,GACD,SAAS,oBAAoB,OAAO,EAAE,KAAK;IACzC,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,SAAS,mBAAmB;QAC1B,SAAS;IACX,CAAC,EACD,MAAM,aAAa,EACnB,OAAO,cAAc,EACrB,QAAQ,eAAe,EACvB,WAAW,GAAG,EACf,GAAG;IACJ,MAAM,YAAY,QAAQ,SAAS;IACnC,MAAM,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;IACpC,MAAM,SAAS,6WAAM,OAAO,CAAC,IAAM,CAAC;YAClC;YACA;QACF,CAAC,GAAG;QAAC;QAAM;KAAU;IACrB,MAAM,mBAAmB,OAAO,aAAa;IAC7C,MAAM,eAAe,CAAC,mBAAmB,WAAW,SAAS,IAAI,KAAK;IACtE,MAAM,gBAAgB,CAAC,mBAAmB,WAAW,SAAS,KAAK,KAAK;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,6WAAM,QAAQ,CAAC,IAAM,CAAC;YAChD,GAAG,qBAAqB,iBAAiB,OAAO;YAChD,GAAG,qBAAqB,kBAAkB,OAAO;QACnD,CAAC;IACD,MAAM,EACJ,SAAS,EACT,MAAM,EACP,GAAG,oBAAoB,SAAS;QAC/B;IACF;IACA,MAAM,aAAa,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IAChC,MAAM,UAAU,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IAC7B,MAAM,WAAW,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IAC9B,MAAM,YAAY,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IAC/B,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,MAAM,gBAAgB,qBAAqB,WAAW,OAAO,EAAE;QAC/D,MAAM,cAAc,qBAAqB,SAAS,OAAO,EAAE;QAC3D,MAAM,eAAe,qBAAqB,UAAU,OAAO,EAAE;QAC7D,MAAM,aAAa,qBAAqB,QAAQ,OAAO,EAAE,WAAW,OAAO,IAAI,CAAC,eAAe,MAAM,CAAC,CAAC,KAAK;YAC1G,GAAG,CAAC,IAAI,GAAG;YACX,OAAO;QACT,GAAG,CAAC;QACJ,IAAI,WAAW,WAAW;YACxB,UAAU,CAAA,SAAU,CAAC;oBACnB,oBAAoB,OAAO,kBAAkB;oBAC7C,GAAG,YAAY;oBACf,GAAG,aAAa;gBAClB,CAAC;QACH;QACA,IAAI,WAAW,QAAQ;YACrB,UAAU;gBACR,oBAAoB,OAAO,IAAI,CAAC,YAAY,GAAG,CAAC,sBAAsB,IAAI,CAAC;gBAC3E,oBAAoB,eAAe;gBACnC,GAAG,YAAY;gBACf,GAAG,UAAU;YACf;QACF;QACA,IAAI,WAAW,SAAS;YACtB,MAAM,SAAS,eAAe;YAC9B,UAAU;gBACR,oBAAoB,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,sBAAsB,IAAI,CAAC;gBACvE,oBAAoB,gBAAgB;gBACpC,GAAG,YAAY;gBACf,GAAG,MAAM;YACX;QACF;IACF,GAAG;QAAC;QAAe;QAAU;QAAY;QAAS;QAAW;QAAc;QAAQ;KAAO;IAC1F,OAAO;QACL;QACA;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS,aAAa,OAAO,EAAE,KAAK;IAClC,IAAI;IACJ,MAAM,EACJ,IAAI,EACJ,OAAO,EACR,GAAG;IACJ,MAAM,EACJ,OAAO,EACP,WAAW,EACX,SAAS,gBAAgB,EACzB,gBAAgB,uBAAuB,EACvC,UAAU,IAAI,EACd,YAAY,IAAI,EAChB,UAAU,GAAG,EACb,aAAa,EAAE,EACf,gBAAgB,IAAI,EACrB,GAAG;IACJ,MAAM,eAAe,6WAAM,MAAM,CAAC,CAAC;IACnC,MAAM,YAAY,6WAAM,MAAM,CAAC;IAC/B,MAAM,eAAe,6WAAM,MAAM,CAAC,CAAC,OAAO,iBAAiB,OAAO,gBAAgB,WAAW,KAAK,OAAO,OAAO,CAAC;IACjH,MAAM,gBAAgB,6WAAM,MAAM,CAAC;IACnC,MAAM,UAAU,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE;IAC/B,MAAM,iBAAiB,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE;IACtC,MAAM,eAAe,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IAClC,MAAM,gBAAgB,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,EAAE;IACnC,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,IAAI,MAAM;YACR,kBAAkB;YAClB,cAAc,OAAO,GAAG;YACxB,UAAU,OAAO,GAAG;QACtB;IACF,GAAG;QAAC;KAAK;IACT,CAAA,GAAA,2UAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB,0DAA0D;QAC1D,IAAI,QAAQ,UAAU,OAAO,KAAK,IAAI;YACpC,IAAI;YACJ,aAAa,OAAO,GAAG,CAAC,QAAQ,iBAAiB,OAAO,gBAAgB,WAAW,KAAK,OAAO,QAAQ,CAAC;QAC1G;IACF,GAAG;QAAC;QAAM;QAAe;KAAY;IACrC,MAAM,kBAAkB,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,CAAA;QACrC,IAAI,OAAO;YACT,IAAI,CAAC,QAAQ,OAAO,CAAC,MAAM,EAAE;gBAC3B,QAAQ,OAAO,CAAC,MAAM,GAAG;gBACzB,eAAe;YACjB;QACF,OAAO;YACL,IAAI,QAAQ,OAAO,CAAC,MAAM,EAAE;gBAC1B,QAAQ,OAAO,CAAC,MAAM,GAAG;gBACzB,eAAe;YACjB;QACF;IACF;IACA,MAAM,YAAY,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,CAAA;QAC/B,SAAS,iBAAiB,IAAI,EAAE,WAAW,EAAE,MAAM;YACjD,MAAM,MAAM,aAAa,OAAO,GAAG,aAAa,OAAO,CAAC,aAAa,UAAU,YAAY,IAAI,CAAC,CAAA,OAAQ,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,iBAAiB,GAAG,OAAO,CAAC,OAAO,iBAAiB,GAAG,MAAM;YACnM,OAAO,MAAM,KAAK,OAAO,CAAC,OAAO,CAAC;QACpC;QACA,MAAM,cAAc,QAAQ,OAAO;QACnC,IAAI,UAAU,OAAO,CAAC,MAAM,GAAG,KAAK,UAAU,OAAO,CAAC,EAAE,KAAK,KAAK;YAChE,IAAI,iBAAiB,aAAa,aAAa,UAAU,OAAO,MAAM,CAAC,GAAG;gBACxE,gBAAgB;YAClB,OAAO,IAAI,MAAM,GAAG,KAAK,KAAK;gBAC5B,CAAA,GAAA,2UAAA,CAAA,YAAS,AAAD,EAAE;YACZ;QACF;QACA,IAAI,eAAe,QAAQ,cAAc,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,KACnE,iBAAiB;QACjB,MAAM,GAAG,CAAC,MAAM,KAAK,KACrB,gBAAgB;QAChB,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,EAAE;YAC9C;QACF;QACA,IAAI,QAAQ,MAAM,GAAG,KAAK,KAAK;YAC7B,CAAA,GAAA,2UAAA,CAAA,YAAS,AAAD,EAAE;YACV,gBAAgB;QAClB;QAEA,sEAAsE;QACtE,8BAA8B;QAC9B,MAAM,oCAAoC,YAAY,KAAK,CAAC,CAAA;YAC1D,IAAI,QAAQ;YACZ,OAAO,OAAO,CAAC,CAAC,SAAS,IAAI,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,OAAO,iBAAiB,EAAE,MAAM,CAAC,CAAC,UAAU,IAAI,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,QAAQ,iBAAiB,EAAE,IAAI;QAC9J;QAEA,yEAAyE;QACzE,uBAAuB;QACvB,IAAI,qCAAqC,UAAU,OAAO,KAAK,MAAM,GAAG,EAAE;YACxE,UAAU,OAAO,GAAG;YACpB,aAAa,OAAO,GAAG,cAAc,OAAO;QAC9C;QACA,UAAU,OAAO,IAAI,MAAM,GAAG;QAC9B,kBAAkB;QAClB,aAAa,OAAO,GAAG,OAAO,UAAU,CAAC;YACvC,UAAU,OAAO,GAAG;YACpB,aAAa,OAAO,GAAG,cAAc,OAAO;YAC5C,gBAAgB;QAClB,GAAG;QACH,MAAM,YAAY,aAAa,OAAO;QACtC,MAAM,QAAQ,iBAAiB,aAAa;eAAI,YAAY,KAAK,CAAC,CAAC,aAAa,CAAC,IAAI;eAAO,YAAY,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI;SAAG,EAAE,UAAU,OAAO;QAC1J,IAAI,UAAU,CAAC,GAAG;YAChB,QAAQ;YACR,cAAc,OAAO,GAAG;QAC1B,OAAO,IAAI,MAAM,GAAG,KAAK,KAAK;YAC5B,UAAU,OAAO,GAAG;YACpB,gBAAgB;QAClB;IACF;IACA,MAAM,YAAY,6WAAM,OAAO,CAAC,IAAM,CAAC;YACrC;QACF,CAAC,GAAG;QAAC;KAAU;IACf,MAAM,WAAW,6WAAM,OAAO,CAAC;QAC7B,OAAO;YACL;YACA,SAAQ,KAAK;gBACX,IAAI,MAAM,GAAG,KAAK,KAAK;oBACrB,gBAAgB;gBAClB;YACF;QACF;IACF,GAAG;QAAC;QAAW;KAAgB;IAC/B,OAAO,6WAAM,OAAO,CAAC,IAAM,UAAU;YACnC;YACA;QACF,IAAI,CAAC,GAAG;QAAC;QAAS;QAAW;KAAS;AACxC;AAEA,SAAS,gCAAgC,KAAK,EAAE,MAAM;IACpD,OAAO;QACL,GAAG,KAAK;QACR,OAAO;YACL,GAAG,MAAM,KAAK;YACd,UAAU;gBACR,GAAG,MAAM,KAAK,CAAC,QAAQ;gBACvB;YACF;QACF;IACF;AACF;AACA;;;;;CAKC,GACD,MAAM,QAAQ,CAAA,QAAS,CAAC;QACtB,MAAM;QACN,SAAS;QACT,MAAM,IAAG,KAAK;YACZ,MAAM,EACJ,OAAO,EACP,WAAW,EACX,gBAAgB,EAChB,QAAQ,cAAc,CAAC,EACvB,QAAQ,CAAC,EACT,kBAAkB,CAAC,EACnB,6BAA6B,CAAC,EAC9B,SAAS,EACT,GAAG,uBACJ,GAAG,CAAA,GAAA,oPAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;YACpB,MAAM,EACJ,KAAK,EACL,UAAU,EACR,QAAQ,EACT,EACF,GAAG;YACJ,MAAM,OAAO,QAAQ,OAAO,CAAC,MAAM;YACnC,MAAM,WAAW,CAAC,aAAa,OAAO,KAAK,IAAI,UAAU,OAAO,KAAK;YAErE,sBAAsB;YACtB,kEAAkE;YAClE,0EAA0E;YAC1E,mEAAmE;YACnE,wEAAwE;YACxE,qCAAqC;YACrC,MAAM,YAAY,SAAS,SAAS,IAAI,SAAS,SAAS;YAC1D,MAAM,qBAAqB,SAAS,SAAS,KAAK;YAClD,MAAM,qBAAqB,SAAS,SAAS,KAAK;YAClD,MAAM,qBAAqB,aAAa;YACxC,wCAA2C;gBACzC,IAAI,CAAC,MAAM,SAAS,CAAC,UAAU,CAAC,WAAW;oBACzC,KAAK,4DAA4D;gBACnE;YACF;YACA,IAAI,CAAC,MAAM;gBACT,OAAO,CAAC;YACV;YACA,MAAM,WAAW;gBACf,GAAG,KAAK;gBACR,GAAI,MAAM,CAAA,GAAA,qWAAA,CAAA,SAAM,AAAD,EAAE,CAAC,KAAK,SAAS,GAAG,SAAS,SAAS,GAAG,MAAM,SAAS,CAAC,MAAM,GAAG,IAAI,KAAK,YAAY,GAAG,IAAI,aAAa,EAAE,CAAC,MAAM;YACrI;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,gCAAgC,UAAU,SAAS,YAAY,GAAG,YAAY,SAAS,SAAS,GAAG;YACzI,MAAM,cAAc,MAAM,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBACjD,GAAG,qBAAqB;gBACxB,gBAAgB;YAClB;YACA,MAAM,QAAQ,CAAA,GAAA,oPAAA,CAAA,MAAG,AAAD,EAAE,GAAG,SAAS,GAAG;YACjC,MAAM,QAAQ,SAAS,CAAC,GAAG;YAC3B,MAAM,eAAe,SAAS,YAAY,GAAG,SAAS,YAAY;YAClE,MAAM,UAAU,eAAe,CAAA,IAAK,IAAI,oPAAA,CAAA,QAAK;YAC7C,MAAM,YAAY,QAAQ,CAAA,GAAA,oPAAA,CAAA,MAAG,AAAD,EAAE,GAAG,SAAS,YAAY,GAAG,CAAC,sBAAsB,sBAAsB,qBAAqB,YAAY,IAAI,CAAC,IAAI,QAAQ,CAAA,GAAA,oPAAA,CAAA,MAAG,AAAD,EAAE,GAAG,SAAS,MAAM;YAC9K,SAAS,KAAK,CAAC,SAAS,GAAG,YAAY;YACvC,SAAS,SAAS,GAAG;YAErB,uEAAuE;YACvE,IAAI,kBAAkB;gBACpB,MAAM,iBAAiB,SAAS,YAAY,GAAG,KAAK,YAAY,GAAG,CAAA,GAAA,oPAAA,CAAA,MAAG,AAAD,EAAE,iBAAiB,QAAQ,OAAO,CAAC,MAAM,IAAI,KAAK,YAAY,GAAG,IAAI,CAAC,8BAA8B,YAAY,MAAM,IAAI,CAAC;gBAChM,CAAA,GAAA,mXAAA,CAAA,YAAkB,AAAD,EAAE,IAAM,iBAAiB;YAC5C;YACA,IAAI,aAAa;gBACf,YAAY,OAAO,GAAG,MAAM,CAAA,GAAA,6PAAA,CAAA,iBAAc,AAAD,EAAE,gCAAgC;oBACzE,GAAG,QAAQ;oBACX,GAAG;gBACL,GAAG,SAAS,YAAY,GAAG,YAAY,SAAS,SAAS,GAAG;YAC9D;YACA,OAAO;gBACL,GAAG;YACL;QACF;IACF,CAAC;AACD;;;;;CAKC,GACD,SAAS,eAAe,OAAO,EAAE,KAAK;IACpC,MAAM,EACJ,IAAI,EACJ,QAAQ,EACT,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,WAAW,EACX,SAAS,EACT,UAAU,iBAAiB,EAC5B,GAAG;IACJ,MAAM,WAAW,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE;IAChC,MAAM,yBAAyB,6WAAM,MAAM,CAAC;IAC5C,MAAM,mBAAmB,6WAAM,MAAM,CAAC;IACtC,MAAM,qBAAqB,6WAAM,MAAM,CAAC;IACxC,6WAAM,SAAS,CAAC;QACd,IAAI,CAAC,SAAS;QACd,SAAS,QAAQ,CAAC;YAChB,IAAI,EAAE,OAAO,IAAI,CAAC,MAAM,YAAY,OAAO,IAAI,MAAM;gBACnD;YACF;YACA,MAAM,KAAK,EAAE,MAAM;YACnB,MAAM,UAAU,YAAY,OAAO,CAAC,GAAG,IAAI,CAAC;YAC5C,MAAM,aAAa,YAAY,OAAO,CAAC,MAAM,IAAI,CAAC;YAClD,MAAM,kBAAkB,GAAG,YAAY,GAAG,GAAG,YAAY;YACzD,MAAM,OAAO,KAAK,IAAI,CAAC,IAAI;YAC3B,MAAM,SAAS,KAAK,IAAI,QAAQ;YAChC,IAAI,GAAG,YAAY,IAAI,GAAG,YAAY,EAAE;gBACtC;YACF;YACA,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC,cAAc,KAAK,GAAG;gBAC/C,EAAE,cAAc;gBAChB,CAAA,GAAA,mXAAA,CAAA,YAAkB,AAAD,EAAE;oBACjB,SAAS,CAAA,IAAK,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,kBAAkB;gBACvD;YACF,OAAO,IAAI,WAAW,IAAI,CAAC,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,MAAM;gBAC1C,qEAAqE;gBACrE,iEAAiE;gBACjE,GAAG,SAAS,IAAI;YAClB;QACF;QACA,MAAM,KAAK,CAAC,aAAa,OAAO,KAAK,IAAI,UAAU,OAAO,KAAK,SAAS,QAAQ;QAChF,IAAI,QAAQ,IAAI;YACd,GAAG,gBAAgB,CAAC,SAAS;YAE7B,qCAAqC;YACrC,sBAAsB;gBACpB,iBAAiB,OAAO,GAAG,GAAG,SAAS;gBACvC,IAAI,YAAY,OAAO,IAAI,MAAM;oBAC/B,mBAAmB,OAAO,GAAG;wBAC3B,GAAG,YAAY,OAAO;oBACxB;gBACF;YACF;YACA,OAAO;gBACL,iBAAiB,OAAO,GAAG;gBAC3B,mBAAmB,OAAO,GAAG;gBAC7B,GAAG,mBAAmB,CAAC,SAAS;YAClC;QACF;IACF,GAAG;QAAC;QAAS;QAAM,SAAS,QAAQ;QAAE;QAAa;QAAW;KAAS;IACvE,MAAM,WAAW,6WAAM,OAAO,CAAC,IAAM,CAAC;YACpC;gBACE,uBAAuB,OAAO,GAAG;YACnC;YACA;gBACE,uBAAuB,OAAO,GAAG;YACnC;YACA;gBACE,uBAAuB,OAAO,GAAG;YACnC;YACA;gBACE,MAAM,KAAK,CAAC,aAAa,OAAO,KAAK,IAAI,UAAU,OAAO,KAAK,SAAS,QAAQ;gBAChF,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC,MAAM,CAAC,uBAAuB,OAAO,EAAE;oBAClE;gBACF;gBACA,IAAI,iBAAiB,OAAO,KAAK,MAAM;oBACrC,MAAM,aAAa,GAAG,SAAS,GAAG,iBAAiB,OAAO;oBAC1D,IAAI,YAAY,OAAO,CAAC,MAAM,GAAG,CAAC,OAAO,aAAa,CAAC,KAAK,YAAY,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,aAAa,GAAG;wBAC5G,CAAA,GAAA,mXAAA,CAAA,YAAkB,AAAD,EAAE,IAAM,SAAS,CAAA,IAAK,IAAI;oBAC7C;gBACF;gBAEA,6DAA6D;gBAC7D,sBAAsB;oBACpB,iBAAiB,OAAO,GAAG,GAAG,SAAS;gBACzC;YACF;QACF,CAAC,GAAG;QAAC,SAAS,QAAQ;QAAE;QAAU;QAAa;KAAU;IACzD,OAAO,6WAAM,OAAO,CAAC,IAAM,UAAU;YACnC;QACF,IAAI,CAAC,GAAG;QAAC;QAAS;KAAS;AAC7B;AAEA,SAAS,gBAAgB,KAAK,EAAE,EAAE,EAAE,gBAAgB;IAClD,IAAI,qBAAqB,KAAK,GAAG;QAC/B,mBAAmB;IACrB;IACA,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA;QAClC,IAAI;QACJ,OAAO,KAAK,QAAQ,KAAK,MAAM,CAAC,CAAC,oBAAoB,CAAC,CAAC,gBAAgB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,cAAc,IAAI,CAAC;IAC7H;IACA,OAAO,eAAe,OAAO,CAAC,CAAA,QAAS;YAAC;eAAU,gBAAgB,OAAO,MAAM,EAAE,EAAE;SAAkB;AACvG;AAEA,SAAS,iBAAiB,KAAK,EAAE,OAAO;IACtC,MAAM,CAAC,GAAG,EAAE,GAAG;IACf,IAAI,WAAW;IACf,MAAM,SAAS,QAAQ,MAAM;IAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,GAAG,IAAI,QAAQ,IAAI,IAAK;QACnD,MAAM,CAAC,IAAI,GAAG,GAAG,OAAO,CAAC,EAAE,IAAI;YAAC;YAAG;SAAE;QACrC,MAAM,CAAC,IAAI,GAAG,GAAG,OAAO,CAAC,EAAE,IAAI;YAAC;YAAG;SAAE;QACrC,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI;QACjF,IAAI,WAAW;YACb,WAAW,CAAC;QACd;IACF;IACA,OAAO;AACT;AACA,SAAS,SAAS,KAAK,EAAE,IAAI;IAC3B,OAAO,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,GAAG,KAAK,MAAM;AACxH;AACA;;;;CAIC,GACD,SAAS,YAAY,OAAO;IAC1B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,SAAS,GAAG,EACZ,qBAAqB,KAAK,EAC1B,gBAAgB,IAAI,EACrB,GAAG;IACJ,MAAM,aAAa;QACjB,SAAS,CAAC;IACZ;IACA,IAAI,YAAY;IAChB,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,IAAI,iBAAiB,YAAY,GAAG;IACpC,SAAS,eAAe,CAAC,EAAE,CAAC;QAC1B,MAAM,cAAc,YAAY,GAAG;QACnC,MAAM,cAAc,cAAc;QAClC,IAAI,UAAU,QAAQ,UAAU,QAAQ,gBAAgB,GAAG;YACzD,QAAQ;YACR,QAAQ;YACR,iBAAiB;YACjB,OAAO;QACT;QACA,MAAM,SAAS,IAAI;QACnB,MAAM,SAAS,IAAI;QACnB,MAAM,WAAW,KAAK,IAAI,CAAC,SAAS,SAAS,SAAS;QACtD,MAAM,QAAQ,WAAW,aAAa,UAAU;QAEhD,QAAQ;QACR,QAAQ;QACR,iBAAiB;QACjB,OAAO;IACT;IACA,MAAM,KAAK,CAAA;QACT,IAAI,EACF,CAAC,EACD,CAAC,EACD,SAAS,EACT,QAAQ,EACR,OAAO,EACP,MAAM,EACN,IAAI,EACL,GAAG;QACJ,OAAO,SAAS,YAAY,KAAK;YAC/B,SAAS;gBACP,kBAAkB;gBAClB;YACF;YACA,kBAAkB;YAClB,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,QAAQ,IAAI,aAAa,QAAQ,KAAK,QAAQ,KAAK,MAAM;gBAC/F;YACF;YACA,MAAM,EACJ,OAAO,EACP,OAAO,EACR,GAAG;YACJ,MAAM,cAAc;gBAAC;gBAAS;aAAQ;YACtC,MAAM,SAAS,UAAU;YACzB,MAAM,UAAU,MAAM,IAAI,KAAK;YAC/B,MAAM,mBAAmB,SAAS,SAAS,QAAQ,EAAE;YACrD,MAAM,oBAAoB,SAAS,SAAS,YAAY,EAAE;YAC1D,MAAM,UAAU,SAAS,YAAY,CAAC,qBAAqB;YAC3D,MAAM,OAAO,SAAS,QAAQ,CAAC,qBAAqB;YACpD,MAAM,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;YACpC,MAAM,uBAAuB,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG;YAC3D,MAAM,wBAAwB,IAAI,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG;YAC9D,MAAM,sBAAsB,SAAS,aAAa;YAClD,MAAM,kBAAkB,KAAK,KAAK,GAAG,QAAQ,KAAK;YAClD,MAAM,mBAAmB,KAAK,MAAM,GAAG,QAAQ,MAAM;YACrD,MAAM,OAAO,CAAC,kBAAkB,UAAU,IAAI,EAAE,IAAI;YACpD,MAAM,QAAQ,CAAC,kBAAkB,UAAU,IAAI,EAAE,KAAK;YACtD,MAAM,MAAM,CAAC,mBAAmB,UAAU,IAAI,EAAE,GAAG;YACnD,MAAM,SAAS,CAAC,mBAAmB,UAAU,IAAI,EAAE,MAAM;YACzD,IAAI,kBAAkB;gBACpB,YAAY;gBACZ,IAAI,CAAC,SAAS;oBACZ;gBACF;YACF;YACA,IAAI,mBAAmB;gBACrB,YAAY;YACd;YACA,IAAI,qBAAqB,CAAC,SAAS;gBACjC,YAAY;gBACZ;YACF;YAEA,yEAAyE;YACzE,+DAA+D;YAC/D,IAAI,WAAW,CAAA,GAAA,2PAAA,CAAA,YAAS,AAAD,EAAE,MAAM,aAAa,KAAK,SAAS,SAAS,QAAQ,EAAE,MAAM,aAAa,GAAG;gBACjG;YACF;YAEA,sCAAsC;YACtC,IAAI,QAAQ,gBAAgB,KAAK,QAAQ,CAAC,OAAO,EAAE,QAAQ,MAAM,EAAE;gBACjE;YACF;YAEA,uEAAuE;YACvE,yEAAyE;YACzE,WAAW;YACX,0DAA0D;YAC1D,IAAI,SAAS,SAAS,KAAK,QAAQ,MAAM,GAAG,KAAK,SAAS,YAAY,KAAK,QAAQ,GAAG,GAAG,KAAK,SAAS,UAAU,KAAK,QAAQ,KAAK,GAAG,KAAK,SAAS,WAAW,KAAK,QAAQ,IAAI,GAAG,GAAG;gBACpL,OAAO;YACT;YAEA,sEAAsE;YACtE,qEAAqE;YACrE,qEAAqE;YACrE,yEAAyE;YACzE,+CAA+C;YAC/C,IAAI,WAAW,EAAE;YACjB,OAAQ;gBACN,KAAK;oBACH,WAAW;wBAAC;4BAAC;4BAAM,QAAQ,GAAG,GAAG;yBAAE;wBAAE;4BAAC;4BAAM,KAAK,MAAM,GAAG;yBAAE;wBAAE;4BAAC;4BAAO,KAAK,MAAM,GAAG;yBAAE;wBAAE;4BAAC;4BAAO,QAAQ,GAAG,GAAG;yBAAE;qBAAC;oBACjH;gBACF,KAAK;oBACH,WAAW;wBAAC;4BAAC;4BAAM,KAAK,GAAG,GAAG;yBAAE;wBAAE;4BAAC;4BAAM,QAAQ,MAAM,GAAG;yBAAE;wBAAE;4BAAC;4BAAO,QAAQ,MAAM,GAAG;yBAAE;wBAAE;4BAAC;4BAAO,KAAK,GAAG,GAAG;yBAAE;qBAAC;oBACjH;gBACF,KAAK;oBACH,WAAW;wBAAC;4BAAC,KAAK,KAAK,GAAG;4BAAG;yBAAO;wBAAE;4BAAC,KAAK,KAAK,GAAG;4BAAG;yBAAI;wBAAE;4BAAC,QAAQ,IAAI,GAAG;4BAAG;yBAAI;wBAAE;4BAAC,QAAQ,IAAI,GAAG;4BAAG;yBAAO;qBAAC;oBACjH;gBACF,KAAK;oBACH,WAAW;wBAAC;4BAAC,QAAQ,KAAK,GAAG;4BAAG;yBAAO;wBAAE;4BAAC,QAAQ,KAAK,GAAG;4BAAG;yBAAI;wBAAE;4BAAC,KAAK,IAAI,GAAG;4BAAG;yBAAI;wBAAE;4BAAC,KAAK,IAAI,GAAG;4BAAG;yBAAO;qBAAC;oBACjH;YACJ;YACA,SAAS,WAAW,KAAK;gBACvB,IAAI,CAAC,GAAG,EAAE,GAAG;gBACb,OAAQ;oBACN,KAAK;wBACH;4BACE,MAAM,iBAAiB;gCAAC,kBAAkB,IAAI,SAAS,IAAI,uBAAuB,IAAI,SAAS,IAAI,IAAI,SAAS;gCAAG,IAAI,SAAS;6BAAE;4BAClI,MAAM,iBAAiB;gCAAC,kBAAkB,IAAI,SAAS,IAAI,uBAAuB,IAAI,SAAS,IAAI,IAAI,SAAS;gCAAG,IAAI,SAAS;6BAAE;4BAClI,MAAM,eAAe;gCAAC;oCAAC,KAAK,IAAI;oCAAE,uBAAuB,KAAK,MAAM,GAAG,SAAS,kBAAkB,KAAK,MAAM,GAAG,SAAS,KAAK,GAAG;iCAAC;gCAAE;oCAAC,KAAK,KAAK;oCAAE,uBAAuB,kBAAkB,KAAK,MAAM,GAAG,SAAS,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;iCAAO;6BAAC;4BAClP,OAAO;gCAAC;gCAAgB;mCAAmB;6BAAa;wBAC1D;oBACF,KAAK;wBACH;4BACE,MAAM,iBAAiB;gCAAC,kBAAkB,IAAI,SAAS,IAAI,uBAAuB,IAAI,SAAS,IAAI,IAAI,SAAS;gCAAG,IAAI;6BAAO;4BAC9H,MAAM,iBAAiB;gCAAC,kBAAkB,IAAI,SAAS,IAAI,uBAAuB,IAAI,SAAS,IAAI,IAAI,SAAS;gCAAG,IAAI;6BAAO;4BAC9H,MAAM,eAAe;gCAAC;oCAAC,KAAK,IAAI;oCAAE,uBAAuB,KAAK,GAAG,GAAG,SAAS,kBAAkB,KAAK,GAAG,GAAG,SAAS,KAAK,MAAM;iCAAC;gCAAE;oCAAC,KAAK,KAAK;oCAAE,uBAAuB,kBAAkB,KAAK,GAAG,GAAG,SAAS,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG;iCAAO;6BAAC;4BAC5O,OAAO;gCAAC;gCAAgB;mCAAmB;6BAAa;wBAC1D;oBACF,KAAK;wBACH;4BACE,MAAM,iBAAiB;gCAAC,IAAI,SAAS;gCAAG,mBAAmB,IAAI,SAAS,IAAI,wBAAwB,IAAI,SAAS,IAAI,IAAI,SAAS;6BAAE;4BACpI,MAAM,iBAAiB;gCAAC,IAAI,SAAS;gCAAG,mBAAmB,IAAI,SAAS,IAAI,wBAAwB,IAAI,SAAS,IAAI,IAAI,SAAS;6BAAE;4BACpI,MAAM,eAAe;gCAAC;oCAAC,wBAAwB,KAAK,KAAK,GAAG,SAAS,mBAAmB,KAAK,KAAK,GAAG,SAAS,KAAK,IAAI;oCAAE,KAAK,GAAG;iCAAC;gCAAE;oCAAC,wBAAwB,mBAAmB,KAAK,KAAK,GAAG,SAAS,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;oCAAQ,KAAK,MAAM;iCAAC;6BAAC;4BACpP,OAAO;mCAAI;gCAAc;gCAAgB;6BAAe;wBAC1D;oBACF,KAAK;wBACH;4BACE,MAAM,iBAAiB;gCAAC,IAAI;gCAAQ,mBAAmB,IAAI,SAAS,IAAI,wBAAwB,IAAI,SAAS,IAAI,IAAI,SAAS;6BAAE;4BAChI,MAAM,iBAAiB;gCAAC,IAAI;gCAAQ,mBAAmB,IAAI,SAAS,IAAI,wBAAwB,IAAI,SAAS,IAAI,IAAI,SAAS;6BAAE;4BAChI,MAAM,eAAe;gCAAC;oCAAC,wBAAwB,KAAK,IAAI,GAAG,SAAS,mBAAmB,KAAK,IAAI,GAAG,SAAS,KAAK,KAAK;oCAAE,KAAK,GAAG;iCAAC;gCAAE;oCAAC,wBAAwB,mBAAmB,KAAK,IAAI,GAAG,SAAS,KAAK,KAAK,GAAG,KAAK,IAAI,GAAG;oCAAQ,KAAK,MAAM;iCAAC;6BAAC;4BAClP,OAAO;gCAAC;gCAAgB;mCAAmB;6BAAa;wBAC1D;gBACJ;YACF;YACA,IAAI,iBAAiB;gBAAC;gBAAS;aAAQ,EAAE,WAAW;gBAClD;YACF;YACA,IAAI,aAAa,CAAC,qBAAqB;gBACrC,OAAO;YACT;YACA,IAAI,CAAC,WAAW,eAAe;gBAC7B,MAAM,cAAc,eAAe,MAAM,OAAO,EAAE,MAAM,OAAO;gBAC/D,MAAM,uBAAuB;gBAC7B,IAAI,gBAAgB,QAAQ,cAAc,sBAAsB;oBAC9D,OAAO;gBACT;YACF;YACA,IAAI,CAAC,iBAAiB;gBAAC;gBAAS;aAAQ,EAAE,WAAW;gBAAC;gBAAG;aAAE,IAAI;gBAC7D;YACF,OAAO,IAAI,CAAC,aAAa,eAAe;gBACtC,WAAW,OAAO,GAAG,OAAO,UAAU,CAAC,OAAO;YAChD;QACF;IACF;IACA,GAAG,SAAS,GAAG;QACb;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}]}