{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/context.tsx"], "sourcesContent": ["'use client'\n\nimport type { RefObject } from 'react'\nimport { createContext, useContext } from 'use-context-selector'\nimport type {\n  Callback,\n  ChatConfig,\n  ChatItemInTree,\n  Feedback,\n} from '../types'\nimport type { ThemeBuilder } from '../embedded-chatbot/theme/theme-context'\nimport type {\n  AppConversationData,\n  AppData,\n  AppMeta,\n  ConversationItem,\n  EmbedSource,\n} from '@/models/share'\nimport { noop } from 'lodash-es'\n\nexport type ChatWithHistoryContextValue = {\n  appInfoError?: any\n  appInfoLoading?: boolean\n  appMeta?: AppMeta\n  appData?: AppData\n  appParams?: ChatConfig\n  appChatListDataLoading?: boolean\n  currentConversationId: string\n  currentConversationItem?: ConversationItem\n  appPrevChatTree: ChatItemInTree[]\n  pinnedConversationList: AppConversationData['data']\n  conversationList: AppConversationData['data']\n  newConversationInputs: Record<string, any>\n  newConversationInputsRef: RefObject<Record<string, any>>\n  handleNewConversationInputsChange: (v: Record<string, any>) => void\n  inputsForms: any[]\n  handleNewConversation: () => void\n  handleStartChat: (callback?: any) => void\n  handleChangeConversation: (conversationId: string) => void\n  handlePinConversation: (conversationId: string) => void\n  handleUnpinConversation: (conversationId: string) => void\n  handleDeleteConversation: (conversationId: string, callback: Callback) => void\n  conversationRenaming: boolean\n  handleRenameConversation: (conversationId: string, newName: string, callback: Callback) => void\n  handleNewConversationCompleted: (newConversationId: string) => void\n  chatShouldReloadKey: string\n  isMobile: boolean\n  isInstalledApp: boolean\n  appId?: string\n  handleFeedback: (messageId: string, feedback: Feedback) => void\n  currentChatInstanceRef: RefObject<{ handleStop: () => void }>\n  themeBuilder?: ThemeBuilder\n  sidebarCollapseState?: boolean\n  handleSidebarCollapse: (state: boolean) => void\n  clearChatList?: boolean\n  setClearChatList: (state: boolean) => void\n  isResponding?: boolean\n  setIsResponding: (state: boolean) => void,\n  currentConversationInputs: Record<string, any> | null,\n  setCurrentConversationInputs: (v: Record<string, any>) => void,\n  isFold?: boolean\n  setIsFold?: (bool: boolean) => void\n  embedSource?: EmbedSource\n  refreshRenderKey?: number\n  larkInfo?: any\n  handleClearAllConversations?: (callback: Callback) => void\n  rightSideInfo?: null | any// 右侧面板信息\n  setRightSideInfo?: (info: any) => void // 右侧面板信息\n}\n\nexport const ChatWithHistoryContext = createContext<ChatWithHistoryContextValue>({\n  currentConversationId: '',\n  appPrevChatTree: [],\n  pinnedConversationList: [],\n  conversationList: [],\n  newConversationInputs: {},\n  newConversationInputsRef: { current: {} },\n  handleNewConversationInputsChange: noop,\n  inputsForms: [],\n  handleNewConversation: noop,\n  handleStartChat: noop,\n  handleChangeConversation: noop,\n  handlePinConversation: noop,\n  handleUnpinConversation: noop,\n  handleDeleteConversation: noop,\n  conversationRenaming: false,\n  handleRenameConversation: noop,\n  handleNewConversationCompleted: noop,\n  chatShouldReloadKey: '',\n  isMobile: false,\n  isInstalledApp: false,\n  handleFeedback: noop,\n  currentChatInstanceRef: { current: { handleStop: noop } },\n  sidebarCollapseState: false,\n  handleSidebarCollapse: noop,\n  clearChatList: false,\n  setClearChatList: noop,\n  isResponding: false,\n  setIsResponding: noop,\n  currentConversationInputs: {},\n  setCurrentConversationInputs: noop,\n  isFold: false,\n  setIsFold: noop,\n  embedSource: '',\n  refreshRenderKey: -1,\n  larkInfo: {},\n  handleClearAllConversations: noop,\n  rightSideInfo: null,\n  setRightSideInfo: noop,\n})\nexport const useChatWithHistoryContext = () => useContext(ChatWithHistoryContext)\n"], "names": [], "mappings": ";;;;AAGA;AAeA;AAlBA;;;AAsEO,MAAM,yBAAyB,CAAA,GAAA,kRAAA,CAAA,gBAAa,AAAD,EAA+B;IAC/E,uBAAuB;IACvB,iBAAiB,EAAE;IACnB,wBAAwB,EAAE;IAC1B,kBAAkB,EAAE;IACpB,uBAAuB,CAAC;IACxB,0BAA0B;QAAE,SAAS,CAAC;IAAE;IACxC,mCAAmC,kOAAA,CAAA,OAAI;IACvC,aAAa,EAAE;IACf,uBAAuB,kOAAA,CAAA,OAAI;IAC3B,iBAAiB,kOAAA,CAAA,OAAI;IACrB,0BAA0B,kOAAA,CAAA,OAAI;IAC9B,uBAAuB,kOAAA,CAAA,OAAI;IAC3B,yBAAyB,kOAAA,CAAA,OAAI;IAC7B,0BAA0B,kOAAA,CAAA,OAAI;IAC9B,sBAAsB;IACtB,0BAA0B,kOAAA,CAAA,OAAI;IAC9B,gCAAgC,kOAAA,CAAA,OAAI;IACpC,qBAAqB;IACrB,UAAU;IACV,gBAAgB;IAChB,gBAAgB,kOAAA,CAAA,OAAI;IACpB,wBAAwB;QAAE,SAAS;YAAE,YAAY,kOAAA,CAAA,OAAI;QAAC;IAAE;IACxD,sBAAsB;IACtB,uBAAuB,kOAAA,CAAA,OAAI;IAC3B,eAAe;IACf,kBAAkB,kOAAA,CAAA,OAAI;IACtB,cAAc;IACd,iBAAiB,kOAAA,CAAA,OAAI;IACrB,2BAA2B,CAAC;IAC5B,8BAA8B,kOAAA,CAAA,OAAI;IAClC,QAAQ;IACR,WAAW,kOAAA,CAAA,OAAI;IACf,aAAa;IACb,kBAAkB,CAAC;IACnB,UAAU,CAAC;IACX,6BAA6B,kOAAA,CAAA,OAAI;IACjC,eAAe;IACf,kBAAkB,kOAAA,CAAA,OAAI;AACxB;AACO,MAAM,4BAA4B,IAAM,CAAA,GAAA,kRAAA,CAAA,aAAU,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/utils/classnames.ts"], "sourcesContent": ["import { twMerge } from 'tailwind-merge'\nimport cn from 'classnames'\n\nconst classNames = (...cls: cn.ArgumentArray) => {\n  return twMerge(cn(cls))\n}\n\nexport default classNames\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,aAAa,CAAC,GAAG;IACrB,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/input/index.tsx"], "sourcesContent": ["import type { CSSProperties } from 'react'\nimport React from 'react'\nimport { useTranslation } from 'react-i18next'\nimport { RiCloseCircleFill, RiErrorWarningLine, RiSearchLine } from '@remixicon/react'\nimport { type VariantProps, cva } from 'class-variance-authority'\nimport cn from '@/utils/classnames'\nimport { noop } from 'lodash-es'\n\nexport const inputVariants = cva(\n  '',\n  {\n    variants: {\n      size: {\n        regular: 'px-3 radius-md system-sm-regular',\n        large: 'px-4 radius-lg system-md-regular',\n      },\n    },\n    defaultVariants: {\n      size: 'regular',\n    },\n  },\n)\n\nexport type InputProps = {\n  showLeftIcon?: boolean\n  showClearIcon?: boolean\n  onClear?: () => void\n  disabled?: boolean\n  destructive?: boolean\n  wrapperClassName?: string\n  styleCss?: CSSProperties\n  unit?: string\n} & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> & VariantProps<typeof inputVariants>\n\nconst Input = ({\n  size,\n  disabled,\n  destructive,\n  showLeftIcon,\n  showClearIcon,\n  onClear,\n  wrapperClassName,\n  className,\n  styleCss,\n  value,\n  placeholder,\n  onChange = noop,\n  unit,\n  ...props\n}: InputProps) => {\n  const { t } = useTranslation()\n  return (\n    <div className={cn('relative w-full', wrapperClassName)}>\n      {showLeftIcon && <RiSearchLine className={cn('absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-components-input-text-placeholder')} />}\n      <input\n        style={styleCss}\n        className={cn(\n          'w-full appearance-none border border-transparent bg-components-input-bg-normal py-[7px] text-components-input-text-filled caret-primary-600 outline-none placeholder:text-components-input-text-placeholder hover:border-components-input-border-hover hover:bg-components-input-bg-hover focus:border-components-input-border-active focus:bg-components-input-bg-active focus:shadow-xs',\n          inputVariants({ size }),\n          showLeftIcon && 'pl-[26px]',\n          showLeftIcon && size === 'large' && 'pl-7',\n          showClearIcon && value && 'pr-[26px]',\n          showClearIcon && value && size === 'large' && 'pr-7',\n          destructive && 'pr-[26px]',\n          destructive && size === 'large' && 'pr-7',\n          disabled && 'cursor-not-allowed border-transparent bg-components-input-bg-disabled text-components-input-text-filled-disabled hover:border-transparent hover:bg-components-input-bg-disabled',\n          destructive && 'border-components-input-border-destructive bg-components-input-bg-destructive text-components-input-text-filled hover:border-components-input-border-destructive hover:bg-components-input-bg-destructive focus:border-components-input-border-destructive focus:bg-components-input-bg-destructive',\n          className,\n        )}\n        placeholder={placeholder ?? (showLeftIcon\n          ? (t('common.operation.search') || '')\n          : (t('common.placeholder.input') || ''))}\n        value={value}\n        onChange={onChange}\n        disabled={disabled}\n        {...props}\n      />\n      {showClearIcon && value && !disabled && !destructive && (\n        <div className={cn('group absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer p-[1px]')} onClick={onClear}>\n          <RiCloseCircleFill className='h-3.5 w-3.5 cursor-pointer text-text-quaternary group-hover:text-text-tertiary' />\n        </div>\n      )}\n      {destructive && (\n        <RiErrorWarningLine className='absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 text-text-destructive-secondary' />\n      )}\n      {\n        unit && (\n          <div className='system-sm-regular absolute right-2 top-1/2 -translate-y-1/2 text-text-tertiary'>\n            {unit}\n          </div>\n        )\n      }\n    </div>\n  )\n}\n\nexport default Input\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEO,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EAC7B,IACA;IACE,UAAU;QACR,MAAM;YACJ,SAAS;YACT,OAAO;QACT;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAcF,MAAM,QAAQ,CAAC,EACb,IAAI,EACJ,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,aAAa,EACb,OAAO,EACP,gBAAgB,EAChB,SAAS,EACT,QAAQ,EACR,KAAK,EACL,WAAW,EACX,WAAW,kOAAA,CAAA,OAAI,EACf,IAAI,EACJ,GAAG,OACQ;IACX,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,2XAAA,CAAA,iBAAc,AAAD;IAC3B,qBACE,qZAAC;QAAI,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE,mBAAmB;;YACnC,8BAAgB,qZAAC,qOAAA,CAAA,eAAY;gBAAC,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE;;;;;;0BAC7C,qZAAC;gBACC,OAAO;gBACP,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EACV,6XACA,cAAc;oBAAE;gBAAK,IACrB,gBAAgB,aAChB,gBAAgB,SAAS,WAAW,QACpC,iBAAiB,SAAS,aAC1B,iBAAiB,SAAS,SAAS,WAAW,QAC9C,eAAe,aACf,eAAe,SAAS,WAAW,QACnC,YAAY,mLACZ,eAAe,uSACf;gBAEF,aAAa,eAAe,CAAC,eACxB,EAAE,8BAA8B,KAChC,EAAE,+BAA+B,EAAG;gBACzC,OAAO;gBACP,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;;;;;YAEV,iBAAiB,SAAS,CAAC,YAAY,CAAC,6BACvC,qZAAC;gBAAI,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE;gBAA2E,SAAS;0BACrG,cAAA,qZAAC,qOAAA,CAAA,oBAAiB;oBAAC,WAAU;;;;;;;;;;;YAGhC,6BACC,qZAAC,qOAAA,CAAA,qBAAkB;gBAAC,WAAU;;;;;;YAG9B,sBACE,qZAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/spinner/index.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport React from 'react'\n\ntype Props = {\n  loading?: boolean\n  className?: string\n  children?: React.ReactNode | string\n}\n\nconst Spinner: FC<Props> = ({ loading = false, children, className }) => {\n  return (\n    <div\n      className={`inline-block h-4 w-4 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] text-gray-200 ${loading ? 'motion-reduce:animate-[spin_1.5s_linear_infinite]' : 'hidden'} ${className ?? ''}`}\n      role=\"status\"\n    >\n      <span\n        className=\"!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]\"\n      >Loading...</span>\n      {children}\n    </div>\n  )\n}\n\nexport default Spinner\n"], "names": [], "mappings": ";;;;;AASA,MAAM,UAAqB,CAAC,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE;IAClE,qBACE,qZAAC;QACC,WAAW,CAAC,wIAAwI,EAAE,UAAU,sDAAsD,SAAS,CAAC,EAAE,aAAa,IAAI;QACnP,MAAK;;0BAEL,qZAAC;gBACC,WAAU;0BACX;;;;;;YACA;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/button/index.tsx"], "sourcesContent": ["import type { CSSProperties } from 'react'\nimport React from 'react'\nimport { type VariantProps, cva } from 'class-variance-authority'\nimport Spinner from '../spinner'\nimport classNames from '@/utils/classnames'\n\nconst buttonVariants = cva(\n  'btn disabled:btn-disabled',\n  {\n    variants: {\n      variant: {\n        'primary': 'btn-primary',\n        'warning': 'btn-warning',\n        'secondary': 'btn-secondary',\n        'secondary-accent': 'btn-secondary-accent',\n        'ghost': 'btn-ghost',\n        'ghost-accent': 'btn-ghost-accent',\n        'tertiary': 'btn-tertiary',\n      },\n      size: {\n        small: 'btn-small',\n        medium: 'btn-medium',\n        large: 'btn-large',\n      },\n    },\n    defaultVariants: {\n      variant: 'secondary',\n      size: 'medium',\n    },\n  },\n)\n\nexport type ButtonProps = {\n  destructive?: boolean\n  loading?: boolean\n  styleCss?: CSSProperties\n  spinnerClassName?: string\n} & React.ButtonHTMLAttributes<HTMLButtonElement> & VariantProps<typeof buttonVariants>\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, destructive, loading, styleCss, children, spinnerClassName, ...props }, ref) => {\n    return (\n      <button\n        type='button'\n        className={classNames(\n          buttonVariants({ variant, size, className }),\n          destructive && 'btn-destructive',\n        )}\n        ref={ref}\n        style={styleCss}\n        {...props}\n      >\n        {children}\n        {loading && <Spinner loading={loading} className={classNames('!text-white !h-3 !w-3 !border-2 !ml-1', spinnerClassName)} />}\n      </button>\n    )\n  },\n)\nButton.displayName = 'Button'\n\nexport default Button\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,6BACA;IACE,UAAU;QACR,SAAS;YACP,WAAW;YACX,WAAW;YACX,aAAa;YACb,oBAAoB;YACpB,SAAS;YACT,gBAAgB;YAChB,YAAY;QACd;QACA,MAAM;YACJ,OAAO;YACP,QAAQ;YACR,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,4WAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG,OAAO,EAAE;IACnG,qBACE,qZAAC;QACC,MAAK;QACL,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAU,AAAD,EAClB,eAAe;YAAE;YAAS;YAAM;QAAU,IAC1C,eAAe;QAEjB,KAAK;QACL,OAAO;QACN,GAAG,KAAK;;YAER;YACA,yBAAW,qZAAC,8IAAA,CAAA,UAAO;gBAAC,SAAS;gBAAS,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAU,AAAD,EAAE,yCAAyC;;;;;;;;;;;;AAG5G;AAEF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/btn-fold.tsx"], "sourcesContent": ["import cls from 'classnames'\nimport Button from '../../button'\nconst IconFold = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" className={className}>\n    <g stroke=\"#1E6AFF\" stroke-width=\"1.4\" fill=\"none\" fill-rule=\"evenodd\">\n      <rect x=\".7\" y=\".7\" width=\"18.6\" height=\"18.6\" rx=\"4\" />\n      <path d=\"M13.5 0L13.5 20\" />\n      <path d=\"M6,7 L9,10 L6,13\" transform=\"matrix(-1 0 0 1 15 0)\" />\n    </g>\n  </svg>\n)\nconst IconUnfold = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" className={className}>\n    <g stroke=\"#1E6AFF\" stroke-width=\"1.4\" fill=\"none\" fill-rule=\"evenodd\">\n      <rect x=\".7\" y=\".7\" width=\"18.6\" height=\"18.6\" rx=\"4\" />\n      <path d=\"M13.5 0L13.5 20\" />\n      <path d=\"M6,7 L9,10 L6,13\" />\n    </g>\n  </svg>\n)\n\nconst SvgClass = 'w-[20px] h-[20px]'\n\ntype Props = {\n  className?: string\n  isFold?: boolean\n  onClick?: () => void\n}\n\nconst BtnFold = ({ className, isFold, onClick }: Props) => {\n  return (\n    <div className={cls(className, 'cursor-pointer')} onClick={onClick}>\n      {isFold\n        ? <Button\n          variant='secondary-accent'\n          className={'h-[40px] rounded-[20px] border-[#356CFF] text-[#434B5B]'}\n        >\n          <IconUnfold className=\"w-[18px] mr-[8px]\" />\n        历史对话\n        </Button>\n        : <IconFold className={SvgClass} />}\n    </div>\n  )\n}\n\nexport default BtnFold\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AACA,MAAM,WAAW,CAAC,EAAE,SAAS,EAAyB,iBACpD,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAE,QAAO;YAAU,gBAAa;YAAM,MAAK;YAAO,aAAU;;8BAC3D,qZAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAO,QAAO;oBAAO,IAAG;;;;;;8BAClD,qZAAC;oBAAK,GAAE;;;;;;8BACR,qZAAC;oBAAK,GAAE;oBAAmB,WAAU;;;;;;;;;;;;;;;;;AAI3C,MAAM,aAAa,CAAC,EAAE,SAAS,EAAyB,iBACtD,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAE,QAAO;YAAU,gBAAa;YAAM,MAAK;YAAO,aAAU;;8BAC3D,qZAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAO,QAAO;oBAAO,IAAG;;;;;;8BAClD,qZAAC;oBAAK,GAAE;;;;;;8BACR,qZAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;;AAKd,MAAM,WAAW;AAQjB,MAAM,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAS;IACpD,qBACE,qZAAC;QAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAG,AAAD,EAAE,WAAW;QAAmB,SAAS;kBACxD,uBACG,qZAAC,6IAAA,CAAA,UAAM;YACP,SAAQ;YACR,WAAW;;8BAEX,qZAAC;oBAAW,WAAU;;;;;;gBAAsB;;;;;;iCAG5C,qZAAC;YAAS,WAAW;;;;;;;;;;;AAG/B;uCAEe", "debugId": null}}, {"offset": {"line": 436, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/config/index.ts"], "sourcesContent": ["export const DEFAULT_APP_ICON = process.env.NEXT_PUBLIC_SITE_LOGO_ICON // '/logo/default-robot-logo.svg' // APP默认图标\n\nexport const WEBSITE_LOGO = process.env.NEXT_PUBLIC_SITE_LOGO_ICON // 网站logo\n\nexport const appDefaultIconBackground = '#D5F5F6'\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,mBAAmB,QAAQ,GAAG,CAAC,0BAA0B,CAAC,4CAA4C;;AAE5G,MAAM,eAAe,QAAQ,GAAG,CAAC,0BAA0B,CAAC,SAAS;;AAErE,MAAM,2BAA2B", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/app-icon/index.tsx"], "sourcesContent": ["'use client'\n\nimport type { FC } from 'react'\nimport { init } from 'emoji-mart'\nimport data from '@emoji-mart/data'\nimport { cva } from 'class-variance-authority'\nimport type { AppIconType } from '@/types/app'\nimport classNames from '@/utils/classnames'\nimport { DEFAULT_APP_ICON } from '@/config'\n\ninit({ data })\n\nexport type AppIconProps = {\n  size?: 'xs' | 'tiny' | 'small' | 'medium' | 'large' | 'xl' | 'xxl'\n  rounded?: boolean\n  iconType?: AppIconType | null\n  icon?: string\n  background?: string | null\n  imageUrl?: string | null\n  className?: string\n  innerIcon?: React.ReactNode\n  onClick?: () => void\n}\nconst appIconVariants = cva(\n  'flex items-center justify-center relative text-lg rounded-lg grow-0 shrink-0 overflow-hidden leading-none',\n  {\n    variants: {\n      size: {\n        xs: 'w-4 h-4 text-xs',\n        tiny: 'w-6 h-6 text-base',\n        small: 'w-8 h-8 text-xl',\n        medium: 'w-9 h-9 text-[22px]',\n        large: 'w-10 h-10 text-[24px]',\n        xl: 'w-12 h-12 text-[28px]',\n        xxl: 'w-14 h-14 text-[32px]',\n      },\n      rounded: {\n        true: 'rounded-full',\n      },\n    },\n    defaultVariants: {\n      size: 'medium',\n      rounded: false,\n    },\n  })\nconst AppIcon: FC<AppIconProps> = ({\n  size = 'medium',\n  rounded = false,\n  iconType,\n  icon,\n  background,\n  imageUrl,\n  className,\n  innerIcon,\n  onClick,\n}) => {\n  const isDefaultIcon = icon === 'default-icon'\n  const isValidImageIcon = (iconType === 'image' && imageUrl)|| isDefaultIcon\n\n  return <span\n    className={classNames(appIconVariants({ size, rounded }), className)}\n    style={{ background: isValidImageIcon ? undefined : (background || '#FFEAD5') }}\n    onClick={onClick}\n  >\n    {isValidImageIcon\n\n      ? <img src={isDefaultIcon ? DEFAULT_APP_ICON : (imageUrl as string)} className=\"h-full w-full\" alt=\"app icon\" />\n      : (innerIcon || ((icon && icon !== '') ? <em-emoji id={icon} /> : <em-emoji id='🤖' />))\n    }\n  </span>\n}\n\nexport default AppIcon\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AACA;AARA;;;;;;;AAUA,CAAA,GAAA,yMAAA,CAAA,OAAI,AAAD,EAAE;IAAE,MAAA,2MAAA,CAAA,UAAI;AAAC;AAaZ,MAAM,kBAAkB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACxB,6GACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,OAAO;YACP,IAAI;YACJ,KAAK;QACP;QACA,SAAS;YACP,MAAM;QACR;IACF;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;AACF;AACF,MAAM,UAA4B,CAAC,EACjC,OAAO,QAAQ,EACf,UAAU,KAAK,EACf,QAAQ,EACR,IAAI,EACJ,UAAU,EACV,QAAQ,EACR,SAAS,EACT,SAAS,EACT,OAAO,EACR;IACC,MAAM,gBAAgB,SAAS;IAC/B,MAAM,mBAAmB,AAAC,aAAa,WAAW,YAAY;IAE9D,qBAAO,qZAAC;QACN,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAU,AAAD,EAAE,gBAAgB;YAAE;YAAM;QAAQ,IAAI;QAC1D,OAAO;YAAE,YAAY,mBAAmB,YAAa,cAAc;QAAW;QAC9E,SAAS;kBAER,iCAEG,qZAAC;YAAI,KAAK,gBAAgB,sHAAA,CAAA,mBAAgB,GAAI;YAAqB,WAAU;YAAgB,KAAI;;;;;mBAChG,aAAa,CAAC,AAAC,QAAQ,SAAS,mBAAM,qZAAC;YAAS,IAAI;;;;;iCAAW,qZAAC;YAAS,IAAG;;;;;gBAAO;;;;;;AAG5F;uCAEe", "debugId": null}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/action-button/index.tsx"], "sourcesContent": ["import type { CSSProperties } from 'react'\nimport React from 'react'\nimport { type VariantProps, cva } from 'class-variance-authority'\nimport classNames from '@/utils/classnames'\n\nenum ActionButtonState {\n  Destructive = 'destructive',\n  Active = 'active',\n  Disabled = 'disabled',\n  Default = '',\n  Hover = 'hover',\n}\n\nconst actionButtonVariants = cva(\n  'action-btn',\n  {\n    variants: {\n      size: {\n        xs: 'action-btn-xs',\n        m: 'action-btn-m',\n        l: 'action-btn-l',\n        xl: 'action-btn-xl',\n      },\n    },\n    defaultVariants: {\n      size: 'm',\n    },\n  },\n)\n\nexport type ActionButtonProps = {\n  size?: 'xs' | 's' | 'm' | 'l' | 'xl'\n  state?: ActionButtonState\n  styleCss?: CSSProperties\n} & React.ButtonHTMLAttributes<HTMLButtonElement> & VariantProps<typeof actionButtonVariants>\n\nfunction getActionButtonState(state: ActionButtonState) {\n  switch (state) {\n    case ActionButtonState.Destructive:\n      return 'action-btn-destructive'\n    case ActionButtonState.Active:\n      return 'action-btn-active'\n    case ActionButtonState.Disabled:\n      return 'action-btn-disabled'\n    case ActionButtonState.Hover:\n      return 'action-btn-hover'\n    default:\n      return ''\n  }\n}\n\nconst ActionButton = React.forwardRef<HTMLButtonElement, ActionButtonProps>(\n  ({ className, size, state = ActionButtonState.Default, styleCss, children, ...props }, ref) => {\n    return (\n      <button\n        type='button'\n        className={classNames(\n          actionButtonVariants({ className, size }),\n          getActionButtonState(state),\n        )}\n        ref={ref}\n        style={styleCss}\n        {...props}\n      >\n        {children}\n      </button>\n    )\n  },\n)\nActionButton.displayName = 'ActionButton'\n\nexport default ActionButton\nexport { ActionButton, ActionButtonState, actionButtonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AACA;;;;;AAEA,IAAA,AAAK,2CAAA;;;;;;WAAA;EAAA;AAQL,MAAM,uBAAuB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EAC7B,cACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,GAAG;YACH,GAAG;YACH,IAAI;QACN;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AASF,SAAS,qBAAqB,KAAwB;IACpD,OAAQ;QACN;YACE,OAAO;QACT;YACE,OAAO;QACT;YACE,OAAO;QACT;YACE,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,6BAAe,4WAAA,CAAA,UAAK,CAAC,UAAU,CACnC,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,UAAiC,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACrF,qBACE,qZAAC;QACC,MAAK;QACL,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAU,AAAD,EAClB,qBAAqB;YAAE;YAAW;QAAK,IACvC,qBAAqB;QAEvB,KAAK;QACL,OAAO;QACN,GAAG,KAAK;kBAER;;;;;;AAGP;AAEF,aAAa,WAAW,GAAG;uCAEZ", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/portal-to-follow-elem/index.tsx"], "sourcesContent": ["'use client'\nimport React from 'react'\nimport {\n  FloatingPortal,\n  autoUpdate,\n  flip,\n  offset,\n  shift,\n  size,\n  useDismiss,\n  useFloating,\n  useFocus,\n  useHover,\n  useInteractions,\n  useMergeRefs,\n  useRole,\n} from '@floating-ui/react'\n\nimport type { OffsetOptions, Placement } from '@floating-ui/react'\nimport cn from '@/utils/classnames'\nexport type PortalToFollowElemOptions = {\n  /*\n  * top, bottom, left, right\n  * start, end. Default is middle\n  * combine: top-start, top-end\n  */\n  placement?: Placement\n  open?: boolean\n  offset?: number | OffsetOptions\n  onOpenChange?: (open: boolean) => void\n  triggerPopupSameWidth?: boolean\n}\n\nexport function usePortalToFollowElem({\n  placement = 'bottom',\n  open,\n  offset: offsetValue = 0,\n  onOpenChange: setControlledOpen,\n  triggerPopupSameWidth,\n}: PortalToFollowElemOptions = {}) {\n  const setOpen = setControlledOpen\n\n  const data = useFloating({\n    placement,\n    open,\n    onOpenChange: setOpen,\n    whileElementsMounted: autoUpdate,\n    middleware: [\n      offset(offsetValue),\n      flip({\n        crossAxis: placement.includes('-'),\n        fallbackAxisSideDirection: 'start',\n        padding: 5,\n      }),\n      shift({ padding: 5 }),\n      size({\n        apply({ rects, elements }) {\n          if (triggerPopupSameWidth)\n            elements.floating.style.width = `${rects.reference.width}px`\n        },\n      }),\n    ],\n  })\n\n  const context = data.context\n\n  const hover = useHover(context, {\n    move: false,\n    enabled: open == null,\n  })\n  const focus = useFocus(context, {\n    enabled: open == null,\n  })\n  const dismiss = useDismiss(context)\n  const role = useRole(context, { role: 'tooltip' })\n\n  const interactions = useInteractions([hover, focus, dismiss, role])\n\n  return React.useMemo(\n    () => ({\n      open,\n      setOpen,\n      ...interactions,\n      ...data,\n    }),\n    [open, setOpen, interactions, data],\n  )\n}\n\ntype ContextType = ReturnType<typeof usePortalToFollowElem> | null\n\nconst PortalToFollowElemContext = React.createContext<ContextType>(null)\n\nexport function usePortalToFollowElemContext() {\n  const context = React.useContext(PortalToFollowElemContext)\n\n  if (context == null)\n    throw new Error('PortalToFollowElem components must be wrapped in <PortalToFollowElem />')\n\n  return context\n}\n\nexport function PortalToFollowElem({\n  children,\n  ...options\n}: { children: React.ReactNode } & PortalToFollowElemOptions) {\n  // This can accept any props as options, e.g. `placement`,\n  // or other positioning options.\n  const tooltip = usePortalToFollowElem(options)\n  return (\n    <PortalToFollowElemContext.Provider value={tooltip}>\n      {children}\n    </PortalToFollowElemContext.Provider>\n  )\n}\n\nexport const PortalToFollowElemTrigger = (\n  {\n    ref: propRef,\n    children,\n    asChild = false,\n    ...props\n  }: React.HTMLProps<HTMLElement> & { ref?: React.RefObject<HTMLElement>, asChild?: boolean },\n) => {\n  const context = usePortalToFollowElemContext()\n  const childrenRef = (children as any).props?.ref\n  const ref = useMergeRefs([context.refs.setReference, propRef, childrenRef])\n\n  // `asChild` allows the user to pass any element as the anchor\n  if (asChild && React.isValidElement(children)) {\n    return React.cloneElement(\n      children,\n      context.getReferenceProps({\n        ref,\n        ...props,\n        ...children.props,\n        'data-state': context.open ? 'open' : 'closed',\n      }),\n    )\n  }\n\n  return (\n    <div\n      ref={ref}\n      className={cn('inline-block', props.className)}\n      // The user can style the trigger based on the state\n      data-state={context.open ? 'open' : 'closed'}\n      {...context.getReferenceProps(props)}\n    >\n      {children}\n    </div>\n  )\n}\nPortalToFollowElemTrigger.displayName = 'PortalToFollowElemTrigger'\n\nexport const PortalToFollowElemContent = (\n  {\n    ref: propRef,\n    style,\n    ...props\n  }: React.HTMLProps<HTMLDivElement> & {\n    ref?: React.RefObject<HTMLDivElement>;\n  },\n) => {\n  const context = usePortalToFollowElemContext()\n  const ref = useMergeRefs([context.refs.setFloating, propRef])\n\n  if (!context.open)\n    return null\n\n  const body = document.body\n\n  return (\n    <FloatingPortal root={body}>\n      <div\n        ref={ref}\n        style={{\n          ...context.floatingStyles,\n          ...style,\n        }}\n        {...context.getFloatingProps(props)}\n      />\n    </FloatingPortal>\n  )\n}\n\nPortalToFollowElemContent.displayName = 'PortalToFollowElemContent'\n"], "names": [], "mappings": ";;;;;;;;AACA;AACA;AAAA;AAAA;AAiBA;AAnBA;;;;;AAiCO,SAAS,sBAAsB,EACpC,YAAY,QAAQ,EACpB,IAAI,EACJ,QAAQ,cAAc,CAAC,EACvB,cAAc,iBAAiB,EAC/B,qBAAqB,EACK,GAAG,CAAC,CAAC;IAC/B,MAAM,UAAU;IAEhB,MAAM,OAAO,CAAA,GAAA,kVAAA,CAAA,cAAW,AAAD,EAAE;QACvB;QACA;QACA,cAAc;QACd,sBAAsB,6PAAA,CAAA,aAAU;QAChC,YAAY;YACV,CAAA,GAAA,qWAAA,CAAA,SAAM,AAAD,EAAE;YACP,CAAA,GAAA,qWAAA,CAAA,OAAI,AAAD,EAAE;gBACH,WAAW,UAAU,QAAQ,CAAC;gBAC9B,2BAA2B;gBAC3B,SAAS;YACX;YACA,CAAA,GAAA,qWAAA,CAAA,QAAK,AAAD,EAAE;gBAAE,SAAS;YAAE;YACnB,CAAA,GAAA,qWAAA,CAAA,OAAI,AAAD,EAAE;gBACH,OAAM,EAAE,KAAK,EAAE,QAAQ,EAAE;oBACvB,IAAI,uBACF,SAAS,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,MAAM,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChE;YACF;SACD;IACH;IAEA,MAAM,UAAU,KAAK,OAAO;IAE5B,MAAM,QAAQ,CAAA,GAAA,kVAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;QAC9B,MAAM;QACN,SAAS,QAAQ;IACnB;IACA,MAAM,QAAQ,CAAA,GAAA,kVAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;QAC9B,SAAS,QAAQ;IACnB;IACA,MAAM,UAAU,CAAA,GAAA,kVAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,MAAM,OAAO,CAAA,GAAA,kVAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,MAAM;IAAU;IAEhD,MAAM,eAAe,CAAA,GAAA,kVAAA,CAAA,kBAAe,AAAD,EAAE;QAAC;QAAO;QAAO;QAAS;KAAK;IAElE,OAAO,4WAAA,CAAA,UAAK,CAAC,OAAO,CAClB,IAAM,CAAC;YACL;YACA;YACA,GAAG,YAAY;YACf,GAAG,IAAI;QACT,CAAC,GACD;QAAC;QAAM;QAAS;QAAc;KAAK;AAEvC;AAIA,MAAM,0CAA4B,4WAAA,CAAA,UAAK,CAAC,aAAa,CAAc;AAE5D,SAAS;IACd,MAAM,UAAU,4WAAA,CAAA,UAAK,CAAC,UAAU,CAAC;IAEjC,IAAI,WAAW,MACb,MAAM,IAAI,MAAM;IAElB,OAAO;AACT;AAEO,SAAS,mBAAmB,EACjC,QAAQ,EACR,GAAG,SACuD;IAC1D,0DAA0D;IAC1D,gCAAgC;IAChC,MAAM,UAAU,sBAAsB;IACtC,qBACE,qZAAC,0BAA0B,QAAQ;QAAC,OAAO;kBACxC;;;;;;AAGP;AAEO,MAAM,4BAA4B,CACvC,EACE,KAAK,OAAO,EACZ,QAAQ,EACR,UAAU,KAAK,EACf,GAAG,OACsF;IAE3F,MAAM,UAAU;IAChB,MAAM,cAAc,AAAC,SAAiB,KAAK,EAAE;IAC7C,MAAM,MAAM,CAAA,GAAA,kVAAA,CAAA,eAAY,AAAD,EAAE;QAAC,QAAQ,IAAI,CAAC,YAAY;QAAE;QAAS;KAAY;IAE1E,8DAA8D;IAC9D,IAAI,yBAAW,4WAAA,CAAA,UAAK,CAAC,cAAc,CAAC,WAAW;QAC7C,qBAAO,4WAAA,CAAA,UAAK,CAAC,YAAY,CACvB,UACA,QAAQ,iBAAiB,CAAC;YACxB;YACA,GAAG,KAAK;YACR,GAAG,SAAS,KAAK;YACjB,cAAc,QAAQ,IAAI,GAAG,SAAS;QACxC;IAEJ;IAEA,qBACE,qZAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE,gBAAgB,MAAM,SAAS;QAC7C,oDAAoD;QACpD,cAAY,QAAQ,IAAI,GAAG,SAAS;QACnC,GAAG,QAAQ,iBAAiB,CAAC,MAAM;kBAEnC;;;;;;AAGP;AACA,0BAA0B,WAAW,GAAG;AAEjC,MAAM,4BAA4B,CACvC,EACE,KAAK,OAAO,EACZ,KAAK,EACL,GAAG,OAGJ;IAED,MAAM,UAAU;IAChB,MAAM,MAAM,CAAA,GAAA,kVAAA,CAAA,eAAY,AAAD,EAAE;QAAC,QAAQ,IAAI,CAAC,WAAW;QAAE;KAAQ;IAE5D,IAAI,CAAC,QAAQ,IAAI,EACf,OAAO;IAET,MAAM,OAAO,SAAS,IAAI;IAE1B,qBACE,qZAAC,kVAAA,CAAA,iBAAc;QAAC,MAAM;kBACpB,cAAA,qZAAC;YACC,KAAK;YACL,OAAO;gBACL,GAAG,QAAQ,cAAc;gBACzB,GAAG,KAAK;YACV;YACC,GAAG,QAAQ,gBAAgB,CAAC,MAAM;;;;;;;;;;;AAI3C;AAEA,0BAA0B,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 769, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/sidebar/operation.tsx"], "sourcesContent": ["'use client'\nimport type { FC } from 'react'\nimport React, { useEffect, useRef, useState } from 'react'\nimport {\n  RiDeleteBinLine,\n  RiEditLine,\n  RiMoreFill,\n  RiPushpinLine,\n  RiUnpinLine,\n} from '@remixicon/react'\nimport { useTranslation } from 'react-i18next'\nimport { useBoolean } from 'ahooks'\nimport { PortalToFollowElem, PortalToFollowElemContent, PortalToFollowElemTrigger } from '@/components/base/portal-to-follow-elem'\nimport ActionButton, { ActionButtonState } from '@/components/base/action-button'\nimport cn from '@/utils/classnames'\nimport { EmbedSource } from '@/models/share'\n\nconst LarkRenameIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 14 14\" className={className}>\n    <g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" fill-rule=\"evenodd\">\n      <path d=\"M9.33333333,4.66666667 L9.33333333,8.33333333 C9.33333333,8.88561808 8.88561808,9.33333333 8.33333333,9.33333333 L1,9.33333333 C0.44771525,9.33333333 3.38176876e-17,8.88561808 0,8.33333333 L0,1 C-6.76353751e-17,0.44771525 0.44771525,6.76353751e-17 1,0 L6.99195588,0 L6.99195588,0\" transform=\"translate(2.3333 2.3333)\" />\n      <path transform=\"rotate(42 4.83572284 7.99757716)\" d=\"M6.90262064 0.08444366L6.51404603 7.49888968\" />\n    </g>\n  </svg>\n\n)\nconst LarkPinIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 14 14\" className={className}>\n    <g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" fill-rule=\"evenodd\">\n      <path d=\"M3,8 L3,1 M3,1 L0,3.8 M3,1 L6,3.8\" transform=\"translate(4 3)\" />\n      <path d=\"M0 0.5L6 0.5\" transform=\"translate(4 3)\" />\n    </g>\n  </svg>\n\n)\nconst LarkDeleteIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 14 14\" className={className}>\n    <g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" fill-rule=\"evenodd\">\n      <path d=\"M8.75,2.04166667 L8.22889873,9.85818568 C8.19387888,10.3834835 7.75757745,10.7916667 7.23111357,10.7916667 L2.10221976,10.7916667 C1.57575588,10.7916667 1.13945446,10.3834835 1.1044346,9.85818568 L0.583333333,2.04166667 L0.583333333,2.04166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M0 2.04166667L9.33333333 2.04166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M3.5 0.29166667L5.83333333 0.29166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M3.5 7.875L3.5 4.95833333\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M5.83333333 7.875L5.83333333 4.95833333\" transform=\"translate(2.3333 1.4583)\" />\n    </g>\n  </svg>\n)\n\ntype Props = {\n  isActive?: boolean\n  isItemHovering?: boolean\n  isPinned: boolean\n  isShowRenameConversation?: boolean\n  onRenameConversation?: () => void\n  isShowDelete: boolean\n  togglePin: () => void\n  onDelete: () => void\n  embedSource?: EmbedSource\n  isMobile?: boolean\n}\n\nconst Operation: FC<Props> = ({\n  isActive,\n  isItemHovering,\n  isPinned,\n  togglePin,\n  isShowRenameConversation,\n  onRenameConversation,\n  isShowDelete,\n  onDelete,\n  embedSource,\n  isMobile\n}) => {\n  const { t } = useTranslation()\n  const [open, setOpen] = useState(false)\n  const ref = useRef(null)\n  const [isHovering, { setTrue: setIsHovering, setFalse: setNotHovering }] = useBoolean(false)\n  const isEmbedMobile = embedSource && isMobile\n\n  useEffect(() => {\n    if (!isItemHovering && !isHovering)\n      setOpen(false)\n  }, [isItemHovering, isHovering])\n\n  return (\n    <PortalToFollowElem\n      open={open}\n      onOpenChange={setOpen}\n      placement='bottom-end'\n      offset={4}\n    >\n      <PortalToFollowElemTrigger\n        onClick={() => setOpen(v => !v)}\n      >\n        <ActionButton\n          className={cn((isItemHovering || open) ? 'opacity-100' : 'opacity-0')}\n          state={\n            isActive\n              ? ActionButtonState.Active\n              : open\n                ? ActionButtonState.Hover\n                : ActionButtonState.Default\n          }\n        >\n          <RiMoreFill className='h-4 w-4' />\n        </ActionButton>\n      </PortalToFollowElemTrigger>\n      <PortalToFollowElemContent className=\"z-50\">\n        <div\n          ref={ref}\n          className={`min-w-[120px] rounded-xl border-[0.5px] border-components-panel-border bg-components-panel-bg-blur p-1 shadow-lg backdrop-blur-sm ${isEmbedMobile && 'flex !bg-[#262933] gap-[10px]'}`}\n          onMouseEnter={setIsHovering}\n          onMouseLeave={setNotHovering}\n          onClick={(e) => {\n            e.stopPropagation()\n          }}\n        >\n          <div className={cn('system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-2 py-1.5 text-text-secondary hover:bg-state-base-hover',isEmbedMobile && 'flex-col !h-auto !gap-[0px] !px-[0px]')} onClick={togglePin}>\n            {isPinned && <RiUnpinLine className='h-4 w-4 shrink-0 text-text-tertiary' />}\n            {!isPinned && <RiPushpinLine className='h-4 w-4 shrink-0 text-text-tertiary' />}\n            <span className={cn('grow',isEmbedMobile && '!text-[#fff] !text-[12px]')}>{isPinned ? t('explore.sidebar.action.unpin') : t('explore.sidebar.action.pin')}</span>\n          </div>\n          {isShowRenameConversation && (\n            <div className={cn('system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-2 py-1.5 text-text-secondary hover:bg-state-base-hover',isEmbedMobile && 'flex-col !h-auto !gap-[0px] !px-[0px]')} onClick={onRenameConversation}>\n              <RiEditLine className='h-4 w-4 shrink-0 text-text-tertiary' />\n              <span className={cn('grow',isEmbedMobile && '!text-[#fff] !text-[12px]')}>{t('explore.sidebar.action.rename')}</span>\n            </div>\n          )}\n          {isShowDelete && (\n            <div className={cn('system-md-regular group flex cursor-pointer items-center space-x-1 rounded-lg px-2 py-1.5 text-text-secondary hover:bg-state-destructive-hover hover:text-text-destructive',isEmbedMobile && 'flex-col !h-auto !gap-[0px] !px-[0px]')} onClick={onDelete} >\n              <RiDeleteBinLine className={cn('h-4 w-4 shrink-0 text-text-tertiary group-hover:text-text-destructive')} />\n              <span className={cn('grow',isEmbedMobile && '!text-[#fff] !text-[12px]')}>{t('explore.sidebar.action.delete')}</span>\n            </div>\n          )}\n        </div>\n      </PortalToFollowElemContent>\n    </PortalToFollowElem>\n  )\n}\nexport default React.memo(Operation)\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AAAA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;AAiBA,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAyB,iBAC1D,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAE,QAAO;YAAe,kBAAe;YAAQ,mBAAgB;YAAQ,MAAK;YAAO,aAAU;;8BAC5F,qZAAC;oBAAK,GAAE;oBAA0R,WAAU;;;;;;8BAC5S,qZAAC;oBAAK,WAAU;oBAAmC,GAAE;;;;;;;;;;;;;;;;;AAK3D,MAAM,cAAc,CAAC,EAAE,SAAS,EAAyB,iBACvD,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAE,QAAO;YAAe,kBAAe;YAAQ,mBAAgB;YAAQ,MAAK;YAAO,aAAU;;8BAC5F,qZAAC;oBAAK,GAAE;oBAAoC,WAAU;;;;;;8BACtD,qZAAC;oBAAK,GAAE;oBAAe,WAAU;;;;;;;;;;;;;;;;;AAKvC,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAyB,iBAC1D,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAE,QAAO;YAAe,kBAAe;YAAQ,mBAAgB;YAAQ,MAAK;YAAO,aAAU;;8BAC5F,qZAAC;oBAAK,GAAE;oBAAsP,WAAU;;;;;;8BACxQ,qZAAC;oBAAK,GAAE;oBAAsC,WAAU;;;;;;8BACxD,qZAAC;oBAAK,GAAE;oBAAwC,WAAU;;;;;;8BAC1D,qZAAC;oBAAK,GAAE;oBAA4B,WAAU;;;;;;8BAC9C,qZAAC;oBAAK,GAAE;oBAA0C,WAAU;;;;;;;;;;;;;;;;;AAkBlE,MAAM,YAAuB,CAAC,EAC5B,QAAQ,EACR,cAAc,EACd,QAAQ,EACR,SAAS,EACT,wBAAwB,EACxB,oBAAoB,EACpB,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,QAAQ,EACT;IACC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,2XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,MAAM,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,CAAC,YAAY,EAAE,SAAS,aAAa,EAAE,UAAU,cAAc,EAAE,CAAC,GAAG,CAAA,GAAA,4TAAA,CAAA,aAAU,AAAD,EAAE;IACtF,MAAM,gBAAgB,eAAe;IAErC,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,kBAAkB,CAAC,YACtB,QAAQ;IACZ,GAAG;QAAC;QAAgB;KAAW;IAE/B,qBACE,qZAAC,qKAAA,CAAA,qBAAkB;QACjB,MAAM;QACN,cAAc;QACd,WAAU;QACV,QAAQ;;0BAER,qZAAC,qKAAA,CAAA,4BAAyB;gBACxB,SAAS,IAAM,QAAQ,CAAA,IAAK,CAAC;0BAE7B,cAAA,qZAAC,uJAAA,CAAA,UAAY;oBACX,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE,AAAC,kBAAkB,OAAQ,gBAAgB;oBACzD,OACE,WACI,uJAAA,CAAA,oBAAiB,CAAC,MAAM,GACxB,OACE,uJAAA,CAAA,oBAAiB,CAAC,KAAK,GACvB,uJAAA,CAAA,oBAAiB,CAAC,OAAO;8BAGjC,cAAA,qZAAC,qOAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAG1B,qZAAC,qKAAA,CAAA,4BAAyB;gBAAC,WAAU;0BACnC,cAAA,qZAAC;oBACC,KAAK;oBACL,WAAW,CAAC,kIAAkI,EAAE,iBAAiB,iCAAiC;oBAClM,cAAc;oBACd,cAAc;oBACd,SAAS,CAAC;wBACR,EAAE,eAAe;oBACnB;;sCAEA,qZAAC;4BAAI,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE,qIAAoI,iBAAiB;4BAA0C,SAAS;;gCACxN,0BAAY,qZAAC,qOAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACnC,CAAC,0BAAY,qZAAC,qOAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACvC,qZAAC;oCAAK,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE,QAAO,iBAAiB;8CAA+B,WAAW,EAAE,kCAAkC,EAAE;;;;;;;;;;;;wBAE7H,0CACC,qZAAC;4BAAI,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE,qIAAoI,iBAAiB;4BAA0C,SAAS;;8CACzN,qZAAC,qOAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,qZAAC;oCAAK,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE,QAAO,iBAAiB;8CAA+B,EAAE;;;;;;;;;;;;wBAGhF,8BACC,qZAAC;4BAAI,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE,8KAA6K,iBAAiB;4BAA0C,SAAS;;8CAClQ,qZAAC,qOAAA,CAAA,kBAAe;oCAAC,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE;;;;;;8CAC/B,qZAAC;oCAAK,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE,QAAO,iBAAiB;8CAA+B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3F;qDACe,4WAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/sidebar/item.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport {\n  memo,\n  useRef,\n} from 'react'\nimport { useHover } from 'ahooks'\nimport type { ConversationItem } from '@/models/share'\nimport Operation from '@/components/base/chat/chat-with-history/sidebar/operation'\nimport cn from '@/utils/classnames'\nimport type { ConversationItem, EmbedSource } from '@/models/share'\nimport { MessageDotsCircle } from '@/components/base/icons/src/vender/solid/communication'\nimport ItemOperation from '@/components/explore/item-operation'\n\ntype ItemProps = {\n  isPin?: boolean\n  item: ConversationItem\n  embedSource?: EmbedSource\n  isMobile?: boolean\n  onOperate: (type: string, item: ConversationItem) => void\n  onChangeConversation: (conversationId: string) => void\n  currentConversationId: string\n}\n\nconst LarkChatIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 16 16\" className={className}>\n    <path d=\"M4.58035669,13.2913263 L5.50680349,12.4266908 L5.51361828,12.4205913 C5.70676076,12.2403023 5.8042226,12.1493067 5.91296138,12.084553 C6.01052049,12.0263953 6.11454787,11.9840536 6.22185803,11.9583082 C6.34281601,11.9293711 6.46881365,11.9293711 6.72173297,11.9293711 L11.9991107,11.9293711 C12.6787052,11.9293711 13.0188976,11.9293711 13.2787229,11.7748985 C13.5074835,11.638937 13.6936288,11.4216966 13.8101672,11.1548093 C13.9425723,10.8516798 13.9425723,10.4552142 13.9425723,9.66235387 L13.9425723,4.26737899 C13.9425723,3.47450452 13.9425723,3.07747862 13.8101672,2.7743491 C13.6936288,2.50744051 13.5071187,2.29059723 13.2783581,2.1546074 C13.0182897,2 12.6782796,2 11.9973477,2 L4.94546777,2 C4.26452973,2 3.92380843,2 3.6637278,2.1546074 C3.43494901,2.29059723 3.24908334,2.50744051 3.13252063,2.7743491 C3,3.0777765 3,3.47528469 3,4.2697124 L3,12.4054136 C3,13.1612515 3,13.5390641 3.13280635,13.7331833 C3.2483052,13.9019826 3.42338028,14.0002124 3.60851644,14 C3.82139203,13.999716 4.07446333,13.7635388 4.58035669,13.2913263 Z\" stroke=\"#B9C2CB\" stroke-width=\"1.2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-dasharray=\"1\" fill=\"none\" fill-rule=\"evenodd\" />\n  </svg>\n\n)\n\nconst Item: FC<ItemProps> = ({\n  isPin,\n  item,\n  embedSource,\n  isMobile,\n  onOperate,\n  onChangeConversation,\n  currentConversationId,\n}) => {\n  const ref = useRef(null)\n  const isHovering = useHover(ref)\n  const isSelected = currentConversationId === item.id\n\n  return (\n    <div\n      ref={ref}\n      key={item.id}\n      className={cn(\n        'system-sm-medium group flex cursor-pointer rounded-lg p-1 pl-3 text-components-menu-item-text hover:bg-state-base-hover',\n        isSelected && 'bg-state-accent-active text-text-accent hover:bg-state-accent-active',\n        (currentConversationId === item.id && !embedSource) && 'text-primary-600 bg-primary-50',\n        (currentConversationId === item.id && embedSource) && 'bg-[#ECECEC]'\n      )}\n      onClick={() => onChangeConversation(item.id)}\n    >\n      {embedSource && !isMobile && <LarkChatIcon className=\"w-[16px] mr-[6px] shrink-0\" />}\n      <div className={`grow truncate p-1 pl-0 ${embedSource && 'overflow-hidden text-ellipsis text-nowrap'}`} title={item.name}>{item.name}</div>\n      {item.id !== '' && (\n        <div className='shrink-0' onClick={e => e.stopPropagation()}>\n          <Operation\n            embedSource={embedSource}\n            isMobile={isMobile}\n            isActive={isSelected}\n            isPinned={!!isPin}\n            isItemHovering={isHovering}\n            togglePin={() => onOperate(isPin ? 'unpin' : 'pin', item)}\n            isShowDelete\n            isShowRenameConversation\n            onRenameConversation={() => onOperate('rename', item)}\n            onDelete={() => onOperate('delete', item)}\n          />\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default memo(Item)\n"], "names": [], "mappings": ";;;;AACA;AAIA;AAEA;AACA;;;;;;AAeA,MAAM,eAAe,CAAC,EAAE,SAAS,EAAyB,iBACxD,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAK,GAAE;YAAkhC,QAAO;YAAU,gBAAa;YAAM,kBAAe;YAAQ,mBAAgB;YAAQ,oBAAiB;YAAI,MAAK;YAAO,aAAU;;;;;;;;;;;AAK5pC,MAAM,OAAsB,CAAC,EAC3B,KAAK,EACL,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,SAAS,EACT,oBAAoB,EACpB,qBAAqB,EACtB;IACC,MAAM,MAAM,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,aAAa,CAAA,GAAA,wTAAA,CAAA,WAAQ,AAAD,EAAE;IAC5B,MAAM,aAAa,0BAA0B,KAAK,EAAE;IAEpD,qBACE,qZAAC;QACC,KAAK;QAEL,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EACV,2HACA,cAAc,wEACd,AAAC,0BAA0B,KAAK,EAAE,IAAI,CAAC,eAAgB,kCACvD,AAAC,0BAA0B,KAAK,EAAE,IAAI,eAAgB;QAExD,SAAS,IAAM,qBAAqB,KAAK,EAAE;;YAE1C,eAAe,CAAC,0BAAY,qZAAC;gBAAa,WAAU;;;;;;0BACrD,qZAAC;gBAAI,WAAW,CAAC,uBAAuB,EAAE,eAAe,6CAA6C;gBAAE,OAAO,KAAK,IAAI;0BAAG,KAAK,IAAI;;;;;;YACnI,KAAK,EAAE,KAAK,oBACX,qZAAC;gBAAI,WAAU;gBAAW,SAAS,CAAA,IAAK,EAAE,eAAe;0BACvD,cAAA,qZAAC,qLAAA,CAAA,UAAS;oBACR,aAAa;oBACb,UAAU;oBACV,UAAU;oBACV,UAAU,CAAC,CAAC;oBACZ,gBAAgB;oBAChB,WAAW,IAAM,UAAU,QAAQ,UAAU,OAAO;oBACpD,YAAY;oBACZ,wBAAwB;oBACxB,sBAAsB,IAAM,UAAU,UAAU;oBAChD,UAAU,IAAM,UAAU,UAAU;;;;;;;;;;;;OAvBrC,KAAK,EAAE;;;;;AA6BlB;qDAEe,CAAA,GAAA,4WAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 1185, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/sidebar/list.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport Item from './item'\nimport type { ConversationItem, EmbedSource } from '@/models/share'\n\ntype ListProps = {\n  isPin?: boolean\n  title?: string\n  list: ConversationItem[]\n  embedSource?: EmbedSource\n  isMobile?: boolean\n  onOperate: (type: string, item: ConversationItem) => void\n  onChangeConversation: (conversationId: string) => void\n  currentConversationId: string\n}\nconst List: FC<ListProps> = ({\n  isPin,\n  title,\n  list,\n  embedSource,\n  isMobile,\n  onOperate,\n  onChangeConversation,\n  currentConversationId,\n}) => {\n  return (\n    <div className='space-y-0.5'>\n      {title && (\n        <div className='system-xs-medium-uppercase px-3 pb-1 pt-2 text-text-tertiary'>{title}</div>\n      )}\n      {list.map(item => (\n        <Item\n          isMobile={isMobile}\n          embedSource={embedSource}\n          key={item.id}\n          isPin={isPin}\n          item={item}\n          onOperate={onOperate}\n          onChangeConversation={onChangeConversation}\n          currentConversationId={currentConversationId}\n        />\n      ))}\n    </div>\n  )\n}\n\nexport default List\n"], "names": [], "mappings": ";;;;AACA;;;AAaA,MAAM,OAAsB,CAAC,EAC3B,KAAK,EACL,KAAK,EACL,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,SAAS,EACT,oBAAoB,EACpB,qBAAqB,EACtB;IACC,qBACE,qZAAC;QAAI,WAAU;;YACZ,uBACC,qZAAC;gBAAI,WAAU;0BAAgE;;;;;;YAEhF,KAAK,GAAG,CAAC,CAAA,qBACR,qZAAC,gLAAA,CAAA,UAAI;oBACH,UAAU;oBACV,aAAa;oBAEb,OAAO;oBACP,MAAM;oBACN,WAAW;oBACX,sBAAsB;oBACtB,uBAAuB;mBALlB,KAAK,EAAE;;;;;;;;;;;AAUtB;uCAEe", "debugId": null}}, {"offset": {"line": 1231, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/modal/index.tsx"], "sourcesContent": ["import { Dialog, DialogPanel, DialogTitle, Transition, TransitionChild } from '@headlessui/react'\nimport { Fragment } from 'react'\nimport { RiCloseLine } from '@remixicon/react'\nimport classNames from '@/utils/classnames'\nimport { noop } from 'lodash-es'\n// https://headlessui.com/react/dialog\n\ntype IModal = {\n  className?: string\n  wrapperClassName?: string\n  isShow: boolean\n  onClose?: () => void\n  title?: React.ReactNode\n  description?: React.ReactNode\n  children?: React.ReactNode\n  closable?: boolean\n  isStatic?: boolean\n  overflowVisible?: boolean\n}\n\nexport default function Modal({\n  className,\n  wrapperClassName,\n  isShow,\n  onClose = noop,\n  title,\n  description,\n  children,\n  closable = false,\n  isStatic = false,\n  overflowVisible = false,\n}: IModal) {\n  return (\n    <Transition appear show={isShow} as={Fragment}>\n      <Dialog as=\"div\" className={classNames('relative z-[60]', wrapperClassName)} onClose={() => {\n        // 禁止点击遮罩层关闭弹窗\n        !isStatic && onClose()\n      }}>\n        <TransitionChild>\n          <div className={classNames(\n            'fixed inset-0 bg-background-overlay',\n            'duration-300 ease-in data-[closed]:opacity-0',\n            'data-[enter]:opacity-100',\n            'data-[leave]:opacity-0',\n          )} />\n        </TransitionChild>\n\n        <div\n          className=\"fixed inset-0 overflow-y-auto\"\n          onClick={(e) => {\n            e.preventDefault()\n            e.stopPropagation()\n          }}\n        >\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <TransitionChild>\n              <DialogPanel className={classNames(\n                'w-full max-w-[480px] transform rounded-2xl bg-components-panel-bg p-6 text-left align-middle shadow-xl transition-all',\n                overflowVisible ? 'overflow-visible' : 'overflow-hidden',\n                'duration-100 ease-in data-[closed]:opacity-0 data-[closed]:scale-95',\n                'data-[enter]:opacity-100 data-[enter]:scale-100',\n                'data-[leave]:opacity-0 data-[enter]:scale-95',\n                className,\n              )}>\n                {title && <DialogTitle\n                  as=\"h3\"\n                  className=\"title-2xl-semi-bold text-text-primary\"\n                >\n                  {title}\n                </DialogTitle>}\n                {description && <div className='body-md-regular mt-2 text-text-secondary'>\n                  {description}\n                </div>}\n                {closable\n                  && <div className='absolute right-6 top-6 z-10 flex h-5 w-5 items-center justify-center rounded-2xl hover:cursor-pointer hover:bg-state-base-hover'>\n                    <RiCloseLine className='h-4 w-4 text-text-tertiary' onClick={\n                      (e) => {\n                        e.stopPropagation()\n                        onClose()\n                      }\n                    } />\n                  </div>}\n                {children}\n              </DialogPanel>\n            </TransitionChild>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAgBe,SAAS,MAAM,EAC5B,SAAS,EACT,gBAAgB,EAChB,MAAM,EACN,UAAU,kOAAA,CAAA,OAAI,EACd,KAAK,EACL,WAAW,EACX,QAAQ,EACR,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,kBAAkB,KAAK,EAChB;IACP,qBACE,qZAAC,sUAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,4WAAA,CAAA,WAAQ;kBAC3C,cAAA,qZAAC,8TAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAU,AAAD,EAAE,mBAAmB;YAAmB,SAAS;gBACpF,cAAc;gBACd,CAAC,YAAY;YACf;;8BACE,qZAAC,sUAAA,CAAA,kBAAe;8BACd,cAAA,qZAAC;wBAAI,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAU,AAAD,EACvB,uCACA,gDACA,4BACA;;;;;;;;;;;8BAIJ,qZAAC;oBACC,WAAU;oBACV,SAAS,CAAC;wBACR,EAAE,cAAc;wBAChB,EAAE,eAAe;oBACnB;8BAEA,cAAA,qZAAC;wBAAI,WAAU;kCACb,cAAA,qZAAC,sUAAA,CAAA,kBAAe;sCACd,cAAA,qZAAC,8TAAA,CAAA,cAAW;gCAAC,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAU,AAAD,EAC/B,yHACA,kBAAkB,qBAAqB,mBACvC,uEACA,mDACA,gDACA;;oCAEC,uBAAS,qZAAC,8TAAA,CAAA,cAAW;wCACpB,IAAG;wCACH,WAAU;kDAET;;;;;;oCAEF,6BAAe,qZAAC;wCAAI,WAAU;kDAC5B;;;;;;oCAEF,0BACI,qZAAC;wCAAI,WAAU;kDAChB,cAAA,qZAAC,qOAAA,CAAA,cAAW;4CAAC,WAAU;4CAA6B,SAClD,CAAC;gDACC,EAAE,eAAe;gDACjB;4CACF;;;;;;;;;;;oCAGL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB", "debugId": null}}, {"offset": {"line": 1360, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/share/text-generation/info-modal.tsx"], "sourcesContent": ["import React from 'react'\nimport Modal from '@/components/base/modal'\nimport AppIcon from '@/components/base/app-icon'\nimport type { SiteInfo } from '@/models/share'\nimport { appDefaultIconBackground } from '@/config'\nimport cn from 'classnames'\n\ntype Props = {\n  data?: SiteInfo\n  isShow: boolean\n  onClose: () => void\n}\n\nconst InfoModal = ({\n  isShow,\n  onClose,\n  data,\n}: Props) => {\n  return (\n    <Modal\n      isShow={isShow}\n      onClose={onClose}\n      className='min-w-[400px] max-w-[400px] !p-0'\n      closable\n    >\n      <div className={cn('flex flex-col items-center gap-4 px-4 pb-8 pt-10')}>\n        <AppIcon\n          size='xxl'\n          iconType={data?.icon_type}\n          icon={data?.icon}\n          background={data?.icon_background || appDefaultIconBackground}\n          imageUrl={data?.icon_url}\n        />\n        <div className='system-xl-semibold text-text-secondary'>{data?.title}</div>\n        <div className='system-xs-regular text-text-tertiary'>\n          {/* copyright */}\n          {data?.copyright && (\n            <div>© {(new Date()).getFullYear()} {data?.copyright}</div>\n          )}\n          {data?.custom_disclaimer && (\n            <div className='mt-2'>{data.custom_disclaimer}</div>\n          )}\n        </div>\n      </div>\n    </Modal>\n  )\n}\n\nexport default InfoModal\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AACA;;;;;;AAQA,MAAM,YAAY,CAAC,EACjB,MAAM,EACN,OAAO,EACP,IAAI,EACE;IACN,qBACE,qZAAC,4IAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;QACV,QAAQ;kBAER,cAAA,qZAAC;YAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE;;8BACjB,qZAAC,kJAAA,CAAA,UAAO;oBACN,MAAK;oBACL,UAAU,MAAM;oBAChB,MAAM,MAAM;oBACZ,YAAY,MAAM,mBAAmB,sHAAA,CAAA,2BAAwB;oBAC7D,UAAU,MAAM;;;;;;8BAElB,qZAAC;oBAAI,WAAU;8BAA0C,MAAM;;;;;;8BAC/D,qZAAC;oBAAI,WAAU;;wBAEZ,MAAM,2BACL,qZAAC;;gCAAI;gCAAI,IAAI,OAAQ,WAAW;gCAAG;gCAAE,MAAM;;;;;;;wBAE5C,MAAM,mCACL,qZAAC;4BAAI,WAAU;sCAAQ,KAAK,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;AAMzD;uCAEe", "debugId": null}}, {"offset": {"line": 1449, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/share/text-generation/menu-dropdown.tsx"], "sourcesContent": ["'use client'\nimport type { FC } from 'react'\nimport React, { useCallback, useRef, useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport type { Placement } from '@floating-ui/react'\nimport {\n  RiEqualizer2Line,\n} from '@remixicon/react'\nimport ActionButton from '@/components/base/action-button'\nimport {\n  PortalToFollowElem,\n  PortalToFollowElemContent,\n  PortalToFollowElemTrigger,\n} from '@/components/base/portal-to-follow-elem'\nimport InfoModal from './info-modal'\nimport type { SiteInfo } from '@/models/share'\nimport cn from '@/utils/classnames'\n\ntype Props = {\n  data?: SiteInfo\n  placement?: Placement\n}\n\nconst MenuDropdown: FC<Props> = ({\n  data,\n  placement,\n}) => {\n  const { t } = useTranslation()\n  const [open, doSetOpen] = useState(false)\n  const openRef = useRef(open)\n  const setOpen = useCallback((v: boolean) => {\n    doSetOpen(v)\n    openRef.current = v\n  }, [doSetOpen])\n\n  const handleTrigger = useCallback(() => {\n    setOpen(!openRef.current)\n  }, [setOpen])\n\n  const [show, setShow] = useState(false)\n\n  return (\n    <>\n      <PortalToFollowElem\n        open={open}\n        onOpenChange={setOpen}\n        placement={placement || 'bottom-end'}\n        offset={{\n          mainAxis: 4,\n          crossAxis: -4,\n        }}\n      >\n        <PortalToFollowElemTrigger onClick={handleTrigger}>\n          <div>\n            <ActionButton size='l' className={cn(open && 'bg-state-base-hover')}>\n              <RiEqualizer2Line className='h-[18px] w-[18px]' />\n            </ActionButton>\n          </div>\n        </PortalToFollowElemTrigger>\n        <PortalToFollowElemContent className='z-50'>\n          <div className='w-[224px] rounded-xl border-[0.5px] border-components-panel-border bg-components-panel-bg-blur shadow-lg backdrop-blur-sm'>\n            <div className='p-1'>\n              {data?.privacy_policy && (\n                <a href={data.privacy_policy} target='_blank' className='system-md-regular flex cursor-pointer items-center rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-base-hover'>\n                  <span className='grow'>{t('share.chat.privacyPolicyMiddle')}</span>\n                </a>\n              )}\n              <div\n                onClick={() => {\n                  handleTrigger()\n                  setShow(true)\n                }}\n                className='system-md-regular cursor-pointer rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-base-hover'\n              >{t('common.userProfile.about')}</div>\n            </div>\n          </div>\n        </PortalToFollowElemContent>\n      </PortalToFollowElem>\n      {show && (\n        <InfoModal\n          isShow={show}\n          onClose={() => {\n            setShow(false)\n          }}\n          data={data}\n        />\n      )}\n    </>\n  )\n}\nexport default React.memo(MenuDropdown)\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAGA;AACA;AAKA;AAEA;AAhBA;;;;;;;;;AAuBA,MAAM,eAA0B,CAAC,EAC/B,IAAI,EACJ,SAAS,EACV;IACC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,2XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,MAAM,UAAU,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,UAAU,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,UAAU,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3B,UAAU;QACV,QAAQ,OAAO,GAAG;IACpB,GAAG;QAAC;KAAU;IAEd,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QAChC,QAAQ,CAAC,QAAQ,OAAO;IAC1B,GAAG;QAAC;KAAQ;IAEZ,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,qBACE;;0BACE,qZAAC,qKAAA,CAAA,qBAAkB;gBACjB,MAAM;gBACN,cAAc;gBACd,WAAW,aAAa;gBACxB,QAAQ;oBACN,UAAU;oBACV,WAAW,CAAC;gBACd;;kCAEA,qZAAC,qKAAA,CAAA,4BAAyB;wBAAC,SAAS;kCAClC,cAAA,qZAAC;sCACC,cAAA,qZAAC,uJAAA,CAAA,UAAY;gCAAC,MAAK;gCAAI,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE,QAAQ;0CAC3C,cAAA,qZAAC,qOAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;kCAIlC,qZAAC,qKAAA,CAAA,4BAAyB;wBAAC,WAAU;kCACnC,cAAA,qZAAC;4BAAI,WAAU;sCACb,cAAA,qZAAC;gCAAI,WAAU;;oCACZ,MAAM,gCACL,qZAAC;wCAAE,MAAM,KAAK,cAAc;wCAAE,QAAO;wCAAS,WAAU;kDACtD,cAAA,qZAAC;4CAAK,WAAU;sDAAQ,EAAE;;;;;;;;;;;kDAG9B,qZAAC;wCACC,SAAS;4CACP;4CACA,QAAQ;wCACV;wCACA,WAAU;kDACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAKX,sBACC,qZAAC,kKAAA,CAAA,UAAS;gBACR,QAAQ;gBACR,SAAS;oBACP,QAAQ;gBACV;gBACA,MAAM;;;;;;;;AAKhB;qDACe,4WAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 1604, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/confirm/index.tsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react'\nimport { createPortal } from 'react-dom'\nimport { useTranslation } from 'react-i18next'\nimport Button from '../button'\n\nexport type IConfirm = {\n  className?: string\n  isEmbedMobile?: boolean\n  isShow: boolean\n  type?: 'info' | 'warning'\n  title: string\n  content?: React.ReactNode\n  confirmText?: string | null\n  onConfirm: () => void\n  cancelText?: string\n  onCancel: () => void\n  isLoading?: boolean\n  isDisabled?: boolean\n  showConfirm?: boolean\n  showCancel?: boolean\n  maskClosable?: boolean\n  footerRender?: () => React.ReactNode\n}\n\nfunction Confirm({\n  isShow,\n  isEmbedMobile,\n  type = 'warning',\n  title,\n  content,\n  confirmText,\n  cancelText,\n  onConfirm,\n  onCancel,\n  showConfirm = true,\n  showCancel = true,\n  isLoading = false,\n  isDisabled = false,\n  maskClosable = true,\n  footerRender\n}: IConfirm) {\n  const { t } = useTranslation()\n  const dialogRef = useRef<HTMLDivElement>(null)\n  const [isVisible, setIsVisible] = useState(isShow)\n\n  const confirmTxt = confirmText || `${t('common.operation.confirm')}`\n  const cancelTxt = cancelText || `${t('common.operation.cancel')}`\n\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape')\n        onCancel()\n      if (event.key === 'Enter' && isShow) {\n        event.preventDefault()\n        onConfirm()\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown)\n    }\n  }, [onCancel, onConfirm, isShow])\n\n  const handleClickOutside = (event: MouseEvent) => {\n    if (maskClosable && dialogRef.current && !dialogRef.current.contains(event.target as Node))\n      onCancel()\n  }\n\n  useEffect(() => {\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [maskClosable])\n\n  useEffect(() => {\n    if (isShow) {\n      setIsVisible(true)\n    }\n    else {\n      const timer = setTimeout(() => setIsVisible(false), 200)\n      return () => clearTimeout(timer)\n    }\n  }, [isShow])\n\n  if (!isVisible)\n    return null\n\n  return createPortal(\n    <div className={'fixed inset-0 z-[10000000] flex items-center justify-center bg-background-overlay'}\n      onClick={(e) => {\n        e.preventDefault()\n        e.stopPropagation()\n      }}>\n      <div ref={dialogRef} className={`relative w-full max-w-[480px] overflow-hidden ${isEmbedMobile && 'mx-[32px]'}`}>\n        <div className='shadows-shadow-lg flex max-w-full flex-col items-start rounded-2xl border-[0.5px] border-solid border-components-panel-border bg-components-panel-bg'>\n          <div className='flex flex-col items-start gap-2 self-stretch pb-4 pl-6 pr-6 pt-6'>\n            <div className={`title-2xl-semi-bold text-text-primary ${isEmbedMobile && 'text-center w-full mb-[34px]'}`}>{title}</div>\n            <div className={`system-md-regular w-full text-text-tertiary ${isEmbedMobile && 'text-[#8C97A4]'}`}>{content}</div>\n          </div>\n          {footerRender ? footerRender() : <div className='flex items-start justify-end gap-2 self-stretch p-6'>\n            {showCancel && <Button onClick={onCancel}>{cancelTxt}</Button>}\n            {showConfirm && <Button variant={'primary'} destructive={type !== 'info'} loading={isLoading} disabled={isDisabled} onClick={onConfirm}>{confirmTxt}</Button>}\n          </div>}\n        </div>\n      </div>\n    </div>, document.body,\n  )\n}\n\nexport default React.memo(Confirm)\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;;;;;;AAqBA,SAAS,QAAQ,EACf,MAAM,EACN,aAAa,EACb,OAAO,SAAS,EAChB,KAAK,EACL,OAAO,EACP,WAAW,EACX,UAAU,EACV,SAAS,EACT,QAAQ,EACR,cAAc,IAAI,EAClB,aAAa,IAAI,EACjB,YAAY,KAAK,EACjB,aAAa,KAAK,EAClB,eAAe,IAAI,EACnB,YAAY,EACH;IACT,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,2XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,YAAY,CAAA,GAAA,4WAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa,eAAe,GAAG,EAAE,6BAA6B;IACpE,MAAM,YAAY,cAAc,GAAG,EAAE,4BAA4B;IAEjE,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,MAAM,GAAG,KAAK,UAChB;YACF,IAAI,MAAM,GAAG,KAAK,WAAW,QAAQ;gBACnC,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;QAAU;QAAW;KAAO;IAEhC,MAAM,qBAAqB,CAAC;QAC1B,IAAI,gBAAgB,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC/E;IACJ;IAEA,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,aAAa;QACf,OACK;YACH,MAAM,QAAQ,WAAW,IAAM,aAAa,QAAQ;YACpD,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAO;IAEX,IAAI,CAAC,WACH,OAAO;IAET,qBAAO,CAAA,GAAA,mXAAA,CAAA,eAAY,AAAD,gBAChB,qZAAC;QAAI,WAAW;QACd,SAAS,CAAC;YACR,EAAE,cAAc;YAChB,EAAE,eAAe;QACnB;kBACA,cAAA,qZAAC;YAAI,KAAK;YAAW,WAAW,CAAC,8CAA8C,EAAE,iBAAiB,aAAa;sBAC7G,cAAA,qZAAC;gBAAI,WAAU;;kCACb,qZAAC;wBAAI,WAAU;;0CACb,qZAAC;gCAAI,WAAW,CAAC,sCAAsC,EAAE,iBAAiB,gCAAgC;0CAAG;;;;;;0CAC7G,qZAAC;gCAAI,WAAW,CAAC,4CAA4C,EAAE,iBAAiB,kBAAkB;0CAAG;;;;;;;;;;;;oBAEtG,eAAe,+BAAiB,qZAAC;wBAAI,WAAU;;4BAC7C,4BAAc,qZAAC,6IAAA,CAAA,UAAM;gCAAC,SAAS;0CAAW;;;;;;4BAC1C,6BAAe,qZAAC,6IAAA,CAAA,UAAM;gCAAC,SAAS;gCAAW,aAAa,SAAS;gCAAQ,SAAS;gCAAW,UAAU;gCAAY,SAAS;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;cAIzI,SAAS,IAAI;AAEzB;qDAEe,4WAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 1753, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/sidebar/rename-modal.tsx"], "sourcesContent": ["'use client'\nimport type { FC } from 'react'\nimport React, { useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport Modal from '@/components/base/modal'\nimport Button from '@/components/base/button'\nimport Input from '@/components/base/input'\n\nexport type IRenameModalProps = {\n  isShow: boolean\n  isEmbedMobile?: boolean\n  saveLoading: boolean\n  name: string\n  onClose: () => void\n  onSave: (name: string) => void\n}\n\nconst RenameModal: FC<IRenameModalProps> = ({\n  isShow,\n  isEmbedMobile,\n  saveLoading,\n  name,\n  onClose,\n  onSave,\n}) => {\n  const { t } = useTranslation()\n  const [tempName, setTempName] = useState(name)\n\n  return (\n    <Modal\n      className={isEmbedMobile ? '!px-[0px] !pb-[0px] text-center' : ''}\n      title={isEmbedMobile ? '重命名' : t('common.chat.renameConversation')}\n      isShow={isShow}\n      onClose={onClose}\n    >\n      {!isEmbedMobile && <div className={'mt-6 text-sm font-medium leading-[21px] text-text-primary'}>{t('common.chat.conversationName')}</div>}\n      <Input className={`mt-2 h-10 w-full  ${isEmbedMobile && 'outline-[#E2E2FF]'}`}\n        value={tempName}\n        onChange={e => setTempName(e.target.value)}\n        placeholder={t('common.chat.conversationNamePlaceholder') || ''}\n      />\n\n      <div className={`mt-10 flex justify-end ${isEmbedMobile && 'border-t-[1px] border-t-[#EEF0F2]'}`}>\n        <Button className={`mr-2 shrink-0 ${isEmbedMobile && 'border-[0px] bg-[#fff] flex-1 h-[56px] mr-[0px] shadow-none border-r-[1px] border-r-[#EEF0F2] rounded-[0px] hover:bg-[#fff] !text-[16px]'}`} onClick={onClose}>{t('common.operation.cancel')}</Button>\n        <Button variant='primary' className={`shrink-0 ${isEmbedMobile && 'border-[0px] bg-[#fff] flex-1 h-[56px] text-[#1E86FF] shadow-none rounded-[0px] hover:bg-[#fff] !text-[16px]'}`} onClick={() => onSave(tempName)} loading={saveLoading}>{t('common.operation.save')}</Button>\n      </div>\n    </Modal>\n  )\n}\nexport default React.memo(RenameModal)\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAiBA,MAAM,cAAqC,CAAC,EAC1C,MAAM,EACN,aAAa,EACb,WAAW,EACX,IAAI,EACJ,OAAO,EACP,MAAM,EACP;IACC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,2XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qBACE,qZAAC,4IAAA,CAAA,UAAK;QACJ,WAAW,gBAAgB,oCAAoC;QAC/D,OAAO,gBAAgB,QAAQ,EAAE;QACjC,QAAQ;QACR,SAAS;;YAER,CAAC,+BAAiB,qZAAC;gBAAI,WAAW;0BAA8D,EAAE;;;;;;0BACnG,qZAAC,4IAAA,CAAA,UAAK;gBAAC,WAAW,CAAC,kBAAkB,EAAE,iBAAiB,qBAAqB;gBAC3E,OAAO;gBACP,UAAU,CAAA,IAAK,YAAY,EAAE,MAAM,CAAC,KAAK;gBACzC,aAAa,EAAE,8CAA8C;;;;;;0BAG/D,qZAAC;gBAAI,WAAW,CAAC,uBAAuB,EAAE,iBAAiB,qCAAqC;;kCAC9F,qZAAC,6IAAA,CAAA,UAAM;wBAAC,WAAW,CAAC,cAAc,EAAE,iBAAiB,4IAA4I;wBAAE,SAAS;kCAAU,EAAE;;;;;;kCACxN,qZAAC,6IAAA,CAAA,UAAM;wBAAC,SAAQ;wBAAU,WAAW,CAAC,SAAS,EAAE,iBAAiB,gHAAgH;wBAAE,SAAS,IAAM,OAAO;wBAAW,SAAS;kCAAc,EAAE;;;;;;;;;;;;;;;;;;AAItP;qDACe,4WAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 1840, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/logo/logo-site.tsx"], "sourcesContent": ["'use client'\nimport type { <PERSON> } from 'react'\nimport classNames from '@/utils/classnames'\nimport { WEBSITE_LOGO } from '@/config'\n\ntype LogoSiteProps = {\n  className?: string\n}\n\nconst LogoSite: FC<LogoSiteProps> = ({\n  className,\n}) => {\n  return (\n    WEBSITE_LOGO && (\n      <picture>\n        <img\n          src={WEBSITE_LOGO}\n          className={classNames('block w-auto h-[22px]', className)}\n          alt='logo'\n        />\n      </picture>\n    )\n  )\n}\n\nexport default LogoSite\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASA,MAAM,WAA8B,CAAC,EACnC,SAAS,EACV;IACC,OACE,sHAAA,CAAA,eAAY,kBACV,qZAAC;kBACC,cAAA,qZAAC;YACC,KAAK,sHAAA,CAAA,eAAY;YACjB,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAU,AAAD,EAAE,yBAAyB;YAC/C,KAAI;;;;;;;;;;;AAKd;uCAEe", "debugId": null}}, {"offset": {"line": 1873, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/base/chat/chat-with-history/index.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"bg\": \"index-module__guMYKq__bg\",\n  \"errorPng\": \"index-module__guMYKq__errorPng\",\n  \"mobileNavTitle\": \"index-module__guMYKq__mobileNavTitle\",\n  \"navBarBg\": \"index-module__guMYKq__navBarBg\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1890, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/assets/lark-app-robot.gif.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 661, height: 661, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,8HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1909, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/sidebar/index.tsx"], "sourcesContent": ["import type { ChangeEvent } from 'react'\nimport {\n  useCallback,\n  useState,\n} from 'react'\nimport { useTranslation } from 'react-i18next'\nimport {\n  RiEditBoxLine,\n  RiExpandRightLine,\n  RiLayoutLeft2Line,\n} from '@remixicon/react'\nimport { throttle } from 'lodash-es'\nimport { useChatWithHistoryContext } from '../context'\nimport Input from '../../../input'\nimport BtnFold from '../btn-fold'\nimport AppIcon from '@/components/base/app-icon'\nimport ActionButton from '@/components/base/action-button'\nimport Button from '@/components/base/button'\nimport List from '@/components/base/chat/chat-with-history/sidebar/list'\nimport MenuDropdown from '@/components/share/text-generation/menu-dropdown'\nimport Confirm from '@/components/base/confirm'\nimport RenameModal from '@/components/base/chat/chat-with-history/sidebar/rename-modal'\nimport LogoSite from '@/components/base/logo/logo-site'\nimport type { ConversationItem } from '@/models/share'\nimport cn from '@/utils/classnames'\nimport styles from '../index.module.css'\nimport Robot from '@/assets/lark-app-robot.gif'\nimport Image from 'next/image'\n\nconst LarkDeleteIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 14 14\" className={className}>\n    <g stroke=\"#A3AFBB\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" fill-rule=\"evenodd\">\n      <path d=\"M8.75,2.04166667 L8.22889873,9.85818568 C8.19387888,10.3834835 7.75757745,10.7916667 7.23111357,10.7916667 L2.10221976,10.7916667 C1.57575588,10.7916667 1.13945446,10.3834835 1.1044346,9.85818568 L0.583333333,2.04166667 L0.583333333,2.04166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M0 2.04166667L9.33333333 2.04166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M3.5 0.29166667L5.83333333 0.29166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M3.5 7.875L3.5 4.95833333\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M5.83333333 7.875L5.83333333 4.95833333\" transform=\"translate(2.3333 1.4583)\" />\n    </g>\n  </svg>\n)\n\ntype Props = {\n  isPanel?: boolean\n}\n\nconst Sidebar = ({ isPanel }: Props) => {\n  const { t } = useTranslation()\n  const {\n    appData,\n    handleNewConversation,\n    pinnedConversationList,\n    conversationList,\n    currentConversationId,\n    handleChangeConversation,\n    handlePinConversation,\n    handleUnpinConversation,\n    conversationRenaming,\n    handleRenameConversation,\n    handleDeleteConversation,\n    sidebarCollapseState,\n    handleSidebarCollapse,\n    isMobile,\n    isResponding,\n    embedSource,\n    isFold,\n    setIsFold,\n    handleClearAllConversations\n  } = useChatWithHistoryContext()\n  const isSidebarCollapsed = sidebarCollapseState\n\n  const [showConfirm, setShowConfirm] = useState<ConversationItem | null>(null)\n  const [showClearAll, setShowClearAll] = useState<boolean | null>(null)\n  const [showRename, setShowRename] = useState<ConversationItem | null>(null)\n  const [keyword, setKeyword] = useState<string>('')\n  const isEmbedMobile = isMobile && embedSource\n\n  const handleOperate = useCallback((type: string, item: ConversationItem) => {\n    if (type === 'pin')\n      handlePinConversation(item.id)\n\n    if (type === 'unpin')\n      handleUnpinConversation(item.id)\n\n    if (type === 'delete')\n      setShowConfirm(item)\n\n    if (type === 'rename')\n      setShowRename(item)\n  }, [handlePinConversation, handleUnpinConversation])\n  const handleCancelConfirm = useCallback(() => {\n    setShowConfirm(null)\n  }, [])\n  const handleDelete = useCallback(() => {\n    if (showConfirm)\n      handleDeleteConversation(showConfirm.id, { onSuccess: handleCancelConfirm })\n  }, [showConfirm, handleDeleteConversation, handleCancelConfirm])\n  const handleCancelRename = useCallback(() => {\n    setShowRename(null)\n  }, [])\n  const handleRename = useCallback((newName: string) => {\n    if (showRename)\n      handleRenameConversation(showRename.id, newName, { onSuccess: handleCancelRename })\n  }, [showRename, handleRenameConversation, handleCancelRename])\n\n  const handleSearch = throttle(({ target }: ChangeEvent<HTMLInputElement>) => {\n    setKeyword(target?.value)\n  }, 100)\n\n  const handleClearAll = () => {\n    setShowClearAll(true)\n  }\n\n  const handleClearAllConfirm = () => {\n    handleClearAllConversations?.({ onSuccess: () => setShowClearAll(false) })\n  }\n\n  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}\n  const aiRobotGifUrl = chatPageConfigData?.aiRobotGifUrl || Robot // AI机器人动画\n\n  return (\n    <div className={cn(\n      'flex w-full grow flex-col',\n      isPanel && 'rounded-xl border-[0.5px] border-components-panel-border-subtle bg-components-panel-bg shadow-lg',\n      isEmbedMobile && styles.bg,\n      isEmbedMobile && 'bg-[#f5f6f8] w-[74vw] !border-none'\n    )}>\n      <div className={cn(\n        'flex shrink-0 items-center gap-3 p-3 pr-2',\n      )}>\n        <div className='shrink-0'>\n          {\n            embedSource ? (\n              !isMobile && <div className=\"flex items-center\">\n                <Input\n                  className='h-[40px] rounded-[8px] border-[1px] border-solid border-[#DFE4E8]'\n                  value={keyword}\n                  placeholder=\"搜索对话...\"\n                  showLeftIcon\n                  showClearIcon\n                  onChange={handleSearch}\n                  onClear={() => setKeyword('')}\n                />\n                <BtnFold className=\"ml-[11px]\" isFold={isFold} onClick={() => setIsFold?.(!isFold)} />\n              </div>\n            ) : (\n              <>\n                <AppIcon\n                  size='large'\n                  iconType={appData?.site.icon_type}\n                  icon={appData?.site.icon}\n                  background={appData?.site.icon_background}\n                  imageUrl={appData?.site.icon_url}\n                />\n                <div className={cn('system-md-semibold grow truncate text-text-secondary')}>{appData?.site.title}</div>\n                {!isMobile && isSidebarCollapsed && (\n                  <ActionButton size='l' onClick={() => handleSidebarCollapse(false)}>\n                    <RiExpandRightLine className='h-[18px] w-[18px]' />\n                  </ActionButton>\n                )}\n                {!isMobile && !isSidebarCollapsed && (\n                  <ActionButton size='l' onClick={() => handleSidebarCollapse(true)}>\n                    <RiLayoutLeft2Line className='h-[18px] w-[18px]' />\n                  </ActionButton>\n                )}\n              </>\n            )\n          }\n        </div>\n      </div>\n      {\n        !embedSource && (\n          <div className='shrink-0 px-3 py-4'>\n            <Button variant='secondary-accent' disabled={isResponding} className='w-full justify-center' onClick={handleNewConversation}>\n              <RiEditBoxLine className='mr-1 h-4 w-4' />\n              {t('share.chat.newChat')}\n            </Button>\n          </div>\n        )\n      }\n      {embedSource && isMobile && <div className=\"mb-[30px]\">\n        <Image src={aiRobotGifUrl} width={100} height={100} className=\"w-[100px] height-[100px] mt-[64px] mx-auto mb-[6px]\" alt=\"\" />\n        <p className=\"text-center text-[18px] text-[#242933] mb-[20px] font-semibold\">Hi～我是{appData?.site.title}</p>\n        <div className=\"px-[12px]\">\n          <Input\n            className='h-[40px] rounded-[8px] border-[1px] border-solid bg-[#fff] hover:bg-[#fff]'\n            value={keyword}\n            placeholder=\"搜索对话...\"\n            showLeftIcon\n            showClearIcon\n            onChange={handleSearch}\n            onClear={() => setKeyword('')}\n          />\n        </div>\n      </div>}\n      <div className=\"flex items-center justify-between px-[20px] mb-[10px]\">\n        <p className=\"text-[16px] text-[#242933] font-medium\">对话记录</p>\n        <button onClick={handleClearAll}><LarkDeleteIcon className=\"w-[20px] h-[20px]\" /></button>\n      </div>\n      <div className='h-0 grow space-y-2 overflow-y-auto px-3 pt-4'>\n        {/* pinned list */}\n        {!!pinnedConversationList.length && (\n          <div className='mb-4'>\n            <List\n              embedSource={embedSource}\n              isMobile={isMobile}\n              isPin\n              title={t('share.chat.pinnedTitle') || ''}\n              list={pinnedConversationList.filter(item => item.name.includes(keyword))}\n              onChangeConversation={handleChangeConversation}\n              onOperate={handleOperate}\n              currentConversationId={currentConversationId}\n            />\n          </div>\n        )}\n        {!!conversationList.length && (\n          <List\n            embedSource={embedSource}\n            isMobile={isMobile}\n            title={(pinnedConversationList.length && t('share.chat.unpinnedTitle')) || ''}\n            list={conversationList.filter(item => item.name.includes(keyword))}\n            onChangeConversation={handleChangeConversation}\n            onOperate={handleOperate}\n            currentConversationId={currentConversationId}\n          />\n        )}\n      </div>\n      <div className='flex shrink-0 items-center justify-between p-3'>\n        <MenuDropdown placement='top-start' data={appData?.site} />\n        {/* powered by */}\n        <div className='shrink-0'>\n          {!appData?.custom_config?.remove_webapp_brand && (\n            <div className={cn(\n              'flex shrink-0 items-center gap-1.5 px-2',\n            )}>\n              <div className='system-2xs-medium-uppercase text-text-tertiary'>{t('share.chat.poweredBy')}</div>\n              {appData?.custom_config?.replace_webapp_logo && (\n                <img src={appData?.custom_config?.replace_webapp_logo} alt='logo' className='block h-5 w-auto' />\n              )}\n              {!appData?.custom_config?.replace_webapp_logo && (\n                <LogoSite className='!h-5' />\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n      {!!showConfirm && (\n        <Confirm\n          title={embedSource && isMobile ? ' 确定要删除吗？' : t('share.chat.deleteConversation.title')}\n          content={embedSource && isMobile ? \"删除后无法撤销\" : t('share.chat.deleteConversation.content') || ''}\n          isShow\n          isEmbedMobile={Boolean(embedSource && isMobile)}\n          onCancel={handleCancelConfirm}\n          onConfirm={handleDelete}\n          footerRender={embedSource && isMobile ? (() => <div className='flex w-full border-t-[1px] border-t-[#EEF0F2] mt-[23px]'>\n            <p className=\"py-[15px] text-[#A3AFBB] flex-1 text-center border-r-[1px] border-r-[#EEF0F2] text-[16px]\" onClick={handleCancelConfirm}>取消</p>\n            <p className=\"py-[15px] text-[#FF4C4C] flex-1 text-center text-[16px]\" onClick={handleDelete}>删除</p>\n          </div>) : undefined}\n        />\n      )}\n      {showClearAll && (\n        <Confirm\n          title={'确定要删除所有对话记录吗？'}\n          content={\"删除后无法撤销\"}\n          isShow\n          isEmbedMobile={Boolean(embedSource && isMobile)}\n          onCancel={() => setShowClearAll(false)}\n          onConfirm={handleClearAllConfirm}\n          footerRender={embedSource && isMobile ? (() => <div className='flex w-full border-t-[1px] border-t-[#EEF0F2] mt-[23px]'>\n            <p className=\"py-[15px] text-[#A3AFBB] flex-1 text-center border-r-[1px] border-r-[#EEF0F2] text-[16px]\" onClick={() => setShowClearAll(false)}>取消</p>\n            <p className=\"py-[15px] text-[#FF4C4C] flex-1 text-center text-[16px]\" onClick={handleClearAllConfirm}>删除</p>\n          </div>) : undefined}\n        />\n      )}\n      {showRename && (\n        <RenameModal\n          isEmbedMobile={Boolean(embedSource && isMobile)}\n          isShow\n          onClose={handleCancelRename}\n          saveLoading={conversationRenaming}\n          name={showRename?.name || ''}\n          onSave={handleRename}\n        />\n      )}\n    </div>\n  )\n}\n\nexport default Sidebar\n"], "names": [], "mappings": ";;;;AACA;AAIA;AAAA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAyB,iBAC1D,qZAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,qZAAC;YAAE,QAAO;YAAU,kBAAe;YAAQ,mBAAgB;YAAQ,MAAK;YAAO,aAAU;;8BACvF,qZAAC;oBAAK,GAAE;oBAAsP,WAAU;;;;;;8BACxQ,qZAAC;oBAAK,GAAE;oBAAsC,WAAU;;;;;;8BACxD,qZAAC;oBAAK,GAAE;oBAAwC,WAAU;;;;;;8BAC1D,qZAAC;oBAAK,GAAE;oBAA4B,WAAU;;;;;;8BAC9C,qZAAC;oBAAK,GAAE;oBAA0C,WAAU;;;;;;;;;;;;;;;;;AASlE,MAAM,UAAU,CAAC,EAAE,OAAO,EAAS;IACjC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,2XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,EACJ,OAAO,EACP,qBAAqB,EACrB,sBAAsB,EACtB,gBAAgB,EAChB,qBAAqB,EACrB,wBAAwB,EACxB,qBAAqB,EACrB,uBAAuB,EACvB,oBAAoB,EACpB,wBAAwB,EACxB,wBAAwB,EACxB,oBAAoB,EACpB,qBAAqB,EACrB,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,MAAM,EACN,SAAS,EACT,2BAA2B,EAC5B,GAAG,CAAA,GAAA,wKAAA,CAAA,4BAAyB,AAAD;IAC5B,MAAM,qBAAqB;IAE3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAA2B;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAkB;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAA2B;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,gBAAgB,YAAY;IAElC,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAC/C,IAAI,SAAS,OACX,sBAAsB,KAAK,EAAE;QAE/B,IAAI,SAAS,SACX,wBAAwB,KAAK,EAAE;QAEjC,IAAI,SAAS,UACX,eAAe;QAEjB,IAAI,SAAS,UACX,cAAc;IAClB,GAAG;QAAC;QAAuB;KAAwB;IACnD,MAAM,sBAAsB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACtC,eAAe;IACjB,GAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,aACF,yBAAyB,YAAY,EAAE,EAAE;YAAE,WAAW;QAAoB;IAC9E,GAAG;QAAC;QAAa;QAA0B;KAAoB;IAC/D,MAAM,qBAAqB,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE;QACrC,cAAc;IAChB,GAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI,YACF,yBAAyB,WAAW,EAAE,EAAE,SAAS;YAAE,WAAW;QAAmB;IACrF,GAAG;QAAC;QAAY;QAA0B;KAAmB;IAE7D,MAAM,eAAe,CAAA,GAAA,0OAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EAAE,MAAM,EAAiC;QACtE,WAAW,QAAQ;IACrB,GAAG;IAEH,MAAM,iBAAiB;QACrB,gBAAgB;IAClB;IAEA,MAAM,wBAAwB;QAC5B,8BAA8B;YAAE,WAAW,IAAM,gBAAgB;QAAO;IAC1E;IAEA,MAAM,qBAAqB,SAAS,MAAM,iBAAiB,KAAK,KAAK,CAAC,SAAS,MAAM,kBAAkB,CAAC;IACxG,MAAM,gBAAgB,oBAAoB,iBAAiB,2SAAM,UAAU;IAAhB,CAAA,UAAK;IAEhE,qBACE,qZAAC;QAAI,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EACf,6BACA,WAAW,oGACX,iBAAiB,iLAAA,CAAA,UAAM,CAAC,EAAE,EAC1B,iBAAiB;;0BAEjB,qZAAC;gBAAI,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EACf;0BAEA,cAAA,qZAAC;oBAAI,WAAU;8BAEX,cACE,CAAC,0BAAY,qZAAC;wBAAI,WAAU;;0CAC1B,qZAAC,4IAAA,CAAA,UAAK;gCACJ,WAAU;gCACV,OAAO;gCACP,aAAY;gCACZ,YAAY;gCACZ,aAAa;gCACb,UAAU;gCACV,SAAS,IAAM,WAAW;;;;;;0CAE5B,qZAAC,4KAAA,CAAA,UAAO;gCAAC,WAAU;gCAAY,QAAQ;gCAAQ,SAAS,IAAM,YAAY,CAAC;;;;;;;;;;;6CAG7E;;0CACE,qZAAC,kJAAA,CAAA,UAAO;gCACN,MAAK;gCACL,UAAU,SAAS,KAAK;gCACxB,MAAM,SAAS,KAAK;gCACpB,YAAY,SAAS,KAAK;gCAC1B,UAAU,SAAS,KAAK;;;;;;0CAE1B,qZAAC;gCAAI,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EAAE;0CAA0D,SAAS,KAAK;;;;;;4BAC1F,CAAC,YAAY,oCACZ,qZAAC,uJAAA,CAAA,UAAY;gCAAC,MAAK;gCAAI,SAAS,IAAM,sBAAsB;0CAC1D,cAAA,qZAAC,qOAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;;;;;;4BAGhC,CAAC,YAAY,CAAC,oCACb,qZAAC,uJAAA,CAAA,UAAY;gCAAC,MAAK;gCAAI,SAAS,IAAM,sBAAsB;0CAC1D,cAAA,qZAAC,qOAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YASzC,CAAC,6BACC,qZAAC;gBAAI,WAAU;0BACb,cAAA,qZAAC,6IAAA,CAAA,UAAM;oBAAC,SAAQ;oBAAmB,UAAU;oBAAc,WAAU;oBAAwB,SAAS;;sCACpG,qZAAC,qOAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;wBACxB,EAAE;;;;;;;;;;;;YAKV,eAAe,0BAAY,qZAAC;gBAAI,WAAU;;kCACzC,qZAAC,oSAAA,CAAA,UAAK;wBAAC,KAAK;wBAAe,OAAO;wBAAK,QAAQ;wBAAK,WAAU;wBAAsD,KAAI;;;;;;kCACxH,qZAAC;wBAAE,WAAU;;4BAAiE;4BAAM,SAAS,KAAK;;;;;;;kCAClG,qZAAC;wBAAI,WAAU;kCACb,cAAA,qZAAC,4IAAA,CAAA,UAAK;4BACJ,WAAU;4BACV,OAAO;4BACP,aAAY;4BACZ,YAAY;4BACZ,aAAa;4BACb,UAAU;4BACV,SAAS,IAAM,WAAW;;;;;;;;;;;;;;;;;0BAIhC,qZAAC;gBAAI,WAAU;;kCACb,qZAAC;wBAAE,WAAU;kCAAyC;;;;;;kCACtD,qZAAC;wBAAO,SAAS;kCAAgB,cAAA,qZAAC;4BAAe,WAAU;;;;;;;;;;;;;;;;;0BAE7D,qZAAC;gBAAI,WAAU;;oBAEZ,CAAC,CAAC,uBAAuB,MAAM,kBAC9B,qZAAC;wBAAI,WAAU;kCACb,cAAA,qZAAC,gLAAA,CAAA,UAAI;4BACH,aAAa;4BACb,UAAU;4BACV,KAAK;4BACL,OAAO,EAAE,6BAA6B;4BACtC,MAAM,uBAAuB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC;4BAC/D,sBAAsB;4BACtB,WAAW;4BACX,uBAAuB;;;;;;;;;;;oBAI5B,CAAC,CAAC,iBAAiB,MAAM,kBACxB,qZAAC,gLAAA,CAAA,UAAI;wBACH,aAAa;wBACb,UAAU;wBACV,OAAO,AAAC,uBAAuB,MAAM,IAAI,EAAE,+BAAgC;wBAC3E,MAAM,iBAAiB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC;wBACzD,sBAAsB;wBACtB,WAAW;wBACX,uBAAuB;;;;;;;;;;;;0BAI7B,qZAAC;gBAAI,WAAU;;kCACb,qZAAC,qKAAA,CAAA,UAAY;wBAAC,WAAU;wBAAY,MAAM,SAAS;;;;;;kCAEnD,qZAAC;wBAAI,WAAU;kCACZ,CAAC,SAAS,eAAe,qCACxB,qZAAC;4BAAI,WAAW,CAAA,GAAA,0HAAA,CAAA,UAAE,AAAD,EACf;;8CAEA,qZAAC;oCAAI,WAAU;8CAAkD,EAAE;;;;;;gCAClE,SAAS,eAAe,qCACvB,qZAAC;oCAAI,KAAK,SAAS,eAAe;oCAAqB,KAAI;oCAAO,WAAU;;;;;;gCAE7E,CAAC,SAAS,eAAe,qCACxB,qZAAC,kJAAA,CAAA,UAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAM7B,CAAC,CAAC,6BACD,qZAAC,8IAAA,CAAA,UAAO;gBACN,OAAO,eAAe,WAAW,aAAa,EAAE;gBAChD,SAAS,eAAe,WAAW,YAAY,EAAE,4CAA4C;gBAC7F,MAAM;gBACN,eAAe,QAAQ,eAAe;gBACtC,UAAU;gBACV,WAAW;gBACX,cAAc,eAAe,WAAY,kBAAM,qZAAC;wBAAI,WAAU;;0CAC5D,qZAAC;gCAAE,WAAU;gCAA4F,SAAS;0CAAqB;;;;;;0CACvI,qZAAC;gCAAE,WAAU;gCAA0D,SAAS;0CAAc;;;;;;;;;;;iCACtF;;;;;;YAGb,8BACC,qZAAC,8IAAA,CAAA,UAAO;gBACN,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,eAAe,QAAQ,eAAe;gBACtC,UAAU,IAAM,gBAAgB;gBAChC,WAAW;gBACX,cAAc,eAAe,WAAY,kBAAM,qZAAC;wBAAI,WAAU;;0CAC5D,qZAAC;gCAAE,WAAU;gCAA4F,SAAS,IAAM,gBAAgB;0CAAQ;;;;;;0CAChJ,qZAAC;gCAAE,WAAU;gCAA0D,SAAS;0CAAuB;;;;;;;;;;;iCAC/F;;;;;;YAGb,4BACC,qZAAC,2LAAA,CAAA,UAAW;gBACV,eAAe,QAAQ,eAAe;gBACtC,MAAM;gBACN,SAAS;gBACT,aAAa;gBACb,MAAM,YAAY,QAAQ;gBAC1B,QAAQ;;;;;;;;;;;;AAKlB;uCAEe", "debugId": null}}, {"offset": {"line": 2484, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/index.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport {\n  useState,\n} from 'react'\nimport Sidebar from './sidebar'\n// import Header from './header'\n// import HeaderInMobile from './header-in-mobile'\n// import ChatWrapper from './chat-wrapper'\nimport styles from './index.module.css'\n// import Loading from '@/components/base/loading'\nimport cn from 'classnames'\n// import RightSidebar from './right-sidebar'\n\ntype ChatWithHistoryProps = {\n  className?: string\n}\n\nconst ChatWithHistory: FC<ChatWithHistoryProps> = ({\n  className,\n}) => {\n  const [showSidePanel, setShowSidePanel] = useState(false)\n  const isMobile = false\n  const embedSource = true\n  const isSidebarCollapsed = false\n  const isFold = false\n  const appChatListDataLoading = false\n  const chatShouldReloadKey = false\n  const refreshRenderKey = false\n  const rightSideInfo = false\n\n  return (\n    <div className={cn(\n      'flex h-full bg-background-default-burn',\n      isMobile && 'flex-col',\n      className,\n      embedSource && isMobile && '!bg-[#f5f6f8]',\n      embedSource && isMobile && styles.bg,\n    )}>\n      {(!isMobile && !isFold) && (\n        <div className={cn(\n          'flex w-[236px] flex-col pr-0 transition-all duration-200 ease-in-out',\n          isSidebarCollapsed && 'w-0 overflow-hidden !p-0',\n          embedSource && !isMobile && '!bg-white',\n        )}>\n          <Sidebar />\n        </div>\n      )}\n      {isMobile && (\n        <HeaderInMobile />\n      )}\n      <div className={cn('relative grow p-2', embedSource && 'p-0', isMobile && 'h-[calc(100%_-_56px)] p-0')}>\n        {isSidebarCollapsed && (\n          <div\n            className={cn(\n              'absolute top-0 z-20 flex h-full w-[256px] flex-col p-2 transition-all duration-500 ease-in-out',\n              showSidePanel ? 'left-0' : 'left-[-248px]',\n            )}\n            onMouseEnter={() => setShowSidePanel(true)}\n            onMouseLeave={() => setShowSidePanel(false)}\n          >\n            {/* <Sidebar isPanel /> */}\n          </div>\n        )}\n        <div className={cn('flex h-full flex-col overflow-hidden border-[0,5px] border-components-panel-border-subtle bg-chatbot-bg', isMobile ? 'rounded-t-2xl' : 'rounded-2xl', embedSource && 'rounded-none')}>\n          {/* {!isMobile && !embedSource && <Header />} */}\n          {/* {appChatListDataLoading && (\n            <Loading />\n          )} */}\n          {/* {!appChatListDataLoading && (\n            <ChatWrapper key={chatShouldReloadKey || refreshRenderKey} />\n          )} */}\n        </div>\n      </div>\n      {/* <RightSidebar isMobile={isMobile} visible={Boolean(rightSideInfo)}/> */}\n    </div>\n  )\n}\n\nexport default ChatWithHistory\n"], "names": [], "mappings": ";;;;AACA;AAGA;AACA,gCAAgC;AAChC,kDAAkD;AAClD,2CAA2C;AAC3C;AACA,kDAAkD;AAClD;;;;;;AAOA,MAAM,kBAA4C,CAAC,EACjD,SAAS,EACV;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW;IACjB,MAAM,cAAc;IACpB,MAAM,qBAAqB;IAC3B,MAAM,SAAS;IACf,MAAM,yBAAyB;IAC/B,MAAM,sBAAsB;IAC5B,MAAM,mBAAmB;IACzB,MAAM,gBAAgB;IAEtB,qBACE,qZAAC;QAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EACf,0CACA,YAAY,YACZ,WACA,eAAe,YAAY,iBAC3B,eAAe,YAAY,iLAAA,CAAA,UAAM,CAAC,EAAE;;YAElC,CAAC,YAAY,CAAC,wBACd,qZAAC;gBAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EACf,wEACA,sBAAsB,4BACtB,eAAe,CAAC,YAAY;0BAE5B,cAAA,qZAAC,iLAAA,CAAA,UAAO;;;;;;;;;;YAGX,0BACC,qZAAC;;;;;0BAEH,qZAAC;gBAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE,qBAAqB,eAAe,OAAO,YAAY;;oBACvE,oCACC,qZAAC;wBACC,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EACV,kGACA,gBAAgB,WAAW;wBAE7B,cAAc,IAAM,iBAAiB;wBACrC,cAAc,IAAM,iBAAiB;;;;;;kCAKzC,qZAAC;wBAAI,WAAW,CAAA,GAAA,0LAAA,CAAA,UAAE,AAAD,EAAE,2GAA2G,6EAA6B,eAAe,eAAe;;;;;;;;;;;;;;;;;;AAajM;uCAEe", "debugId": null}}, {"offset": {"line": 2570, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/app/chat/page.tsx"], "sourcesContent": ["'use client'\nimport React from 'react'\n// import Chat<PERSON>ithHistoryWrap from '@/components/base/chat/chat-with-history'\nimport ChatWithHistoryWrap from '@/components/base/chat/chat-with-history/index'\n\nconst Chat = () => {\n  return (\n    <>\n      <ChatWithHistoryWrap />\n    </>\n  )\n}\n\nexport default React.memo(Chat)\n"], "names": [], "mappings": ";;;;AACA;AACA,6EAA6E;AAC7E;AAHA;;;;AAKA,MAAM,OAAO;IACX,qBACE;kBACE,cAAA,qZAAC,sKAAA,CAAA,UAAmB;;;;;;AAG1B;qDAEe,4WAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}]}