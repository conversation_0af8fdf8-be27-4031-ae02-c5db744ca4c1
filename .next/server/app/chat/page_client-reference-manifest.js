globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/chat/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js <module evaluation>":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/src/components/deer-flow/theme-provider-wrapper.tsx <module evaluation>":{"id":"[project]/src/components/deer-flow/theme-provider-wrapper.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/src/components/deer-flow/theme-provider-wrapper.tsx":{"id":"[project]/src/components/deer-flow/theme-provider-wrapper.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/src/components/deer-flow/toaster.tsx <module evaluation>":{"id":"[project]/src/components/deer-flow/toaster.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/src/components/deer-flow/toaster.tsx":{"id":"[project]/src/components/deer-flow/toaster.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js"],"async":false},"[project]/src/app/chat/page.tsx <module evaluation>":{"id":"[project]/src/app/chat/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js","/_next/static/chunks/src_components_base_file-uploader_pdf-preview_tsx_46859092._.js","/_next/static/chunks/src_f3b7cd20._.js","/_next/static/chunks/a44c2_%40remixicon_react_index_mjs_c3a827a1._.js","/_next/static/chunks/9c939_tailwind-merge_dist_bundle-mjs_mjs_5274cb0a._.js","/_next/static/chunks/c3d6e_emoji-mart_dist_module_cc437c67.js","/_next/static/chunks/6c0ff_%40emoji-mart_data_sets_15_native_json_8ac1e7eb._.js","/_next/static/chunks/node_modules__pnpm_77592bb3._.js","/_next/static/chunks/5cfcf_%40headlessui_react_dist_268718d2._.js","/_next/static/chunks/0fbf0_next_b74fd131._.js","/_next/static/chunks/node_modules__pnpm_7fd1ef0a._.js","/_next/static/chunks/src_app_chat_page_tsx_59ab4d14._.js"],"async":false},"[project]/src/app/chat/page.tsx":{"id":"[project]/src/app/chat/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules__pnpm_790a9273._.js","/_next/static/chunks/src_components_6ddcf391._.js","/_next/static/chunks/src_app_layout_tsx_74336905._.js","/_next/static/chunks/src_components_base_file-uploader_pdf-preview_tsx_46859092._.js","/_next/static/chunks/src_f3b7cd20._.js","/_next/static/chunks/a44c2_%40remixicon_react_index_mjs_c3a827a1._.js","/_next/static/chunks/9c939_tailwind-merge_dist_bundle-mjs_mjs_5274cb0a._.js","/_next/static/chunks/c3d6e_emoji-mart_dist_module_cc437c67.js","/_next/static/chunks/6c0ff_%40emoji-mart_data_sets_15_native_json_8ac1e7eb._.js","/_next/static/chunks/node_modules__pnpm_77592bb3._.js","/_next/static/chunks/5cfcf_%40headlessui_react_dist_268718d2._.js","/_next/static/chunks/0fbf0_next_b74fd131._.js","/_next/static/chunks/node_modules__pnpm_7fd1ef0a._.js","/_next/static/chunks/src_app_chat_page_tsx_59ab4d14._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules__pnpm_d0b8dcd9._.js","server/chunks/ssr/[root-of-the-server]__53523a6d._.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules__pnpm_d0b8dcd9._.js","server/chunks/ssr/[root-of-the-server]__53523a6d._.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules__pnpm_d0b8dcd9._.js","server/chunks/ssr/[root-of-the-server]__53523a6d._.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules__pnpm_d0b8dcd9._.js","server/chunks/ssr/[root-of-the-server]__53523a6d._.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules__pnpm_d0b8dcd9._.js","server/chunks/ssr/[root-of-the-server]__53523a6d._.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules__pnpm_d0b8dcd9._.js","server/chunks/ssr/[root-of-the-server]__53523a6d._.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules__pnpm_d0b8dcd9._.js","server/chunks/ssr/[root-of-the-server]__53523a6d._.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules__pnpm_d0b8dcd9._.js","server/chunks/ssr/[root-of-the-server]__53523a6d._.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules__pnpm_d0b8dcd9._.js","server/chunks/ssr/[root-of-the-server]__53523a6d._.js"],"async":false}},"[project]/src/components/deer-flow/theme-provider-wrapper.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/deer-flow/theme-provider-wrapper.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules__pnpm_d0b8dcd9._.js","server/chunks/ssr/[root-of-the-server]__53523a6d._.js"],"async":false}},"[project]/src/components/deer-flow/toaster.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/deer-flow/toaster.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules__pnpm_d0b8dcd9._.js","server/chunks/ssr/[root-of-the-server]__53523a6d._.js"],"async":false}},"[project]/src/app/chat/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/chat/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules__pnpm_d0b8dcd9._.js","server/chunks/ssr/[root-of-the-server]__53523a6d._.js","server/chunks/ssr/src_67141973._.js","server/chunks/ssr/a44c2_@remixicon_react_index_mjs_a265f92d._.js","server/chunks/ssr/9c939_tailwind-merge_dist_bundle-mjs_mjs_32ee59eb._.js","server/chunks/ssr/c3d6e_emoji-mart_dist_module_8eb65c45.js","server/chunks/ssr/6c0ff_@emoji-mart_data_sets_15_native_json_7c5cbf12._.js","server/chunks/ssr/node_modules__pnpm_ecf28157._.js","server/chunks/ssr/5cfcf_@headlessui_react_dist_ab24a9cb._.js","server/chunks/ssr/0fbf0_next_59cda29e._.js","server/chunks/ssr/node_modules__pnpm_804ec0e9._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/chat/page.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/chat/page.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/chat/page.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/chat/page.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/chat/page.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/chat/page.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/chat/page.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/chat/page.js"],"async":false}},"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js (client reference/proxy)","name":"*","chunks":["server/app/chat/page.js"],"async":false}},"[project]/src/components/deer-flow/theme-provider-wrapper.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/deer-flow/theme-provider-wrapper.tsx (client reference/proxy)","name":"*","chunks":["server/app/chat/page.js"],"async":false}},"[project]/src/components/deer-flow/toaster.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/deer-flow/toaster.tsx (client reference/proxy)","name":"*","chunks":["server/app/chat/page.js"],"async":false}},"[project]/src/app/chat/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/chat/page.tsx (client reference/proxy)","name":"*","chunks":["server/app/chat/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/layout":[{"path":"static/chunks/[root-of-the-server]__698e6b9e._.css","inlined":false}],"[project]/src/app/chat/page":[{"path":"static/chunks/[root-of-the-server]__698e6b9e._.css","inlined":false},{"path":"static/chunks/src_components_base_5910afb5._.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/layout":["static/chunks/node_modules__pnpm_790a9273._.js","static/chunks/src_components_6ddcf391._.js","static/chunks/src_app_layout_tsx_74336905._.js"],"[project]/src/app/chat/page":["static/chunks/node_modules__pnpm_790a9273._.js","static/chunks/src_components_6ddcf391._.js","static/chunks/src_app_layout_tsx_74336905._.js","static/chunks/src_components_base_file-uploader_pdf-preview_tsx_46859092._.js","static/chunks/src_f3b7cd20._.js","static/chunks/a44c2_@remixicon_react_index_mjs_c3a827a1._.js","static/chunks/9c939_tailwind-merge_dist_bundle-mjs_mjs_5274cb0a._.js","static/chunks/c3d6e_emoji-mart_dist_module_cc437c67.js","static/chunks/6c0ff_@emoji-mart_data_sets_15_native_json_8ac1e7eb._.js","static/chunks/node_modules__pnpm_77592bb3._.js","static/chunks/5cfcf_@headlessui_react_dist_268718d2._.js","static/chunks/0fbf0_next_b74fd131._.js","static/chunks/node_modules__pnpm_7fd1ef0a._.js","static/chunks/src_app_chat_page_tsx_59ab4d14._.js"]}}
