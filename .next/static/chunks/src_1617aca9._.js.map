{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/context.tsx"], "sourcesContent": ["'use client'\n\nimport type { RefObject } from 'react'\nimport { createContext, useContext } from 'use-context-selector'\nimport type {\n  Callback,\n  ChatConfig,\n  ChatItemInTree,\n  Feedback,\n} from '../types'\nimport type { ThemeBuilder } from '../embedded-chatbot/theme/theme-context'\nimport type {\n  AppConversationData,\n  AppData,\n  AppMeta,\n  ConversationItem,\n  EmbedSource,\n} from '@/models/share'\nimport { noop } from 'lodash-es'\n\nexport type ChatWithHistoryContextValue = {\n  appInfoError?: any\n  appInfoLoading?: boolean\n  appMeta?: AppMeta\n  appData?: AppData\n  appParams?: ChatConfig\n  appChatListDataLoading?: boolean\n  currentConversationId: string\n  currentConversationItem?: ConversationItem\n  appPrevChatTree: ChatItemInTree[]\n  pinnedConversationList: AppConversationData['data']\n  conversationList: AppConversationData['data']\n  newConversationInputs: Record<string, any>\n  newConversationInputsRef: RefObject<Record<string, any>>\n  handleNewConversationInputsChange: (v: Record<string, any>) => void\n  inputsForms: any[]\n  handleNewConversation: () => void\n  handleStartChat: (callback?: any) => void\n  handleChangeConversation: (conversationId: string) => void\n  handlePinConversation: (conversationId: string) => void\n  handleUnpinConversation: (conversationId: string) => void\n  handleDeleteConversation: (conversationId: string, callback: Callback) => void\n  conversationRenaming: boolean\n  handleRenameConversation: (conversationId: string, newName: string, callback: Callback) => void\n  handleNewConversationCompleted: (newConversationId: string) => void\n  chatShouldReloadKey: string\n  isMobile: boolean\n  isInstalledApp: boolean\n  appId?: string\n  handleFeedback: (messageId: string, feedback: Feedback) => void\n  currentChatInstanceRef: RefObject<{ handleStop: () => void }>\n  themeBuilder?: ThemeBuilder\n  sidebarCollapseState?: boolean\n  handleSidebarCollapse: (state: boolean) => void\n  clearChatList?: boolean\n  setClearChatList: (state: boolean) => void\n  isResponding?: boolean\n  setIsResponding: (state: boolean) => void,\n  currentConversationInputs: Record<string, any> | null,\n  setCurrentConversationInputs: (v: Record<string, any>) => void,\n  isFold?: boolean\n  setIsFold?: (bool: boolean) => void\n  embedSource?: EmbedSource\n  refreshRenderKey?: number\n  larkInfo?: any\n  handleClearAllConversations?: (callback: Callback) => void\n  rightSideInfo?: null | any// 右侧面板信息\n  setRightSideInfo?: (info: any) => void // 右侧面板信息\n}\n\nexport const ChatWithHistoryContext = createContext<ChatWithHistoryContextValue>({\n  currentConversationId: '',\n  appPrevChatTree: [],\n  pinnedConversationList: [],\n  conversationList: [],\n  newConversationInputs: {},\n  newConversationInputsRef: { current: {} },\n  handleNewConversationInputsChange: noop,\n  inputsForms: [],\n  handleNewConversation: noop,\n  handleStartChat: noop,\n  handleChangeConversation: noop,\n  handlePinConversation: noop,\n  handleUnpinConversation: noop,\n  handleDeleteConversation: noop,\n  conversationRenaming: false,\n  handleRenameConversation: noop,\n  handleNewConversationCompleted: noop,\n  chatShouldReloadKey: '',\n  isMobile: false,\n  isInstalledApp: false,\n  handleFeedback: noop,\n  currentChatInstanceRef: { current: { handleStop: noop } },\n  sidebarCollapseState: false,\n  handleSidebarCollapse: noop,\n  clearChatList: false,\n  setClearChatList: noop,\n  isResponding: false,\n  setIsResponding: noop,\n  currentConversationInputs: {},\n  setCurrentConversationInputs: noop,\n  isFold: false,\n  setIsFold: noop,\n  embedSource: '',\n  refreshRenderKey: -1,\n  larkInfo: {},\n  handleClearAllConversations: noop,\n  rightSideInfo: null,\n  setRightSideInfo: noop,\n})\nexport const useChatWithHistoryContext = () => useContext(ChatWithHistoryContext)\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;;AAsEO,MAAM,yBAAyB,cAA2C;IAC/E,uBAAuB;IACvB,iBAAiB,EAAE;IACnB,wBAAwB,EAAE;IAC1B,kBAAkB,EAAE;IACpB,uBAAuB,CAAC;IACxB,0BAA0B;QAAE,SAAS,CAAC;IAAE;IACxC,mCAAmC;IACnC,aAAa,EAAE;IACf,uBAAuB;IACvB,iBAAiB;IACjB,0BAA0B;IAC1B,uBAAuB;IACvB,yBAAyB;IACzB,0BAA0B;IAC1B,sBAAsB;IACtB,0BAA0B;IAC1B,gCAAgC;IAChC,qBAAqB;IACrB,UAAU;IACV,gBAAgB;IAChB,gBAAgB;IAChB,wBAAwB;QAAE,SAAS;YAAE,YAAY;QAAK;IAAE;IACxD,sBAAsB;IACtB,uBAAuB;IACvB,eAAe;IACf,kBAAkB;IAClB,cAAc;IACd,iBAAiB;IACjB,2BAA2B,CAAC;IAC5B,8BAA8B;IAC9B,QAAQ;IACR,WAAW;IACX,aAAa;IACb,kBAAkB,CAAC;IACnB,UAAU,CAAC;IACX,6BAA6B;IAC7B,eAAe;IACf,kBAAkB;AACpB;AACO,MAAM,4BAA4B;;IAAM,OAAA,WAAW;AAAsB;GAAnE", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/input/index.tsx"], "sourcesContent": ["import type { CSSProperties } from 'react'\nimport React from 'react'\nimport { useTranslation } from 'react-i18next'\nimport { RiCloseCircleFill, RiErrorWarningLine, RiSearchLine } from '@remixicon/react'\nimport { type VariantProps, cva } from 'class-variance-authority'\nimport cn from '@/utils/classnames'\nimport { noop } from 'lodash-es'\n\nexport const inputVariants = cva(\n  '',\n  {\n    variants: {\n      size: {\n        regular: 'px-3 radius-md system-sm-regular',\n        large: 'px-4 radius-lg system-md-regular',\n      },\n    },\n    defaultVariants: {\n      size: 'regular',\n    },\n  },\n)\n\nexport type InputProps = {\n  showLeftIcon?: boolean\n  showClearIcon?: boolean\n  onClear?: () => void\n  disabled?: boolean\n  destructive?: boolean\n  wrapperClassName?: string\n  styleCss?: CSSProperties\n  unit?: string\n} & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> & VariantProps<typeof inputVariants>\n\nconst Input = ({\n  size,\n  disabled,\n  destructive,\n  showLeftIcon,\n  showClearIcon,\n  onClear,\n  wrapperClassName,\n  className,\n  styleCss,\n  value,\n  placeholder,\n  onChange = noop,\n  unit,\n  ...props\n}: InputProps) => {\n  const { t } = useTranslation()\n  return (\n    <div className={cn('relative w-full', wrapperClassName)}>\n      {showLeftIcon && <RiSearchLine className={cn('absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-components-input-text-placeholder')} />}\n      <input\n        style={styleCss}\n        className={cn(\n          'w-full appearance-none border border-transparent bg-components-input-bg-normal py-[7px] text-components-input-text-filled caret-primary-600 outline-none placeholder:text-components-input-text-placeholder hover:border-components-input-border-hover hover:bg-components-input-bg-hover focus:border-components-input-border-active focus:bg-components-input-bg-active focus:shadow-xs',\n          inputVariants({ size }),\n          showLeftIcon && 'pl-[26px]',\n          showLeftIcon && size === 'large' && 'pl-7',\n          showClearIcon && value && 'pr-[26px]',\n          showClearIcon && value && size === 'large' && 'pr-7',\n          destructive && 'pr-[26px]',\n          destructive && size === 'large' && 'pr-7',\n          disabled && 'cursor-not-allowed border-transparent bg-components-input-bg-disabled text-components-input-text-filled-disabled hover:border-transparent hover:bg-components-input-bg-disabled',\n          destructive && 'border-components-input-border-destructive bg-components-input-bg-destructive text-components-input-text-filled hover:border-components-input-border-destructive hover:bg-components-input-bg-destructive focus:border-components-input-border-destructive focus:bg-components-input-bg-destructive',\n          className,\n        )}\n        placeholder={placeholder ?? (showLeftIcon\n          ? (t('common.operation.search') || '')\n          : (t('common.placeholder.input') || ''))}\n        value={value}\n        onChange={onChange}\n        disabled={disabled}\n        {...props}\n      />\n      {showClearIcon && value && !disabled && !destructive && (\n        <div className={cn('group absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer p-[1px]')} onClick={onClear}>\n          <RiCloseCircleFill className='h-3.5 w-3.5 cursor-pointer text-text-quaternary group-hover:text-text-tertiary' />\n        </div>\n      )}\n      {destructive && (\n        <RiErrorWarningLine className='absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 text-text-destructive-secondary' />\n      )}\n      {\n        unit && (\n          <div className='system-sm-regular absolute right-2 top-1/2 -translate-y-1/2 text-text-tertiary'>\n            {unit}\n          </div>\n        )\n      }\n    </div>\n  )\n}\n\nexport default Input\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAIA;;;;;;;;;;;;;;;;;;AAIO,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EAC7B,IACA;IACE,UAAU;QACR,MAAM;YACJ,SAAS;YACT,OAAO;QACT;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAcF,MAAM,QAAQ,CAAC,EACb,IAAI,EACJ,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,aAAa,EACb,OAAO,EACP,gBAAgB,EAChB,SAAS,EACT,QAAQ,EACR,KAAK,EACL,WAAW,EACX,WAAW,IAAI,EACf,IAAI,EACJ,GAAG,OACQ;;IACX,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,qBACE,oWAAC;QAAI,WAAW,GAAG,mBAAmB;;YACnC,8BAAgB,oWAAC;gBAAa,WAAW,GAAG;;;;;;0BAC7C,oWAAC;gBACC,OAAO;gBACP,WAAW,GACT,6XACA,cAAc;oBAAE;gBAAK,IACrB,gBAAgB,aAChB,gBAAgB,SAAS,WAAW,QACpC,iBAAiB,SAAS,aAC1B,iBAAiB,SAAS,SAAS,WAAW,QAC9C,eAAe,aACf,eAAe,SAAS,WAAW,QACnC,YAAY,mLACZ,eAAe,uSACf;gBAEF,aAAa,eAAe,CAAC,eACxB,EAAE,8BAA8B,KAChC,EAAE,+BAA+B,EAAG;gBACzC,OAAO;gBACP,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;;;;;YAEV,iBAAiB,SAAS,CAAC,YAAY,CAAC,6BACvC,oWAAC;gBAAI,WAAW,GAAG;gBAA2E,SAAS;0BACrG,cAAA,oWAAC;oBAAkB,WAAU;;;;;;;;;;;YAGhC,6BACC,oWAAC;gBAAmB,WAAU;;;;;;YAG9B,sBACE,oWAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAMb;GA5DM;;QAgBU;;;KAhBV;uCA8DS", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/spinner/index.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport React from 'react'\n\ntype Props = {\n  loading?: boolean\n  className?: string\n  children?: React.ReactNode | string\n}\n\nconst Spinner: FC<Props> = ({ loading = false, children, className }) => {\n  return (\n    <div\n      className={`inline-block h-4 w-4 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] text-gray-200 ${loading ? 'motion-reduce:animate-[spin_1.5s_linear_infinite]' : 'hidden'} ${className ?? ''}`}\n      role=\"status\"\n    >\n      <span\n        className=\"!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]\"\n      >Loading...</span>\n      {children}\n    </div>\n  )\n}\n\nexport default Spinner\n"], "names": [], "mappings": ";;;;;AASA,MAAM,UAAqB,CAAC,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE;IAClE,qBACE,oWAAC;QACC,WAAW,CAAC,wIAAwI,EAAE,UAAU,sDAAsD,SAAS,CAAC,EAAE,aAAa,IAAI;QACnP,MAAK;;0BAEL,oWAAC;gBACC,WAAU;0BACX;;;;;;YACA;;;;;;;AAGP;KAZM;uCAcS", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/button/index.tsx"], "sourcesContent": ["import type { CSSProperties } from 'react'\nimport React from 'react'\nimport { type VariantProps, cva } from 'class-variance-authority'\nimport Spinner from '../spinner'\nimport classNames from '@/utils/classnames'\n\nconst buttonVariants = cva(\n  'btn disabled:btn-disabled',\n  {\n    variants: {\n      variant: {\n        'primary': 'btn-primary',\n        'warning': 'btn-warning',\n        'secondary': 'btn-secondary',\n        'secondary-accent': 'btn-secondary-accent',\n        'ghost': 'btn-ghost',\n        'ghost-accent': 'btn-ghost-accent',\n        'tertiary': 'btn-tertiary',\n      },\n      size: {\n        small: 'btn-small',\n        medium: 'btn-medium',\n        large: 'btn-large',\n      },\n    },\n    defaultVariants: {\n      variant: 'secondary',\n      size: 'medium',\n    },\n  },\n)\n\nexport type ButtonProps = {\n  destructive?: boolean\n  loading?: boolean\n  styleCss?: CSSProperties\n  spinnerClassName?: string\n} & React.ButtonHTMLAttributes<HTMLButtonElement> & VariantProps<typeof buttonVariants>\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, destructive, loading, styleCss, children, spinnerClassName, ...props }, ref) => {\n    return (\n      <button\n        type='button'\n        className={classNames(\n          buttonVariants({ variant, size, className }),\n          destructive && 'btn-destructive',\n        )}\n        ref={ref}\n        style={styleCss}\n        {...props}\n      >\n        {children}\n        {loading && <Spinner loading={loading} className={classNames('!text-white !h-3 !w-3 !border-2 !ml-1', spinnerClassName)} />}\n      </button>\n    )\n  },\n)\nButton.displayName = 'Button'\n\nexport default Button\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;;;;;;;;;;;AAGA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,6BACA;IACE,UAAU;QACR,SAAS;YACP,WAAW;YACX,WAAW;YACX,aAAa;YACb,oBAAoB;YACpB,SAAS;YACT,gBAAgB;YAChB,YAAY;QACd;QACA,MAAM;YACJ,OAAO;YACP,QAAQ;YACR,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,oUAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG,OAAO,EAAE;IACnG,qBACE,oWAAC;QACC,MAAK;QACL,WAAW,WACT,eAAe;YAAE;YAAS;YAAM;QAAU,IAC1C,eAAe;QAEjB,KAAK;QACL,OAAO;QACN,GAAG,KAAK;;YAER;YACA,yBAAW,oWAAC,iJAAA,CAAA,UAAO;gBAAC,SAAS;gBAAS,WAAW,WAAW,yCAAyC;;;;;;;;;;;;AAG5G;;AAEF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/btn-fold.tsx"], "sourcesContent": ["import cls from 'classnames'\nimport Button from '../../button'\nconst IconFold = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" className={className}>\n    <g stroke=\"#1E6AFF\" stroke-width=\"1.4\" fill=\"none\" fill-rule=\"evenodd\">\n      <rect x=\".7\" y=\".7\" width=\"18.6\" height=\"18.6\" rx=\"4\" />\n      <path d=\"M13.5 0L13.5 20\" />\n      <path d=\"M6,7 L9,10 L6,13\" transform=\"matrix(-1 0 0 1 15 0)\" />\n    </g>\n  </svg>\n)\nconst IconUnfold = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" className={className}>\n    <g stroke=\"#1E6AFF\" stroke-width=\"1.4\" fill=\"none\" fill-rule=\"evenodd\">\n      <rect x=\".7\" y=\".7\" width=\"18.6\" height=\"18.6\" rx=\"4\" />\n      <path d=\"M13.5 0L13.5 20\" />\n      <path d=\"M6,7 L9,10 L6,13\" />\n    </g>\n  </svg>\n)\n\nconst SvgClass = 'w-[20px] h-[20px]'\n\ntype Props = {\n  className?: string\n  isFold?: boolean\n  onClick?: () => void\n}\n\nconst BtnFold = ({ className, isFold, onClick }: Props) => {\n  return (\n    <div className={cls(className, 'cursor-pointer')} onClick={onClick}>\n      {isFold\n        ? <Button\n          variant='secondary-accent'\n          className={'h-[40px] rounded-[20px] border-[#356CFF] text-[#434B5B]'}\n        >\n          <IconUnfold className=\"w-[18px] mr-[8px]\" />\n        历史对话\n        </Button>\n        : <IconFold className={SvgClass} />}\n    </div>\n  )\n}\n\nexport default BtnFold\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AACA,MAAM,WAAW,CAAC,EAAE,SAAS,EAAyB,iBACpD,oWAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,oWAAC;YAAE,QAAO;YAAU,gBAAa;YAAM,MAAK;YAAO,aAAU;;8BAC3D,oWAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAO,QAAO;oBAAO,IAAG;;;;;;8BAClD,oWAAC;oBAAK,GAAE;;;;;;8BACR,oWAAC;oBAAK,GAAE;oBAAmB,WAAU;;;;;;;;;;;;;;;;;KALrC;AASN,MAAM,aAAa,CAAC,EAAE,SAAS,EAAyB,iBACtD,oWAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,oWAAC;YAAE,QAAO;YAAU,gBAAa;YAAM,MAAK;YAAO,aAAU;;8BAC3D,oWAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAO,QAAO;oBAAO,IAAG;;;;;;8BAClD,oWAAC;oBAAK,GAAE;;;;;;8BACR,oWAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;;MALR;AAUN,MAAM,WAAW;AAQjB,MAAM,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAS;IACpD,qBACE,oWAAC;QAAI,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAG,AAAD,EAAE,WAAW;QAAmB,SAAS;kBACxD,uBACG,oWAAC,gJAAA,CAAA,UAAM;YACP,SAAQ;YACR,WAAW;;8BAEX,oWAAC;oBAAW,WAAU;;;;;;gBAAsB;;;;;;iCAG5C,oWAAC;YAAS,WAAW;;;;;;;;;;;AAG/B;MAdM;uCAgBS", "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/base/chat/chat-with-history/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"bg\": \"index-module__guMYKq__bg\",\n  \"errorPng\": \"index-module__guMYKq__errorPng\",\n  \"mobileNavTitle\": \"index-module__guMYKq__mobileNavTitle\",\n  \"navBarBg\": \"index-module__guMYKq__navBarBg\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/sidebar/index.tsx"], "sourcesContent": ["import type { ChangeEvent } from 'react'\nimport {\n  useCallback,\n  useState,\n} from 'react'\nimport { useTranslation } from 'react-i18next'\nimport {\n  RiEditBoxLine,\n  RiExpandRightLine,\n  RiLayoutLeft2Line,\n} from '@remixicon/react'\nimport { throttle } from 'lodash-es'\nimport { useChatWithHistoryContext } from '../context'\nimport Input from '../../../input'\nimport BtnFold from '../btn-fold'\nimport AppIcon from '@/components/base/app-icon'\nimport ActionButton from '@/components/base/action-button'\nimport Button from '@/components/base/button'\nimport List from '@/components/base/chat/chat-with-history/sidebar/list'\nimport MenuDropdown from '@/components/share/text-generation/menu-dropdown'\nimport Confirm from '@/components/base/confirm'\nimport RenameModal from '@/components/base/chat/chat-with-history/sidebar/rename-modal'\nimport LogoSite from '@/components/base/logo/logo-site'\nimport type { ConversationItem } from '@/models/share'\nimport cn from '@/utils/classnames'\nimport styles from '../index.module.css'\nimport Robot from '@/assets/lark-app-robot.gif'\nimport Image from 'next/image'\n\nconst LarkDeleteIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 14 14\" className={className}>\n    <g stroke=\"#A3AFBB\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" fill-rule=\"evenodd\">\n      <path d=\"M8.75,2.04166667 L8.22889873,9.85818568 C8.19387888,10.3834835 7.75757745,10.7916667 7.23111357,10.7916667 L2.10221976,10.7916667 C1.57575588,10.7916667 1.13945446,10.3834835 1.1044346,9.85818568 L0.583333333,2.04166667 L0.583333333,2.04166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M0 2.04166667L9.33333333 2.04166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M3.5 0.29166667L5.83333333 0.29166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M3.5 7.875L3.5 4.95833333\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M5.83333333 7.875L5.83333333 4.95833333\" transform=\"translate(2.3333 1.4583)\" />\n    </g>\n  </svg>\n)\n\ntype Props = {\n  isPanel?: boolean\n}\n\nconst Sidebar = ({ isPanel }: Props) => {\n  const { t } = useTranslation()\n  const {\n    appData,\n    handleNewConversation,\n    pinnedConversationList,\n    conversationList,\n    currentConversationId,\n    handleChangeConversation,\n    handlePinConversation,\n    handleUnpinConversation,\n    conversationRenaming,\n    handleRenameConversation,\n    handleDeleteConversation,\n    sidebarCollapseState,\n    handleSidebarCollapse,\n    isMobile,\n    isResponding,\n    embedSource,\n    isFold,\n    setIsFold,\n    handleClearAllConversations\n  } = useChatWithHistoryContext()\n  const isSidebarCollapsed = sidebarCollapseState\n\n  const [showConfirm, setShowConfirm] = useState<ConversationItem | null>(null)\n  const [showClearAll, setShowClearAll] = useState<boolean | null>(null)\n  const [showRename, setShowRename] = useState<ConversationItem | null>(null)\n  const [keyword, setKeyword] = useState<string>('')\n  const isEmbedMobile = isMobile && embedSource\n\n  const handleOperate = useCallback((type: string, item: ConversationItem) => {\n    if (type === 'pin')\n      handlePinConversation(item.id)\n\n    if (type === 'unpin')\n      handleUnpinConversation(item.id)\n\n    if (type === 'delete')\n      setShowConfirm(item)\n\n    if (type === 'rename')\n      setShowRename(item)\n  }, [handlePinConversation, handleUnpinConversation])\n  const handleCancelConfirm = useCallback(() => {\n    setShowConfirm(null)\n  }, [])\n  const handleDelete = useCallback(() => {\n    if (showConfirm)\n      handleDeleteConversation(showConfirm.id, { onSuccess: handleCancelConfirm })\n  }, [showConfirm, handleDeleteConversation, handleCancelConfirm])\n  const handleCancelRename = useCallback(() => {\n    setShowRename(null)\n  }, [])\n  const handleRename = useCallback((newName: string) => {\n    if (showRename)\n      handleRenameConversation(showRename.id, newName, { onSuccess: handleCancelRename })\n  }, [showRename, handleRenameConversation, handleCancelRename])\n\n  const handleSearch = throttle(({ target }: ChangeEvent<HTMLInputElement>) => {\n    setKeyword(target?.value)\n  }, 100)\n\n  const handleClearAll = () => {\n    setShowClearAll(true)\n  }\n\n  const handleClearAllConfirm = () => {\n    handleClearAllConversations?.({ onSuccess: () => setShowClearAll(false) })\n  }\n\n  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}\n  const aiRobotGifUrl = chatPageConfigData?.aiRobotGifUrl || Robot // AI机器人动画\n\n  return (\n    <div className={cn(\n      'flex w-full grow flex-col',\n      isPanel && 'rounded-xl border-[0.5px] border-components-panel-border-subtle bg-components-panel-bg shadow-lg',\n      isEmbedMobile && styles.bg,\n      isEmbedMobile && 'bg-[#f5f6f8] w-[74vw] !border-none'\n    )}>\n      <div className={cn(\n        'flex shrink-0 items-center gap-3 p-3 pr-2',\n      )}>\n        <div className='shrink-0'>\n          {\n            embedSource ? (\n              !isMobile && <div className=\"flex items-center\">\n                <Input\n                  className='h-[40px] rounded-[8px] border-[1px] border-solid border-[#DFE4E8]'\n                  value={keyword}\n                  placeholder=\"搜索对话...\"\n                  showLeftIcon\n                  showClearIcon\n                  onChange={handleSearch}\n                  onClear={() => setKeyword('')}\n                />\n                <BtnFold className=\"ml-[11px]\" isFold={isFold} onClick={() => setIsFold?.(!isFold)} />\n              </div>\n            ) : (\n              <>\n                <AppIcon\n                  size='large'\n                  iconType={appData?.site.icon_type}\n                  icon={appData?.site.icon}\n                  background={appData?.site.icon_background}\n                  imageUrl={appData?.site.icon_url}\n                />\n                <div className={cn('system-md-semibold grow truncate text-text-secondary')}>{appData?.site.title}</div>\n                {!isMobile && isSidebarCollapsed && (\n                  <ActionButton size='l' onClick={() => handleSidebarCollapse(false)}>\n                    <RiExpandRightLine className='h-[18px] w-[18px]' />\n                  </ActionButton>\n                )}\n                {!isMobile && !isSidebarCollapsed && (\n                  <ActionButton size='l' onClick={() => handleSidebarCollapse(true)}>\n                    <RiLayoutLeft2Line className='h-[18px] w-[18px]' />\n                  </ActionButton>\n                )}\n              </>\n            )\n          }\n        </div>\n      </div>\n      {\n        !embedSource && (\n          <div className='shrink-0 px-3 py-4'>\n            <Button variant='secondary-accent' disabled={isResponding} className='w-full justify-center' onClick={handleNewConversation}>\n              <RiEditBoxLine className='mr-1 h-4 w-4' />\n              {t('share.chat.newChat')}\n            </Button>\n          </div>\n        )\n      }\n      {embedSource && isMobile && <div className=\"mb-[30px]\">\n        <Image src={aiRobotGifUrl} width={100} height={100} className=\"w-[100px] height-[100px] mt-[64px] mx-auto mb-[6px]\" alt=\"\" />\n        <p className=\"text-center text-[18px] text-[#242933] mb-[20px] font-semibold\">Hi～我是{appData?.site.title}</p>\n        <div className=\"px-[12px]\">\n          <Input\n            className='h-[40px] rounded-[8px] border-[1px] border-solid bg-[#fff] hover:bg-[#fff]'\n            value={keyword}\n            placeholder=\"搜索对话...\"\n            showLeftIcon\n            showClearIcon\n            onChange={handleSearch}\n            onClear={() => setKeyword('')}\n          />\n        </div>\n      </div>}\n      <div className=\"flex items-center justify-between px-[20px] mb-[10px]\">\n        <p className=\"text-[16px] text-[#242933] font-medium\">对话记录</p>\n        <button onClick={handleClearAll}><LarkDeleteIcon className=\"w-[20px] h-[20px]\" /></button>\n      </div>\n      <div className='h-0 grow space-y-2 overflow-y-auto px-3 pt-4'>\n        {/* pinned list */}\n        {!!pinnedConversationList.length && (\n          <div className='mb-4'>\n            <List\n              embedSource={embedSource}\n              isMobile={isMobile}\n              isPin\n              title={t('share.chat.pinnedTitle') || ''}\n              list={pinnedConversationList.filter(item => item.name.includes(keyword))}\n              onChangeConversation={handleChangeConversation}\n              onOperate={handleOperate}\n              currentConversationId={currentConversationId}\n            />\n          </div>\n        )}\n        {!!conversationList.length && (\n          <List\n            embedSource={embedSource}\n            isMobile={isMobile}\n            title={(pinnedConversationList.length && t('share.chat.unpinnedTitle')) || ''}\n            list={conversationList.filter(item => item.name.includes(keyword))}\n            onChangeConversation={handleChangeConversation}\n            onOperate={handleOperate}\n            currentConversationId={currentConversationId}\n          />\n        )}\n      </div>\n      <div className='flex shrink-0 items-center justify-between p-3'>\n        <MenuDropdown placement='top-start' data={appData?.site} />\n        {/* powered by */}\n        <div className='shrink-0'>\n          {!appData?.custom_config?.remove_webapp_brand && (\n            <div className={cn(\n              'flex shrink-0 items-center gap-1.5 px-2',\n            )}>\n              <div className='system-2xs-medium-uppercase text-text-tertiary'>{t('share.chat.poweredBy')}</div>\n              {appData?.custom_config?.replace_webapp_logo && (\n                <img src={appData?.custom_config?.replace_webapp_logo} alt='logo' className='block h-5 w-auto' />\n              )}\n              {!appData?.custom_config?.replace_webapp_logo && (\n                <LogoSite className='!h-5' />\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n      {!!showConfirm && (\n        <Confirm\n          title={embedSource && isMobile ? ' 确定要删除吗？' : t('share.chat.deleteConversation.title')}\n          content={embedSource && isMobile ? \"删除后无法撤销\" : t('share.chat.deleteConversation.content') || ''}\n          isShow\n          isEmbedMobile={Boolean(embedSource && isMobile)}\n          onCancel={handleCancelConfirm}\n          onConfirm={handleDelete}\n          footerRender={embedSource && isMobile ? (() => <div className='flex w-full border-t-[1px] border-t-[#EEF0F2] mt-[23px]'>\n            <p className=\"py-[15px] text-[#A3AFBB] flex-1 text-center border-r-[1px] border-r-[#EEF0F2] text-[16px]\" onClick={handleCancelConfirm}>取消</p>\n            <p className=\"py-[15px] text-[#FF4C4C] flex-1 text-center text-[16px]\" onClick={handleDelete}>删除</p>\n          </div>) : undefined}\n        />\n      )}\n      {showClearAll && (\n        <Confirm\n          title={'确定要删除所有对话记录吗？'}\n          content={\"删除后无法撤销\"}\n          isShow\n          isEmbedMobile={Boolean(embedSource && isMobile)}\n          onCancel={() => setShowClearAll(false)}\n          onConfirm={handleClearAllConfirm}\n          footerRender={embedSource && isMobile ? (() => <div className='flex w-full border-t-[1px] border-t-[#EEF0F2] mt-[23px]'>\n            <p className=\"py-[15px] text-[#A3AFBB] flex-1 text-center border-r-[1px] border-r-[#EEF0F2] text-[16px]\" onClick={() => setShowClearAll(false)}>取消</p>\n            <p className=\"py-[15px] text-[#FF4C4C] flex-1 text-center text-[16px]\" onClick={handleClearAllConfirm}>删除</p>\n          </div>) : undefined}\n        />\n      )}\n      {showRename && (\n        <RenameModal\n          isEmbedMobile={Boolean(embedSource && isMobile)}\n          isShow\n          onClose={handleCancelRename}\n          saveLoading={conversationRenaming}\n          name={showRename?.name || ''}\n          onSave={handleRename}\n        />\n      )}\n    </div>\n  )\n}\n\nexport default Sidebar\n"], "names": [], "mappings": ";;;;AACA;;;;;;;;;;;;;;;;AAWA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA;;;;;;AAEA;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAyB,iBAC1D,oWAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,oWAAC;YAAE,QAAO;YAAU,kBAAe;YAAQ,mBAAgB;YAAQ,MAAK;YAAO,aAAU;;8BACvF,oWAAC;oBAAK,GAAE;oBAAsP,WAAU;;;;;;8BACxQ,oWAAC;oBAAK,GAAE;oBAAsC,WAAU;;;;;;8BACxD,oWAAC;oBAAK,GAAE;oBAAwC,WAAU;;;;;;8BAC1D,oWAAC;oBAAK,GAAE;oBAA4B,WAAU;;;;;;8BAC9C,oWAAC;oBAAK,GAAE;oBAA0C,WAAU;;;;;;;;;;;;;;;;;KAP5D;AAgBN,MAAM,UAAU,CAAC,EAAE,OAAO,EAAS;;IACjC,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,MAAM,EACJ,OAAO,EACP,qBAAqB,EACrB,sBAAsB,EACtB,gBAAgB,EAChB,qBAAqB,EACrB,wBAAwB,EACxB,qBAAqB,EACrB,uBAAuB,EACvB,oBAAoB,EACpB,wBAAwB,EACxB,wBAAwB,EACxB,oBAAoB,EACpB,qBAAqB,EACrB,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,MAAM,EACN,SAAS,EACT,2BAA2B,EAC5B,GAAG,CAAA,GAAA,2KAAA,CAAA,4BAAyB,AAAD;IAC5B,MAAM,qBAAqB;IAE3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA2B;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA2B;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,gBAAgB,YAAY;IAElC,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;8CAAE,CAAC,MAAc;YAC/C,IAAI,SAAS,OACX,sBAAsB,KAAK,EAAE;YAE/B,IAAI,SAAS,SACX,wBAAwB,KAAK,EAAE;YAEjC,IAAI,SAAS,UACX,eAAe;YAEjB,IAAI,SAAS,UACX,cAAc;QAClB;6CAAG;QAAC;QAAuB;KAAwB;IACnD,MAAM,sBAAsB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;oDAAE;YACtC,eAAe;QACjB;mDAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;6CAAE;YAC/B,IAAI,aACF,yBAAyB,YAAY,EAAE,EAAE;gBAAE,WAAW;YAAoB;QAC9E;4CAAG;QAAC;QAAa;QAA0B;KAAoB;IAC/D,MAAM,qBAAqB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;mDAAE;YACrC,cAAc;QAChB;kDAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;6CAAE,CAAC;YAChC,IAAI,YACF,yBAAyB,WAAW,EAAE,EAAE,SAAS;gBAAE,WAAW;YAAmB;QACrF;4CAAG;QAAC;QAAY;QAA0B;KAAmB;IAE7D,MAAM,eAAe,SAAS,CAAC,EAAE,MAAM,EAAiC;QACtE,WAAW,QAAQ;IACrB,GAAG;IAEH,MAAM,iBAAiB;QACrB,gBAAgB;IAClB;IAEA,MAAM,wBAAwB;QAC5B,8BAA8B;YAAE,WAAW,IAAM,gBAAgB;QAAO;IAC1E;IAEA,MAAM,qBAAqB,SAAS,MAAM,iBAAiB,KAAK,KAAK,CAAC,SAAS,MAAM,kBAAkB,CAAC;IACxG,MAAM,gBAAgB,oBAAoB,iBAAiB,MAAM,UAAU;;IAE3E,qBACE,oWAAC;QAAI,WAAW,GACd,6BACA,WAAW,oGACX,iBAAiB,oLAAA,CAAA,UAAM,CAAC,EAAE,EAC1B,iBAAiB;;0BAEjB,oWAAC;gBAAI,WAAW,GACd;0BAEA,cAAA,oWAAC;oBAAI,WAAU;8BAEX,cACE,CAAC,0BAAY,oWAAC;wBAAI,WAAU;;0CAC1B,oWAAC,+IAAA,CAAA,UAAK;gCACJ,WAAU;gCACV,OAAO;gCACP,aAAY;gCACZ,YAAY;gCACZ,aAAa;gCACb,UAAU;gCACV,SAAS,IAAM,WAAW;;;;;;0CAE5B,oWAAC,+KAAA,CAAA,UAAO;gCAAC,WAAU;gCAAY,QAAQ;gCAAQ,SAAS,IAAM,YAAY,CAAC;;;;;;;;;;;6CAG7E;;0CACE,oWAAC;gCACC,MAAK;gCACL,UAAU,SAAS,KAAK;gCACxB,MAAM,SAAS,KAAK;gCACpB,YAAY,SAAS,KAAK;gCAC1B,UAAU,SAAS,KAAK;;;;;;0CAE1B,oWAAC;gCAAI,WAAW,GAAG;0CAA0D,SAAS,KAAK;;;;;;4BAC1F,CAAC,YAAY,oCACZ,oWAAC;gCAAa,MAAK;gCAAI,SAAS,IAAM,sBAAsB;0CAC1D,cAAA,oWAAC;oCAAkB,WAAU;;;;;;;;;;;4BAGhC,CAAC,YAAY,CAAC,oCACb,oWAAC;gCAAa,MAAK;gCAAI,SAAS,IAAM,sBAAsB;0CAC1D,cAAA,oWAAC;oCAAkB,WAAU;;;;;;;;;;;;;;;;;;;;;;;YASzC,CAAC,6BACC,oWAAC;gBAAI,WAAU;0BACb,cAAA,oWAAC;oBAAO,SAAQ;oBAAmB,UAAU;oBAAc,WAAU;oBAAwB,SAAS;;sCACpG,oWAAC;4BAAc,WAAU;;;;;;wBACxB,EAAE;;;;;;;;;;;;YAKV,eAAe,0BAAY,oWAAC;gBAAI,WAAU;;kCACzC,oWAAC,uSAAA,CAAA,UAAK;wBAAC,KAAK;wBAAe,OAAO;wBAAK,QAAQ;wBAAK,WAAU;wBAAsD,KAAI;;;;;;kCACxH,oWAAC;wBAAE,WAAU;;4BAAiE;4BAAM,SAAS,KAAK;;;;;;;kCAClG,oWAAC;wBAAI,WAAU;kCACb,cAAA,oWAAC,+IAAA,CAAA,UAAK;4BACJ,WAAU;4BACV,OAAO;4BACP,aAAY;4BACZ,YAAY;4BACZ,aAAa;4BACb,UAAU;4BACV,SAAS,IAAM,WAAW;;;;;;;;;;;;;;;;;0BAIhC,oWAAC;gBAAI,WAAU;;kCACb,oWAAC;wBAAE,WAAU;kCAAyC;;;;;;kCACtD,oWAAC;wBAAO,SAAS;kCAAgB,cAAA,oWAAC;4BAAe,WAAU;;;;;;;;;;;;;;;;;0BAE7D,oWAAC;gBAAI,WAAU;;oBAEZ,CAAC,CAAC,uBAAuB,MAAM,kBAC9B,oWAAC;wBAAI,WAAU;kCACb,cAAA,oWAAC;4BACC,aAAa;4BACb,UAAU;4BACV,KAAK;4BACL,OAAO,EAAE,6BAA6B;4BACtC,MAAM,uBAAuB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC;4BAC/D,sBAAsB;4BACtB,WAAW;4BACX,uBAAuB;;;;;;;;;;;oBAI5B,CAAC,CAAC,iBAAiB,MAAM,kBACxB,oWAAC;wBACC,aAAa;wBACb,UAAU;wBACV,OAAO,AAAC,uBAAuB,MAAM,IAAI,EAAE,+BAAgC;wBAC3E,MAAM,iBAAiB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC;wBACzD,sBAAsB;wBACtB,WAAW;wBACX,uBAAuB;;;;;;;;;;;;0BAI7B,oWAAC;gBAAI,WAAU;;kCACb,oWAAC;wBAAa,WAAU;wBAAY,MAAM,SAAS;;;;;;kCAEnD,oWAAC;wBAAI,WAAU;kCACZ,CAAC,SAAS,eAAe,qCACxB,oWAAC;4BAAI,WAAW,GACd;;8CAEA,oWAAC;oCAAI,WAAU;8CAAkD,EAAE;;;;;;gCAClE,SAAS,eAAe,qCACvB,oWAAC;oCAAI,KAAK,SAAS,eAAe;oCAAqB,KAAI;oCAAO,WAAU;;;;;;gCAE7E,CAAC,SAAS,eAAe,qCACxB,oWAAC;oCAAS,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAM7B,CAAC,CAAC,6BACD,oWAAC;gBACC,OAAO,eAAe,WAAW,aAAa,EAAE;gBAChD,SAAS,eAAe,WAAW,YAAY,EAAE,4CAA4C;gBAC7F,MAAM;gBACN,eAAe,QAAQ,eAAe;gBACtC,UAAU;gBACV,WAAW;gBACX,cAAc,eAAe,WAAY,kBAAM,oWAAC;wBAAI,WAAU;;0CAC5D,oWAAC;gCAAE,WAAU;gCAA4F,SAAS;0CAAqB;;;;;;0CACvI,oWAAC;gCAAE,WAAU;gCAA0D,SAAS;0CAAc;;;;;;;;;;;iCACtF;;;;;;YAGb,8BACC,oWAAC;gBACC,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,eAAe,QAAQ,eAAe;gBACtC,UAAU,IAAM,gBAAgB;gBAChC,WAAW;gBACX,cAAc,eAAe,WAAY,kBAAM,oWAAC;wBAAI,WAAU;;0CAC5D,oWAAC;gCAAE,WAAU;gCAA4F,SAAS,IAAM,gBAAgB;0CAAQ;;;;;;0CAChJ,oWAAC;gCAAE,WAAU;gCAA0D,SAAS;0CAAuB;;;;;;;;;;;iCAC/F;;;;;;YAGb,4BACC,oWAAC;gBACC,eAAe,QAAQ,eAAe;gBACtC,MAAM;gBACN,SAAS;gBACT,aAAa;gBACb,MAAM,YAAY,QAAQ;gBAC1B,QAAQ;;;;;;;;;;;;AAKlB;GAhPM;;QACU;QAqBV,2KAAA,CAAA,4BAAyB;;;MAtBzB;uCAkPS", "debugId": null}}, {"offset": {"line": 1153, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/index.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport {\n  useState,\n} from 'react'\nimport Sidebar from './sidebar'\n// import Header from './header'\n// import HeaderInMobile from './header-in-mobile'\n// import ChatWrapper from './chat-wrapper'\nimport styles from './index.module.css'\n// import Loading from '@/components/base/loading'\nimport cn from 'classnames'\n// import RightSidebar from './right-sidebar'\n\ntype ChatWithHistoryProps = {\n  className?: string\n}\n\nconst ChatWithHistory: FC<ChatWithHistoryProps> = ({\n  className,\n}) => {\n  const [showSidePanel, setShowSidePanel] = useState(false)\n  const isMobile = false\n  const embedSource = true\n  const isSidebarCollapsed = false\n  const isFold = false\n  const appChatListDataLoading = false\n  const chatShouldReloadKey = false\n  const refreshRenderKey = false\n  const rightSideInfo = false\n\n  return (\n    <div className={cn(\n      'flex h-full bg-background-default-burn',\n      isMobile && 'flex-col',\n      className,\n      embedSource && isMobile && '!bg-[#f5f6f8]',\n      embedSource && isMobile && styles.bg,\n    )}>\n      {(!isMobile && !isFold) && (\n        <div className={cn(\n          'flex w-[236px] flex-col pr-0 transition-all duration-200 ease-in-out',\n          isSidebarCollapsed && 'w-0 overflow-hidden !p-0',\n          embedSource && !isMobile && '!bg-white',\n        )}>\n          <Sidebar />\n        </div>\n      )}\n      {/* {isMobile && (\n        <HeaderInMobile />\n      )} */}\n      <div className={cn('relative grow p-2', embedSource && 'p-0', isMobile && 'h-[calc(100%_-_56px)] p-0')}>\n        {isSidebarCollapsed && (\n          <div\n            className={cn(\n              'absolute top-0 z-20 flex h-full w-[256px] flex-col p-2 transition-all duration-500 ease-in-out',\n              showSidePanel ? 'left-0' : 'left-[-248px]',\n            )}\n            onMouseEnter={() => setShowSidePanel(true)}\n            onMouseLeave={() => setShowSidePanel(false)}\n          >\n            {/* <Sidebar isPanel /> */}\n          </div>\n        )}\n        <div className={cn('flex h-full flex-col overflow-hidden border-[0,5px] border-components-panel-border-subtle bg-chatbot-bg', isMobile ? 'rounded-t-2xl' : 'rounded-2xl', embedSource && 'rounded-none')}>\n          {/* {!isMobile && !embedSource && <Header />} */}\n          {/* {appChatListDataLoading && (\n            <Loading />\n          )} */}\n          {/* {!appChatListDataLoading && (\n            <ChatWrapper key={chatShouldReloadKey || refreshRenderKey} />\n          )} */}\n        </div>\n      </div>\n      {/* <RightSidebar isMobile={isMobile} visible={Boolean(rightSideInfo)}/> */}\n    </div>\n  )\n}\n\nexport default ChatWithHistory\n"], "names": [], "mappings": ";;;;AACA;AAGA;AACA,gCAAgC;AAChC,kDAAkD;AAClD,2CAA2C;AAC3C;AACA,sDAAsD;AACtD;;;;;;;AAOA,MAAM,kBAA4C,CAAC,EACjD,SAAS,EACV;;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW;IACjB,MAAM,cAAc;IACpB,MAAM,qBAAqB;IAC3B,MAAM,SAAS;IACf,MAAM,yBAAyB;IAC/B,MAAM,sBAAsB;IAC5B,MAAM,mBAAmB;IACzB,MAAM,gBAAgB;IAEtB,qBACE,oWAAC;QAAI,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAE,AAAD,EACf,0CACA,YAAY,YACZ,WACA,eAAe,YAAY,iBAC3B,eAAe,YAAY,oLAAA,CAAA,UAAM,CAAC,EAAE;;YAElC,CAAC,YAAY,CAAC,wBACd,oWAAC;gBAAI,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAE,AAAD,EACf,wEACA,sBAAsB,4BACtB,eAAe,CAAC,YAAY;0BAE5B,cAAA,oWAAC,oLAAA,CAAA,UAAO;;;;;;;;;;0BAMZ,oWAAC;gBAAI,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAE,AAAD,EAAE,qBAAqB,eAAe,OAAO,YAAY;;oBACvE,oCACC,oWAAC;wBACC,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAE,AAAD,EACV,kGACA,gBAAgB,WAAW;wBAE7B,cAAc,IAAM,iBAAiB;wBACrC,cAAc,IAAM,iBAAiB;;;;;;kCAKzC,oWAAC;wBAAI,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAE,AAAD,EAAE,2GAA2G,6EAA6B,eAAe,eAAe;;;;;;;;;;;;;;;;;;AAajM;GA3DM;KAAA;uCA6DS", "debugId": null}}, {"offset": {"line": 1243, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/app/chat/page.tsx"], "sourcesContent": ["'use client'\nimport React from 'react'\n// import Chat<PERSON>ithHistoryWrap from '@/components/base/chat/chat-with-history'\nimport ChatWithHistoryWrap from '@/components/base/chat/chat-with-history/index'\n\nconst Chat = () => {\n  return (\n    <>\n      <ChatWithHistoryWrap />\n    </>\n  )\n}\n\nexport default React.memo(Chat)\n"], "names": [], "mappings": ";;;;AACA;AACA,6EAA6E;AAC7E;AAHA;;;;AAKA,MAAM,OAAO;IACX,qBACE;kBACE,cAAA,oWAAC,yKAAA,CAAA,UAAmB;;;;;;AAG1B;KANM;2DAQS,oUAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}]}