{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/keyboard.js"], "sourcesContent": ["var o=(r=>(r.<PERSON>=\" \",r.<PERSON><PERSON>=\"Enter\",r.<PERSON>=\"Escape\",r.<PERSON>space=\"Backspace\",r.Delete=\"Delete\",r.<PERSON>=\"ArrowLeft\",r.<PERSON>p=\"ArrowUp\",r.<PERSON>=\"ArrowRight\",r.<PERSON>=\"ArrowDown\",r.Home=\"Home\",r.End=\"End\",r.PageUp=\"PageUp\",r.PageDown=\"PageDown\",r.Tab=\"Tab\",r))(o||{});export{o as Keys};\n"], "names": [], "mappings": ";;;AAAA,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,EAAE,KAAK,GAAC,KAAI,EAAE,KAAK,GAAC,SAAQ,EAAE,MAAM,GAAC,UAAS,EAAE,SAAS,GAAC,aAAY,EAAE,MAAM,GAAC,UAAS,EAAE,SAAS,GAAC,aAAY,EAAE,OAAO,GAAC,WAAU,EAAE,UAAU,GAAC,cAAa,EAAE,SAAS,GAAC,aAAY,EAAE,IAAI,GAAC,QAAO,EAAE,GAAG,GAAC,OAAM,EAAE,MAAM,GAAC,UAAS,EAAE,QAAQ,GAAC,YAAW,EAAE,GAAG,GAAC,OAAM,CAAC,CAAC,EAAE,KAAG,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/env.js"], "sourcesContent": ["var i=Object.defineProperty;var d=(t,e,n)=>e in t?i(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var r=(t,e,n)=>(d(t,typeof e!=\"symbol\"?e+\"\":e,n),n);class o{constructor(){r(this,\"current\",this.detect());r(this,\"handoffState\",\"pending\");r(this,\"currentId\",0)}set(e){this.current!==e&&(this.handoffState=\"pending\",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current===\"server\"}get isClient(){return this.current===\"client\"}detect(){return typeof window==\"undefined\"||typeof document==\"undefined\"?\"server\":\"client\"}handoff(){this.handoffState===\"pending\"&&(this.handoffState=\"complete\")}get isHandoffComplete(){return this.handoffState===\"complete\"}}let s=new o;export{s as env};\n"], "names": [], "mappings": ";;;AAAA,IAAI,IAAE,OAAO,cAAc;AAAC,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,KAAK,IAAE,EAAE,GAAE,GAAE;QAAC,YAAW,CAAC;QAAE,cAAa,CAAC;QAAE,UAAS,CAAC;QAAE,OAAM;IAAC,KAAG,CAAC,CAAC,EAAE,GAAC;AAAE,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,CAAC,EAAE,GAAE,OAAO,KAAG,WAAS,IAAE,KAAG,GAAE,IAAG,CAAC;AAAE,MAAM;IAAE,aAAa;QAAC,EAAE,IAAI,EAAC,WAAU,IAAI,CAAC,MAAM;QAAI,EAAE,IAAI,EAAC,gBAAe;QAAW,EAAE,IAAI,EAAC,aAAY;IAAE;IAAC,IAAI,CAAC,EAAC;QAAC,IAAI,CAAC,OAAO,KAAG,KAAG,CAAC,IAAI,CAAC,YAAY,GAAC,WAAU,IAAI,CAAC,SAAS,GAAC,GAAE,IAAI,CAAC,OAAO,GAAC,CAAC;IAAC;IAAC,QAAO;QAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM;IAAG;IAAC,SAAQ;QAAC,OAAM,EAAE,IAAI,CAAC,SAAS;IAAA;IAAC,IAAI,WAAU;QAAC,OAAO,IAAI,CAAC,OAAO,KAAG;IAAQ;IAAC,IAAI,WAAU;QAAC,OAAO,IAAI,CAAC,OAAO,KAAG;IAAQ;IAAC,SAAQ;QAAC,OAAO,OAAO,UAAQ,eAAa,OAAO,YAAU,cAAY,WAAS;IAAQ;IAAC,UAAS;QAAC,IAAI,CAAC,YAAY,KAAG,aAAW,CAAC,IAAI,CAAC,YAAY,GAAC,UAAU;IAAC;IAAC,IAAI,oBAAmB;QAAC,OAAO,IAAI,CAAC,YAAY,KAAG;IAAU;AAAC;AAAC,IAAI,IAAE,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-iso-morphic-effect.js"], "sourcesContent": ["import{useEffect as f,useLayoutEffect as c}from\"react\";import{env as i}from'../utils/env.js';let n=(e,t)=>{i.isServer?f(e,t):c(e,t)};export{n as useIsoMorphicEffect};\n"], "names": [], "mappings": ";;;AAAA;AAAuD;;;AAAsC,IAAI,IAAE,CAAC,GAAE;IAAK,+SAAA,CAAA,MAAC,CAAC,QAAQ,GAAC,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE,GAAE,KAAG,CAAA,GAAA,oUAAA,CAAA,kBAAC,AAAD,EAAE,GAAE;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-latest-value.js"], "sourcesContent": ["import{useRef as t}from\"react\";import{useIsoMorphicEffect as o}from'./use-iso-morphic-effect.js';function s(e){let r=t(e);return o(()=>{r.current=e},[e]),r}export{s as useLatestValue};\n"], "names": [], "mappings": ";;;AAAA;AAA+B;;;AAAkE,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE;IAAG,OAAO,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,EAAE,OAAO,GAAC;IAAC,GAAE;QAAC;KAAE,GAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-event-listener.js"], "sourcesContent": ["import{useEffect as d}from\"react\";import{useLatestValue as s}from'./use-latest-value.js';function E(n,e,a,t){let i=s(a);d(()=>{n=n!=null?n:window;function r(o){i.current(o)}return n.addEventListener(e,r,t),()=>n.removeEventListener(e,r,t)},[n,e,t])}export{E as useEventListener};\n"], "names": [], "mappings": ";;;AAAA;AAAkC;;;AAAuD,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,kUAAA,CAAA,iBAAC,AAAD,EAAE;IAAG,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE;QAAK,IAAE,KAAG,OAAK,IAAE;QAAO,SAAS,EAAE,CAAC;YAAE,EAAE,OAAO,CAAC;QAAE;QAAC,OAAO,EAAE,gBAAgB,CAAC,GAAE,GAAE,IAAG,IAAI,EAAE,mBAAmB,CAAC,GAAE,GAAE;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/default-map.js"], "sourcesContent": ["class a extends Map{constructor(t){super();this.factory=t}get(t){let e=super.get(t);return e===void 0&&(e=this.factory(t),this.set(t,e)),e}}export{a as DefaultMap};\n"], "names": [], "mappings": ";;;AAAA,MAAM,UAAU;IAAI,YAAY,CAAC,CAAC;QAAC,KAAK;QAAG,IAAI,CAAC,OAAO,GAAC;IAAC;IAAC,IAAI,CAAC,EAAC;QAAC,IAAI,IAAE,KAAK,CAAC,IAAI;QAAG,OAAO,MAAI,KAAK,KAAG,CAAC,IAAE,IAAI,CAAC,OAAO,CAAC,IAAG,IAAI,CAAC,GAAG,CAAC,GAAE,EAAE,GAAE;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/micro-task.js"], "sourcesContent": ["function t(e){typeof queueMicrotask==\"function\"?queueMicrotask(e):Promise.resolve().then(e).catch(o=>setTimeout(()=>{throw o}))}export{t as microTask};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,CAAC;IAAE,OAAO,kBAAgB,aAAW,eAAe,KAAG,QAAQ,OAAO,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,CAAA,IAAG,WAAW;YAAK,MAAM;QAAC;AAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/disposables.js"], "sourcesContent": ["import{microTask as a}from'./micro-task.js';function o(){let s=[],r={addEventListener(e,t,n,i){return e.addEventListener(t,n,i),r.add(()=>e.removeEventListener(t,n,i))},requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return r.add(()=>cancelAnimationFrame(t))},nextFrame(...e){return r.requestAnimationFrame(()=>r.requestAnimationFrame(...e))},setTimeout(...e){let t=setTimeout(...e);return r.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return a(()=>{t.current&&e[0]()}),r.add(()=>{t.current=!1})},style(e,t,n){let i=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:i})})},group(e){let t=o();return e(t),this.add(()=>t.dispose())},add(e){return s.includes(e)||s.push(e),()=>{let t=s.indexOf(e);if(t>=0)for(let n of s.splice(t,1))n()}},dispose(){for(let e of s.splice(0))e()}};return r}export{o as disposables};\n"], "names": [], "mappings": ";;;AAAA;;AAA4C,SAAS;IAAI,IAAI,IAAE,EAAE,EAAC,IAAE;QAAC,kBAAiB,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,OAAO,EAAE,gBAAgB,CAAC,GAAE,GAAE,IAAG,EAAE,GAAG,CAAC,IAAI,EAAE,mBAAmB,CAAC,GAAE,GAAE;QAAG;QAAE,uBAAsB,GAAG,CAAC;YAAE,IAAI,IAAE,yBAAyB;YAAG,OAAO,EAAE,GAAG,CAAC,IAAI,qBAAqB;QAAG;QAAE,WAAU,GAAG,CAAC;YAAE,OAAO,EAAE,qBAAqB,CAAC,IAAI,EAAE,qBAAqB,IAAI;QAAG;QAAE,YAAW,GAAG,CAAC;YAAE,IAAI,IAAE,cAAc;YAAG,OAAO,EAAE,GAAG,CAAC,IAAI,aAAa;QAAG;QAAE,WAAU,GAAG,CAAC;YAAE,IAAI,IAAE;gBAAC,SAAQ,CAAC;YAAC;YAAE,OAAO,CAAA,GAAA,yTAAA,CAAA,YAAC,AAAD,EAAE;gBAAK,EAAE,OAAO,IAAE,CAAC,CAAC,EAAE;YAAE,IAAG,EAAE,GAAG,CAAC;gBAAK,EAAE,OAAO,GAAC,CAAC;YAAC;QAAE;QAAE,OAAM,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,KAAK,CAAC,gBAAgB,CAAC;YAAG,OAAO,OAAO,MAAM,CAAC,EAAE,KAAK,EAAC;gBAAC,CAAC,EAAE,EAAC;YAAC,IAAG,IAAI,CAAC,GAAG,CAAC;gBAAK,OAAO,MAAM,CAAC,EAAE,KAAK,EAAC;oBAAC,CAAC,EAAE,EAAC;gBAAC;YAAE;QAAE;QAAE,OAAM,CAAC;YAAE,IAAI,IAAE;YAAI,OAAO,EAAE,IAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO;QAAG;QAAE,KAAI,CAAC;YAAE,OAAO,EAAE,QAAQ,CAAC,MAAI,EAAE,IAAI,CAAC,IAAG;gBAAK,IAAI,IAAE,EAAE,OAAO,CAAC;gBAAG,IAAG,KAAG,GAAE,KAAI,IAAI,KAAK,EAAE,MAAM,CAAC,GAAE,GAAG;YAAG;QAAC;QAAE;YAAU,KAAI,IAAI,KAAK,EAAE,MAAM,CAAC,GAAG;QAAG;IAAC;IAAE,OAAO;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/machine.js"], "sourcesContent": ["var p=Object.defineProperty;var h=(t,e,r)=>e in t?p(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var f=(t,e,r)=>(h(t,typeof e!=\"symbol\"?e+\"\":e,r),r),b=(t,e,r)=>{if(!e.has(t))throw TypeError(\"Cannot \"+r)};var n=(t,e,r)=>(b(t,e,\"read from private field\"),r?r.call(t):e.get(t)),c=(t,e,r)=>{if(e.has(t))throw TypeError(\"Cannot add the same private member more than once\");e instanceof WeakSet?e.add(t):e.set(t,r)},u=(t,e,r,s)=>(b(t,e,\"write to private field\"),s?s.call(t,r):e.set(t,r),r);var i,a,o;import{DefaultMap as v}from'./utils/default-map.js';import{disposables as S}from'./utils/disposables.js';class E{constructor(e){c(this,i,{});c(this,a,new v(()=>new Set));c(this,o,new Set);f(this,\"disposables\",S());u(this,i,e)}dispose(){this.disposables.dispose()}get state(){return n(this,i)}subscribe(e,r){let s={selector:e,callback:r,current:e(n(this,i))};return n(this,o).add(s),this.disposables.add(()=>{n(this,o).delete(s)})}on(e,r){return n(this,a).get(e).add(r),this.disposables.add(()=>{n(this,a).get(e).delete(r)})}send(e){let r=this.reduce(n(this,i),e);if(r!==n(this,i)){u(this,i,r);for(let s of n(this,o)){let l=s.selector(n(this,i));j(s.current,l)||(s.current=l,s.callback(l))}for(let s of n(this,a).get(e.type))s(n(this,i),e)}}}i=new WeakMap,a=new WeakMap,o=new WeakMap;function j(t,e){return Object.is(t,e)?!0:typeof t!=\"object\"||t===null||typeof e!=\"object\"||e===null?!1:Array.isArray(t)&&Array.isArray(e)?t.length!==e.length?!1:d(t[Symbol.iterator](),e[Symbol.iterator]()):t instanceof Map&&e instanceof Map||t instanceof Set&&e instanceof Set?t.size!==e.size?!1:d(t.entries(),e.entries()):y(t)&&y(e)?d(Object.entries(t)[Symbol.iterator](),Object.entries(e)[Symbol.iterator]()):!1}function d(t,e){do{let r=t.next(),s=e.next();if(r.done&&s.done)return!0;if(r.done||s.done||!Object.is(r.value,s.value))return!1}while(!0)}function y(t){if(Object.prototype.toString.call(t)!==\"[object Object]\")return!1;let e=Object.getPrototypeOf(t);return e===null||Object.getPrototypeOf(e)===null}function x(t){let[e,r]=t(),s=S();return(...l)=>{e(...l),s.dispose(),s.microTask(r)}}export{E as Machine,x as batch,j as shallowEqual};\n"], "names": [], "mappings": ";;;;;AAAigB;AAAoD;AAArjB,IAAI,IAAE,OAAO,cAAc;AAAC,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,KAAK,IAAE,EAAE,GAAE,GAAE;QAAC,YAAW,CAAC;QAAE,cAAa,CAAC;QAAE,UAAS,CAAC;QAAE,OAAM;IAAC,KAAG,CAAC,CAAC,EAAE,GAAC;AAAE,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,CAAC,EAAE,GAAE,OAAO,KAAG,WAAS,IAAE,KAAG,GAAE,IAAG,CAAC,GAAE,IAAE,CAAC,GAAE,GAAE;IAAK,IAAG,CAAC,EAAE,GAAG,CAAC,IAAG,MAAM,UAAU,YAAU;AAAE;AAAE,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,CAAC,EAAE,GAAE,GAAE,4BAA2B,IAAE,EAAE,IAAI,CAAC,KAAG,EAAE,GAAG,CAAC,EAAE,GAAE,IAAE,CAAC,GAAE,GAAE;IAAK,IAAG,EAAE,GAAG,CAAC,IAAG,MAAM,UAAU;IAAqD,aAAa,UAAQ,EAAE,GAAG,CAAC,KAAG,EAAE,GAAG,CAAC,GAAE;AAAE,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,IAAI,CAAC,EAAE,GAAE,GAAE,2BAA0B,IAAE,EAAE,IAAI,CAAC,GAAE,KAAG,EAAE,GAAG,CAAC,GAAE,IAAG,CAAC;AAAE,IAAI,GAAE,GAAE;;;AAA2G,MAAM;IAAE,YAAY,CAAC,CAAC;QAAC,EAAE,IAAI,EAAC,GAAE,CAAC;QAAG,EAAE,IAAI,EAAC,GAAE,IAAI,0TAAA,CAAA,aAAC,CAAC,IAAI,IAAI;QAAM,EAAE,IAAI,EAAC,GAAE,IAAI;QAAK,EAAE,IAAI,EAAC,eAAc,CAAA,GAAA,uTAAA,CAAA,cAAC,AAAD;QAAK,EAAE,IAAI,EAAC,GAAE;IAAE;IAAC,UAAS;QAAC,IAAI,CAAC,WAAW,CAAC,OAAO;IAAE;IAAC,IAAI,QAAO;QAAC,OAAO,EAAE,IAAI,EAAC;IAAE;IAAC,UAAU,CAAC,EAAC,CAAC,EAAC;QAAC,IAAI,IAAE;YAAC,UAAS;YAAE,UAAS;YAAE,SAAQ,EAAE,EAAE,IAAI,EAAC;QAAG;QAAE,OAAO,EAAE,IAAI,EAAC,GAAG,GAAG,CAAC,IAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;YAAK,EAAE,IAAI,EAAC,GAAG,MAAM,CAAC;QAAE;IAAE;IAAC,GAAG,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,EAAE,IAAI,EAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;YAAK,EAAE,IAAI,EAAC,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC;QAAE;IAAE;IAAC,KAAK,CAAC,EAAC;QAAC,IAAI,IAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAC,IAAG;QAAG,IAAG,MAAI,EAAE,IAAI,EAAC,IAAG;YAAC,EAAE,IAAI,EAAC,GAAE;YAAG,KAAI,IAAI,KAAK,EAAE,IAAI,EAAC,GAAG;gBAAC,IAAI,IAAE,EAAE,QAAQ,CAAC,EAAE,IAAI,EAAC;gBAAI,EAAE,EAAE,OAAO,EAAC,MAAI,CAAC,EAAE,OAAO,GAAC,GAAE,EAAE,QAAQ,CAAC,EAAE;YAAC;YAAC,KAAI,IAAI,KAAK,EAAE,IAAI,EAAC,GAAG,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAC,IAAG;QAAE;IAAC;AAAC;AAAC,IAAE,IAAI,SAAQ,IAAE,IAAI,SAAQ,IAAE,IAAI;AAAQ,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,OAAO,OAAO,EAAE,CAAC,GAAE,KAAG,CAAC,IAAE,OAAO,KAAG,YAAU,MAAI,QAAM,OAAO,KAAG,YAAU,MAAI,OAAK,CAAC,IAAE,MAAM,OAAO,CAAC,MAAI,MAAM,OAAO,CAAC,KAAG,EAAE,MAAM,KAAG,EAAE,MAAM,GAAC,CAAC,IAAE,EAAE,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAG,CAAC,CAAC,OAAO,QAAQ,CAAC,MAAI,aAAa,OAAK,aAAa,OAAK,aAAa,OAAK,aAAa,MAAI,EAAE,IAAI,KAAG,EAAE,IAAI,GAAC,CAAC,IAAE,EAAE,EAAE,OAAO,IAAG,EAAE,OAAO,MAAI,EAAE,MAAI,EAAE,KAAG,EAAE,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO,QAAQ,CAAC,IAAG,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO,QAAQ,CAAC,MAAI,CAAC;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,GAAE;QAAC,IAAI,IAAE,EAAE,IAAI,IAAG,IAAE,EAAE,IAAI;QAAG,IAAG,EAAE,IAAI,IAAE,EAAE,IAAI,EAAC,OAAM,CAAC;QAAE,IAAG,EAAE,IAAI,IAAE,EAAE,IAAI,IAAE,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,EAAC,EAAE,KAAK,GAAE,OAAM,CAAC;IAAC,QAAO,CAAC,EAAE;AAAA;AAAC,SAAS,EAAE,CAAC;IAAE,IAAG,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAK,mBAAkB,OAAM,CAAC;IAAE,IAAI,IAAE,OAAO,cAAc,CAAC;IAAG,OAAO,MAAI,QAAM,OAAO,cAAc,CAAC,OAAK;AAAI;AAAC,SAAS,EAAE,CAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,KAAI,IAAE,CAAA,GAAA,uTAAA,CAAA,cAAC,AAAD;IAAI,OAAM,CAAC,GAAG;QAAK,KAAK,IAAG,EAAE,OAAO,IAAG,EAAE,SAAS,CAAC;IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/match.js"], "sourcesContent": ["function u(r,n,...a){if(r in n){let e=n[r];return typeof e==\"function\"?e(...a):e}let t=new Error(`Tried to handle \"${r}\" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(e=>`\"${e}\"`).join(\", \")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}export{u as match};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,CAAC;IAAE,IAAG,KAAK,GAAE;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,OAAO,OAAO,KAAG,aAAW,KAAK,KAAG;IAAC;IAAC,IAAI,IAAE,IAAI,MAAM,CAAC,iBAAiB,EAAE,EAAE,8DAA8D,EAAE,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA,IAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAAE,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/machines/stack-machine.js"], "sourcesContent": ["var a=Object.defineProperty;var r=(e,c,t)=>c in e?a(e,c,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[c]=t;var p=(e,c,t)=>(r(e,typeof c!=\"symbol\"?c+\"\":c,t),t);import{Machine as d}from'../machine.js';import{DefaultMap as l}from'../utils/default-map.js';import{match as u}from'../utils/match.js';var k=(t=>(t[t.Push=0]=\"Push\",t[t.Pop=1]=\"Pop\",t))(k||{});let y={[0](e,c){let t=c.id,s=e.stack,i=e.stack.indexOf(t);if(i!==-1){let n=e.stack.slice();return n.splice(i,1),n.push(t),s=n,{...e,stack:s}}return{...e,stack:[...e.stack,t]}},[1](e,c){let t=c.id,s=e.stack.indexOf(t);if(s===-1)return e;let i=e.stack.slice();return i.splice(s,1),{...e,stack:i}}};class o extends d{constructor(){super(...arguments);p(this,\"actions\",{push:t=>this.send({type:0,id:t}),pop:t=>this.send({type:1,id:t})});p(this,\"selectors\",{isTop:(t,s)=>t.stack[t.stack.length-1]===s,inStack:(t,s)=>t.stack.includes(s)})}static new(){return new o({stack:[]})}reduce(t,s){return u(s.type,y,t,s)}}const x=new l(()=>o.new());export{k as ActionTypes,x as stackMachines};\n"], "names": [], "mappings": ";;;;AAAwK;AAAwC;AAAqD;AAArQ,IAAI,IAAE,OAAO,cAAc;AAAC,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,KAAK,IAAE,EAAE,GAAE,GAAE;QAAC,YAAW,CAAC;QAAE,cAAa,CAAC;QAAE,UAAS,CAAC;QAAE,OAAM;IAAC,KAAG,CAAC,CAAC,EAAE,GAAC;AAAE,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,CAAC,EAAE,GAAE,OAAO,KAAG,WAAS,IAAE,KAAG,GAAE,IAAG,CAAC;;;;AAAyI,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,GAAG,GAAC,EAAE,GAAC,OAAM,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,IAAI,IAAE;IAAC,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,EAAE,EAAE,EAAC,IAAE,EAAE,KAAK,EAAC,IAAE,EAAE,KAAK,CAAC,OAAO,CAAC;QAAG,IAAG,MAAI,CAAC,GAAE;YAAC,IAAI,IAAE,EAAE,KAAK,CAAC,KAAK;YAAG,OAAO,EAAE,MAAM,CAAC,GAAE,IAAG,EAAE,IAAI,CAAC,IAAG,IAAE,GAAE;gBAAC,GAAG,CAAC;gBAAC,OAAM;YAAC;QAAC;QAAC,OAAM;YAAC,GAAG,CAAC;YAAC,OAAM;mBAAI,EAAE,KAAK;gBAAC;aAAE;QAAA;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,EAAE,EAAE,EAAC,IAAE,EAAE,KAAK,CAAC,OAAO,CAAC;QAAG,IAAG,MAAI,CAAC,GAAE,OAAO;QAAE,IAAI,IAAE,EAAE,KAAK,CAAC,KAAK;QAAG,OAAO,EAAE,MAAM,CAAC,GAAE,IAAG;YAAC,GAAG,CAAC;YAAC,OAAM;QAAC;IAAC;AAAC;AAAE,MAAM,UAAU,0SAAA,CAAA,UAAC;IAAC,aAAa;QAAC,KAAK,IAAI;QAAW,EAAE,IAAI,EAAC,WAAU;YAAC,MAAK,CAAA,IAAG,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,IAAG;gBAAC;YAAG,KAAI,CAAA,IAAG,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,IAAG;gBAAC;QAAE;QAAG,EAAE,IAAI,EAAC,aAAY;YAAC,OAAM,CAAC,GAAE,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,GAAC,EAAE,KAAG;YAAE,SAAQ,CAAC,GAAE,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC;QAAE;IAAE;IAAC,OAAO,MAAK;QAAC,OAAO,IAAI,EAAE;YAAC,OAAM,EAAE;QAAA;IAAE;IAAC,OAAO,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,CAAA,GAAA,iTAAA,CAAA,QAAC,AAAD,EAAE,EAAE,IAAI,EAAC,GAAE,GAAE;IAAE;AAAC;AAAC,MAAM,IAAE,IAAI,0TAAA,CAAA,aAAC,CAAC,IAAI,EAAE,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-event.js"], "sourcesContent": ["import a from\"react\";import{useLatestValue as n}from'./use-latest-value.js';let o=function(t){let e=n(t);return a.useCallback((...r)=>e.current(...r),[e])};export{o as useEvent};\n"], "names": [], "mappings": ";;;AAAA;AAAqB;;;AAAuD,IAAI,IAAE,SAAS,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,kUAAA,CAAA,iBAAC,AAAD,EAAE;IAAG,OAAO,oUAAA,CAAA,UAAC,CAAC,WAAW;yBAAC,CAAC,GAAG,IAAI,EAAE,OAAO,IAAI;wBAAG;QAAC;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/react-glue.js"], "sourcesContent": ["import{useSyncExternalStoreWithSelector as a}from\"use-sync-external-store/with-selector\";import{useEvent as t}from'./hooks/use-event.js';import{shallowEqual as o}from'./machine.js';function S(e,n,r=o){return a(t(i=>e.subscribe(s,i)),t(()=>e.state),t(()=>e.state),t(n),r)}function s(e){return e}export{S as useSlice};\n"], "names": [], "mappings": ";;;AAAA;AAAyF;AAAgD;;;;AAA4C,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,IAAE,0SAAA,CAAA,eAAC;IAAE,OAAO,CAAA,GAAA,0QAAA,CAAA,mCAAC,AAAD,EAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,EAAE,SAAS,CAAC,GAAE,KAAI,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,IAAI,EAAE,KAAK,GAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,IAAI,EAAE,KAAK,GAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,IAAG;AAAE;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-is-top-layer.js"], "sourcesContent": ["import{useCallback as n,useId as u}from\"react\";import{stackMachines as p}from'../machines/stack-machine.js';import{useSlice as f}from'../react-glue.js';import{useIsoMorphicEffect as a}from'./use-iso-morphic-effect.js';function I(o,s){let t=u(),r=p.get(s),[i,c]=f(r,n(e=>[r.selectors.isTop(e,t),r.selectors.inStack(e,t)],[r,t]));return a(()=>{if(o)return r.actions.push(t),()=>r.actions.pop(t)},[r,o,t]),o?c?i:!0:!1}export{I as useIsTopLayer};\n"], "names": [], "mappings": ";;;AAAA;AAA+C;AAA6D;AAA4C;;;;;AAAkE,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAC,AAAD,KAAI,IAAE,+TAAA,CAAA,gBAAC,CAAC,GAAG,CAAC,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG;YAAC,EAAE,SAAS,CAAC,KAAK,CAAC,GAAE;YAAG,EAAE,SAAS,CAAC,OAAO,CAAC,GAAE;SAAG,EAAC;QAAC;QAAE;KAAE;IAAG,OAAO,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,GAAE,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,IAAG,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE,GAAE,IAAE,IAAE,IAAE,CAAC,IAAE,CAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-escape.js"], "sourcesContent": ["import{Keys as u}from'../components/keyboard.js';import{useEventListener as i}from'./use-event-listener.js';import{useIsTopLayer as f}from'./use-is-top-layer.js';function a(o,r=typeof document!=\"undefined\"?document.defaultView:null,t){let n=f(o,\"escape\");i(r,\"keydown\",e=>{n&&(e.defaultPrevented||e.key===u.Escape&&t(e))})}export{a as useEscape};\n"], "names": [], "mappings": ";;;AAAA;AAAiD;AAA2D;;;;AAAsD,SAAS,EAAE,CAAC,EAAC,IAAE,OAAO,YAAU,cAAY,SAAS,WAAW,GAAC,IAAI,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qUAAA,CAAA,gBAAC,AAAD,EAAE,GAAE;IAAU,CAAA,GAAA,oUAAA,CAAA,mBAAC,AAAD,EAAE,GAAE,WAAU,CAAA;QAAI,KAAG,CAAC,EAAE,gBAAgB,IAAE,EAAE,GAAG,KAAG,yTAAA,CAAA,OAAC,CAAC,MAAM,IAAE,EAAE,EAAE;IAAC;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/owner.js"], "sourcesContent": ["import{env as t}from'./env.js';function o(n){var e,r;return t.isServer?null:n?\"ownerDocument\"in n?n.ownerDocument:\"current\"in n?(r=(e=n.current)==null?void 0:e.ownerDocument)!=null?r:document:null:document}export{o as getOwnerDocument};\n"], "names": [], "mappings": ";;;AAAA;;AAA+B,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE;IAAE,OAAO,+SAAA,CAAA,MAAC,CAAC,QAAQ,GAAC,OAAK,IAAE,mBAAkB,IAAE,EAAE,aAAa,GAAC,aAAY,IAAE,CAAC,IAAE,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,KAAG,OAAK,IAAE,WAAS,OAAK;AAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-inert-others.js"], "sourcesContent": ["import{disposables as M}from'../utils/disposables.js';import{getOwnerDocument as b}from'../utils/owner.js';import{useIsTopLayer as L}from'./use-is-top-layer.js';import{useIsoMorphicEffect as T}from'./use-iso-morphic-effect.js';let f=new Map,u=new Map;function h(t){var e;let r=(e=u.get(t))!=null?e:0;return u.set(t,r+1),r!==0?()=>m(t):(f.set(t,{\"aria-hidden\":t.getAttribute(\"aria-hidden\"),inert:t.inert}),t.setAttribute(\"aria-hidden\",\"true\"),t.inert=!0,()=>m(t))}function m(t){var i;let r=(i=u.get(t))!=null?i:1;if(r===1?u.delete(t):u.set(t,r-1),r!==1)return;let e=f.get(t);e&&(e[\"aria-hidden\"]===null?t.removeAttribute(\"aria-hidden\"):t.setAttribute(\"aria-hidden\",e[\"aria-hidden\"]),t.inert=e.inert,f.delete(t))}function y(t,{allowed:r,disallowed:e}={}){let i=L(t,\"inert-others\");T(()=>{var d,c;if(!i)return;let a=M();for(let n of(d=e==null?void 0:e())!=null?d:[])n&&a.add(h(n));let s=(c=r==null?void 0:r())!=null?c:[];for(let n of s){if(!n)continue;let l=b(n);if(!l)continue;let o=n.parentElement;for(;o&&o!==l.body;){for(let p of o.children)s.some(E=>p.contains(E))||a.add(h(p));o=o.parentElement}}return a.dispose},[i,r,e])}export{y as useInertOthers};\n"], "names": [], "mappings": ";;;AAAA;AAAsD;AAAqD;AAAsD;;;;;AAAkE,IAAI,IAAE,IAAI,KAAI,IAAE,IAAI;AAAI,SAAS,EAAE,CAAC;IAAE,IAAI;IAAE,IAAI,IAAE,CAAC,IAAE,EAAE,GAAG,CAAC,EAAE,KAAG,OAAK,IAAE;IAAE,OAAO,EAAE,GAAG,CAAC,GAAE,IAAE,IAAG,MAAI,IAAE,IAAI,EAAE,KAAG,CAAC,EAAE,GAAG,CAAC,GAAE;QAAC,eAAc,EAAE,YAAY,CAAC;QAAe,OAAM,EAAE,KAAK;IAAA,IAAG,EAAE,YAAY,CAAC,eAAc,SAAQ,EAAE,KAAK,GAAC,CAAC,GAAE,IAAI,EAAE,EAAE;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI;IAAE,IAAI,IAAE,CAAC,IAAE,EAAE,GAAG,CAAC,EAAE,KAAG,OAAK,IAAE;IAAE,IAAG,MAAI,IAAE,EAAE,MAAM,CAAC,KAAG,EAAE,GAAG,CAAC,GAAE,IAAE,IAAG,MAAI,GAAE;IAAO,IAAI,IAAE,EAAE,GAAG,CAAC;IAAG,KAAG,CAAC,CAAC,CAAC,cAAc,KAAG,OAAK,EAAE,eAAe,CAAC,iBAAe,EAAE,YAAY,CAAC,eAAc,CAAC,CAAC,cAAc,GAAE,EAAE,KAAK,GAAC,EAAE,KAAK,EAAC,EAAE,MAAM,CAAC,EAAE;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,EAAC,SAAQ,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qUAAA,CAAA,gBAAC,AAAD,EAAE,GAAE;IAAgB,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAI,GAAE;QAAE,IAAG,CAAC,GAAE;QAAO,IAAI,IAAE,CAAA,GAAA,uTAAA,CAAA,cAAC,AAAD;QAAI,KAAI,IAAI,KAAI,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,GAAG,KAAG,OAAK,IAAE,EAAE,CAAC,KAAG,EAAE,GAAG,CAAC,EAAE;QAAI,IAAI,IAAE,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,GAAG,KAAG,OAAK,IAAE,EAAE;QAAC,KAAI,IAAI,KAAK,EAAE;YAAC,IAAG,CAAC,GAAE;YAAS,IAAI,IAAE,CAAA,GAAA,iTAAA,CAAA,mBAAC,AAAD,EAAE;YAAG,IAAG,CAAC,GAAE;YAAS,IAAI,IAAE,EAAE,aAAa;YAAC,MAAK,KAAG,MAAI,EAAE,IAAI,EAAE;gBAAC,KAAI,IAAI,KAAK,EAAE,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAA,IAAG,EAAE,QAAQ,CAAC,OAAK,EAAE,GAAG,CAAC,EAAE;gBAAI,IAAE,EAAE,aAAa;YAAA;QAAC;QAAC,OAAO,EAAE,OAAO;IAAA,GAAE;QAAC;QAAE;QAAE;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-is-touch-device.js"], "sourcesContent": ["import{useState as i}from\"react\";import{useIsoMorphicEffect as s}from'./use-iso-morphic-effect.js';function f(){var t;let[e]=i(()=>typeof window!=\"undefined\"&&typeof window.matchMedia==\"function\"?window.matchMedia(\"(pointer: coarse)\"):null),[o,c]=i((t=e==null?void 0:e.matches)!=null?t:!1);return s(()=>{if(!e)return;function n(r){c(r.matches)}return e.addEventListener(\"change\",n),()=>e.removeEventListener(\"change\",n)},[e]),o}export{f as useIsTouchDevice};\n"], "names": [], "mappings": ";;;AAAA;AAAiC;;;AAAkE,SAAS;IAAI,IAAI;IAAE,IAAG,CAAC,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,IAAI,OAAO,UAAQ,eAAa,OAAO,OAAO,UAAU,IAAE,aAAW,OAAO,UAAU,CAAC,uBAAqB,OAAM,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,KAAG,OAAK,IAAE,CAAC;IAAG,OAAO,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE;QAAO,SAAS,EAAE,CAAC;YAAE,EAAE,EAAE,OAAO;QAAC;QAAC,OAAO,EAAE,gBAAgB,CAAC,UAAS,IAAG,IAAI,EAAE,mBAAmB,CAAC,UAAS;IAAE,GAAE;QAAC;KAAE,GAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 630, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/dom.js"], "sourcesContent": ["function o(e){return typeof e!=\"object\"||e===null?!1:\"nodeType\"in e}function t(e){return o(e)&&\"tagName\"in e}function n(e){return t(e)&&\"accessKey\"in e}function i(e){return t(e)&&\"tabIndex\"in e}function r(e){return t(e)&&\"style\"in e}function u(e){return n(e)&&e.nodeName===\"IFRAME\"}function l(e){return n(e)&&e.nodeName===\"INPUT\"}function s(e){return n(e)&&e.nodeName===\"TEXTAREA\"}function m(e){return n(e)&&e.nodeName===\"LABEL\"}function a(e){return n(e)&&e.nodeName===\"FIELDSET\"}function E(e){return n(e)&&e.nodeName===\"LEGEND\"}function L(e){return t(e)?e.matches('a[href],audio[controls],button,details,embed,iframe,img[usemap],input:not([type=\"hidden\"]),label,select,textarea,video[controls]'):!1}export{r as hasInlineStyle,t as isElement,n as isHTMLElement,a as isHTMLFieldSetElement,u as isHTMLIframeElement,l as isHTMLInputElement,m as isHTMLLabelElement,E as isHTMLLegendElement,s as isHTMLTextAreaElement,i as isHTMLorSVGElement,L as isInteractiveElement,o as isNode};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,SAAS,EAAE,CAAC;IAAE,OAAO,OAAO,KAAG,YAAU,MAAI,OAAK,CAAC,IAAE,cAAa;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,aAAY;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,eAAc;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,cAAa;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,WAAU;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,EAAE,QAAQ,KAAG;AAAQ;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,EAAE,QAAQ,KAAG;AAAO;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,EAAE,QAAQ,KAAG;AAAU;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,EAAE,QAAQ,KAAG;AAAO;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,EAAE,QAAQ,KAAG;AAAU;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,MAAI,EAAE,QAAQ,KAAG;AAAQ;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,KAAG,EAAE,OAAO,CAAC,sIAAoI,CAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-on-disappear.js"], "sourcesContent": ["import{useEffect as l}from\"react\";import{disposables as u}from'../utils/disposables.js';import*as c from'../utils/dom.js';import{useLatestValue as d}from'./use-latest-value.js';function p(s,n,o){let i=d(t=>{let e=t.getBoundingClientRect();e.x===0&&e.y===0&&e.width===0&&e.height===0&&o()});l(()=>{if(!s)return;let t=n===null?null:c.isHTMLElement(n)?n:n.current;if(!t)return;let e=u();if(typeof ResizeObserver!=\"undefined\"){let r=new ResizeObserver(()=>i.current(t));r.observe(t),e.add(()=>r.disconnect())}if(typeof IntersectionObserver!=\"undefined\"){let r=new IntersectionObserver(()=>i.current(t));r.observe(t),e.add(()=>r.disconnect())}return()=>e.dispose()},[n,i,s])}export{p as useOnDisappear};\n"], "names": [], "mappings": ";;;AAAA;AAAkC;AAAsD;AAAkC;;;;;AAAuD,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,kUAAA,CAAA,iBAAC,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,EAAE,qBAAqB;QAAG,EAAE,CAAC,KAAG,KAAG,EAAE,CAAC,KAAG,KAAG,EAAE,KAAK,KAAG,KAAG,EAAE,MAAM,KAAG,KAAG;IAAG;IAAG,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE;QAAO,IAAI,IAAE,MAAI,OAAK,OAAK,CAAA,GAAA,+SAAA,CAAA,gBAAe,AAAD,EAAE,KAAG,IAAE,EAAE,OAAO;QAAC,IAAG,CAAC,GAAE;QAAO,IAAI,IAAE,CAAA,GAAA,uTAAA,CAAA,cAAC,AAAD;QAAI,IAAG,OAAO,kBAAgB,aAAY;YAAC,IAAI,IAAE,IAAI,eAAe,IAAI,EAAE,OAAO,CAAC;YAAI,EAAE,OAAO,CAAC,IAAG,EAAE,GAAG,CAAC,IAAI,EAAE,UAAU;QAAG;QAAC,IAAG,OAAO,wBAAsB,aAAY;YAAC,IAAI,IAAE,IAAI,qBAAqB,IAAI,EAAE,OAAO,CAAC;YAAI,EAAE,OAAO,CAAC,IAAG,EAAE,GAAG,CAAC,IAAI,EAAE,UAAU;QAAG;QAAC,OAAM,IAAI,EAAE,OAAO;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/focus-management.js"], "sourcesContent": ["import{disposables as N}from'./disposables.js';import*as p from'./dom.js';import{match as L}from'./match.js';import{getOwnerDocument as E}from'./owner.js';let f=[\"[contentEditable=true]\",\"[tabindex]\",\"a[href]\",\"area[href]\",\"button:not([disabled])\",\"iframe\",\"input:not([disabled])\",\"select:not([disabled])\",\"textarea:not([disabled])\"].map(e=>`${e}:not([tabindex='-1'])`).join(\",\"),F=[\"[data-autofocus]\"].map(e=>`${e}:not([tabindex='-1'])`).join(\",\");var T=(n=>(n[n.First=1]=\"First\",n[n.Previous=2]=\"Previous\",n[n.Next=4]=\"Next\",n[n.Last=8]=\"Last\",n[n.WrapAround=16]=\"WrapAround\",n[n.NoScroll=32]=\"NoScroll\",n[n.AutoFocus=64]=\"AutoFocus\",n))(T||{}),y=(o=>(o[o.Error=0]=\"Error\",o[o.Overflow=1]=\"Overflow\",o[o.Success=2]=\"Success\",o[o.Underflow=3]=\"Underflow\",o))(y||{}),S=(t=>(t[t.Previous=-1]=\"Previous\",t[t.Next=1]=\"Next\",t))(S||{});function b(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(f)).sort((r,t)=>Math.sign((r.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}function O(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(F)).sort((r,t)=>Math.sign((r.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var h=(t=>(t[t.Strict=0]=\"Strict\",t[t.Loose=1]=\"Loose\",t))(h||{});function A(e,r=0){var t;return e===((t=E(e))==null?void 0:t.body)?!1:L(r,{[0](){return e.matches(f)},[1](){let l=e;for(;l!==null;){if(l.matches(f))return!0;l=l.parentElement}return!1}})}function V(e){let r=E(e);N().nextFrame(()=>{r&&p.isHTMLorSVGElement(r.activeElement)&&!A(r.activeElement,0)&&I(e)})}var H=(t=>(t[t.Keyboard=0]=\"Keyboard\",t[t.Mouse=1]=\"Mouse\",t))(H||{});typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"keydown\",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible=\"\")},!0),document.addEventListener(\"click\",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible=\"\")},!0));function I(e){e==null||e.focus({preventScroll:!0})}let w=[\"textarea\",\"input\"].join(\",\");function _(e){var r,t;return(t=(r=e==null?void 0:e.matches)==null?void 0:r.call(e,w))!=null?t:!1}function P(e,r=t=>t){return e.slice().sort((t,l)=>{let o=r(t),c=r(l);if(o===null||c===null)return 0;let u=o.compareDocumentPosition(c);return u&Node.DOCUMENT_POSITION_FOLLOWING?-1:u&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function j(e,r){return g(b(),r,{relativeTo:e})}function g(e,r,{sorted:t=!0,relativeTo:l=null,skipElements:o=[]}={}){let c=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,u=Array.isArray(e)?t?P(e):e:r&64?O(e):b(e);o.length>0&&u.length>1&&(u=u.filter(s=>!o.some(a=>a!=null&&\"current\"in a?(a==null?void 0:a.current)===s:a===s))),l=l!=null?l:c.activeElement;let n=(()=>{if(r&5)return 1;if(r&10)return-1;throw new Error(\"Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last\")})(),x=(()=>{if(r&1)return 0;if(r&2)return Math.max(0,u.indexOf(l))-1;if(r&4)return Math.max(0,u.indexOf(l))+1;if(r&8)return u.length-1;throw new Error(\"Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last\")})(),M=r&32?{preventScroll:!0}:{},m=0,d=u.length,i;do{if(m>=d||m+d<=0)return 0;let s=x+m;if(r&16)s=(s+d)%d;else{if(s<0)return 3;if(s>=d)return 1}i=u[s],i==null||i.focus(M),m+=n}while(i!==c.activeElement);return r&6&&_(i)&&i.select(),2}export{T as Focus,y as FocusResult,h as FocusableMode,I as focusElement,j as focusFrom,g as focusIn,f as focusableSelector,O as getAutoFocusableElements,b as getFocusableElements,A as isFocusableElement,V as restoreFocusIfNecessary,P as sortByDomNode};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAA+C;AAA2B;AAAmC;;;;;AAA8C,IAAI,IAAE;IAAC;IAAyB;IAAa;IAAU;IAAa;IAAyB;IAAS;IAAwB;IAAyB;CAA2B,CAAC,GAAG,CAAC,CAAA,IAAG,GAAG,EAAE,qBAAqB,CAAC,EAAE,IAAI,CAAC,MAAK,IAAE;IAAC;CAAmB,CAAC,GAAG,CAAC,CAAA,IAAG,GAAG,EAAE,qBAAqB,CAAC,EAAE,IAAI,CAAC;AAAK,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,QAAQ,GAAC,EAAE,GAAC,YAAW,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,UAAU,GAAC,GAAG,GAAC,cAAa,CAAC,CAAC,EAAE,QAAQ,GAAC,GAAG,GAAC,YAAW,CAAC,CAAC,EAAE,SAAS,GAAC,GAAG,GAAC,aAAY,CAAC,CAAC,EAAE,KAAG,CAAC,IAAG,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,QAAQ,GAAC,EAAE,GAAC,YAAW,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,SAAS,GAAC,EAAE,GAAC,aAAY,CAAC,CAAC,EAAE,KAAG,CAAC,IAAG,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAC,CAAC,EAAE,GAAC,YAAW,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,IAAE,SAAS,IAAI;IAAE,OAAO,KAAG,OAAK,EAAE,GAAC,MAAM,IAAI,CAAC,EAAE,gBAAgB,CAAC,IAAI,IAAI,CAAC,CAAC,GAAE,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAE,OAAO,gBAAgB,IAAE,CAAC,EAAE,QAAQ,IAAE,OAAO,gBAAgB;AAAG;AAAC,SAAS,EAAE,IAAE,SAAS,IAAI;IAAE,OAAO,KAAG,OAAK,EAAE,GAAC,MAAM,IAAI,CAAC,EAAE,gBAAgB,CAAC,IAAI,IAAI,CAAC,CAAC,GAAE,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAE,OAAO,gBAAgB,IAAE,CAAC,EAAE,QAAQ,IAAE,OAAO,gBAAgB;AAAG;AAAC,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,CAAC,EAAC,IAAE,CAAC;IAAE,IAAI;IAAE,OAAO,MAAI,CAAC,CAAC,IAAE,CAAA,GAAA,iTAAA,CAAA,mBAAC,AAAD,EAAE,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,IAAE,CAAC,IAAE,CAAA,GAAA,iTAAA,CAAA,QAAC,AAAD,EAAE,GAAE;QAAC,CAAC,EAAE;YAAG,OAAO,EAAE,OAAO,CAAC;QAAE;QAAE,CAAC,EAAE;YAAG,IAAI,IAAE;YAAE,MAAK,MAAI,MAAM;gBAAC,IAAG,EAAE,OAAO,CAAC,IAAG,OAAM,CAAC;gBAAE,IAAE,EAAE,aAAa;YAAA;YAAC,OAAM,CAAC;QAAC;IAAC;AAAE;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,iTAAA,CAAA,mBAAC,AAAD,EAAE;IAAG,CAAA,GAAA,uTAAA,CAAA,cAAC,AAAD,IAAI,SAAS,CAAC;QAAK,KAAG,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,EAAE,aAAa,KAAG,CAAC,EAAE,EAAE,aAAa,EAAC,MAAI,EAAE;IAAE;AAAE;AAAC,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAC,EAAE,GAAC,YAAW,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,OAAO,UAAQ,eAAa,OAAO,YAAU,eAAa,CAAC,SAAS,gBAAgB,CAAC,WAAU,CAAA;IAAI,EAAE,OAAO,IAAE,EAAE,MAAM,IAAE,EAAE,OAAO,IAAE,CAAC,SAAS,eAAe,CAAC,OAAO,CAAC,sBAAsB,GAAC,EAAE;AAAC,GAAE,CAAC,IAAG,SAAS,gBAAgB,CAAC,SAAQ,CAAA;IAAI,EAAE,MAAM,KAAG,IAAE,OAAO,SAAS,eAAe,CAAC,OAAO,CAAC,sBAAsB,GAAC,EAAE,MAAM,KAAG,KAAG,CAAC,SAAS,eAAe,CAAC,OAAO,CAAC,sBAAsB,GAAC,EAAE;AAAC,GAAE,CAAC,EAAE;AAAE,SAAS,EAAE,CAAC;IAAE,KAAG,QAAM,EAAE,KAAK,CAAC;QAAC,eAAc,CAAC;IAAC;AAAE;AAAC,IAAI,IAAE;IAAC;IAAW;CAAQ,CAAC,IAAI,CAAC;AAAK,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE;IAAE,OAAM,CAAC,IAAE,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,GAAE,EAAE,KAAG,OAAK,IAAE,CAAC;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,IAAE,CAAA,IAAG,CAAC;IAAE,OAAO,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC,GAAE;QAAK,IAAI,IAAE,EAAE,IAAG,IAAE,EAAE;QAAG,IAAG,MAAI,QAAM,MAAI,MAAK,OAAO;QAAE,IAAI,IAAE,EAAE,uBAAuB,CAAC;QAAG,OAAO,IAAE,KAAK,2BAA2B,GAAC,CAAC,IAAE,IAAE,KAAK,2BAA2B,GAAC,IAAE;IAAC;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,OAAO,EAAE,KAAI,GAAE;QAAC,YAAW;IAAC;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,EAAC,QAAO,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,IAAI,EAAC,cAAa,IAAE,EAAE,EAAC,GAAC,CAAC,CAAC;IAAE,IAAI,IAAE,MAAM,OAAO,CAAC,KAAG,EAAE,MAAM,GAAC,IAAE,CAAC,CAAC,EAAE,CAAC,aAAa,GAAC,WAAS,EAAE,aAAa,EAAC,IAAE,MAAM,OAAO,CAAC,KAAG,IAAE,EAAE,KAAG,IAAE,IAAE,KAAG,EAAE,KAAG,EAAE;IAAG,EAAE,MAAM,GAAC,KAAG,EAAE,MAAM,GAAC,KAAG,CAAC,IAAE,EAAE,MAAM,CAAC,CAAA,IAAG,CAAC,EAAE,IAAI,CAAC,CAAA,IAAG,KAAG,QAAM,aAAY,IAAE,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,MAAI,IAAE,MAAI,GAAG,GAAE,IAAE,KAAG,OAAK,IAAE,EAAE,aAAa;IAAC,IAAI,IAAE,CAAC;QAAK,IAAG,IAAE,GAAE,OAAO;QAAE,IAAG,IAAE,IAAG,OAAM,CAAC;QAAE,MAAM,IAAI,MAAM;IAAgE,CAAC,KAAI,IAAE,CAAC;QAAK,IAAG,IAAE,GAAE,OAAO;QAAE,IAAG,IAAE,GAAE,OAAO,KAAK,GAAG,CAAC,GAAE,EAAE,OAAO,CAAC,MAAI;QAAE,IAAG,IAAE,GAAE,OAAO,KAAK,GAAG,CAAC,GAAE,EAAE,OAAO,CAAC,MAAI;QAAE,IAAG,IAAE,GAAE,OAAO,EAAE,MAAM,GAAC;QAAE,MAAM,IAAI,MAAM;IAAgE,CAAC,KAAI,IAAE,IAAE,KAAG;QAAC,eAAc,CAAC;IAAC,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC;IAAE,GAAE;QAAC,IAAG,KAAG,KAAG,IAAE,KAAG,GAAE,OAAO;QAAE,IAAI,IAAE,IAAE;QAAE,IAAG,IAAE,IAAG,IAAE,CAAC,IAAE,CAAC,IAAE;aAAM;YAAC,IAAG,IAAE,GAAE,OAAO;YAAE,IAAG,KAAG,GAAE,OAAO;QAAC;QAAC,IAAE,CAAC,CAAC,EAAE,EAAC,KAAG,QAAM,EAAE,KAAK,CAAC,IAAG,KAAG;IAAC,QAAO,MAAI,EAAE,aAAa,CAAE;IAAA,OAAO,IAAE,KAAG,EAAE,MAAI,EAAE,MAAM,IAAG;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/platform.js"], "sourcesContent": ["function t(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function i(){return/Android/gi.test(window.navigator.userAgent)}function n(){return t()||i()}export{i as isAndroid,t as isIOS,n as isMobile};\n"], "names": [], "mappings": ";;;;;AAAA,SAAS;IAAI,OAAM,WAAW,IAAI,CAAC,OAAO,SAAS,CAAC,QAAQ,KAAG,QAAQ,IAAI,CAAC,OAAO,SAAS,CAAC,QAAQ,KAAG,OAAO,SAAS,CAAC,cAAc,GAAC;AAAC;AAAC,SAAS;IAAI,OAAM,YAAY,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS;AAAC;AAAC,SAAS;IAAI,OAAO,OAAK;AAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 883, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-document-event.js"], "sourcesContent": ["import{useEffect as c}from\"react\";import{useLatestValue as a}from'./use-latest-value.js';function i(t,e,o,n){let u=a(o);c(()=>{if(!t)return;function r(m){u.current(m)}return document.addEventListener(e,r,n),()=>document.removeEventListener(e,r,n)},[t,e,n])}export{i as useDocumentEvent};\n"], "names": [], "mappings": ";;;AAAA;AAAkC;;;AAAuD,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,kUAAA,CAAA,iBAAC,AAAD,EAAE;IAAG,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE;QAAO,SAAS,EAAE,CAAC;YAAE,EAAE,OAAO,CAAC;QAAE;QAAC,OAAO,SAAS,gBAAgB,CAAC,GAAE,GAAE,IAAG,IAAI,SAAS,mBAAmB,CAAC,GAAE,GAAE;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-window-event.js"], "sourcesContent": ["import{useEffect as a}from\"react\";import{useLatestValue as f}from'./use-latest-value.js';function s(t,e,o,n){let i=f(o);a(()=>{if(!t)return;function r(d){i.current(d)}return window.addEventListener(e,r,n),()=>window.removeEventListener(e,r,n)},[t,e,n])}export{s as useWindowEvent};\n"], "names": [], "mappings": ";;;AAAA;AAAkC;;;AAAuD,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,kUAAA,CAAA,iBAAC,AAAD,EAAE;IAAG,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE;QAAO,SAAS,EAAE,CAAC;YAAE,EAAE,OAAO,CAAC;QAAE;QAAC,OAAO,OAAO,gBAAgB,CAAC,GAAE,GAAE,IAAG,IAAI,OAAO,mBAAmB,CAAC,GAAE,GAAE;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 939, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-outside-click.js"], "sourcesContent": ["import{useCallback as T,useRef as E}from\"react\";import*as d from'../utils/dom.js';import{FocusableMode as g,isFocusableElement as y}from'../utils/focus-management.js';import{isMobile as p}from'../utils/platform.js';import{useDocumentEvent as a}from'./use-document-event.js';import{useLatestValue as L}from'./use-latest-value.js';import{useWindowEvent as x}from'./use-window-event.js';const C=30;function k(o,f,h){let m=L(h),s=T(function(e,c){if(e.defaultPrevented)return;let r=c(e);if(r===null||!r.getRootNode().contains(r)||!r.isConnected)return;let M=function u(n){return typeof n==\"function\"?u(n()):Array.isArray(n)||n instanceof Set?n:[n]}(f);for(let u of M)if(u!==null&&(u.contains(r)||e.composed&&e.composedPath().includes(u)))return;return!y(r,g.Loose)&&r.tabIndex!==-1&&e.preventDefault(),m.current(e,r)},[m,f]),i=E(null);a(o,\"pointerdown\",t=>{var e,c;p()||(i.current=((c=(e=t.composedPath)==null?void 0:e.call(t))==null?void 0:c[0])||t.target)},!0),a(o,\"pointerup\",t=>{if(p()||!i.current)return;let e=i.current;return i.current=null,s(t,()=>e)},!0);let l=E({x:0,y:0});a(o,\"touchstart\",t=>{l.current.x=t.touches[0].clientX,l.current.y=t.touches[0].clientY},!0),a(o,\"touchend\",t=>{let e={x:t.changedTouches[0].clientX,y:t.changedTouches[0].clientY};if(!(Math.abs(e.x-l.current.x)>=C||Math.abs(e.y-l.current.y)>=C))return s(t,()=>d.isHTMLorSVGElement(t.target)?t.target:null)},!0),x(o,\"blur\",t=>s(t,()=>d.isHTMLIframeElement(window.document.activeElement)?window.document.activeElement:null),!0)}export{k as useOutsideClick};\n"], "names": [], "mappings": ";;;AAAA;AAAgD;AAAkC;AAAqF;AAAgD;AAA2D;AAAuD;;;;;;;;AAAuD,MAAM,IAAE;AAAG,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,kUAAA,CAAA,iBAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,SAAS,CAAC,EAAC,CAAC;QAAE,IAAG,EAAE,gBAAgB,EAAC;QAAO,IAAI,IAAE,EAAE;QAAG,IAAG,MAAI,QAAM,CAAC,EAAE,WAAW,GAAG,QAAQ,CAAC,MAAI,CAAC,EAAE,WAAW,EAAC;QAAO,IAAI,IAAE,SAAS,EAAE,CAAC;YAAE,OAAO,OAAO,KAAG,aAAW,EAAE,OAAK,MAAM,OAAO,CAAC,MAAI,aAAa,MAAI,IAAE;gBAAC;aAAE;QAAA,EAAE;QAAG,KAAI,IAAI,KAAK,EAAE,IAAG,MAAI,QAAM,CAAC,EAAE,QAAQ,CAAC,MAAI,EAAE,QAAQ,IAAE,EAAE,YAAY,GAAG,QAAQ,CAAC,EAAE,GAAE;QAAO,OAAM,CAAC,CAAA,GAAA,+TAAA,CAAA,qBAAC,AAAD,EAAE,GAAE,+TAAA,CAAA,gBAAC,CAAC,KAAK,KAAG,EAAE,QAAQ,KAAG,CAAC,KAAG,EAAE,cAAc,IAAG,EAAE,OAAO,CAAC,GAAE;IAAE,GAAE;QAAC;QAAE;KAAE,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE;IAAM,CAAA,GAAA,oUAAA,CAAA,mBAAC,AAAD,EAAE,GAAE,eAAc,CAAA;QAAI,IAAI,GAAE;QAAE,CAAA,GAAA,oTAAA,CAAA,WAAC,AAAD,OAAK,CAAC,EAAE,OAAO,GAAC,CAAC,CAAC,IAAE,CAAC,IAAE,EAAE,YAAY,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,EAAE,KAAG,OAAK,KAAK,IAAE,CAAC,CAAC,EAAE,KAAG,EAAE,MAAM;IAAC,GAAE,CAAC,IAAG,CAAA,GAAA,oUAAA,CAAA,mBAAC,AAAD,EAAE,GAAE,aAAY,CAAA;QAAI,IAAG,CAAA,GAAA,oTAAA,CAAA,WAAC,AAAD,OAAK,CAAC,EAAE,OAAO,EAAC;QAAO,IAAI,IAAE,EAAE,OAAO;QAAC,OAAO,EAAE,OAAO,GAAC,MAAK,EAAE,GAAE,IAAI;IAAE,GAAE,CAAC;IAAG,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE;QAAC,GAAE;QAAE,GAAE;IAAC;IAAG,CAAA,GAAA,oUAAA,CAAA,mBAAC,AAAD,EAAE,GAAE,cAAa,CAAA;QAAI,EAAE,OAAO,CAAC,CAAC,GAAC,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAC,EAAE,OAAO,CAAC,CAAC,GAAC,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO;IAAA,GAAE,CAAC,IAAG,CAAA,GAAA,oUAAA,CAAA,mBAAC,AAAD,EAAE,GAAE,YAAW,CAAA;QAAI,IAAI,IAAE;YAAC,GAAE,EAAE,cAAc,CAAC,EAAE,CAAC,OAAO;YAAC,GAAE,EAAE,cAAc,CAAC,EAAE,CAAC,OAAO;QAAA;QAAE,IAAG,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,GAAC,EAAE,OAAO,CAAC,CAAC,KAAG,KAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAC,EAAE,OAAO,CAAC,CAAC,KAAG,CAAC,GAAE,OAAO,EAAE,GAAE,IAAI,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,EAAE,MAAM,IAAE,EAAE,MAAM,GAAC;IAAK,GAAE,CAAC,IAAG,CAAA,GAAA,kUAAA,CAAA,iBAAC,AAAD,EAAE,GAAE,QAAO,CAAA,IAAG,EAAE,GAAE,IAAI,CAAA,GAAA,+SAAA,CAAA,sBAAqB,AAAD,EAAE,OAAO,QAAQ,CAAC,aAAa,IAAE,OAAO,QAAQ,CAAC,aAAa,GAAC,OAAM,CAAC;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-owner.js"], "sourcesContent": ["import{useMemo as t}from\"react\";import{getOwnerDocument as o}from'../utils/owner.js';function n(...e){return t(()=>o(...e),[...e])}export{n as useOwnerDocument};\n"], "names": [], "mappings": ";;;AAAA;AAAgC;;;AAAqD,SAAS,EAAE,GAAG,CAAC;IAAE,OAAO,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAA,GAAA,iTAAA,CAAA,mBAAC,AAAD,KAAK,IAAG;WAAI;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/class-names.js"], "sourcesContent": ["function t(...r){return Array.from(new Set(r.flatMap(n=>typeof n==\"string\"?n.split(\" \"):[]))).filter(Boolean).join(\" \")}export{t as classNames};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,GAAG,CAAC;IAAE,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,EAAE,OAAO,CAAC,CAAA,IAAG,OAAO,KAAG,WAAS,EAAE,KAAK,CAAC,OAAK,EAAE,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC;AAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/render.js"], "sourcesContent": ["import E,{Fragment as b,cloneElement as j,createElement as v,forwardRef as S,isValidElement as w,use<PERSON><PERSON>back as x,useRef as k}from\"react\";import{classNames as N}from'./class-names.js';import{match as M}from'./match.js';var O=(a=>(a[a.None=0]=\"None\",a[a.RenderStrategy=1]=\"RenderStrategy\",a[a.Static=2]=\"Static\",a))(O||{}),A=(e=>(e[e.Unmount=0]=\"Unmount\",e[e.Hidden=1]=\"Hidden\",e))(A||{});function L(){let n=U();return x(r=>C({mergeRefs:n,...r}),[n])}function C({ourProps:n,theirProps:r,slot:e,defaultTag:a,features:s,visible:t=!0,name:l,mergeRefs:i}){i=i!=null?i:$;let o=P(r,n);if(t)return F(o,e,a,l,i);let y=s!=null?s:0;if(y&2){let{static:f=!1,...u}=o;if(f)return F(u,e,a,l,i)}if(y&1){let{unmount:f=!0,...u}=o;return M(f?0:1,{[0](){return null},[1](){return F({...u,hidden:!0,style:{display:\"none\"}},e,a,l,i)}})}return F(o,e,a,l,i)}function F(n,r={},e,a,s){let{as:t=e,children:l,refName:i=\"ref\",...o}=h(n,[\"unmount\",\"static\"]),y=n.ref!==void 0?{[i]:n.ref}:{},f=typeof l==\"function\"?l(r):l;\"className\"in o&&o.className&&typeof o.className==\"function\"&&(o.className=o.className(r)),o[\"aria-labelledby\"]&&o[\"aria-labelledby\"]===o.id&&(o[\"aria-labelledby\"]=void 0);let u={};if(r){let d=!1,p=[];for(let[c,T]of Object.entries(r))typeof T==\"boolean\"&&(d=!0),T===!0&&p.push(c.replace(/([A-Z])/g,g=>`-${g.toLowerCase()}`));if(d){u[\"data-headlessui-state\"]=p.join(\" \");for(let c of p)u[`data-${c}`]=\"\"}}if(t===b&&(Object.keys(m(o)).length>0||Object.keys(m(u)).length>0))if(!w(f)||Array.isArray(f)&&f.length>1){if(Object.keys(m(o)).length>0)throw new Error(['Passing props on \"Fragment\"!',\"\",`The current component <${a} /> is rendering a \"Fragment\".`,\"However we need to passthrough the following props:\",Object.keys(m(o)).concat(Object.keys(m(u))).map(d=>`  - ${d}`).join(`\n`),\"\",\"You can apply a few solutions:\",['Add an `as=\"...\"` prop, to ensure that we render an actual element instead of a \"Fragment\".',\"Render a single element as the child so that we can forward the props onto that element.\"].map(d=>`  - ${d}`).join(`\n`)].join(`\n`))}else{let d=f.props,p=d==null?void 0:d.className,c=typeof p==\"function\"?(...R)=>N(p(...R),o.className):N(p,o.className),T=c?{className:c}:{},g=P(f.props,m(h(o,[\"ref\"])));for(let R in u)R in g&&delete u[R];return j(f,Object.assign({},g,u,y,{ref:s(H(f),y.ref)},T))}return v(t,Object.assign({},h(o,[\"ref\"]),t!==b&&y,t!==b&&u),f)}function U(){let n=k([]),r=x(e=>{for(let a of n.current)a!=null&&(typeof a==\"function\"?a(e):a.current=e)},[]);return(...e)=>{if(!e.every(a=>a==null))return n.current=e,r}}function $(...n){return n.every(r=>r==null)?void 0:r=>{for(let e of n)e!=null&&(typeof e==\"function\"?e(r):e.current=r)}}function P(...n){var a;if(n.length===0)return{};if(n.length===1)return n[0];let r={},e={};for(let s of n)for(let t in s)t.startsWith(\"on\")&&typeof s[t]==\"function\"?((a=e[t])!=null||(e[t]=[]),e[t].push(s[t])):r[t]=s[t];if(r.disabled||r[\"aria-disabled\"])for(let s in e)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(s)&&(e[s]=[t=>{var l;return(l=t==null?void 0:t.preventDefault)==null?void 0:l.call(t)}]);for(let s in e)Object.assign(r,{[s](t,...l){let i=e[s];for(let o of i){if((t instanceof Event||(t==null?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;o(t,...l)}}});return r}function _(...n){var a;if(n.length===0)return{};if(n.length===1)return n[0];let r={},e={};for(let s of n)for(let t in s)t.startsWith(\"on\")&&typeof s[t]==\"function\"?((a=e[t])!=null||(e[t]=[]),e[t].push(s[t])):r[t]=s[t];for(let s in e)Object.assign(r,{[s](...t){let l=e[s];for(let i of l)i==null||i(...t)}});return r}function K(n){var r;return Object.assign(S(n),{displayName:(r=n.displayName)!=null?r:n.name})}function m(n){let r=Object.assign({},n);for(let e in r)r[e]===void 0&&delete r[e];return r}function h(n,r=[]){let e=Object.assign({},n);for(let a of r)a in e&&delete e[a];return e}function H(n){return E.version.split(\".\")[0]>=\"19\"?n.props.ref:n.ref}export{O as RenderFeatures,A as RenderStrategy,m as compact,K as forwardRefWithAs,_ as mergeProps,L as useRender};\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAA0I;AAA8C;;;;AAAmC,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,cAAc,GAAC,EAAE,GAAC,kBAAiB,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAG,CAAC,IAAG,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS;IAAI,IAAI,IAAE;IAAI,OAAO,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE;YAAC,WAAU;YAAE,GAAG,CAAC;QAAA,IAAG;QAAC;KAAE;AAAC;AAAC,SAAS,EAAE,EAAC,UAAS,CAAC,EAAC,YAAW,CAAC,EAAC,MAAK,CAAC,EAAC,YAAW,CAAC,EAAC,UAAS,CAAC,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,MAAK,CAAC,EAAC,WAAU,CAAC,EAAC;IAAE,IAAE,KAAG,OAAK,IAAE;IAAE,IAAI,IAAE,EAAE,GAAE;IAAG,IAAG,GAAE,OAAO,EAAE,GAAE,GAAE,GAAE,GAAE;IAAG,IAAI,IAAE,KAAG,OAAK,IAAE;IAAE,IAAG,IAAE,GAAE;QAAC,IAAG,EAAC,QAAO,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC;QAAE,IAAG,GAAE,OAAO,EAAE,GAAE,GAAE,GAAE,GAAE;IAAE;IAAC,IAAG,IAAE,GAAE;QAAC,IAAG,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC;QAAE,OAAO,CAAA,GAAA,iTAAA,CAAA,QAAC,AAAD,EAAE,IAAE,IAAE,GAAE;YAAC,CAAC,EAAE;gBAAG,OAAO;YAAI;YAAE,CAAC,EAAE;gBAAG,OAAO,EAAE;oBAAC,GAAG,CAAC;oBAAC,QAAO,CAAC;oBAAE,OAAM;wBAAC,SAAQ;oBAAM;gBAAC,GAAE,GAAE,GAAE,GAAE;YAAE;QAAC;IAAE;IAAC,OAAO,EAAE,GAAE,GAAE,GAAE,GAAE;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,IAAE,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,IAAG,IAAE,CAAC,EAAC,UAAS,CAAC,EAAC,SAAQ,IAAE,KAAK,EAAC,GAAG,GAAE,GAAC,EAAE,GAAE;QAAC;QAAU;KAAS,GAAE,IAAE,EAAE,GAAG,KAAG,KAAK,IAAE;QAAC,CAAC,EAAE,EAAC,EAAE,GAAG;IAAA,IAAE,CAAC,GAAE,IAAE,OAAO,KAAG,aAAW,EAAE,KAAG;IAAE,eAAc,KAAG,EAAE,SAAS,IAAE,OAAO,EAAE,SAAS,IAAE,cAAY,CAAC,EAAE,SAAS,GAAC,EAAE,SAAS,CAAC,EAAE,GAAE,CAAC,CAAC,kBAAkB,IAAE,CAAC,CAAC,kBAAkB,KAAG,EAAE,EAAE,IAAE,CAAC,CAAC,CAAC,kBAAkB,GAAC,KAAK,CAAC;IAAE,IAAI,IAAE,CAAC;IAAE,IAAG,GAAE;QAAC,IAAI,IAAE,CAAC,GAAE,IAAE,EAAE;QAAC,KAAI,IAAG,CAAC,GAAE,EAAE,IAAG,OAAO,OAAO,CAAC,GAAG,OAAO,KAAG,aAAW,CAAC,IAAE,CAAC,CAAC,GAAE,MAAI,CAAC,KAAG,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,YAAW,CAAA,IAAG,CAAC,CAAC,EAAE,EAAE,WAAW,IAAI;QAAG,IAAG,GAAE;YAAC,CAAC,CAAC,wBAAwB,GAAC,EAAE,IAAI,CAAC;YAAK,KAAI,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,GAAC;QAAE;IAAC;IAAC,IAAG,MAAI,oUAAA,CAAA,WAAC,IAAE,CAAC,OAAO,IAAI,CAAC,EAAE,IAAI,MAAM,GAAC,KAAG,OAAO,IAAI,CAAC,EAAE,IAAI,MAAM,GAAC,CAAC,GAAE,IAAG,CAAC,CAAA,GAAA,oUAAA,CAAA,iBAAC,AAAD,EAAE,MAAI,MAAM,OAAO,CAAC,MAAI,EAAE,MAAM,GAAC,GAAE;QAAC,IAAG,OAAO,IAAI,CAAC,EAAE,IAAI,MAAM,GAAC,GAAE,MAAM,IAAI,MAAM;YAAC;YAA+B;YAAG,CAAC,uBAAuB,EAAE,EAAE,8BAA8B,CAAC;YAAC;YAAsD,OAAO,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,CAAA,IAAG,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AACruD,CAAC;YAAE;YAAG;YAAiC;gBAAC;gBAA8F;aAA2F,CAAC,GAAG,CAAC,CAAA,IAAG,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3P,CAAC;SAAE,CAAC,IAAI,CAAC,CAAC;AACV,CAAC;IAAE,OAAK;QAAC,IAAI,IAAE,EAAE,KAAK,EAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,SAAS,EAAC,IAAE,OAAO,KAAG,aAAW,CAAC,GAAG,IAAI,CAAA,GAAA,0TAAA,CAAA,aAAC,AAAD,EAAE,KAAK,IAAG,EAAE,SAAS,IAAE,CAAA,GAAA,0TAAA,CAAA,aAAC,AAAD,EAAE,GAAE,EAAE,SAAS,GAAE,IAAE,IAAE;YAAC,WAAU;QAAC,IAAE,CAAC,GAAE,IAAE,EAAE,EAAE,KAAK,EAAC,EAAE,EAAE,GAAE;YAAC;SAAM;QAAI,IAAI,IAAI,KAAK,EAAE,KAAK,KAAG,OAAO,CAAC,CAAC,EAAE;QAAC,OAAO,CAAA,GAAA,oUAAA,CAAA,eAAC,AAAD,EAAE,GAAE,OAAO,MAAM,CAAC,CAAC,GAAE,GAAE,GAAE,GAAE;YAAC,KAAI,EAAE,EAAE,IAAG,EAAE,GAAG;QAAC,GAAE;IAAG;IAAC,OAAO,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE,GAAE,OAAO,MAAM,CAAC,CAAC,GAAE,EAAE,GAAE;QAAC;KAAM,GAAE,MAAI,oUAAA,CAAA,WAAC,IAAE,GAAE,MAAI,oUAAA,CAAA,WAAC,IAAE,IAAG;AAAE;AAAC,SAAS;IAAI,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,EAAE,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA;QAAI,KAAI,IAAI,KAAK,EAAE,OAAO,CAAC,KAAG,QAAM,CAAC,OAAO,KAAG,aAAW,EAAE,KAAG,EAAE,OAAO,GAAC,CAAC;IAAC,GAAE,EAAE;IAAE,OAAM,CAAC,GAAG;QAAK,IAAG,CAAC,EAAE,KAAK,CAAC,CAAA,IAAG,KAAG,OAAM,OAAO,EAAE,OAAO,GAAC,GAAE;IAAC;AAAC;AAAC,SAAS,EAAE,GAAG,CAAC;IAAE,OAAO,EAAE,KAAK,CAAC,CAAA,IAAG,KAAG,QAAM,KAAK,IAAE,CAAA;QAAI,KAAI,IAAI,KAAK,EAAE,KAAG,QAAM,CAAC,OAAO,KAAG,aAAW,EAAE,KAAG,EAAE,OAAO,GAAC,CAAC;IAAC;AAAC;AAAC,SAAS,EAAE,GAAG,CAAC;IAAE,IAAI;IAAE,IAAG,EAAE,MAAM,KAAG,GAAE,OAAM,CAAC;IAAE,IAAG,EAAE,MAAM,KAAG,GAAE,OAAO,CAAC,CAAC,EAAE;IAAC,IAAI,IAAE,CAAC,GAAE,IAAE,CAAC;IAAE,KAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,UAAU,CAAC,SAAO,OAAO,CAAC,CAAC,EAAE,IAAE,aAAW,CAAC,CAAC,IAAE,CAAC,CAAC,EAAE,KAAG,QAAM,CAAC,CAAC,CAAC,EAAE,GAAC,EAAE,GAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE;IAAC,IAAG,EAAE,QAAQ,IAAE,CAAC,CAAC,gBAAgB,EAAC,IAAI,IAAI,KAAK,EAAE,sDAAsD,IAAI,CAAC,MAAI,CAAC,CAAC,CAAC,EAAE,GAAC;QAAC,CAAA;YAAI,IAAI;YAAE,OAAM,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,cAAc,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC;QAAE;KAAE;IAAE,IAAI,IAAI,KAAK,EAAE,OAAO,MAAM,CAAC,GAAE;QAAC,CAAC,EAAE,EAAC,CAAC,EAAC,GAAG,CAAC;YAAE,IAAI,IAAE,CAAC,CAAC,EAAE;YAAC,KAAI,IAAI,KAAK,EAAE;gBAAC,IAAG,CAAC,aAAa,SAAO,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,WAAW,aAAY,KAAK,KAAG,EAAE,gBAAgB,EAAC;gBAAO,EAAE,MAAK;YAAE;QAAC;IAAC;IAAG,OAAO;AAAC;AAAC,SAAS,EAAE,GAAG,CAAC;IAAE,IAAI;IAAE,IAAG,EAAE,MAAM,KAAG,GAAE,OAAM,CAAC;IAAE,IAAG,EAAE,MAAM,KAAG,GAAE,OAAO,CAAC,CAAC,EAAE;IAAC,IAAI,IAAE,CAAC,GAAE,IAAE,CAAC;IAAE,KAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,UAAU,CAAC,SAAO,OAAO,CAAC,CAAC,EAAE,IAAE,aAAW,CAAC,CAAC,IAAE,CAAC,CAAC,EAAE,KAAG,QAAM,CAAC,CAAC,CAAC,EAAE,GAAC,EAAE,GAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE;IAAC,IAAI,IAAI,KAAK,EAAE,OAAO,MAAM,CAAC,GAAE;QAAC,CAAC,EAAE,EAAC,GAAG,CAAC;YAAE,IAAI,IAAE,CAAC,CAAC,EAAE;YAAC,KAAI,IAAI,KAAK,EAAE,KAAG,QAAM,KAAK;QAAE;IAAC;IAAG,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI;IAAE,OAAO,OAAO,MAAM,CAAC,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE,IAAG;QAAC,aAAY,CAAC,IAAE,EAAE,WAAW,KAAG,OAAK,IAAE,EAAE,IAAI;IAAA;AAAE;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,OAAO,MAAM,CAAC,CAAC,GAAE;IAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,KAAG,KAAK,KAAG,OAAO,CAAC,CAAC,EAAE;IAAC,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,IAAE,EAAE;IAAE,IAAI,IAAE,OAAO,MAAM,CAAC,CAAC,GAAE;IAAG,KAAI,IAAI,KAAK,EAAE,KAAK,KAAG,OAAO,CAAC,CAAC,EAAE;IAAC,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAE,OAAK,EAAE,KAAK,CAAC,GAAG,GAAC,EAAE,GAAG;AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/internal/hidden.js"], "sourcesContent": ["import{forwardRefWithAs as i,useRender as p}from'../utils/render.js';let a=\"span\";var s=(e=>(e[e.None=1]=\"None\",e[e.Focusable=2]=\"Focusable\",e[e.Hidden=4]=\"Hidden\",e))(s||{});function l(t,r){var n;let{features:d=1,...e}=t,o={ref:r,\"aria-hidden\":(d&2)===2?!0:(n=e[\"aria-hidden\"])!=null?n:void 0,hidden:(d&4)===4?!0:void 0,style:{position:\"fixed\",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:\"hidden\",clip:\"rect(0, 0, 0, 0)\",whiteSpace:\"nowrap\",borderWidth:\"0\",...(d&4)===4&&(d&2)!==2&&{display:\"none\"}}};return p()({ourProps:o,theirProps:e,slot:{},defaultTag:a,name:\"Hidden\"})}let f=i(l);export{f as Hidden,s as HiddenFeatures};\n"], "names": [], "mappings": ";;;;AAAA;;AAAqE,IAAI,IAAE;AAAO,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,SAAS,GAAC,EAAE,GAAC,aAAY,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI;IAAE,IAAG,EAAC,UAAS,IAAE,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE;QAAC,KAAI;QAAE,eAAc,CAAC,IAAE,CAAC,MAAI,IAAE,CAAC,IAAE,CAAC,IAAE,CAAC,CAAC,cAAc,KAAG,OAAK,IAAE,KAAK;QAAE,QAAO,CAAC,IAAE,CAAC,MAAI,IAAE,CAAC,IAAE,KAAK;QAAE,OAAM;YAAC,UAAS;YAAQ,KAAI;YAAE,MAAK;YAAE,OAAM;YAAE,QAAO;YAAE,SAAQ;YAAE,QAAO,CAAC;YAAE,UAAS;YAAS,MAAK;YAAmB,YAAW;YAAS,aAAY;YAAI,GAAG,CAAC,IAAE,CAAC,MAAI,KAAG,CAAC,IAAE,CAAC,MAAI,KAAG;gBAAC,SAAQ;YAAM,CAAC;QAAA;IAAC;IAAE,OAAO,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD,IAAI;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK,CAAC;QAAE,YAAW;QAAE,MAAK;IAAQ;AAAE;AAAC,IAAI,IAAE,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-root-containers.js"], "sourcesContent": ["import s,{createContext as E,useContext as h,useState as p}from\"react\";import{Hidden as b,HiddenFeatures as M}from'../internal/hidden.js';import*as f from'../utils/dom.js';import{getOwnerDocument as v}from'../utils/owner.js';import{useEvent as m}from'./use-event.js';import{useOwnerDocument as x}from'./use-owner.js';function H({defaultContainers:r=[],portals:n,mainTreeNode:o}={}){let l=x(o),u=m(()=>{var i,c;let t=[];for(let e of r)e!==null&&(f.isElement(e)?t.push(e):\"current\"in e&&f.isElement(e.current)&&t.push(e.current));if(n!=null&&n.current)for(let e of n.current)t.push(e);for(let e of(i=l==null?void 0:l.querySelectorAll(\"html > *, body > *\"))!=null?i:[])e!==document.body&&e!==document.head&&f.isElement(e)&&e.id!==\"headlessui-portal-root\"&&(o&&(e.contains(o)||e.contains((c=o==null?void 0:o.getRootNode())==null?void 0:c.host))||t.some(d=>e.contains(d))||t.push(e));return t});return{resolveContainers:u,contains:m(t=>u().some(i=>i.contains(t)))}}let a=E(null);function P({children:r,node:n}){let[o,l]=p(null),u=y(n!=null?n:o);return s.createElement(a.Provider,{value:u},r,u===null&&s.createElement(b,{features:M.Hidden,ref:t=>{var i,c;if(t){for(let e of(c=(i=v(t))==null?void 0:i.querySelectorAll(\"html > *, body > *\"))!=null?c:[])if(e!==document.body&&e!==document.head&&f.isElement(e)&&e!=null&&e.contains(t)){l(e);break}}}}))}function y(r=null){var n;return(n=h(a))!=null?n:r}export{P as MainTreeProvider,y as useMainTreeNode,H as useRootContainers};\n"], "names": [], "mappings": ";;;;;AAAA;AAAuE;AAAmE;AAAkC;AAAqD;AAA0C;;;;;;;AAAkD,SAAS,EAAE,EAAC,mBAAkB,IAAE,EAAE,EAAC,SAAQ,CAAC,EAAC,cAAa,CAAC,EAAC,GAAC,CAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,mBAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAI,GAAE;QAAE,IAAI,IAAE,EAAE;QAAC,KAAI,IAAI,KAAK,EAAE,MAAI,QAAM,CAAC,CAAA,GAAA,+SAAA,CAAA,YAAW,AAAD,EAAE,KAAG,EAAE,IAAI,CAAC,KAAG,aAAY,KAAG,CAAA,GAAA,+SAAA,CAAA,YAAW,AAAD,EAAE,EAAE,OAAO,KAAG,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC;QAAE,IAAG,KAAG,QAAM,EAAE,OAAO,EAAC,KAAI,IAAI,KAAK,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC;QAAG,KAAI,IAAI,KAAI,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,gBAAgB,CAAC,qBAAqB,KAAG,OAAK,IAAE,EAAE,CAAC,MAAI,SAAS,IAAI,IAAE,MAAI,SAAS,IAAI,IAAE,CAAA,GAAA,+SAAA,CAAA,YAAW,AAAD,EAAE,MAAI,EAAE,EAAE,KAAG,4BAA0B,CAAC,KAAG,CAAC,EAAE,QAAQ,CAAC,MAAI,EAAE,QAAQ,CAAC,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,WAAW,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,KAAG,EAAE,IAAI,CAAC,CAAA,IAAG,EAAE,QAAQ,CAAC,OAAK,EAAE,IAAI,CAAC,EAAE;QAAE,OAAO;IAAC;IAAG,OAAM;QAAC,mBAAkB;QAAE,UAAS,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,IAAI,IAAI,CAAC,CAAA,IAAG,EAAE,QAAQ,CAAC;IAAI;AAAC;AAAC,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,SAAS,EAAE,EAAC,UAAS,CAAC,EAAC,MAAK,CAAC,EAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,OAAM,IAAE,EAAE,KAAG,OAAK,IAAE;IAAG,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,GAAE,MAAI,QAAM,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,qTAAA,CAAA,SAAC,EAAC;QAAC,UAAS,qTAAA,CAAA,iBAAC,CAAC,MAAM;QAAC,KAAI,CAAA;YAAI,IAAI,GAAE;YAAE,IAAG,GAAE;gBAAC,KAAI,IAAI,KAAI,CAAC,IAAE,CAAC,IAAE,CAAA,GAAA,iTAAA,CAAA,mBAAC,AAAD,EAAE,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,gBAAgB,CAAC,qBAAqB,KAAG,OAAK,IAAE,EAAE,CAAC,IAAG,MAAI,SAAS,IAAI,IAAE,MAAI,SAAS,IAAI,IAAE,CAAA,GAAA,+SAAA,CAAA,YAAW,AAAD,EAAE,MAAI,KAAG,QAAM,EAAE,QAAQ,CAAC,IAAG;oBAAC,EAAE;oBAAG;gBAAK;YAAC;QAAC;IAAC;AAAG;AAAC,SAAS,EAAE,IAAE,IAAI;IAAE,IAAI;IAAE,OAAM,CAAC,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE,EAAE,KAAG,OAAK,IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1318, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-store.js"], "sourcesContent": ["import{useSyncExternalStore as e}from\"react\";function o(t){return e(t.subscribe,t.getSnapshot,t.getSnapshot)}export{o as useStore};\n"], "names": [], "mappings": ";;;AAAA;;AAA6C,SAAS,EAAE,CAAC;IAAE,OAAO,CAAA,GAAA,oUAAA,CAAA,uBAAC,AAAD,EAAE,EAAE,SAAS,EAAC,EAAE,WAAW,EAAC,EAAE,WAAW;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1333, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/store.js"], "sourcesContent": ["function a(o,r){let t=o(),n=new Set;return{getSnapshot(){return t},subscribe(e){return n.add(e),()=>n.delete(e)},dispatch(e,...s){let i=r[e].call(t,...s);i&&(t=i,n.forEach(c=>c()))}}}export{a as createStore};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,KAAI,IAAE,IAAI;IAAI,OAAM;QAAC;YAAc,OAAO;QAAC;QAAE,WAAU,CAAC;YAAE,OAAO,EAAE,GAAG,CAAC,IAAG,IAAI,EAAE,MAAM,CAAC;QAAE;QAAE,UAAS,CAAC,EAAC,GAAG,CAAC;YAAE,IAAI,IAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,MAAK;YAAG,KAAG,CAAC,IAAE,GAAE,EAAE,OAAO,CAAC,CAAA,IAAG,IAAI;QAAC;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1358, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js"], "sourcesContent": ["function d(){let r;return{before({doc:e}){var l;let o=e.documentElement,t=(l=e.defaultView)!=null?l:window;r=Math.max(0,t.innerWidth-o.clientWidth)},after({doc:e,d:o}){let t=e.documentElement,l=Math.max(0,t.clientWidth-t.offsetWidth),n=Math.max(0,r-l);o.style(t,\"paddingRight\",`${n}px`)}}}export{d as adjustScrollbarPadding};\n"], "names": [], "mappings": ";;;AAAA,SAAS;IAAI,IAAI;IAAE,OAAM;QAAC,QAAO,EAAC,KAAI,CAAC,EAAC;YAAE,IAAI;YAAE,IAAI,IAAE,EAAE,eAAe,EAAC,IAAE,CAAC,IAAE,EAAE,WAAW,KAAG,OAAK,IAAE;YAAO,IAAE,KAAK,GAAG,CAAC,GAAE,EAAE,UAAU,GAAC,EAAE,WAAW;QAAC;QAAE,OAAM,EAAC,KAAI,CAAC,EAAC,GAAE,CAAC,EAAC;YAAE,IAAI,IAAE,EAAE,eAAe,EAAC,IAAE,KAAK,GAAG,CAAC,GAAE,EAAE,WAAW,GAAC,EAAE,WAAW,GAAE,IAAE,KAAK,GAAG,CAAC,GAAE,IAAE;YAAG,EAAE,KAAK,CAAC,GAAE,gBAAe,GAAG,EAAE,EAAE,CAAC;QAAC;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1382, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js"], "sourcesContent": ["import{disposables as u}from'../../utils/disposables.js';import*as o from'../../utils/dom.js';import{isIOS as p}from'../../utils/platform.js';function w(){return p()?{before({doc:n,d:l,meta:f}){function i(a){return f.containers.flatMap(r=>r()).some(r=>r.contains(a))}l.microTask(()=>{var c;if(window.getComputedStyle(n.documentElement).scrollBehavior!==\"auto\"){let t=u();t.style(n.documentElement,\"scrollBehavior\",\"auto\"),l.add(()=>l.microTask(()=>t.dispose()))}let a=(c=window.scrollY)!=null?c:window.pageYOffset,r=null;l.addEventListener(n,\"click\",t=>{if(o.isHTMLorSVGElement(t.target))try{let e=t.target.closest(\"a\");if(!e)return;let{hash:m}=new URL(e.href),s=n.querySelector(m);o.isHTMLorSVGElement(s)&&!i(s)&&(r=s)}catch{}},!0),l.addEventListener(n,\"touchstart\",t=>{if(o.isHTMLorSVGElement(t.target)&&o.hasInlineStyle(t.target))if(i(t.target)){let e=t.target;for(;e.parentElement&&i(e.parentElement);)e=e.parentElement;l.style(e,\"overscrollBehavior\",\"contain\")}else l.style(t.target,\"touchAction\",\"none\")}),l.addEventListener(n,\"touchmove\",t=>{if(o.isHTMLorSVGElement(t.target)){if(o.isHTMLInputElement(t.target))return;if(i(t.target)){let e=t.target;for(;e.parentElement&&e.dataset.headlessuiPortal!==\"\"&&!(e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth);)e=e.parentElement;e.dataset.headlessuiPortal===\"\"&&t.preventDefault()}else t.preventDefault()}},{passive:!1}),l.add(()=>{var e;let t=(e=window.scrollY)!=null?e:window.pageYOffset;a!==t&&window.scrollTo(0,a),r&&r.isConnected&&(r.scrollIntoView({block:\"nearest\"}),r=null)})})}}:{}}export{w as handleIOSLocking};\n"], "names": [], "mappings": ";;;AAAA;AAAyD;AAAqC;;;;AAAgD,SAAS;IAAI,OAAO,CAAA,GAAA,oTAAA,CAAA,QAAC,AAAD,MAAI;QAAC,QAAO,EAAC,KAAI,CAAC,EAAC,GAAE,CAAC,EAAC,MAAK,CAAC,EAAC;YAAE,SAAS,EAAE,CAAC;gBAAE,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,CAAA,IAAG,KAAK,IAAI,CAAC,CAAA,IAAG,EAAE,QAAQ,CAAC;YAAG;YAAC,EAAE,SAAS,CAAC;gBAAK,IAAI;gBAAE,IAAG,OAAO,gBAAgB,CAAC,EAAE,eAAe,EAAE,cAAc,KAAG,QAAO;oBAAC,IAAI,IAAE,CAAA,GAAA,uTAAA,CAAA,cAAC,AAAD;oBAAI,EAAE,KAAK,CAAC,EAAE,eAAe,EAAC,kBAAiB,SAAQ,EAAE,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,EAAE,OAAO;gBAAI;gBAAC,IAAI,IAAE,CAAC,IAAE,OAAO,OAAO,KAAG,OAAK,IAAE,OAAO,WAAW,EAAC,IAAE;gBAAK,EAAE,gBAAgB,CAAC,GAAE,SAAQ,CAAA;oBAAI,IAAG,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,EAAE,MAAM,GAAE,IAAG;wBAAC,IAAI,IAAE,EAAE,MAAM,CAAC,OAAO,CAAC;wBAAK,IAAG,CAAC,GAAE;wBAAO,IAAG,EAAC,MAAK,CAAC,EAAC,GAAC,IAAI,IAAI,EAAE,IAAI,GAAE,IAAE,EAAE,aAAa,CAAC;wBAAG,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,MAAI,CAAC,EAAE,MAAI,CAAC,IAAE,CAAC;oBAAC,EAAC,OAAK,CAAC;gBAAC,GAAE,CAAC,IAAG,EAAE,gBAAgB,CAAC,GAAE,cAAa,CAAA;oBAAI,IAAG,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,EAAE,MAAM,KAAG,CAAA,GAAA,+SAAA,CAAA,iBAAgB,AAAD,EAAE,EAAE,MAAM,GAAE,IAAG,EAAE,EAAE,MAAM,GAAE;wBAAC,IAAI,IAAE,EAAE,MAAM;wBAAC,MAAK,EAAE,aAAa,IAAE,EAAE,EAAE,aAAa,GAAG,IAAE,EAAE,aAAa;wBAAC,EAAE,KAAK,CAAC,GAAE,sBAAqB;oBAAU,OAAM,EAAE,KAAK,CAAC,EAAE,MAAM,EAAC,eAAc;gBAAO,IAAG,EAAE,gBAAgB,CAAC,GAAE,aAAY,CAAA;oBAAI,IAAG,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,EAAE,MAAM,GAAE;wBAAC,IAAG,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,EAAE,MAAM,GAAE;wBAAO,IAAG,EAAE,EAAE,MAAM,GAAE;4BAAC,IAAI,IAAE,EAAE,MAAM;4BAAC,MAAK,EAAE,aAAa,IAAE,EAAE,OAAO,CAAC,gBAAgB,KAAG,MAAI,CAAC,CAAC,EAAE,YAAY,GAAC,EAAE,YAAY,IAAE,EAAE,WAAW,GAAC,EAAE,WAAW,GAAG,IAAE,EAAE,aAAa;4BAAC,EAAE,OAAO,CAAC,gBAAgB,KAAG,MAAI,EAAE,cAAc;wBAAE,OAAM,EAAE,cAAc;oBAAE;gBAAC,GAAE;oBAAC,SAAQ,CAAC;gBAAC,IAAG,EAAE,GAAG,CAAC;oBAAK,IAAI;oBAAE,IAAI,IAAE,CAAC,IAAE,OAAO,OAAO,KAAG,OAAK,IAAE,OAAO,WAAW;oBAAC,MAAI,KAAG,OAAO,QAAQ,CAAC,GAAE,IAAG,KAAG,EAAE,WAAW,IAAE,CAAC,EAAE,cAAc,CAAC;wBAAC,OAAM;oBAAS,IAAG,IAAE,IAAI;gBAAC;YAAE;QAAE;IAAC,IAAE,CAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1446, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/document-overflow/prevent-scroll.js"], "sourcesContent": ["function r(){return{before({doc:e,d:o}){o.style(e.documentElement,\"overflow\",\"hidden\")}}}export{r as preventScroll};\n"], "names": [], "mappings": ";;;AAAA,SAAS;IAAI,OAAM;QAAC,QAAO,EAAC,KAAI,CAAC,EAAC,GAAE,CAAC,EAAC;YAAE,EAAE,KAAK,CAAC,EAAE,eAAe,EAAC,YAAW;QAAS;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/document-overflow/overflow-store.js"], "sourcesContent": ["import{disposables as s}from'../../utils/disposables.js';import{createStore as i}from'../../utils/store.js';import{adjustScrollbarPadding as l}from'./adjust-scrollbar-padding.js';import{handleIOSLocking as d}from'./handle-ios-locking.js';import{preventScroll as p}from'./prevent-scroll.js';function m(e){let n={};for(let t of e)Object.assign(n,t(n));return n}let a=i(()=>new Map,{PUSH(e,n){var o;let t=(o=this.get(e))!=null?o:{doc:e,count:0,d:s(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let o={doc:e,d:n,meta:m(t)},c=[d(),l(),p()];c.forEach(({before:r})=>r==null?void 0:r(o)),c.forEach(({after:r})=>r==null?void 0:r(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});a.subscribe(()=>{let e=a.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let o=n.get(t.doc)===\"hidden\",c=t.count!==0;(c&&!o||!c&&o)&&a.dispatch(t.count>0?\"SCROLL_PREVENT\":\"SCROLL_ALLOW\",t),t.count===0&&a.dispatch(\"TEARDOWN\",t)}});export{a as overflows};\n"], "names": [], "mappings": ";;;AAAA;AAAyD;AAAmD;AAAuE;AAA2D;;;;;;AAAoD,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAC;IAAE,KAAI,IAAI,KAAK,EAAE,OAAO,MAAM,CAAC,GAAE,EAAE;IAAI,OAAO;AAAC;AAAC,IAAI,IAAE,CAAA,GAAA,iTAAA,CAAA,cAAC,AAAD,EAAE,IAAI,IAAI,KAAI;IAAC,MAAK,CAAC,EAAC,CAAC;QAAE,IAAI;QAAE,IAAI,IAAE,CAAC,IAAE,IAAI,CAAC,GAAG,CAAC,EAAE,KAAG,OAAK,IAAE;YAAC,KAAI;YAAE,OAAM;YAAE,GAAE,CAAA,GAAA,uTAAA,CAAA,cAAC,AAAD;YAAI,MAAK,IAAI;QAAG;QAAE,OAAO,EAAE,KAAK,IAAG,EAAE,IAAI,CAAC,GAAG,CAAC,IAAG,IAAI,CAAC,GAAG,CAAC,GAAE,IAAG,IAAI;IAAA;IAAE,KAAI,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,IAAI,CAAC,GAAG,CAAC;QAAG,OAAO,KAAG,CAAC,EAAE,KAAK,IAAG,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,GAAE,IAAI;IAAA;IAAE,gBAAe,EAAC,KAAI,CAAC,EAAC,GAAE,CAAC,EAAC,MAAK,CAAC,EAAC;QAAE,IAAI,IAAE;YAAC,KAAI;YAAE,GAAE;YAAE,MAAK,EAAE;QAAE,GAAE,IAAE;YAAC,CAAA,GAAA,4VAAA,CAAA,mBAAC,AAAD;YAAI,CAAA,GAAA,kWAAA,CAAA,yBAAC,AAAD;YAAI,CAAA,GAAA,qVAAA,CAAA,gBAAC,AAAD;SAAI;QAAC,EAAE,OAAO,CAAC,CAAC,EAAC,QAAO,CAAC,EAAC,GAAG,KAAG,OAAK,KAAK,IAAE,EAAE,KAAI,EAAE,OAAO,CAAC,CAAC,EAAC,OAAM,CAAC,EAAC,GAAG,KAAG,OAAK,KAAK,IAAE,EAAE;IAAG;IAAE,cAAa,EAAC,GAAE,CAAC,EAAC;QAAE,EAAE,OAAO;IAAE;IAAE,UAAS,EAAC,KAAI,CAAC,EAAC;QAAE,IAAI,CAAC,MAAM,CAAC;IAAE;AAAC;AAAG,EAAE,SAAS,CAAC;IAAK,IAAI,IAAE,EAAE,WAAW,IAAG,IAAE,IAAI;IAAI,KAAI,IAAG,CAAC,EAAE,IAAG,EAAE,EAAE,GAAG,CAAC,GAAE,EAAE,eAAe,CAAC,KAAK,CAAC,QAAQ;IAAE,KAAI,IAAI,KAAK,EAAE,MAAM,GAAG;QAAC,IAAI,IAAE,EAAE,GAAG,CAAC,EAAE,GAAG,MAAI,UAAS,IAAE,EAAE,KAAK,KAAG;QAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,EAAE,QAAQ,CAAC,EAAE,KAAK,GAAC,IAAE,mBAAiB,gBAAe,IAAG,EAAE,KAAK,KAAG,KAAG,EAAE,QAAQ,CAAC,YAAW;IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1530, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/document-overflow/use-document-overflow.js"], "sourcesContent": ["import{useStore as s}from'../../hooks/use-store.js';import{useIsoMorphicEffect as u}from'../use-iso-morphic-effect.js';import{overflows as t}from'./overflow-store.js';function a(r,e,n=()=>({containers:[]})){let f=s(t),o=e?f.get(e):void 0,i=o?o.count>0:!1;return u(()=>{if(!(!e||!r))return t.dispatch(\"PUSH\",e,n),()=>t.dispatch(\"POP\",e,n)},[r,e]),i}export{a as useDocumentOverflowLockedEffect};\n"], "names": [], "mappings": ";;;AAAA;AAAoD;AAAmE;;;;AAAgD,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,IAAE,IAAI,CAAC;QAAC,YAAW,EAAE;IAAA,CAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,qVAAA,CAAA,YAAC,GAAE,IAAE,IAAE,EAAE,GAAG,CAAC,KAAG,KAAK,GAAE,IAAE,IAAE,EAAE,KAAK,GAAC,IAAE,CAAC;IAAE,OAAO,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,CAAC,CAAC,CAAC,KAAG,CAAC,CAAC,GAAE,OAAO,qVAAA,CAAA,YAAC,CAAC,QAAQ,CAAC,QAAO,GAAE,IAAG,IAAI,qVAAA,CAAA,YAAC,CAAC,QAAQ,CAAC,OAAM,GAAE;IAAE,GAAE;QAAC;QAAE;KAAE,GAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1557, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-scroll-lock.js"], "sourcesContent": ["import{useDocumentOverflowLockedEffect as l}from'./document-overflow/use-document-overflow.js';import{useIsTopLayer as m}from'./use-is-top-layer.js';function f(e,c,n=()=>[document.body]){let r=m(e,\"scroll-lock\");l(r,c,t=>{var o;return{containers:[...(o=t.containers)!=null?o:[],n]}})}export{f as useScrollLock};\n"], "names": [], "mappings": ";;;AAAA;AAA+F;;;AAAsD,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,IAAE,IAAI;QAAC,SAAS,IAAI;KAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qUAAA,CAAA,gBAAC,AAAD,EAAE,GAAE;IAAe,CAAA,GAAA,+VAAA,CAAA,kCAAC,AAAD,EAAE,GAAE,GAAE,CAAA;QAAI,IAAI;QAAE,OAAM;YAAC,YAAW;mBAAI,CAAC,IAAE,EAAE,UAAU,KAAG,OAAK,IAAE,EAAE;gBAAC;aAAE;QAAA;IAAC;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1585, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-server-handoff-complete.js"], "sourcesContent": ["import*as t from\"react\";import{env as f}from'../utils/env.js';function s(){let r=typeof document==\"undefined\";return\"useSyncExternalStore\"in t?(o=>o.useSyncExternalStore)(t)(()=>()=>{},()=>!1,()=>!r):!1}function l(){let r=s(),[e,n]=t.useState(f.isHandoffComplete);return e&&f.isHandoffComplete===!1&&n(!1),t.useEffect(()=>{e!==!0&&n(!0)},[e]),t.useEffect(()=>f.handoff(),[]),r?!1:e}export{l as useServerHandoffComplete};\n"], "names": [], "mappings": ";;;AAAA;AAAwB;;;AAAsC,SAAS;IAAI,IAAI,IAAE,OAAO,YAAU;IAAY,OAAM,0BAAyB,uUAAE,CAAC,CAAA,IAAG,EAAE,oBAAoB,EAAE,sUAAG,IAAI,KAAK,GAAE,IAAI,CAAC,GAAE,IAAI,CAAC,KAAG,CAAC;AAAC;AAAC,SAAS;IAAI,IAAI,IAAE,KAAI,CAAC,GAAE,EAAE,GAAC,qUAAE,QAAQ,CAAC,+SAAA,CAAA,MAAC,CAAC,iBAAiB;IAAE,OAAO,KAAG,+SAAA,CAAA,MAAC,CAAC,iBAAiB,KAAG,CAAC,KAAG,EAAE,CAAC,IAAG,qUAAE,SAAS;uBAAC;YAAK,MAAI,CAAC,KAAG,EAAE,CAAC;QAAE;sBAAE;QAAC;KAAE,GAAE,qUAAE,SAAS;uBAAC,IAAI,+SAAA,CAAA,MAAC,CAAC,OAAO;sBAAG,EAAE,GAAE,IAAE,CAAC,IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1615, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-sync-refs.js"], "sourcesContent": ["import{useEffect as l,useRef as i}from\"react\";import{useEvent as r}from'./use-event.js';let u=Symbol();function T(t,n=!0){return Object.assign(t,{[u]:n})}function y(...t){let n=i(t);l(()=>{n.current=t},[t]);let c=r(e=>{for(let o of n.current)o!=null&&(typeof o==\"function\"?o(e):o.current=e)});return t.every(e=>e==null||(e==null?void 0:e[u]))?void 0:c}export{T as optionalRef,y as useSyncRefs};\n"], "names": [], "mappings": ";;;;AAAA;AAA8C;;;AAA0C,IAAI,IAAE;AAAS,SAAS,EAAE,CAAC,EAAC,IAAE,CAAC,CAAC;IAAE,OAAO,OAAO,MAAM,CAAC,GAAE;QAAC,CAAC,EAAE,EAAC;IAAC;AAAE;AAAC,SAAS,EAAE,GAAG,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE;IAAG,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE;QAAK,EAAE,OAAO,GAAC;IAAC,GAAE;QAAC;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,KAAI,IAAI,KAAK,EAAE,OAAO,CAAC,KAAG,QAAM,CAAC,OAAO,KAAG,aAAW,EAAE,KAAG,EAAE,OAAO,GAAC,CAAC;IAAC;IAAG,OAAO,EAAE,KAAK,CAAC,CAAA,IAAG,KAAG,QAAM,CAAC,KAAG,OAAK,KAAK,IAAE,CAAC,CAAC,EAAE,KAAG,KAAK,IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1648, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/internal/close-provider.js"], "sourcesContent": ["\"use client\";import r,{createContext as n,useContext as i}from\"react\";let e=n(()=>{});function u(){return i(e)}function C({value:t,children:o}){return r.createElement(e.Provider,{value:t},o)}export{C as CloseProvider,u as useClose};\n"], "names": [], "mappings": ";;;;AAAa;AAAb;;AAAsE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE,KAAK;AAAG,SAAS;IAAI,OAAO,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;AAAE;AAAC,SAAS,EAAE,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC;IAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1671, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/internal/open-closed.js"], "sourcesContent": ["import r,{createContext as l,useContext as d}from\"react\";let n=l(null);n.displayName=\"OpenClosedContext\";var i=(e=>(e[e.Open=1]=\"Open\",e[e.Closed=2]=\"Closed\",e[e.Closing=4]=\"Closing\",e[e.Opening=8]=\"Opening\",e))(i||{});function u(){return d(n)}function c({value:o,children:t}){return r.createElement(n.Provider,{value:o},t)}function s({children:o}){return r.createElement(n.Provider,{value:null},o)}export{c as OpenClosedProvider,s as ResetOpenClosedProvider,i as State,u as useOpenClosed};\n"], "names": [], "mappings": ";;;;;;AAAA;;AAAyD,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAoB,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS;IAAI,OAAO,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;AAAE;AAAC,SAAS,EAAE,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC;IAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE;AAAE;AAAC,SAAS,EAAE,EAAC,UAAS,CAAC,EAAC;IAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAI,GAAE;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1702, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/internal/portal-force-root.js"], "sourcesContent": ["import t,{createContext as r,useContext as c}from\"react\";let e=r(!1);function a(){return c(e)}function l(o){return t.createElement(e.Provider,{value:o.force},o.children)}export{l as ForcePortalRoot,a as usePortalRoot};\n"], "names": [], "mappings": ";;;;AAAA;;AAAyD,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE,CAAC;AAAG,SAAS;IAAI,OAAO,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;AAAE;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM,EAAE,KAAK;IAAA,GAAE,EAAE,QAAQ;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1724, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/internal/disabled.js"], "sourcesContent": ["import n,{createContext as r,useContext as i}from\"react\";let e=r(void 0);function a(){return i(e)}function l({value:t,children:o}){return n.createElement(e.Provider,{value:t},o)}export{l as DisabledProvider,a as useDisabled};\n"], "names": [], "mappings": ";;;;AAAA;;AAAyD,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE,KAAK;AAAG,SAAS;IAAI,OAAO,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;AAAE;AAAC,SAAS,EAAE,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC;IAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1746, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/description/description.js"], "sourcesContent": ["\"use client\";import m,{createContext as T,useContext as u,useMemo as c,useState as P}from\"react\";import{useEvent as g}from'../../hooks/use-event.js';import{useId as x}from'../../hooks/use-id.js';import{useIsoMorphicEffect as y}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as E}from'../../hooks/use-sync-refs.js';import{useDisabled as v}from'../../internal/disabled.js';import{forwardRefWithAs as R,useRender as I}from'../../utils/render.js';let a=T(null);a.displayName=\"DescriptionContext\";function f(){let r=u(a);if(r===null){let e=new Error(\"You used a <Description /> component, but it is not inside a relevant parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(e,f),e}return r}function U(){var r,e;return(e=(r=u(a))==null?void 0:r.value)!=null?e:void 0}function w(){let[r,e]=P([]);return[r.length>0?r.join(\" \"):void 0,c(()=>function(t){let i=g(n=>(e(s=>[...s,n]),()=>e(s=>{let o=s.slice(),p=o.indexOf(n);return p!==-1&&o.splice(p,1),o}))),l=c(()=>({register:i,slot:t.slot,name:t.name,props:t.props,value:t.value}),[i,t.slot,t.name,t.props,t.value]);return m.createElement(a.Provider,{value:l},t.children)},[e])]}let S=\"p\";function C(r,e){let d=x(),t=v(),{id:i=`headlessui-description-${d}`,...l}=r,n=f(),s=E(e);y(()=>n.register(i),[i,n.register]);let o=t||!1,p=c(()=>({...n.slot,disabled:o}),[n.slot,o]),D={ref:s,...n.props,id:i};return I()({ourProps:D,theirProps:l,slot:p,defaultTag:S,name:n.name||\"Description\"})}let _=R(C),H=Object.assign(_,{});export{H as Description,U as useDescribedBy,w as useDescriptions};\n"], "names": [], "mappings": ";;;;;AAAa;AAAoF;AAAkG;AAA4E;AAA2D;AAAyD;AAAnY;;;;;;;;AAA2c,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAqB,SAAS;IAAI,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;IAAG,IAAG,MAAI,MAAK;QAAC,IAAI,IAAE,IAAI,MAAM;QAAiF,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG;IAAC;IAAC,OAAO;AAAC;AAAC,SAAS;IAAI,IAAI,GAAE;IAAE,OAAM,CAAC,IAAE,CAAC,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,KAAK,KAAG,OAAK,IAAE,KAAK;AAAC;AAAC,SAAS;IAAI,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,EAAE;IAAE,OAAM;QAAC,EAAE,MAAM,GAAC,IAAE,EAAE,IAAI,CAAC,OAAK,KAAK;QAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,SAAS,CAAC;gBAAE,IAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,CAAC,EAAE,CAAA,IAAG;+BAAI;4BAAE;yBAAE,GAAE,IAAI,EAAE,CAAA;4BAAI,IAAI,IAAE,EAAE,KAAK,IAAG,IAAE,EAAE,OAAO,CAAC;4BAAG,OAAO,MAAI,CAAC,KAAG,EAAE,MAAM,CAAC,GAAE,IAAG;wBAAC,EAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;wBAAC,UAAS;wBAAE,MAAK,EAAE,IAAI;wBAAC,MAAK,EAAE,IAAI;wBAAC,OAAM,EAAE,KAAK;wBAAC,OAAM,EAAE,KAAK;oBAAA,CAAC,GAAE;oBAAC;oBAAE,EAAE,IAAI;oBAAC,EAAE,IAAI;oBAAC,EAAE,KAAK;oBAAC,EAAE,KAAK;iBAAC;gBAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;oBAAC,OAAM;gBAAC,GAAE,EAAE,QAAQ;YAAC,GAAE;YAAC;SAAE;KAAE;AAAA;AAAC,IAAI,IAAE;AAAI,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAC,AAAD,KAAI,IAAE,CAAA,GAAA,uTAAA,CAAA,cAAC,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,uBAAuB,EAAE,GAAG,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,KAAI,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE;IAAG,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAG;QAAC;QAAE,EAAE,QAAQ;KAAC;IAAE,IAAI,IAAE,KAAG,CAAC,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,GAAG,EAAE,IAAI;YAAC,UAAS;QAAC,CAAC,GAAE;QAAC,EAAE,IAAI;QAAC;KAAE,GAAE,IAAE;QAAC,KAAI;QAAE,GAAG,EAAE,KAAK;QAAC,IAAG;IAAC;IAAE,OAAO,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD,IAAI;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAE,MAAK,EAAE,IAAI,IAAE;IAAa;AAAE;AAAC,IAAI,IAAE,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,IAAG,IAAE,OAAO,MAAM,CAAC,GAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1845, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-disposables.js"], "sourcesContent": ["import{useEffect as s,useState as o}from\"react\";import{disposables as t}from'../utils/disposables.js';function p(){let[e]=o(t);return s(()=>()=>e.dispose(),[e]),e}export{p as useDisposables};\n"], "names": [], "mappings": ";;;AAAA;AAAgD;;;AAAsD,SAAS;IAAI,IAAG,CAAC,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,uTAAA,CAAA,cAAC;IAAE,OAAO,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE,IAAI,IAAI,EAAE,OAAO,IAAG;QAAC;KAAE,GAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1865, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-is-mounted.js"], "sourcesContent": ["import{useRef as r}from\"react\";import{useIsoMorphicEffect as t}from'./use-iso-morphic-effect.js';function f(){let e=r(!1);return t(()=>(e.current=!0,()=>{e.current=!1}),[]),e}export{f as useIsMounted};\n"], "names": [], "mappings": ";;;AAAA;AAA+B;;;AAAkE,SAAS;IAAI,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,CAAC;IAAG,OAAO,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE,IAAI,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE;YAAK,EAAE,OAAO,GAAC,CAAC;QAAC,CAAC,GAAE,EAAE,GAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1885, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-on-unmount.js"], "sourcesContent": ["import{useEffect as u,useRef as n}from\"react\";import{microTask as o}from'../utils/micro-task.js';import{useEvent as f}from'./use-event.js';function c(t){let r=f(t),e=n(!1);u(()=>(e.current=!1,()=>{e.current=!0,o(()=>{e.current&&r()})}),[r])}export{c as useOnUnmount};\n"], "names": [], "mappings": ";;;AAAA;AAA8C;AAAmD;;;;AAA0C,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,CAAC;IAAG,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE,IAAI,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE;YAAK,EAAE,OAAO,GAAC,CAAC,GAAE,CAAA,GAAA,yTAAA,CAAA,YAAC,AAAD,EAAE;gBAAK,EAAE,OAAO,IAAE;YAAG;QAAE,CAAC,GAAE;QAAC;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1911, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-tab-direction.js"], "sourcesContent": ["import{useRef as o}from\"react\";import{useWindowEvent as t}from'./use-window-event.js';var a=(r=>(r[r.Forwards=0]=\"Forwards\",r[r.Backwards=1]=\"Backwards\",r))(a||{});function u(){let e=o(0);return t(!0,\"keydown\",r=>{r.key===\"Tab\"&&(e.current=r.shiftKey?1:0)},!0),e}export{a as Direction,u as useTabDirection};\n"], "names": [], "mappings": ";;;;AAAA;AAA+B;;;AAAuD,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAC,EAAE,GAAC,YAAW,CAAC,CAAC,EAAE,SAAS,GAAC,EAAE,GAAC,aAAY,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS;IAAI,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE;IAAG,OAAO,CAAA,GAAA,kUAAA,CAAA,iBAAC,AAAD,EAAE,CAAC,GAAE,WAAU,CAAA;QAAI,EAAE,GAAG,KAAG,SAAO,CAAC,EAAE,OAAO,GAAC,EAAE,QAAQ,GAAC,IAAE,CAAC;IAAC,GAAE,CAAC,IAAG;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1933, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-watch.js"], "sourcesContent": ["import{useEffect as f,useRef as s}from\"react\";import{useEvent as i}from'./use-event.js';function m(u,t){let e=s([]),r=i(u);f(()=>{let o=[...e.current];for(let[a,l]of t.entries())if(e.current[a]!==l){let n=r(t,o);return e.current=t,n}},[r,...t])}export{m as useWatch};\n"], "names": [], "mappings": ";;;AAAA;AAA8C;;;AAA0C,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,EAAE,GAAE,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;IAAG,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE;QAAK,IAAI,IAAE;eAAI,EAAE,OAAO;SAAC;QAAC,KAAI,IAAG,CAAC,GAAE,EAAE,IAAG,EAAE,OAAO,GAAG,IAAG,EAAE,OAAO,CAAC,EAAE,KAAG,GAAE;YAAC,IAAI,IAAE,EAAE,GAAE;YAAG,OAAO,EAAE,OAAO,GAAC,GAAE;QAAC;IAAC,GAAE;QAAC;WAAK;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1962, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/document-ready.js"], "sourcesContent": ["function t(n){function e(){document.readyState!==\"loading\"&&(n(),document.removeEventListener(\"DOMContentLoaded\",e))}typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"DOMContentLoaded\",e),e())}export{t as onDocumentReady};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,CAAC;IAAE,SAAS;QAAI,SAAS,UAAU,KAAG,aAAW,CAAC,KAAI,SAAS,mBAAmB,CAAC,oBAAmB,EAAE;IAAC;IAAC,OAAO,UAAQ,eAAa,OAAO,YAAU,eAAa,CAAC,SAAS,gBAAgB,CAAC,oBAAmB,IAAG,GAAG;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1978, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/active-element-history.js"], "sourcesContent": ["import{onDocumentReady as d}from'./document-ready.js';import*as u from'./dom.js';import{focusableSelector as i}from'./focus-management.js';let n=[];d(()=>{function e(t){if(!u.isHTMLorSVGElement(t.target)||t.target===document.body||n[0]===t.target)return;let r=t.target;r=r.closest(i),n.unshift(r!=null?r:t.target),n=n.filter(o=>o!=null&&o.isConnected),n.splice(10)}window.addEventListener(\"click\",e,{capture:!0}),window.addEventListener(\"mousedown\",e,{capture:!0}),window.addEventListener(\"focus\",e,{capture:!0}),document.body.addEventListener(\"click\",e,{capture:!0}),document.body.addEventListener(\"mousedown\",e,{capture:!0}),document.body.addEventListener(\"focus\",e,{capture:!0})});export{n as history};\n"], "names": [], "mappings": ";;;AAAA;AAAsD;AAA2B;;;;AAA0D,IAAI,IAAE,EAAE;AAAC,CAAA,GAAA,6TAAA,CAAA,kBAAC,AAAD,EAAE;IAAK,SAAS,EAAE,CAAC;QAAE,IAAG,CAAC,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,EAAE,MAAM,KAAG,EAAE,MAAM,KAAG,SAAS,IAAI,IAAE,CAAC,CAAC,EAAE,KAAG,EAAE,MAAM,EAAC;QAAO,IAAI,IAAE,EAAE,MAAM;QAAC,IAAE,EAAE,OAAO,CAAC,+TAAA,CAAA,oBAAC,GAAE,EAAE,OAAO,CAAC,KAAG,OAAK,IAAE,EAAE,MAAM,GAAE,IAAE,EAAE,MAAM,CAAC,CAAA,IAAG,KAAG,QAAM,EAAE,WAAW,GAAE,EAAE,MAAM,CAAC;IAAG;IAAC,OAAO,gBAAgB,CAAC,SAAQ,GAAE;QAAC,SAAQ,CAAC;IAAC,IAAG,OAAO,gBAAgB,CAAC,aAAY,GAAE;QAAC,SAAQ,CAAC;IAAC,IAAG,OAAO,gBAAgB,CAAC,SAAQ,GAAE;QAAC,SAAQ,CAAC;IAAC,IAAG,SAAS,IAAI,CAAC,gBAAgB,CAAC,SAAQ,GAAE;QAAC,SAAQ,CAAC;IAAC,IAAG,SAAS,IAAI,CAAC,gBAAgB,CAAC,aAAY,GAAE;QAAC,SAAQ,CAAC;IAAC,IAAG,SAAS,IAAI,CAAC,gBAAgB,CAAC,SAAQ,GAAE;QAAC,SAAQ,CAAC;IAAC;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2015, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/focus-trap/focus-trap.js"], "sourcesContent": ["\"use client\";import F,{useRef as M}from\"react\";import{useDisposables as W}from'../../hooks/use-disposables.js';import{useEvent as O}from'../../hooks/use-event.js';import{useEventListener as K}from'../../hooks/use-event-listener.js';import{useIsMounted as P}from'../../hooks/use-is-mounted.js';import{useIsTopLayer as C}from'../../hooks/use-is-top-layer.js';import{useOnUnmount as q}from'../../hooks/use-on-unmount.js';import{useOwnerDocument as J}from'../../hooks/use-owner.js';import{useServerHandoffComplete as X}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as z}from'../../hooks/use-sync-refs.js';import{Direction as y,useTabDirection as Q}from'../../hooks/use-tab-direction.js';import{useWatch as R}from'../../hooks/use-watch.js';import{Hidden as _,HiddenFeatures as S}from'../../internal/hidden.js';import{history as H}from'../../utils/active-element-history.js';import*as T from'../../utils/dom.js';import{Focus as i,FocusResult as h,focusElement as p,focusIn as d}from'../../utils/focus-management.js';import{match as j}from'../../utils/match.js';import{microTask as U}from'../../utils/micro-task.js';import{forwardRefWithAs as Y,useRender as Z}from'../../utils/render.js';function x(s){if(!s)return new Set;if(typeof s==\"function\")return new Set(s());let e=new Set;for(let t of s.current)T.isElement(t.current)&&e.add(t.current);return e}let $=\"div\";var G=(n=>(n[n.None=0]=\"None\",n[n.InitialFocus=1]=\"InitialFocus\",n[n.TabLock=2]=\"TabLock\",n[n.FocusLock=4]=\"FocusLock\",n[n.RestoreFocus=8]=\"RestoreFocus\",n[n.AutoFocus=16]=\"AutoFocus\",n))(G||{});function D(s,e){let t=M(null),r=z(t,e),{initialFocus:o,initialFocusFallback:a,containers:n,features:u=15,...f}=s;X()||(u=0);let l=J(t);te(u,{ownerDocument:l});let m=re(u,{ownerDocument:l,container:t,initialFocus:o,initialFocusFallback:a});ne(u,{ownerDocument:l,container:t,containers:n,previousActiveElement:m});let g=Q(),v=O(c=>{if(!T.isHTMLElement(t.current))return;let E=t.current;(V=>V())(()=>{j(g.current,{[y.Forwards]:()=>{d(E,i.First,{skipElements:[c.relatedTarget,a]})},[y.Backwards]:()=>{d(E,i.Last,{skipElements:[c.relatedTarget,a]})}})})}),A=C(!!(u&2),\"focus-trap#tab-lock\"),N=W(),b=M(!1),k={ref:r,onKeyDown(c){c.key==\"Tab\"&&(b.current=!0,N.requestAnimationFrame(()=>{b.current=!1}))},onBlur(c){if(!(u&4))return;let E=x(n);T.isHTMLElement(t.current)&&E.add(t.current);let L=c.relatedTarget;T.isHTMLorSVGElement(L)&&L.dataset.headlessuiFocusGuard!==\"true\"&&(I(E,L)||(b.current?d(t.current,j(g.current,{[y.Forwards]:()=>i.Next,[y.Backwards]:()=>i.Previous})|i.WrapAround,{relativeTo:c.target}):T.isHTMLorSVGElement(c.target)&&p(c.target)))}},B=Z();return F.createElement(F.Fragment,null,A&&F.createElement(_,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:v,features:S.Focusable}),B({ourProps:k,theirProps:f,defaultTag:$,name:\"FocusTrap\"}),A&&F.createElement(_,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:v,features:S.Focusable}))}let w=Y(D),Re=Object.assign(w,{features:G});function ee(s=!0){let e=M(H.slice());return R(([t],[r])=>{r===!0&&t===!1&&U(()=>{e.current.splice(0)}),r===!1&&t===!0&&(e.current=H.slice())},[s,H,e]),O(()=>{var t;return(t=e.current.find(r=>r!=null&&r.isConnected))!=null?t:null})}function te(s,{ownerDocument:e}){let t=!!(s&8),r=ee(t);R(()=>{t||(e==null?void 0:e.activeElement)===(e==null?void 0:e.body)&&p(r())},[t]),q(()=>{t&&p(r())})}function re(s,{ownerDocument:e,container:t,initialFocus:r,initialFocusFallback:o}){let a=M(null),n=C(!!(s&1),\"focus-trap#initial-focus\"),u=P();return R(()=>{if(s===0)return;if(!n){o!=null&&o.current&&p(o.current);return}let f=t.current;f&&U(()=>{if(!u.current)return;let l=e==null?void 0:e.activeElement;if(r!=null&&r.current){if((r==null?void 0:r.current)===l){a.current=l;return}}else if(f.contains(l)){a.current=l;return}if(r!=null&&r.current)p(r.current);else{if(s&16){if(d(f,i.First|i.AutoFocus)!==h.Error)return}else if(d(f,i.First)!==h.Error)return;if(o!=null&&o.current&&(p(o.current),(e==null?void 0:e.activeElement)===o.current))return;console.warn(\"There are no focusable elements inside the <FocusTrap />\")}a.current=e==null?void 0:e.activeElement})},[o,n,s]),a}function ne(s,{ownerDocument:e,container:t,containers:r,previousActiveElement:o}){let a=P(),n=!!(s&4);K(e==null?void 0:e.defaultView,\"focus\",u=>{if(!n||!a.current)return;let f=x(r);T.isHTMLElement(t.current)&&f.add(t.current);let l=o.current;if(!l)return;let m=u.target;T.isHTMLElement(m)?I(f,m)?(o.current=m,p(m)):(u.preventDefault(),u.stopPropagation(),p(l)):p(o.current)},!0)}function I(s,e){for(let t of s)if(t.contains(e))return!0;return!1}export{Re as FocusTrap,G as FocusTrapFeatures};\n"], "names": [], "mappings": ";;;;AAAa;AAAkC;AAAgE;AAAoD;AAAqE;AAA6D;AAAgE;AAA6D;AAA4D;AAAsF;AAA2D;AAAkF;AAAoD;AAAsE;AAAgE;AAAqC;AAAwG;AAA6C;AAAsD;AAA3mC;;;;;;;;;;;;;;;;;;;;AAAmrC,SAAS,EAAE,CAAC;IAAE,IAAG,CAAC,GAAE,OAAO,IAAI;IAAI,IAAG,OAAO,KAAG,YAAW,OAAO,IAAI,IAAI;IAAK,IAAI,IAAE,IAAI;IAAI,KAAI,IAAI,KAAK,EAAE,OAAO,CAAC,CAAA,GAAA,+SAAA,CAAA,YAAW,AAAD,EAAE,EAAE,OAAO,KAAG,EAAE,GAAG,CAAC,EAAE,OAAO;IAAE,OAAO;AAAC;AAAC,IAAI,IAAE;AAAM,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,YAAY,GAAC,EAAE,GAAC,gBAAe,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,SAAS,GAAC,EAAE,GAAC,aAAY,CAAC,CAAC,EAAE,YAAY,GAAC,EAAE,GAAC,gBAAe,CAAC,CAAC,EAAE,SAAS,GAAC,GAAG,GAAC,aAAY,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE,GAAE,IAAG,EAAC,cAAa,CAAC,EAAC,sBAAqB,CAAC,EAAC,YAAW,CAAC,EAAC,UAAS,IAAE,EAAE,EAAC,GAAG,GAAE,GAAC;IAAE,CAAA,GAAA,gVAAA,CAAA,2BAAC,AAAD,OAAK,CAAC,IAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,mBAAC,AAAD,EAAE;IAAG,GAAG,GAAE;QAAC,eAAc;IAAC;IAAG,IAAI,IAAE,GAAG,GAAE;QAAC,eAAc;QAAE,WAAU;QAAE,cAAa;QAAE,sBAAqB;IAAC;IAAG,GAAG,GAAE;QAAC,eAAc;QAAE,WAAU;QAAE,YAAW;QAAE,uBAAsB;IAAC;IAAG,IAAI,IAAE,CAAA,GAAA,mUAAA,CAAA,kBAAC,AAAD,KAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAG,CAAC,CAAA,GAAA,+SAAA,CAAA,gBAAe,AAAD,EAAE,EAAE,OAAO,GAAE;QAAO,IAAI,IAAE,EAAE,OAAO;QAAC,CAAC,CAAA,IAAG,GAAG,EAAE;YAAK,CAAA,GAAA,iTAAA,CAAA,QAAC,AAAD,EAAE,EAAE,OAAO,EAAC;gBAAC,CAAC,mUAAA,CAAA,YAAC,CAAC,QAAQ,CAAC,EAAC;oBAAK,CAAA,GAAA,+TAAA,CAAA,UAAC,AAAD,EAAE,GAAE,+TAAA,CAAA,QAAC,CAAC,KAAK,EAAC;wBAAC,cAAa;4BAAC,EAAE,aAAa;4BAAC;yBAAE;oBAAA;gBAAE;gBAAE,CAAC,mUAAA,CAAA,YAAC,CAAC,SAAS,CAAC,EAAC;oBAAK,CAAA,GAAA,+TAAA,CAAA,UAAC,AAAD,EAAE,GAAE,+TAAA,CAAA,QAAC,CAAC,IAAI,EAAC;wBAAC,cAAa;4BAAC,EAAE,aAAa;4BAAC;yBAAE;oBAAA;gBAAE;YAAC;QAAE;IAAE,IAAG,IAAE,CAAA,GAAA,qUAAA,CAAA,gBAAC,AAAD,EAAE,CAAC,CAAC,CAAC,IAAE,CAAC,GAAE,wBAAuB,IAAE,CAAA,GAAA,8TAAA,CAAA,iBAAC,AAAD,KAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,CAAC,IAAG,IAAE;QAAC,KAAI;QAAE,WAAU,CAAC;YAAE,EAAE,GAAG,IAAE,SAAO,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE,EAAE,qBAAqB,CAAC;gBAAK,EAAE,OAAO,GAAC,CAAC;YAAC,EAAE;QAAC;QAAE,QAAO,CAAC;YAAE,IAAG,CAAC,CAAC,IAAE,CAAC,GAAE;YAAO,IAAI,IAAE,EAAE;YAAG,CAAA,GAAA,+SAAA,CAAA,gBAAe,AAAD,EAAE,EAAE,OAAO,KAAG,EAAE,GAAG,CAAC,EAAE,OAAO;YAAE,IAAI,IAAE,EAAE,aAAa;YAAC,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,MAAI,EAAE,OAAO,CAAC,oBAAoB,KAAG,UAAQ,CAAC,EAAE,GAAE,MAAI,CAAC,EAAE,OAAO,GAAC,CAAA,GAAA,+TAAA,CAAA,UAAC,AAAD,EAAE,EAAE,OAAO,EAAC,CAAA,GAAA,iTAAA,CAAA,QAAC,AAAD,EAAE,EAAE,OAAO,EAAC;gBAAC,CAAC,mUAAA,CAAA,YAAC,CAAC,QAAQ,CAAC,EAAC,IAAI,+TAAA,CAAA,QAAC,CAAC,IAAI;gBAAC,CAAC,mUAAA,CAAA,YAAC,CAAC,SAAS,CAAC,EAAC,IAAI,+TAAA,CAAA,QAAC,CAAC,QAAQ;YAAA,KAAG,+TAAA,CAAA,QAAC,CAAC,UAAU,EAAC;gBAAC,YAAW,EAAE,MAAM;YAAA,KAAG,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,EAAE,MAAM,KAAG,CAAA,GAAA,+TAAA,CAAA,eAAC,AAAD,EAAE,EAAE,MAAM,CAAC,CAAC;QAAC;IAAC,GAAE,IAAE,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD;IAAI,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,oUAAA,CAAA,UAAC,CAAC,QAAQ,EAAC,MAAK,KAAG,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,qTAAA,CAAA,SAAC,EAAC;QAAC,IAAG;QAAS,MAAK;QAAS,+BAA8B,CAAC;QAAE,SAAQ;QAAE,UAAS,qTAAA,CAAA,iBAAC,CAAC,SAAS;IAAA,IAAG,EAAE;QAAC,UAAS;QAAE,YAAW;QAAE,YAAW;QAAE,MAAK;IAAW,IAAG,KAAG,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,qTAAA,CAAA,SAAC,EAAC;QAAC,IAAG;QAAS,MAAK;QAAS,+BAA8B,CAAC;QAAE,SAAQ;QAAE,UAAS,qTAAA,CAAA,iBAAC,CAAC,SAAS;IAAA;AAAG;AAAC,IAAI,IAAE,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,IAAG,KAAG,OAAO,MAAM,CAAC,GAAE;IAAC,UAAS;AAAC;AAAG,SAAS,GAAG,IAAE,CAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,wUAAA,CAAA,UAAC,CAAC,KAAK;IAAI,OAAO,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAC,CAAC,EAAE,EAAC,CAAC,EAAE;QAAI,MAAI,CAAC,KAAG,MAAI,CAAC,KAAG,CAAA,GAAA,yTAAA,CAAA,YAAC,AAAD,EAAE;YAAK,EAAE,OAAO,CAAC,MAAM,CAAC;QAAE,IAAG,MAAI,CAAC,KAAG,MAAI,CAAC,KAAG,CAAC,EAAE,OAAO,GAAC,wUAAA,CAAA,UAAC,CAAC,KAAK,EAAE;IAAC,GAAE;QAAC;QAAE,wUAAA,CAAA,UAAC;QAAC;KAAE,GAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAI;QAAE,OAAM,CAAC,IAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA,IAAG,KAAG,QAAM,EAAE,WAAW,CAAC,KAAG,OAAK,IAAE;IAAI;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,EAAC,eAAc,CAAC,EAAC;IAAE,IAAI,IAAE,CAAC,CAAC,CAAC,IAAE,CAAC,GAAE,IAAE,GAAG;IAAG,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,KAAG,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,MAAI,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,KAAG,CAAA,GAAA,+TAAA,CAAA,eAAC,AAAD,EAAE;IAAI,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,gUAAA,CAAA,eAAC,AAAD,EAAE;QAAK,KAAG,CAAA,GAAA,+TAAA,CAAA,eAAC,AAAD,EAAE;IAAI;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,EAAC,eAAc,CAAC,EAAC,WAAU,CAAC,EAAC,cAAa,CAAC,EAAC,sBAAqB,CAAC,EAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,qUAAA,CAAA,gBAAC,AAAD,EAAE,CAAC,CAAC,CAAC,IAAE,CAAC,GAAE,6BAA4B,IAAE,CAAA,GAAA,gUAAA,CAAA,eAAC,AAAD;IAAI,OAAO,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAG,MAAI,GAAE;QAAO,IAAG,CAAC,GAAE;YAAC,KAAG,QAAM,EAAE,OAAO,IAAE,CAAA,GAAA,+TAAA,CAAA,eAAC,AAAD,EAAE,EAAE,OAAO;YAAE;QAAM;QAAC,IAAI,IAAE,EAAE,OAAO;QAAC,KAAG,CAAA,GAAA,yTAAA,CAAA,YAAC,AAAD,EAAE;YAAK,IAAG,CAAC,EAAE,OAAO,EAAC;YAAO,IAAI,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa;YAAC,IAAG,KAAG,QAAM,EAAE,OAAO,EAAC;gBAAC,IAAG,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,MAAI,GAAE;oBAAC,EAAE,OAAO,GAAC;oBAAE;gBAAM;YAAC,OAAM,IAAG,EAAE,QAAQ,CAAC,IAAG;gBAAC,EAAE,OAAO,GAAC;gBAAE;YAAM;YAAC,IAAG,KAAG,QAAM,EAAE,OAAO,EAAC,CAAA,GAAA,+TAAA,CAAA,eAAC,AAAD,EAAE,EAAE,OAAO;iBAAM;gBAAC,IAAG,IAAE,IAAG;oBAAC,IAAG,CAAA,GAAA,+TAAA,CAAA,UAAC,AAAD,EAAE,GAAE,+TAAA,CAAA,QAAC,CAAC,KAAK,GAAC,+TAAA,CAAA,QAAC,CAAC,SAAS,MAAI,+TAAA,CAAA,cAAC,CAAC,KAAK,EAAC;gBAAM,OAAM,IAAG,CAAA,GAAA,+TAAA,CAAA,UAAC,AAAD,EAAE,GAAE,+TAAA,CAAA,QAAC,CAAC,KAAK,MAAI,+TAAA,CAAA,cAAC,CAAC,KAAK,EAAC;gBAAO,IAAG,KAAG,QAAM,EAAE,OAAO,IAAE,CAAC,CAAA,GAAA,+TAAA,CAAA,eAAC,AAAD,EAAE,EAAE,OAAO,GAAE,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,MAAI,EAAE,OAAO,GAAE;gBAAO,QAAQ,IAAI,CAAC;YAA2D;YAAC,EAAE,OAAO,GAAC,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa;QAAA;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE,GAAE;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,EAAC,eAAc,CAAC,EAAC,WAAU,CAAC,EAAC,YAAW,CAAC,EAAC,uBAAsB,CAAC,EAAC;IAAE,IAAI,IAAE,CAAA,GAAA,gUAAA,CAAA,eAAC,AAAD,KAAI,IAAE,CAAC,CAAC,CAAC,IAAE,CAAC;IAAE,CAAA,GAAA,oUAAA,CAAA,mBAAC,AAAD,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,WAAW,EAAC,SAAQ,CAAA;QAAI,IAAG,CAAC,KAAG,CAAC,EAAE,OAAO,EAAC;QAAO,IAAI,IAAE,EAAE;QAAG,CAAA,GAAA,+SAAA,CAAA,gBAAe,AAAD,EAAE,EAAE,OAAO,KAAG,EAAE,GAAG,CAAC,EAAE,OAAO;QAAE,IAAI,IAAE,EAAE,OAAO;QAAC,IAAG,CAAC,GAAE;QAAO,IAAI,IAAE,EAAE,MAAM;QAAC,CAAA,GAAA,+SAAA,CAAA,gBAAe,AAAD,EAAE,KAAG,EAAE,GAAE,KAAG,CAAC,EAAE,OAAO,GAAC,GAAE,CAAA,GAAA,+TAAA,CAAA,eAAC,AAAD,EAAE,EAAE,IAAE,CAAC,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,CAAA,GAAA,+TAAA,CAAA,eAAC,AAAD,EAAE,EAAE,IAAE,CAAA,GAAA,+TAAA,CAAA,eAAC,AAAD,EAAE,EAAE,OAAO;IAAC,GAAE,CAAC;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,KAAI,IAAI,KAAK,EAAE,IAAG,EAAE,QAAQ,CAAC,IAAG,OAAM,CAAC;IAAE,OAAM,CAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2236, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/portal/portal.js"], "sourcesContent": ["\"use client\";import T,{Fragment as E,createContext as A,useContext as d,useEffect as G,useMemo as x,useRef as L,useState as c}from\"react\";import{createPortal as h}from\"react-dom\";import{useEvent as _}from'../../hooks/use-event.js';import{useIsoMorphicEffect as C}from'../../hooks/use-iso-morphic-effect.js';import{useOnUnmount as F}from'../../hooks/use-on-unmount.js';import{useOwnerDocument as U}from'../../hooks/use-owner.js';import{useServerHandoffComplete as N}from'../../hooks/use-server-handoff-complete.js';import{optionalRef as S,useSyncRefs as m}from'../../hooks/use-sync-refs.js';import{usePortalRoot as W}from'../../internal/portal-force-root.js';import*as j from'../../utils/dom.js';import{env as v}from'../../utils/env.js';import{forwardRefWithAs as y,useRender as R}from'../../utils/render.js';function I(e){let l=W(),o=d(H),[r,u]=c(()=>{var i;if(!l&&o!==null)return(i=o.current)!=null?i:null;if(v.isServer)return null;let t=e==null?void 0:e.getElementById(\"headlessui-portal-root\");if(t)return t;if(e===null)return null;let a=e.createElement(\"div\");return a.setAttribute(\"id\",\"headlessui-portal-root\"),e.body.appendChild(a)});return G(()=>{r!==null&&(e!=null&&e.body.contains(r)||e==null||e.body.appendChild(r))},[r,e]),G(()=>{l||o!==null&&u(o.current)},[o,u,l]),r}let M=E,D=y(function(l,o){let{ownerDocument:r=null,...u}=l,t=L(null),a=m(S(s=>{t.current=s}),o),i=U(t),f=r!=null?r:i,p=I(f),[n]=c(()=>{var s;return v.isServer?null:(s=f==null?void 0:f.createElement(\"div\"))!=null?s:null}),P=d(g),O=N();C(()=>{!p||!n||p.contains(n)||(n.setAttribute(\"data-headlessui-portal\",\"\"),p.appendChild(n))},[p,n]),C(()=>{if(n&&P)return P.register(n)},[P,n]),F(()=>{var s;!p||!n||(j.isNode(n)&&p.contains(n)&&p.removeChild(n),p.childNodes.length<=0&&((s=p.parentElement)==null||s.removeChild(p)))});let b=R();return O?!p||!n?null:h(b({ourProps:{ref:a},theirProps:u,slot:{},defaultTag:M,name:\"Portal\"}),n):null});function J(e,l){let o=m(l),{enabled:r=!0,ownerDocument:u,...t}=e,a=R();return r?T.createElement(D,{...t,ownerDocument:u,ref:o}):a({ourProps:{ref:o},theirProps:t,slot:{},defaultTag:M,name:\"Portal\"})}let X=E,H=A(null);function k(e,l){let{target:o,...r}=e,t={ref:m(l)},a=R();return T.createElement(H.Provider,{value:o},a({ourProps:t,theirProps:r,defaultTag:X,name:\"Popover.Group\"}))}let g=A(null);function oe(){let e=d(g),l=L([]),o=_(t=>(l.current.push(t),e&&e.register(t),()=>r(t))),r=_(t=>{let a=l.current.indexOf(t);a!==-1&&l.current.splice(a,1),e&&e.unregister(t)}),u=x(()=>({register:o,unregister:r,portals:l}),[o,r,l]);return[l,x(()=>function({children:a}){return T.createElement(g.Provider,{value:u},a)},[u])]}let B=y(J),q=y(k),ne=Object.assign(B,{Group:q});export{ne as Portal,q as PortalGroup,oe as useNestedPortals};\n"], "names": [], "mappings": ";;;;;AAAa;AAA6H;AAAyC;AAAoD;AAA4E;AAA6D;AAA4D;AAAsF;AAA4E;AAAoE;AAAqC;AAAyC;AAAhuB;;;;;;;;;;;;;AAAwyB,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,sUAAA,CAAA,gBAAC,AAAD,KAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAI;QAAE,IAAG,CAAC,KAAG,MAAI,MAAK,OAAM,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,IAAE;QAAK,IAAG,+SAAA,CAAA,MAAC,CAAC,QAAQ,EAAC,OAAO;QAAK,IAAI,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,cAAc,CAAC;QAA0B,IAAG,GAAE,OAAO;QAAE,IAAG,MAAI,MAAK,OAAO;QAAK,IAAI,IAAE,EAAE,aAAa,CAAC;QAAO,OAAO,EAAE,YAAY,CAAC,MAAK,2BAA0B,EAAE,IAAI,CAAC,WAAW,CAAC;IAAE;IAAG,OAAO,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE;QAAK,MAAI,QAAM,CAAC,KAAG,QAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAI,KAAG,QAAM,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE;IAAC,GAAE;QAAC;QAAE;KAAE,GAAE,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE;QAAK,KAAG,MAAI,QAAM,EAAE,EAAE,OAAO;IAAC,GAAE;QAAC;QAAE;QAAE;KAAE,GAAE;AAAC;AAAC,IAAI,IAAE,oUAAA,CAAA,WAAC,EAAC,IAAE,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,SAAS,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,eAAc,IAAE,IAAI,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE,CAAA;QAAI,EAAE,OAAO,GAAC;IAAC,IAAG,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,mBAAC,AAAD,EAAE,IAAG,IAAE,KAAG,OAAK,IAAE,GAAE,IAAE,EAAE,IAAG,CAAC,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAI;QAAE,OAAO,+SAAA,CAAA,MAAC,CAAC,QAAQ,GAAC,OAAK,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,CAAC,MAAM,KAAG,OAAK,IAAE;IAAI,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,gVAAA,CAAA,2BAAC,AAAD;IAAI,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,CAAC,KAAG,CAAC,KAAG,EAAE,QAAQ,CAAC,MAAI,CAAC,EAAE,YAAY,CAAC,0BAAyB,KAAI,EAAE,WAAW,CAAC,EAAE;IAAC,GAAE;QAAC;QAAE;KAAE,GAAE,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,KAAG,GAAE,OAAO,EAAE,QAAQ,CAAC;IAAE,GAAE;QAAC;QAAE;KAAE,GAAE,CAAA,GAAA,gUAAA,CAAA,eAAC,AAAD,EAAE;QAAK,IAAI;QAAE,CAAC,KAAG,CAAC,KAAG,CAAC,CAAA,GAAA,+SAAA,CAAA,SAAQ,AAAD,EAAE,MAAI,EAAE,QAAQ,CAAC,MAAI,EAAE,WAAW,CAAC,IAAG,EAAE,UAAU,CAAC,MAAM,IAAE,KAAG,CAAC,CAAC,IAAE,EAAE,aAAa,KAAG,QAAM,EAAE,WAAW,CAAC,EAAE,CAAC;IAAC;IAAG,IAAI,IAAE,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD;IAAI,OAAO,IAAE,CAAC,KAAG,CAAC,IAAE,OAAK,CAAA,GAAA,2UAAA,CAAA,eAAC,AAAD,EAAE,EAAE;QAAC,UAAS;YAAC,KAAI;QAAC;QAAE,YAAW;QAAE,MAAK,CAAC;QAAE,YAAW;QAAE,MAAK;IAAQ,IAAG,KAAG;AAAI;AAAG,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE,IAAG,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,eAAc,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD;IAAI,OAAO,IAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE;QAAC,GAAG,CAAC;QAAC,eAAc;QAAE,KAAI;IAAC,KAAG,EAAE;QAAC,UAAS;YAAC,KAAI;QAAC;QAAE,YAAW;QAAE,MAAK,CAAC;QAAE,YAAW;QAAE,MAAK;IAAQ;AAAE;AAAC,IAAI,IAAE,oUAAA,CAAA,WAAC,EAAC,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,QAAO,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE;QAAC,KAAI,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE;IAAE,GAAE,IAAE,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD;IAAI,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,EAAE;QAAC,UAAS;QAAE,YAAW;QAAE,YAAW;QAAE,MAAK;IAAe;AAAG;AAAC,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,SAAS;IAAK,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,EAAE,GAAE,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,IAAG,KAAG,EAAE,QAAQ,CAAC,IAAG,IAAI,EAAE,EAAE,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,EAAE,OAAO,CAAC,OAAO,CAAC;QAAG,MAAI,CAAC,KAAG,EAAE,OAAO,CAAC,MAAM,CAAC,GAAE,IAAG,KAAG,EAAE,UAAU,CAAC;IAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,UAAS;YAAE,YAAW;YAAE,SAAQ;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;KAAE;IAAE,OAAM;QAAC;QAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,SAAS,EAAC,UAAS,CAAC,EAAC;gBAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;oBAAC,OAAM;gBAAC,GAAE;YAAE,GAAE;YAAC;SAAE;KAAE;AAAA;AAAC,IAAI,IAAE,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,IAAG,KAAG,OAAO,MAAM,CAAC,GAAE;IAAC,OAAM;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2387, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-flags.js"], "sourcesContent": ["import{useCallback as r,useState as b}from\"react\";function c(u=0){let[t,l]=b(u),g=r(e=>l(e),[t]),s=r(e=>l(a=>a|e),[t]),m=r(e=>(t&e)===e,[t]),n=r(e=>l(a=>a&~e),[l]),F=r(e=>l(a=>a^e),[l]);return{flags:t,setFlag:g,addFlag:s,hasFlag:m,removeFlag:n,toggleFlag:F}}export{c as useFlags};\n"], "names": [], "mappings": ";;;AAAA;;AAAkD,SAAS,EAAE,IAAE,CAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,IAAG;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,CAAA,IAAG,IAAE,IAAG;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,CAAC,IAAE,CAAC,MAAI,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,CAAA,IAAG,IAAE,CAAC,IAAG;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,CAAA,IAAG,IAAE,IAAG;QAAC;KAAE;IAAE,OAAM;QAAC,OAAM;QAAE,SAAQ;QAAE,SAAQ;QAAE,SAAQ;QAAE,YAAW;QAAE,YAAW;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2420, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-transition.js"], "sourcesContent": ["var T,b;import{useRef as c,useState as S}from\"react\";import{disposables as m}from'../utils/disposables.js';import{useDisposables as g}from'./use-disposables.js';import{useFlags as y}from'./use-flags.js';import{useIsoMorphicEffect as A}from'./use-iso-morphic-effect.js';typeof process!=\"undefined\"&&typeof globalThis!=\"undefined\"&&typeof Element!=\"undefined\"&&((T=process==null?void 0:process.env)==null?void 0:T[\"NODE_ENV\"])===\"test\"&&typeof((b=Element==null?void 0:Element.prototype)==null?void 0:b.getAnimations)==\"undefined\"&&(Element.prototype.getAnimations=function(){return console.warn([\"Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\",\"Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\",\"\",\"Example usage:\",\"```js\",\"import { mockAnimationsApi } from 'jsdom-testing-mocks'\",\"mockAnimationsApi()\",\"```\"].join(`\n`)),[]});var L=(r=>(r[r.None=0]=\"None\",r[r.Closed=1]=\"Closed\",r[r.Enter=2]=\"Enter\",r[r.Leave=4]=\"Leave\",r))(L||{});function R(t){let n={};for(let e in t)t[e]===!0&&(n[`data-${e}`]=\"\");return n}function x(t,n,e,i){let[r,o]=S(e),{hasFlag:s,addFlag:a,removeFlag:l}=y(t&&r?3:0),u=c(!1),f=c(!1),E=g();return A(()=>{var d;if(t){if(e&&o(!0),!n){e&&a(3);return}return(d=i==null?void 0:i.start)==null||d.call(i,e),C(n,{inFlight:u,prepare(){f.current?f.current=!1:f.current=u.current,u.current=!0,!f.current&&(e?(a(3),l(4)):(a(4),l(2)))},run(){f.current?e?(l(3),a(4)):(l(4),a(3)):e?l(1):a(1)},done(){var p;f.current&&typeof n.getAnimations==\"function\"&&n.getAnimations().length>0||(u.current=!1,l(7),e||o(!1),(p=i==null?void 0:i.end)==null||p.call(i,e))}})}},[t,e,n,E]),t?[r,{closed:s(1),enter:s(2),leave:s(4),transition:s(2)||s(4)}]:[e,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function C(t,{prepare:n,run:e,done:i,inFlight:r}){let o=m();return j(t,{prepare:n,inFlight:r}),o.nextFrame(()=>{e(),o.requestAnimationFrame(()=>{o.add(M(t,i))})}),o.dispose}function M(t,n){var o,s;let e=m();if(!t)return e.dispose;let i=!1;e.add(()=>{i=!0});let r=(s=(o=t.getAnimations)==null?void 0:o.call(t).filter(a=>a instanceof CSSTransition))!=null?s:[];return r.length===0?(n(),e.dispose):(Promise.allSettled(r.map(a=>a.finished)).then(()=>{i||n()}),e.dispose)}function j(t,{inFlight:n,prepare:e}){if(n!=null&&n.current){e();return}let i=t.style.transition;t.style.transition=\"none\",e(),t.offsetHeight,t.style.transition=i}export{R as transitionDataAttributes,x as useTransition};\n"], "names": [], "mappings": ";;;;AAAoR;AAA5Q;AAA6C;AAAsD;AAAsD;AAA0C;AAA3M,IAAI,GAAE;;;;;;AAAuQ,OAAO,uUAAA,CAAA,UAAO,IAAE,eAAa,OAAO,cAAY,eAAa,OAAO,WAAS,eAAa,CAAC,CAAC,IAAE,uUAAA,CAAA,UAAO,IAAE,OAAK,KAAK,IAAE,uUAAA,CAAA,UAAO,CAAC,GAAG,KAAG,OAAK,KAAK,IAAE,CAAC,CAAC,WAAW,MAAI,UAAQ,OAAM,CAAC,CAAC,IAAE,WAAS,OAAK,KAAK,IAAE,QAAQ,SAAS,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,KAAG,eAAa,CAAC,QAAQ,SAAS,CAAC,aAAa,GAAC;IAAW,OAAO,QAAQ,IAAI,CAAC;QAAC;QAA+E;QAA0F;QAAG;QAAiB;QAAQ;QAA0D;QAAsB;KAAM,CAAC,IAAI,CAAC,CAAC;AACp3B,CAAC,IAAG,EAAE;AAAA,CAAC;AAAE,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAC;IAAE,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,KAAG,CAAC,KAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,GAAC,EAAE;IAAE,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,IAAG,EAAC,SAAQ,CAAC,EAAC,SAAQ,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,KAAG,IAAE,IAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,CAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,CAAC,IAAG,IAAE,CAAA,GAAA,8TAAA,CAAA,iBAAC,AAAD;IAAI,OAAO,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAI;QAAE,IAAG,GAAE;YAAC,IAAG,KAAG,EAAE,CAAC,IAAG,CAAC,GAAE;gBAAC,KAAG,EAAE;gBAAG;YAAM;YAAC,OAAM,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,KAAK,KAAG,QAAM,EAAE,IAAI,CAAC,GAAE,IAAG,EAAE,GAAE;gBAAC,UAAS;gBAAE;oBAAU,EAAE,OAAO,GAAC,EAAE,OAAO,GAAC,CAAC,IAAE,EAAE,OAAO,GAAC,EAAE,OAAO,EAAC,EAAE,OAAO,GAAC,CAAC,GAAE,CAAC,EAAE,OAAO,IAAE,CAAC,IAAE,CAAC,EAAE,IAAG,EAAE,EAAE,IAAE,CAAC,EAAE,IAAG,EAAE,EAAE,CAAC;gBAAC;gBAAE;oBAAM,EAAE,OAAO,GAAC,IAAE,CAAC,EAAE,IAAG,EAAE,EAAE,IAAE,CAAC,EAAE,IAAG,EAAE,EAAE,IAAE,IAAE,EAAE,KAAG,EAAE;gBAAE;gBAAE;oBAAO,IAAI;oBAAE,EAAE,OAAO,IAAE,OAAO,EAAE,aAAa,IAAE,cAAY,EAAE,aAAa,GAAG,MAAM,GAAC,KAAG,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE,EAAE,IAAG,KAAG,EAAE,CAAC,IAAG,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,GAAG,KAAG,QAAM,EAAE,IAAI,CAAC,GAAE,EAAE;gBAAC;YAAC;QAAE;IAAC,GAAE;QAAC;QAAE;QAAE;QAAE;KAAE,GAAE,IAAE;QAAC;QAAE;YAAC,QAAO,EAAE;YAAG,OAAM,EAAE;YAAG,OAAM,EAAE;YAAG,YAAW,EAAE,MAAI,EAAE;QAAE;KAAE,GAAC;QAAC;QAAE;YAAC,QAAO,KAAK;YAAE,OAAM,KAAK;YAAE,OAAM,KAAK;YAAE,YAAW,KAAK;QAAC;KAAE;AAAA;AAAC,SAAS,EAAE,CAAC,EAAC,EAAC,SAAQ,CAAC,EAAC,KAAI,CAAC,EAAC,MAAK,CAAC,EAAC,UAAS,CAAC,EAAC;IAAE,IAAI,IAAE,CAAA,GAAA,uTAAA,CAAA,cAAC,AAAD;IAAI,OAAO,EAAE,GAAE;QAAC,SAAQ;QAAE,UAAS;IAAC,IAAG,EAAE,SAAS,CAAC;QAAK,KAAI,EAAE,qBAAqB,CAAC;YAAK,EAAE,GAAG,CAAC,EAAE,GAAE;QAAG;IAAE,IAAG,EAAE,OAAO;AAAA;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE;IAAE,IAAI,IAAE,CAAA,GAAA,uTAAA,CAAA,cAAC,AAAD;IAAI,IAAG,CAAC,GAAE,OAAO,EAAE,OAAO;IAAC,IAAI,IAAE,CAAC;IAAE,EAAE,GAAG,CAAC;QAAK,IAAE,CAAC;IAAC;IAAG,IAAI,IAAE,CAAC,IAAE,CAAC,IAAE,EAAE,aAAa,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,CAAA,IAAG,aAAa,cAAc,KAAG,OAAK,IAAE,EAAE;IAAC,OAAO,EAAE,MAAM,KAAG,IAAE,CAAC,KAAI,EAAE,OAAO,IAAE,CAAC,QAAQ,UAAU,CAAC,EAAE,GAAG,CAAC,CAAA,IAAG,EAAE,QAAQ,GAAG,IAAI,CAAC;QAAK,KAAG;IAAG,IAAG,EAAE,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,EAAC,UAAS,CAAC,EAAC,SAAQ,CAAC,EAAC;IAAE,IAAG,KAAG,QAAM,EAAE,OAAO,EAAC;QAAC;QAAI;IAAM;IAAC,IAAI,IAAE,EAAE,KAAK,CAAC,UAAU;IAAC,EAAE,KAAK,CAAC,UAAU,GAAC,QAAO,KAAI,EAAE,YAAY,EAAC,EAAE,KAAK,CAAC,UAAU,GAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2540, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/transition/transition.js"], "sourcesContent": ["\"use client\";import c,{Fragment as O,createContext as ne,useContext as q,useEffect as ge,useMemo as ie,useRef as b,useState as V}from\"react\";import{useDisposables as ve}from'../../hooks/use-disposables.js';import{useEvent as E}from'../../hooks/use-event.js';import{useIsMounted as be}from'../../hooks/use-is-mounted.js';import{useIsoMorphicEffect as D}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as Ee}from'../../hooks/use-latest-value.js';import{useServerHandoffComplete as re}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as oe}from'../../hooks/use-sync-refs.js';import{transitionDataAttributes as Se,useTransition as Re}from'../../hooks/use-transition.js';import{OpenClosedProvider as ye,State as x,useOpenClosed as se}from'../../internal/open-closed.js';import{classNames as Pe}from'../../utils/class-names.js';import{match as le}from'../../utils/match.js';import{RenderFeatures as xe,RenderStrategy as P,compact as Ne,forwardRefWithAs as J,useRender as ae}from'../../utils/render.js';function ue(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||((t=e.as)!=null?t:de)!==O||c.Children.count(e.children)===1}let w=ne(null);w.displayName=\"TransitionContext\";var _e=(n=>(n.Visible=\"visible\",n.Hidden=\"hidden\",n))(_e||{});function De(){let e=q(w);if(e===null)throw new Error(\"A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.\");return e}function He(){let e=q(M);if(e===null)throw new Error(\"A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.\");return e}let M=ne(null);M.displayName=\"NestingContext\";function U(e){return\"children\"in e?U(e.children):e.current.filter(({el:t})=>t.current!==null).filter(({state:t})=>t===\"visible\").length>0}function Te(e,t){let n=Ee(e),l=b([]),S=be(),R=ve(),d=E((o,i=P.Hidden)=>{let a=l.current.findIndex(({el:s})=>s===o);a!==-1&&(le(i,{[P.Unmount](){l.current.splice(a,1)},[P.Hidden](){l.current[a].state=\"hidden\"}}),R.microTask(()=>{var s;!U(l)&&S.current&&((s=n.current)==null||s.call(n))}))}),y=E(o=>{let i=l.current.find(({el:a})=>a===o);return i?i.state!==\"visible\"&&(i.state=\"visible\"):l.current.push({el:o,state:\"visible\"}),()=>d(o,P.Unmount)}),C=b([]),p=b(Promise.resolve()),h=b({enter:[],leave:[]}),g=E((o,i,a)=>{C.current.splice(0),t&&(t.chains.current[i]=t.chains.current[i].filter(([s])=>s!==o)),t==null||t.chains.current[i].push([o,new Promise(s=>{C.current.push(s)})]),t==null||t.chains.current[i].push([o,new Promise(s=>{Promise.all(h.current[i].map(([r,f])=>f)).then(()=>s())})]),i===\"enter\"?p.current=p.current.then(()=>t==null?void 0:t.wait.current).then(()=>a(i)):a(i)}),v=E((o,i,a)=>{Promise.all(h.current[i].splice(0).map(([s,r])=>r)).then(()=>{var s;(s=C.current.shift())==null||s()}).then(()=>a(i))});return ie(()=>({children:l,register:y,unregister:d,onStart:g,onStop:v,wait:p,chains:h}),[y,d,l,g,v,h,p])}let de=O,fe=xe.RenderStrategy;function Ae(e,t){var ee,te;let{transition:n=!0,beforeEnter:l,afterEnter:S,beforeLeave:R,afterLeave:d,enter:y,enterFrom:C,enterTo:p,entered:h,leave:g,leaveFrom:v,leaveTo:o,...i}=e,[a,s]=V(null),r=b(null),f=ue(e),j=oe(...f?[r,t,s]:t===null?[]:[t]),H=(ee=i.unmount)==null||ee?P.Unmount:P.Hidden,{show:u,appear:z,initial:K}=De(),[m,G]=V(u?\"visible\":\"hidden\"),Q=He(),{register:A,unregister:I}=Q;D(()=>A(r),[A,r]),D(()=>{if(H===P.Hidden&&r.current){if(u&&m!==\"visible\"){G(\"visible\");return}return le(m,{[\"hidden\"]:()=>I(r),[\"visible\"]:()=>A(r)})}},[m,r,A,I,u,H]);let B=re();D(()=>{if(f&&B&&m===\"visible\"&&r.current===null)throw new Error(\"Did you forget to passthrough the `ref` to the actual DOM node?\")},[r,m,B,f]);let ce=K&&!z,Y=z&&u&&K,W=b(!1),L=Te(()=>{W.current||(G(\"hidden\"),I(r))},Q),Z=E(k=>{W.current=!0;let F=k?\"enter\":\"leave\";L.onStart(r,F,_=>{_===\"enter\"?l==null||l():_===\"leave\"&&(R==null||R())})}),$=E(k=>{let F=k?\"enter\":\"leave\";W.current=!1,L.onStop(r,F,_=>{_===\"enter\"?S==null||S():_===\"leave\"&&(d==null||d())}),F===\"leave\"&&!U(L)&&(G(\"hidden\"),I(r))});ge(()=>{f&&n||(Z(u),$(u))},[u,f,n]);let pe=(()=>!(!n||!f||!B||ce))(),[,T]=Re(pe,a,u,{start:Z,end:$}),Ce=Ne({ref:j,className:((te=Pe(i.className,Y&&y,Y&&C,T.enter&&y,T.enter&&T.closed&&C,T.enter&&!T.closed&&p,T.leave&&g,T.leave&&!T.closed&&v,T.leave&&T.closed&&o,!T.transition&&u&&h))==null?void 0:te.trim())||void 0,...Se(T)}),N=0;m===\"visible\"&&(N|=x.Open),m===\"hidden\"&&(N|=x.Closed),u&&m===\"hidden\"&&(N|=x.Opening),!u&&m===\"visible\"&&(N|=x.Closing);let he=ae();return c.createElement(M.Provider,{value:L},c.createElement(ye,{value:N},he({ourProps:Ce,theirProps:i,defaultTag:de,features:fe,visible:m===\"visible\",name:\"Transition.Child\"})))}function Ie(e,t){let{show:n,appear:l=!1,unmount:S=!0,...R}=e,d=b(null),y=ue(e),C=oe(...y?[d,t]:t===null?[]:[t]);re();let p=se();if(n===void 0&&p!==null&&(n=(p&x.Open)===x.Open),n===void 0)throw new Error(\"A <Transition /> is used but it is missing a `show={true | false}` prop.\");let[h,g]=V(n?\"visible\":\"hidden\"),v=Te(()=>{n||g(\"hidden\")}),[o,i]=V(!0),a=b([n]);D(()=>{o!==!1&&a.current[a.current.length-1]!==n&&(a.current.push(n),i(!1))},[a,n]);let s=ie(()=>({show:n,appear:l,initial:o}),[n,l,o]);D(()=>{n?g(\"visible\"):!U(v)&&d.current!==null&&g(\"hidden\")},[n,v]);let r={unmount:S},f=E(()=>{var u;o&&i(!1),(u=e.beforeEnter)==null||u.call(e)}),j=E(()=>{var u;o&&i(!1),(u=e.beforeLeave)==null||u.call(e)}),H=ae();return c.createElement(M.Provider,{value:v},c.createElement(w.Provider,{value:s},H({ourProps:{...r,as:O,children:c.createElement(me,{ref:C,...r,...R,beforeEnter:f,beforeLeave:j})},theirProps:{},defaultTag:O,features:fe,visible:h===\"visible\",name:\"Transition\"})))}function Le(e,t){let n=q(w)!==null,l=se()!==null;return c.createElement(c.Fragment,null,!n&&l?c.createElement(X,{ref:t,...e}):c.createElement(me,{ref:t,...e}))}let X=J(Ie),me=J(Ae),Fe=J(Le),ze=Object.assign(X,{Child:Fe,Root:X});export{ze as Transition,Fe as TransitionChild};\n"], "names": [], "mappings": ";;;;AAAa;AAAgI;AAAiE;AAAoD;AAA8D;AAA4E;AAAkE;AAAuF;AAA4D;AAA8F;AAAmG;AAAyD;AAA8C;AAAz4B;;;;;;;;;;;;;;AAAygC,SAAS,GAAG,CAAC;IAAE,IAAI;IAAE,OAAM,CAAC,CAAC,CAAC,EAAE,KAAK,IAAE,EAAE,SAAS,IAAE,EAAE,OAAO,IAAE,EAAE,KAAK,IAAE,EAAE,SAAS,IAAE,EAAE,OAAO,KAAG,CAAC,CAAC,IAAE,EAAE,EAAE,KAAG,OAAK,IAAE,EAAE,MAAI,oUAAA,CAAA,WAAC,IAAE,oUAAA,CAAA,UAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,QAAQ,MAAI;AAAC;AAAC,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAE,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAoB,IAAI,KAAG,CAAC,CAAA,IAAG,CAAC,EAAE,OAAO,GAAC,WAAU,EAAE,MAAM,GAAC,UAAS,CAAC,CAAC,EAAE,MAAI,CAAC;AAAG,SAAS;IAAK,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;IAAG,IAAG,MAAI,MAAK,MAAM,IAAI,MAAM;IAAoG,OAAO;AAAC;AAAC,SAAS;IAAK,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;IAAG,IAAG,MAAI,MAAK,MAAM,IAAI,MAAM;IAAoG,OAAO;AAAC;AAAC,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAE,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAiB,SAAS,EAAE,CAAC;IAAE,OAAM,cAAa,IAAE,EAAE,EAAE,QAAQ,IAAE,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,EAAC,IAAG,CAAC,EAAC,GAAG,EAAE,OAAO,KAAG,MAAM,MAAM,CAAC,CAAC,EAAC,OAAM,CAAC,EAAC,GAAG,MAAI,WAAW,MAAM,GAAC;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,EAAE,GAAE,IAAE,CAAA,GAAA,gUAAA,CAAA,eAAE,AAAD,KAAI,IAAE,CAAA,GAAA,8TAAA,CAAA,iBAAE,AAAD,KAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAC,GAAE,IAAE,kTAAA,CAAA,iBAAC,CAAC,MAAM;QAAI,IAAI,IAAE,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAC,IAAG,CAAC,EAAC,GAAG,MAAI;QAAG,MAAI,CAAC,KAAG,CAAC,CAAA,GAAA,iTAAA,CAAA,QAAE,AAAD,EAAE,GAAE;YAAC,CAAC,kTAAA,CAAA,iBAAC,CAAC,OAAO,CAAC;gBAAG,EAAE,OAAO,CAAC,MAAM,CAAC,GAAE;YAAE;YAAE,CAAC,kTAAA,CAAA,iBAAC,CAAC,MAAM,CAAC;gBAAG,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,GAAC;YAAQ;QAAC,IAAG,EAAE,SAAS,CAAC;YAAK,IAAI;YAAE,CAAC,EAAE,MAAI,EAAE,OAAO,IAAE,CAAC,CAAC,IAAE,EAAE,OAAO,KAAG,QAAM,EAAE,IAAI,CAAC,EAAE;QAAC,EAAE;IAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,EAAC,IAAG,CAAC,EAAC,GAAG,MAAI;QAAG,OAAO,IAAE,EAAE,KAAK,KAAG,aAAW,CAAC,EAAE,KAAK,GAAC,SAAS,IAAE,EAAE,OAAO,CAAC,IAAI,CAAC;YAAC,IAAG;YAAE,OAAM;QAAS,IAAG,IAAI,EAAE,GAAE,kTAAA,CAAA,iBAAC,CAAC,OAAO;IAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,EAAE,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,QAAQ,OAAO,KAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE;QAAC,OAAM,EAAE;QAAC,OAAM,EAAE;IAAA,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAC,GAAE,GAAE;QAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IAAG,KAAG,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,GAAC,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,MAAI,EAAE,GAAE,KAAG,QAAM,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;YAAC;YAAE,IAAI,QAAQ,CAAA;gBAAI,EAAE,OAAO,CAAC,IAAI,CAAC;YAAE;SAAG,GAAE,KAAG,QAAM,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;YAAC;YAAE,IAAI,QAAQ,CAAA;gBAAI,QAAQ,GAAG,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAE,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI;YAAI;SAAG,GAAE,MAAI,UAAQ,EAAE,OAAO,GAAC,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,MAAI,EAAE;IAAE,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAC,GAAE,GAAE;QAAK,QAAQ,GAAG,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAE,EAAE,GAAG,IAAI,IAAI,CAAC;YAAK,IAAI;YAAE,CAAC,IAAE,EAAE,OAAO,CAAC,KAAK,EAAE,KAAG,QAAM;QAAG,GAAG,IAAI,CAAC,IAAI,EAAE;IAAG;IAAG,OAAO,CAAA,GAAA,oUAAA,CAAA,UAAE,AAAD,EAAE,IAAI,CAAC;YAAC,UAAS;YAAE,UAAS;YAAE,YAAW;YAAE,SAAQ;YAAE,QAAO;YAAE,MAAK;YAAE,QAAO;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;AAAC;AAAC,IAAI,KAAG,oUAAA,CAAA,WAAC,EAAC,KAAG,kTAAA,CAAA,iBAAE,CAAC,cAAc;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAG;IAAG,IAAG,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,OAAM,CAAC,EAAC,WAAU,CAAC,EAAC,SAAQ,CAAC,EAAC,SAAQ,CAAC,EAAC,OAAM,CAAC,EAAC,WAAU,CAAC,EAAC,SAAQ,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,GAAG,IAAG,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAE,AAAD,KAAK,IAAE;QAAC;QAAE;QAAE;KAAE,GAAC,MAAI,OAAK,EAAE,GAAC;QAAC;KAAE,GAAE,IAAE,CAAC,KAAG,EAAE,OAAO,KAAG,QAAM,KAAG,kTAAA,CAAA,iBAAC,CAAC,OAAO,GAAC,kTAAA,CAAA,iBAAC,CAAC,MAAM,EAAC,EAAC,MAAK,CAAC,EAAC,QAAO,CAAC,EAAC,SAAQ,CAAC,EAAC,GAAC,MAAK,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,IAAE,YAAU,WAAU,IAAE,MAAK,EAAC,UAAS,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC;IAAE,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE,IAAI,EAAE,IAAG;QAAC;QAAE;KAAE,GAAE,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,MAAI,kTAAA,CAAA,iBAAC,CAAC,MAAM,IAAE,EAAE,OAAO,EAAC;YAAC,IAAG,KAAG,MAAI,WAAU;gBAAC,EAAE;gBAAW;YAAM;YAAC,OAAO,CAAA,GAAA,iTAAA,CAAA,QAAE,AAAD,EAAE,GAAE;gBAAC,CAAC,SAAS,EAAC,IAAI,EAAE;gBAAG,CAAC,UAAU,EAAC,IAAI,EAAE;YAAE;QAAE;IAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,gVAAA,CAAA,2BAAE,AAAD;IAAI,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,KAAG,KAAG,MAAI,aAAW,EAAE,OAAO,KAAG,MAAK,MAAM,IAAI,MAAM;IAAkE,GAAE;QAAC;QAAE;QAAE;QAAE;KAAE;IAAE,IAAI,KAAG,KAAG,CAAC,GAAE,IAAE,KAAG,KAAG,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,CAAC,IAAG,IAAE,GAAG;QAAK,EAAE,OAAO,IAAE,CAAC,EAAE,WAAU,EAAE,EAAE;IAAC,GAAE,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,EAAE,OAAO,GAAC,CAAC;QAAE,IAAI,IAAE,IAAE,UAAQ;QAAQ,EAAE,OAAO,CAAC,GAAE,GAAE,CAAA;YAAI,MAAI,UAAQ,KAAG,QAAM,MAAI,MAAI,WAAS,CAAC,KAAG,QAAM,GAAG;QAAC;IAAE,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,IAAE,UAAQ;QAAQ,EAAE,OAAO,GAAC,CAAC,GAAE,EAAE,MAAM,CAAC,GAAE,GAAE,CAAA;YAAI,MAAI,UAAQ,KAAG,QAAM,MAAI,MAAI,WAAS,CAAC,KAAG,QAAM,GAAG;QAAC,IAAG,MAAI,WAAS,CAAC,EAAE,MAAI,CAAC,EAAE,WAAU,EAAE,EAAE;IAAC;IAAG,CAAA,GAAA,oUAAA,CAAA,YAAE,AAAD,EAAE;QAAK,KAAG,KAAG,CAAC,EAAE,IAAG,EAAE,EAAE;IAAC,GAAE;QAAC;QAAE;QAAE;KAAE;IAAE,IAAI,KAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,EAAE,CAAC,KAAI,GAAE,EAAE,GAAC,CAAA,GAAA,6TAAA,CAAA,gBAAE,AAAD,EAAE,IAAG,GAAE,GAAE;QAAC,OAAM;QAAE,KAAI;IAAC,IAAG,KAAG,CAAA,GAAA,kTAAA,CAAA,UAAE,AAAD,EAAE;QAAC,KAAI;QAAE,WAAU,CAAC,CAAC,KAAG,CAAA,GAAA,0TAAA,CAAA,aAAE,AAAD,EAAE,EAAE,SAAS,EAAC,KAAG,GAAE,KAAG,GAAE,EAAE,KAAK,IAAE,GAAE,EAAE,KAAK,IAAE,EAAE,MAAM,IAAE,GAAE,EAAE,KAAK,IAAE,CAAC,EAAE,MAAM,IAAE,GAAE,EAAE,KAAK,IAAE,GAAE,EAAE,KAAK,IAAE,CAAC,EAAE,MAAM,IAAE,GAAE,EAAE,KAAK,IAAE,EAAE,MAAM,IAAE,GAAE,CAAC,EAAE,UAAU,IAAE,KAAG,EAAE,KAAG,OAAK,KAAK,IAAE,GAAG,IAAI,EAAE,KAAG,KAAK;QAAE,GAAG,CAAA,GAAA,6TAAA,CAAA,2BAAE,AAAD,EAAE,EAAE;IAAA,IAAG,IAAE;IAAE,MAAI,aAAW,CAAC,KAAG,6TAAA,CAAA,QAAC,CAAC,IAAI,GAAE,MAAI,YAAU,CAAC,KAAG,6TAAA,CAAA,QAAC,CAAC,MAAM,GAAE,KAAG,MAAI,YAAU,CAAC,KAAG,6TAAA,CAAA,QAAC,CAAC,OAAO,GAAE,CAAC,KAAG,MAAI,aAAW,CAAC,KAAG,6TAAA,CAAA,QAAC,CAAC,OAAO;IAAE,IAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,YAAE,AAAD;IAAI,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,6TAAA,CAAA,qBAAE,EAAC;QAAC,OAAM;IAAC,GAAE,GAAG;QAAC,UAAS;QAAG,YAAW;QAAE,YAAW;QAAG,UAAS;QAAG,SAAQ,MAAI;QAAU,MAAK;IAAkB;AAAI;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,MAAK,CAAC,EAAC,QAAO,IAAE,CAAC,CAAC,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,GAAG,IAAG,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAE,AAAD,KAAK,IAAE;QAAC;QAAE;KAAE,GAAC,MAAI,OAAK,EAAE,GAAC;QAAC;KAAE;IAAE,CAAA,GAAA,gVAAA,CAAA,2BAAE,AAAD;IAAI,IAAI,IAAE,CAAA,GAAA,6TAAA,CAAA,gBAAE,AAAD;IAAI,IAAG,MAAI,KAAK,KAAG,MAAI,QAAM,CAAC,IAAE,CAAC,IAAE,6TAAA,CAAA,QAAC,CAAC,IAAI,MAAI,6TAAA,CAAA,QAAC,CAAC,IAAI,GAAE,MAAI,KAAK,GAAE,MAAM,IAAI,MAAM;IAA4E,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,IAAE,YAAU,WAAU,IAAE,GAAG;QAAK,KAAG,EAAE;IAAS,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,CAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE;QAAC;KAAE;IAAE,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,MAAI,CAAC,KAAG,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,MAAM,GAAC,EAAE,KAAG,KAAG,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,IAAG,EAAE,CAAC,EAAE;IAAC,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAE,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK;YAAE,QAAO;YAAE,SAAQ;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;KAAE;IAAE,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAE,EAAE,aAAW,CAAC,EAAE,MAAI,EAAE,OAAO,KAAG,QAAM,EAAE;IAAS,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE;QAAC,SAAQ;IAAC,GAAE,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAI;QAAE,KAAG,EAAE,CAAC,IAAG,CAAC,IAAE,EAAE,WAAW,KAAG,QAAM,EAAE,IAAI,CAAC;IAAE,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAI;QAAE,KAAG,EAAE,CAAC,IAAG,CAAC,IAAE,EAAE,WAAW,KAAG,QAAM,EAAE,IAAI,CAAC;IAAE,IAAG,IAAE,CAAA,GAAA,kTAAA,CAAA,YAAE,AAAD;IAAI,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,EAAE;QAAC,UAAS;YAAC,GAAG,CAAC;YAAC,IAAG,oUAAA,CAAA,WAAC;YAAC,UAAS,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,IAAG;gBAAC,KAAI;gBAAE,GAAG,CAAC;gBAAC,GAAG,CAAC;gBAAC,aAAY;gBAAE,aAAY;YAAC;QAAE;QAAE,YAAW,CAAC;QAAE,YAAW,oUAAA,CAAA,WAAC;QAAC,UAAS;QAAG,SAAQ,MAAI;QAAU,MAAK;IAAY;AAAI;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE,OAAK,MAAK,IAAE,CAAA,GAAA,6TAAA,CAAA,gBAAE,AAAD,QAAM;IAAK,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,oUAAA,CAAA,UAAC,CAAC,QAAQ,EAAC,MAAK,CAAC,KAAG,IAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE;QAAC,KAAI;QAAE,GAAG,CAAC;IAAA,KAAG,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,IAAG;QAAC,KAAI;QAAE,GAAG,CAAC;IAAA;AAAG;AAAC,IAAI,IAAE,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,OAAO,MAAM,CAAC,GAAE;IAAC,OAAM;IAAG,MAAK;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2826, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/dialog/dialog.js"], "sourcesContent": ["\"use client\";import l,{Fragment as $,createContext as se,createRef as pe,use<PERSON><PERSON>back as de,useContext as ue,useEffect as fe,useMemo as A,useReducer as Te,useRef as j}from\"react\";import{useEscape as ge}from'../../hooks/use-escape.js';import{useEvent as _}from'../../hooks/use-event.js';import{useId as k}from'../../hooks/use-id.js';import{useInertOthers as ce}from'../../hooks/use-inert-others.js';import{useIsTouchDevice as me}from'../../hooks/use-is-touch-device.js';import{useIsoMorphicEffect as De}from'../../hooks/use-iso-morphic-effect.js';import{useOnDisappear as Pe}from'../../hooks/use-on-disappear.js';import{useOutsideClick as ye}from'../../hooks/use-outside-click.js';import{useOwnerDocument as Ee}from'../../hooks/use-owner.js';import{MainTreeProvider as Y,useMainTreeNode as Ae,useRootContainers as _e}from'../../hooks/use-root-containers.js';import{useScrollLock as Ce}from'../../hooks/use-scroll-lock.js';import{useServerHandoffComplete as Re}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as G}from'../../hooks/use-sync-refs.js';import{CloseProvider as Fe}from'../../internal/close-provider.js';import{ResetOpenClosedProvider as be,State as x,useOpenClosed as J}from'../../internal/open-closed.js';import{ForcePortalRoot as K}from'../../internal/portal-force-root.js';import{stackMachines as ve}from'../../machines/stack-machine.js';import{useSlice as Le}from'../../react-glue.js';import{match as xe}from'../../utils/match.js';import{RenderFeatures as X,forwardRefWithAs as C,useRender as h}from'../../utils/render.js';import{Description as V,useDescriptions as he}from'../description/description.js';import{FocusTrap as Oe,FocusTrapFeatures as R}from'../focus-trap/focus-trap.js';import{Portal as Se,PortalGroup as Ie,useNestedPortals as Me}from'../portal/portal.js';import{Transition as ke,TransitionChild as q}from'../transition/transition.js';var Ge=(o=>(o[o.Open=0]=\"Open\",o[o.Closed=1]=\"Closed\",o))(Ge||{}),we=(t=>(t[t.SetTitleId=0]=\"SetTitleId\",t))(we||{});let Be={[0](e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},w=se(null);w.displayName=\"DialogContext\";function O(e){let t=ue(w);if(t===null){let o=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,O),o}return t}function Ue(e,t){return xe(t.type,Be,e,t)}let z=C(function(t,o){let a=k(),{id:n=`headlessui-dialog-${a}`,open:i,onClose:s,initialFocus:d,role:p=\"dialog\",autoFocus:T=!0,__demoMode:u=!1,unmount:y=!1,...S}=t,F=j(!1);p=function(){return p===\"dialog\"||p===\"alertdialog\"?p:(F.current||(F.current=!0,console.warn(`Invalid role [${p}] passed to <Dialog />. Only \\`dialog\\` and and \\`alertdialog\\` are supported. Using \\`dialog\\` instead.`)),\"dialog\")}();let c=J();i===void 0&&c!==null&&(i=(c&x.Open)===x.Open);let f=j(null),I=G(f,o),b=Ee(f),g=i?0:1,[v,Q]=Te(Ue,{titleId:null,descriptionId:null,panelRef:pe()}),m=_(()=>s(!1)),B=_(r=>Q({type:0,id:r})),D=Re()?g===0:!1,[Z,ee]=Me(),te={get current(){var r;return(r=v.panelRef.current)!=null?r:f.current}},L=Ae(),{resolveContainers:M}=_e({mainTreeNode:L,portals:Z,defaultContainers:[te]}),U=c!==null?(c&x.Closing)===x.Closing:!1;ce(u||U?!1:D,{allowed:_(()=>{var r,W;return[(W=(r=f.current)==null?void 0:r.closest(\"[data-headlessui-portal]\"))!=null?W:null]}),disallowed:_(()=>{var r;return[(r=L==null?void 0:L.closest(\"body > *:not(#headlessui-portal-root)\"))!=null?r:null]})});let P=ve.get(null);De(()=>{if(D)return P.actions.push(n),()=>P.actions.pop(n)},[P,n,D]);let H=Le(P,de(r=>P.selectors.isTop(r,n),[P,n]));ye(H,M,r=>{r.preventDefault(),m()}),ge(H,b==null?void 0:b.defaultView,r=>{r.preventDefault(),r.stopPropagation(),document.activeElement&&\"blur\"in document.activeElement&&typeof document.activeElement.blur==\"function\"&&document.activeElement.blur(),m()}),Ce(u||U?!1:D,b,M),Pe(D,f,m);let[oe,ne]=he(),re=A(()=>[{dialogState:g,close:m,setTitleId:B,unmount:y},v],[g,v,m,B,y]),N=A(()=>({open:g===0}),[g]),le={ref:I,id:n,role:p,tabIndex:-1,\"aria-modal\":u?void 0:g===0?!0:void 0,\"aria-labelledby\":v.titleId,\"aria-describedby\":oe,unmount:y},ae=!me(),E=R.None;D&&!u&&(E|=R.RestoreFocus,E|=R.TabLock,T&&(E|=R.AutoFocus),ae&&(E|=R.InitialFocus));let ie=h();return l.createElement(be,null,l.createElement(K,{force:!0},l.createElement(Se,null,l.createElement(w.Provider,{value:re},l.createElement(Ie,{target:f},l.createElement(K,{force:!1},l.createElement(ne,{slot:N},l.createElement(ee,null,l.createElement(Oe,{initialFocus:d,initialFocusFallback:f,containers:M,features:E},l.createElement(Fe,{value:m},ie({ourProps:le,theirProps:S,slot:N,defaultTag:He,features:Ne,visible:g===0,name:\"Dialog\"})))))))))))}),He=\"div\",Ne=X.RenderStrategy|X.Static;function We(e,t){let{transition:o=!1,open:a,...n}=e,i=J(),s=e.hasOwnProperty(\"open\")||i!==null,d=e.hasOwnProperty(\"onClose\");if(!s&&!d)throw new Error(\"You have to provide an `open` and an `onClose` prop to the `Dialog` component.\");if(!s)throw new Error(\"You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.\");if(!d)throw new Error(\"You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.\");if(!i&&typeof e.open!=\"boolean\")throw new Error(`You provided an \\`open\\` prop to the \\`Dialog\\`, but the value is not a boolean. Received: ${e.open}`);if(typeof e.onClose!=\"function\")throw new Error(`You provided an \\`onClose\\` prop to the \\`Dialog\\`, but the value is not a function. Received: ${e.onClose}`);return(a!==void 0||o)&&!n.static?l.createElement(Y,null,l.createElement(ke,{show:a,transition:o,unmount:n.unmount},l.createElement(z,{ref:t,...n}))):l.createElement(Y,null,l.createElement(z,{ref:t,open:a,...n}))}let $e=\"div\";function je(e,t){let o=k(),{id:a=`headlessui-dialog-panel-${o}`,transition:n=!1,...i}=e,[{dialogState:s,unmount:d},p]=O(\"Dialog.Panel\"),T=G(t,p.panelRef),u=A(()=>({open:s===0}),[s]),y=_(I=>{I.stopPropagation()}),S={ref:T,id:a,onClick:y},F=n?q:$,c=n?{unmount:d}:{},f=h();return l.createElement(F,{...c},f({ourProps:S,theirProps:i,slot:u,defaultTag:$e,name:\"Dialog.Panel\"}))}let Ye=\"div\";function Je(e,t){let{transition:o=!1,...a}=e,[{dialogState:n,unmount:i}]=O(\"Dialog.Backdrop\"),s=A(()=>({open:n===0}),[n]),d={ref:t,\"aria-hidden\":!0},p=o?q:$,T=o?{unmount:i}:{},u=h();return l.createElement(p,{...T},u({ourProps:d,theirProps:a,slot:s,defaultTag:Ye,name:\"Dialog.Backdrop\"}))}let Ke=\"h2\";function Xe(e,t){let o=k(),{id:a=`headlessui-dialog-title-${o}`,...n}=e,[{dialogState:i,setTitleId:s}]=O(\"Dialog.Title\"),d=G(t);fe(()=>(s(a),()=>s(null)),[a,s]);let p=A(()=>({open:i===0}),[i]),T={ref:d,id:a};return h()({ourProps:T,theirProps:n,slot:p,defaultTag:Ke,name:\"Dialog.Title\"})}let Ve=C(We),qe=C(je),bt=C(Je),ze=C(Xe),vt=V,Lt=Object.assign(Ve,{Panel:qe,Title:ze,Description:V});export{Lt as Dialog,bt as DialogBackdrop,vt as DialogDescription,qe as DialogPanel,ze as DialogTitle};\n"], "names": [], "mappings": ";;;;;;;AAAa;AAAqK;AAAuD;AAAkG;AAAkE;AAAuE;AAA6E;AAAkE;AAAoE;AAA6D;AAAoH;AAAgE;AAAuF;AAA2D;AAAkE;AAAuG;AAAsE;AAAiE;AAAgD;AAA8C;AAA4F;AAAkF;AAAgF;AAAuF;AAA7wD;;;;;;;;;;;;;;;;;;;;;;;;;;AAA41D,IAAI,KAAG,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,MAAI,CAAC,IAAG,KAAG,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,UAAU,GAAC,EAAE,GAAC,cAAa,CAAC,CAAC,EAAE,MAAI,CAAC;AAAG,IAAI,KAAG;IAAC,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,OAAO,EAAE,OAAO,KAAG,EAAE,EAAE,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,SAAQ,EAAE,EAAE;QAAA;IAAC;AAAC,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAE,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAgB,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAE,AAAD,EAAE;IAAG,IAAG,MAAI,MAAK;QAAC,IAAI,IAAE,IAAI,MAAM,CAAC,CAAC,EAAE,EAAE,6CAA6C,CAAC;QAAE,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG;IAAC;IAAC,OAAO;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,OAAO,CAAA,GAAA,iTAAA,CAAA,QAAE,AAAD,EAAE,EAAE,IAAI,EAAC,IAAG,GAAE;AAAE;AAAC,IAAI,IAAE,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,SAAS,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAC,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,kBAAkB,EAAE,GAAG,EAAC,MAAK,CAAC,EAAC,SAAQ,CAAC,EAAC,cAAa,CAAC,EAAC,MAAK,IAAE,QAAQ,EAAC,WAAU,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,CAAC;IAAG,IAAE;QAAW,OAAO,MAAI,YAAU,MAAI,gBAAc,IAAE,CAAC,EAAE,OAAO,IAAE,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE,QAAQ,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE,wGAAwG,CAAC,CAAC,GAAE,QAAQ;IAAC;IAAI,IAAI,IAAE,CAAA,GAAA,6TAAA,CAAA,gBAAC,AAAD;IAAI,MAAI,KAAK,KAAG,MAAI,QAAM,CAAC,IAAE,CAAC,IAAE,6TAAA,CAAA,QAAC,CAAC,IAAI,MAAI,6TAAA,CAAA,QAAC,CAAC,IAAI;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE,GAAE,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,mBAAE,AAAD,EAAE,IAAG,IAAE,IAAE,IAAE,GAAE,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,aAAE,AAAD,EAAE,IAAG;QAAC,SAAQ;QAAK,eAAc;QAAK,UAAS,CAAA,GAAA,oUAAA,CAAA,YAAE,AAAD;IAAG,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,IAAI,EAAE,CAAC,KAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,EAAE;YAAC,MAAK;YAAE,IAAG;QAAC,KAAI,IAAE,CAAA,GAAA,gVAAA,CAAA,2BAAE,AAAD,MAAI,MAAI,IAAE,CAAC,GAAE,CAAC,GAAE,GAAG,GAAC,CAAA,GAAA,iUAAA,CAAA,mBAAE,AAAD,KAAI,KAAG;QAAC,IAAI,WAAS;YAAC,IAAI;YAAE,OAAM,CAAC,IAAE,EAAE,QAAQ,CAAC,OAAO,KAAG,OAAK,IAAE,EAAE,OAAO;QAAA;IAAC,GAAE,IAAE,CAAA,GAAA,qUAAA,CAAA,kBAAE,AAAD,KAAI,EAAC,mBAAkB,CAAC,EAAC,GAAC,CAAA,GAAA,qUAAA,CAAA,oBAAE,AAAD,EAAE;QAAC,cAAa;QAAE,SAAQ;QAAE,mBAAkB;YAAC;SAAG;IAAA,IAAG,IAAE,MAAI,OAAK,CAAC,IAAE,6TAAA,CAAA,QAAC,CAAC,OAAO,MAAI,6TAAA,CAAA,QAAC,CAAC,OAAO,GAAC,CAAC;IAAE,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE,KAAG,IAAE,CAAC,IAAE,GAAE;QAAC,SAAQ,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;YAAK,IAAI,GAAE;YAAE,OAAM;gBAAC,CAAC,IAAE,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,CAAC,2BAA2B,KAAG,OAAK,IAAE;aAAK;QAAA;QAAG,YAAW,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;YAAK,IAAI;YAAE,OAAM;gBAAC,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,CAAC,wCAAwC,KAAG,OAAK,IAAE;aAAK;QAAA;IAAE;IAAG,IAAI,IAAE,+TAAA,CAAA,gBAAE,CAAC,GAAG,CAAC;IAAM,CAAA,GAAA,2UAAA,CAAA,sBAAE,AAAD,EAAE;QAAK,IAAG,GAAE,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,IAAG,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAE,AAAD,EAAE,GAAE,CAAA,GAAA,oUAAA,CAAA,cAAE,AAAD,EAAE,CAAA,IAAG,EAAE,SAAS,CAAC,KAAK,CAAC,GAAE,IAAG;QAAC;QAAE;KAAE;IAAG,CAAA,GAAA,mUAAA,CAAA,kBAAE,AAAD,EAAE,GAAE,GAAE,CAAA;QAAI,EAAE,cAAc,IAAG;IAAG,IAAG,CAAA,GAAA,yTAAA,CAAA,YAAE,AAAD,EAAE,GAAE,KAAG,OAAK,KAAK,IAAE,EAAE,WAAW,EAAC,CAAA;QAAI,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,SAAS,aAAa,IAAE,UAAS,SAAS,aAAa,IAAE,OAAO,SAAS,aAAa,CAAC,IAAI,IAAE,cAAY,SAAS,aAAa,CAAC,IAAI,IAAG;IAAG,IAAG,CAAA,GAAA,iUAAA,CAAA,gBAAE,AAAD,EAAE,KAAG,IAAE,CAAC,IAAE,GAAE,GAAE,IAAG,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE,GAAE,GAAE;IAAG,IAAG,CAAC,IAAG,GAAG,GAAC,CAAA,GAAA,2UAAA,CAAA,kBAAE,AAAD,KAAI,KAAG,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI;YAAC;gBAAC,aAAY;gBAAE,OAAM;gBAAE,YAAW;gBAAE,SAAQ;YAAC;YAAE;SAAE,EAAC;QAAC;QAAE;QAAE;QAAE;QAAE;KAAE,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI;QAAC,CAAC,GAAE;QAAC;KAAE,GAAE,KAAG;QAAC,KAAI;QAAE,IAAG;QAAE,MAAK;QAAE,UAAS,CAAC;QAAE,cAAa,IAAE,KAAK,IAAE,MAAI,IAAE,CAAC,IAAE,KAAK;QAAE,mBAAkB,EAAE,OAAO;QAAC,oBAAmB;QAAG,SAAQ;IAAC,GAAE,KAAG,CAAC,CAAA,GAAA,wUAAA,CAAA,mBAAE,AAAD,KAAI,IAAE,+UAAA,CAAA,oBAAC,CAAC,IAAI;IAAC,KAAG,CAAC,KAAG,CAAC,KAAG,+UAAA,CAAA,oBAAC,CAAC,YAAY,EAAC,KAAG,+UAAA,CAAA,oBAAC,CAAC,OAAO,EAAC,KAAG,CAAC,KAAG,+UAAA,CAAA,oBAAC,CAAC,SAAS,GAAE,MAAI,CAAC,KAAG,+UAAA,CAAA,oBAAC,CAAC,YAAY,CAAC;IAAE,IAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD;IAAI,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,6TAAA,CAAA,0BAAE,EAAC,MAAK,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,sUAAA,CAAA,kBAAC,EAAC;QAAC,OAAM,CAAC;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,iUAAA,CAAA,SAAE,EAAC,MAAK,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAE,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,iUAAA,CAAA,cAAE,EAAC;QAAC,QAAO;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,sUAAA,CAAA,kBAAC,EAAC;QAAC,OAAM,CAAC;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,IAAG;QAAC,MAAK;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,IAAG,MAAK,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,+UAAA,CAAA,YAAE,EAAC;QAAC,cAAa;QAAE,sBAAqB;QAAE,YAAW;QAAE,UAAS;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,gUAAA,CAAA,gBAAE,EAAC;QAAC,OAAM;IAAC,GAAE,GAAG;QAAC,UAAS;QAAG,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,UAAS;QAAG,SAAQ,MAAI;QAAE,MAAK;IAAQ;AAAY,IAAG,KAAG,OAAM,KAAG,kTAAA,CAAA,iBAAC,CAAC,cAAc,GAAC,kTAAA,CAAA,iBAAC,CAAC,MAAM;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,MAAK,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,6TAAA,CAAA,gBAAC,AAAD,KAAI,IAAE,EAAE,cAAc,CAAC,WAAS,MAAI,MAAK,IAAE,EAAE,cAAc,CAAC;IAAW,IAAG,CAAC,KAAG,CAAC,GAAE,MAAM,IAAI,MAAM;IAAkF,IAAG,CAAC,GAAE,MAAM,IAAI,MAAM;IAA8E,IAAG,CAAC,GAAE,MAAM,IAAI,MAAM;IAA8E,IAAG,CAAC,KAAG,OAAO,EAAE,IAAI,IAAE,WAAU,MAAM,IAAI,MAAM,CAAC,2FAA2F,EAAE,EAAE,IAAI,EAAE;IAAE,IAAG,OAAO,EAAE,OAAO,IAAE,YAAW,MAAM,IAAI,MAAM,CAAC,+FAA+F,EAAE,EAAE,OAAO,EAAE;IAAE,OAAM,CAAC,MAAI,KAAK,KAAG,CAAC,KAAG,CAAC,EAAE,MAAM,GAAC,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,qUAAA,CAAA,mBAAC,EAAC,MAAK,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,yUAAA,CAAA,aAAE,EAAC;QAAC,MAAK;QAAE,YAAW;QAAE,SAAQ,EAAE,OAAO;IAAA,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE;QAAC,KAAI;QAAE,GAAG,CAAC;IAAA,OAAK,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,qUAAA,CAAA,mBAAC,EAAC,MAAK,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE;QAAC,KAAI;QAAE,MAAK;QAAE,GAAG,CAAC;IAAA;AAAG;AAAC,IAAI,KAAG;AAAM,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAC,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,wBAAwB,EAAE,GAAG,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,CAAC,EAAC,aAAY,CAAC,EAAC,SAAQ,CAAC,EAAC,EAAC,EAAE,GAAC,EAAE,iBAAgB,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE,GAAE,EAAE,QAAQ,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI;QAAC,CAAC,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,EAAE,eAAe;IAAE,IAAG,IAAE;QAAC,KAAI;QAAE,IAAG;QAAE,SAAQ;IAAC,GAAE,IAAE,IAAE,yUAAA,CAAA,kBAAC,GAAC,oUAAA,CAAA,WAAC,EAAC,IAAE,IAAE;QAAC,SAAQ;IAAC,IAAE,CAAC,GAAE,IAAE,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD;IAAI,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE;QAAC,GAAG,CAAC;IAAA,GAAE,EAAE;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAc;AAAG;AAAC,IAAI,KAAG;AAAM,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,CAAC,EAAC,aAAY,CAAC,EAAC,SAAQ,CAAC,EAAC,CAAC,GAAC,EAAE,oBAAmB,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI;QAAC,CAAC,GAAE;QAAC;KAAE,GAAE,IAAE;QAAC,KAAI;QAAE,eAAc,CAAC;IAAC,GAAE,IAAE,IAAE,yUAAA,CAAA,kBAAC,GAAC,oUAAA,CAAA,WAAC,EAAC,IAAE,IAAE;QAAC,SAAQ;IAAC,IAAE,CAAC,GAAE,IAAE,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD;IAAI,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE;QAAC,GAAG,CAAC;IAAA,GAAE,EAAE;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAiB;AAAG;AAAC,IAAI,KAAG;AAAK,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAC,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,wBAAwB,EAAE,GAAG,EAAC,GAAG,GAAE,GAAC,GAAE,CAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,CAAC,GAAC,EAAE,iBAAgB,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE;IAAG,CAAA,GAAA,oUAAA,CAAA,YAAE,AAAD,EAAE,IAAI,CAAC,EAAE,IAAG,IAAI,EAAE,KAAK,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI;QAAC,CAAC,GAAE;QAAC;KAAE,GAAE,IAAE;QAAC,KAAI;QAAE,IAAG;IAAC;IAAE,OAAO,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD,IAAI;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAc;AAAE;AAAC,IAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,2UAAA,CAAA,cAAC,EAAC,KAAG,OAAO,MAAM,CAAC,IAAG;IAAC,OAAM;IAAG,OAAM;IAAG,aAAY,2UAAA,CAAA,cAAC;AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3120, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-active-press.js"], "sourcesContent": ["import{useRef as a,useState as m}from\"react\";import{getOwnerDocument as d}from'../utils/owner.js';import{useDisposables as g}from'./use-disposables.js';import{useEvent as u}from'./use-event.js';function E(e){let t=e.width/2,n=e.height/2;return{top:e.clientY-n,right:e.clientX+t,bottom:e.clientY+n,left:e.clientX-t}}function P(e,t){return!(!e||!t||e.right<t.left||e.left>t.right||e.bottom<t.top||e.top>t.bottom)}function w({disabled:e=!1}={}){let t=a(null),[n,l]=m(!1),r=g(),o=u(()=>{t.current=null,l(!1),r.dispose()}),f=u(s=>{if(r.dispose(),t.current===null){t.current=s.currentTarget,l(!0);{let i=d(s.currentTarget);r.addEventListener(i,\"pointerup\",o,!1),r.addEventListener(i,\"pointermove\",c=>{if(t.current){let p=E(c);l(P(p,t.current.getBoundingClientRect()))}},!1),r.addEventListener(i,\"pointercancel\",o,!1)}}});return{pressed:n,pressProps:e?{}:{onPointerDown:f,onPointerUp:o,onClick:o}}}export{w as useActivePress};\n"], "names": [], "mappings": ";;;AAAA;AAA6C;AAAqD;AAAsD;;;;;AAA0C,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,EAAE,KAAK,GAAC,GAAE,IAAE,EAAE,MAAM,GAAC;IAAE,OAAM;QAAC,KAAI,EAAE,OAAO,GAAC;QAAE,OAAM,EAAE,OAAO,GAAC;QAAE,QAAO,EAAE,OAAO,GAAC;QAAE,MAAK,EAAE,OAAO,GAAC;IAAC;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,OAAM,CAAC,CAAC,CAAC,KAAG,CAAC,KAAG,EAAE,KAAK,GAAC,EAAE,IAAI,IAAE,EAAE,IAAI,GAAC,EAAE,KAAK,IAAE,EAAE,MAAM,GAAC,EAAE,GAAG,IAAE,EAAE,GAAG,GAAC,EAAE,MAAM;AAAC;AAAC,SAAS,EAAE,EAAC,UAAS,IAAE,CAAC,CAAC,EAAC,GAAC,CAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,OAAM,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,CAAC,IAAG,IAAE,CAAA,GAAA,8TAAA,CAAA,iBAAC,AAAD,KAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,EAAE,OAAO,GAAC,MAAK,EAAE,CAAC,IAAG,EAAE,OAAO;IAAE,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAG,EAAE,OAAO,IAAG,EAAE,OAAO,KAAG,MAAK;YAAC,EAAE,OAAO,GAAC,EAAE,aAAa,EAAC,EAAE,CAAC;YAAG;gBAAC,IAAI,IAAE,CAAA,GAAA,iTAAA,CAAA,mBAAC,AAAD,EAAE,EAAE,aAAa;gBAAE,EAAE,gBAAgB,CAAC,GAAE,aAAY,GAAE,CAAC,IAAG,EAAE,gBAAgB,CAAC,GAAE,eAAc,CAAA;oBAAI,IAAG,EAAE,OAAO,EAAC;wBAAC,IAAI,IAAE,EAAE;wBAAG,EAAE,EAAE,GAAE,EAAE,OAAO,CAAC,qBAAqB;oBAAI;gBAAC,GAAE,CAAC,IAAG,EAAE,gBAAgB,CAAC,GAAE,iBAAgB,GAAE,CAAC;YAAE;QAAC;IAAC;IAAG,OAAM;QAAC,SAAQ;QAAE,YAAW,IAAE,CAAC,IAAE;YAAC,eAAc;YAAE,aAAY;YAAE,SAAQ;QAAC;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3176, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-by-comparator.js"], "sourcesContent": ["import{useCallback as n}from\"react\";function l(e,r){return e!==null&&r!==null&&typeof e==\"object\"&&typeof r==\"object\"&&\"id\"in e&&\"id\"in r?e.id===r.id:e===r}function u(e=l){return n((r,t)=>{if(typeof e==\"string\"){let o=e;return(r==null?void 0:r[o])===(t==null?void 0:t[o])}return e(r,t)},[e])}export{u as useByComparator};\n"], "names": [], "mappings": ";;;AAAA;;AAAoC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,OAAO,MAAI,QAAM,MAAI,QAAM,OAAO,KAAG,YAAU,OAAO,KAAG,YAAU,QAAO,KAAG,QAAO,IAAE,EAAE,EAAE,KAAG,EAAE,EAAE,GAAC,MAAI;AAAC;AAAC,SAAS,EAAE,IAAE,CAAC;IAAE,OAAO,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAC,GAAE;QAAK,IAAG,OAAO,KAAG,UAAS;YAAC,IAAI,IAAE;YAAE,OAAM,CAAC,KAAG,OAAK,KAAK,IAAE,CAAC,CAAC,EAAE,MAAI,CAAC,KAAG,OAAK,KAAK,IAAE,CAAC,CAAC,EAAE;QAAC;QAAC,OAAO,EAAE,GAAE;IAAE,GAAE;QAAC;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3202, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-controllable.js"], "sourcesContent": ["import{useRef as o,useState as f}from\"react\";import{useEvent as a}from'./use-event.js';function T(l,r,c){let[i,s]=f(c),e=l!==void 0,t=o(e),u=o(!1),d=o(!1);return e&&!t.current&&!u.current?(u.current=!0,t.current=e,console.error(\"A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.\")):!e&&t.current&&!d.current&&(d.current=!0,t.current=e,console.error(\"A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.\")),[e?l:i,a(n=>(e||s(n),r==null?void 0:r(n)))]}export{T as useControllable};\n"], "names": [], "mappings": ";;;AAAA;AAA6C;;;AAA0C,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,IAAG,IAAE,MAAI,KAAK,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,CAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,CAAC;IAAG,OAAO,KAAG,CAAC,EAAE,OAAO,IAAE,CAAC,EAAE,OAAO,GAAC,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE,EAAE,OAAO,GAAC,GAAE,QAAQ,KAAK,CAAC,gKAAgK,IAAE,CAAC,KAAG,EAAE,OAAO,IAAE,CAAC,EAAE,OAAO,IAAE,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE,EAAE,OAAO,GAAC,GAAE,QAAQ,KAAK,CAAC,gKAAgK,GAAE;QAAC,IAAE,IAAE;QAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,CAAC,KAAG,EAAE,IAAG,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE;KAAG;AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3223, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-default-value.js"], "sourcesContent": ["import{useState as u}from\"react\";function l(e){let[t]=u(e);return t}export{l as useDefaultValue};\n"], "names": [], "mappings": ";;;AAAA;;AAAiC,SAAS,EAAE,CAAC;IAAE,IAAG,CAAC,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE;IAAG,OAAO;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3239, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-element-size.js"], "sourcesContent": ["import{useMemo as o,useReducer as h}from\"react\";import{useIsoMorphicEffect as s}from'./use-iso-morphic-effect.js';function f(e){if(e===null)return{width:0,height:0};let{width:t,height:r}=e.getBoundingClientRect();return{width:t,height:r}}function d(e,t=!1){let[r,u]=h(()=>({}),{}),i=o(()=>f(e),[e,r]);return s(()=>{if(!e)return;let n=new ResizeObserver(u);return n.observe(e),()=>{n.disconnect()}},[e]),t?{width:`${i.width}px`,height:`${i.height}px`}:i}export{d as useElementSize};\n"], "names": [], "mappings": ";;;AAAA;AAAgD;;;AAAkE,SAAS,EAAE,CAAC;IAAE,IAAG,MAAI,MAAK,OAAM;QAAC,OAAM;QAAE,QAAO;IAAC;IAAE,IAAG,EAAC,OAAM,CAAC,EAAC,QAAO,CAAC,EAAC,GAAC,EAAE,qBAAqB;IAAG,OAAM;QAAC,OAAM;QAAE,QAAO;IAAC;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,IAAE,CAAC,CAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE,IAAI,CAAC,CAAC,CAAC,GAAE,CAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,EAAE,IAAG;QAAC;QAAE;KAAE;IAAE,OAAO,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE;QAAO,IAAI,IAAE,IAAI,eAAe;QAAG,OAAO,EAAE,OAAO,CAAC,IAAG;YAAK,EAAE,UAAU;QAAE;IAAC,GAAE;QAAC;KAAE,GAAE,IAAE;QAAC,OAAM,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;QAAC,QAAO,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC;IAAA,IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3282, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-quick-release.js"], "sourcesContent": ["import{useRef as d}from\"react\";import*as l from'../utils/dom.js';import{useDocumentEvent as c}from'./use-document-event.js';var m=(e=>(e[e.Ignore=0]=\"Ignore\",e[e.Select=1]=\"Select\",e[e.Close=2]=\"Close\",e))(m||{});const g={Ignore:{kind:0},Select:r=>({kind:1,target:r}),Close:{kind:2}},E=200;function k(r,{trigger:n,action:s,close:e,select:a}){let o=d(null);c(r&&n!==null,\"pointerdown\",t=>{l.isNode(t==null?void 0:t.target)&&n!=null&&n.contains(t.target)&&(o.current=new Date)}),c(r&&n!==null,\"pointerup\",t=>{if(o.current===null||!l.isHTMLorSVGElement(t.target))return;let i=s(t),u=new Date().getTime()-o.current.getTime();switch(o.current=null,i.kind){case 0:return;case 1:{u>E&&(a(i.target),e());break}case 2:{e();break}}},{capture:!0})}export{g as Action,k as useQuickRelease};\n"], "names": [], "mappings": ";;;;AAAA;AAA+B;AAAkC;;;;AAA2D,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,MAAM,IAAE;IAAC,QAAO;QAAC,MAAK;IAAC;IAAE,QAAO,CAAA,IAAG,CAAC;YAAC,MAAK;YAAE,QAAO;QAAC,CAAC;IAAE,OAAM;QAAC,MAAK;IAAC;AAAC,GAAE,IAAE;AAAI,SAAS,EAAE,CAAC,EAAC,EAAC,SAAQ,CAAC,EAAC,QAAO,CAAC,EAAC,OAAM,CAAC,EAAC,QAAO,CAAC,EAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE;IAAM,CAAA,GAAA,oUAAA,CAAA,mBAAC,AAAD,EAAE,KAAG,MAAI,MAAK,eAAc,CAAA;QAAI,CAAA,GAAA,+SAAA,CAAA,SAAQ,AAAD,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,MAAM,KAAG,KAAG,QAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,KAAG,CAAC,EAAE,OAAO,GAAC,IAAI,IAAI;IAAC,IAAG,CAAA,GAAA,oUAAA,CAAA,mBAAC,AAAD,EAAE,KAAG,MAAI,MAAK,aAAY,CAAA;QAAI,IAAG,EAAE,OAAO,KAAG,QAAM,CAAC,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,EAAE,MAAM,GAAE;QAAO,IAAI,IAAE,EAAE,IAAG,IAAE,IAAI,OAAO,OAAO,KAAG,EAAE,OAAO,CAAC,OAAO;QAAG,OAAO,EAAE,OAAO,GAAC,MAAK,EAAE,IAAI;YAAE,KAAK;gBAAE;YAAO,KAAK;gBAAE;oBAAC,IAAE,KAAG,CAAC,EAAE,EAAE,MAAM,GAAE,GAAG;oBAAE;gBAAK;YAAC,KAAK;gBAAE;oBAAC;oBAAI;gBAAK;QAAC;IAAC,GAAE;QAAC,SAAQ,CAAC;IAAC;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3337, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-refocusable-input.js"], "sourcesContent": ["import{useRef as u}from\"react\";import*as r from'../utils/dom.js';import{useEvent as o}from'./use-event.js';import{useEventListener as s}from'./use-event-listener.js';function f(e){let l=u({value:\"\",selectionStart:null,selectionEnd:null});return s(e,\"blur\",n=>{let t=n.target;r.isHTMLInputElement(t)&&(l.current={value:t.value,selectionStart:t.selectionStart,selectionEnd:t.selectionEnd})}),o(()=>{if(document.activeElement!==e&&r.isHTMLInputElement(e)&&e.isConnected){if(e.focus({preventScroll:!0}),e.value!==l.current.value)e.setSelectionRange(e.value.length,e.value.length);else{let{selectionStart:n,selectionEnd:t}=l.current;n!==null&&t!==null&&e.setSelectionRange(n,t)}l.current={value:\"\",selectionStart:null,selectionEnd:null}}})}export{f as useRefocusableInput};\n"], "names": [], "mappings": ";;;AAAA;AAA+B;AAAkC;AAA0C;;;;;AAA2D,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE;QAAC,OAAM;QAAG,gBAAe;QAAK,cAAa;IAAI;IAAG,OAAO,CAAA,GAAA,oUAAA,CAAA,mBAAC,AAAD,EAAE,GAAE,QAAO,CAAA;QAAI,IAAI,IAAE,EAAE,MAAM;QAAC,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,MAAI,CAAC,EAAE,OAAO,GAAC;YAAC,OAAM,EAAE,KAAK;YAAC,gBAAe,EAAE,cAAc;YAAC,cAAa,EAAE,YAAY;QAAA,CAAC;IAAC,IAAG,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAG,SAAS,aAAa,KAAG,KAAG,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,MAAI,EAAE,WAAW,EAAC;YAAC,IAAG,EAAE,KAAK,CAAC;gBAAC,eAAc,CAAC;YAAC,IAAG,EAAE,KAAK,KAAG,EAAE,OAAO,CAAC,KAAK,EAAC,EAAE,iBAAiB,CAAC,EAAE,KAAK,CAAC,MAAM,EAAC,EAAE,KAAK,CAAC,MAAM;iBAAM;gBAAC,IAAG,EAAC,gBAAe,CAAC,EAAC,cAAa,CAAC,EAAC,GAAC,EAAE,OAAO;gBAAC,MAAI,QAAM,MAAI,QAAM,EAAE,iBAAiB,CAAC,GAAE;YAAE;YAAC,EAAE,OAAO,GAAC;gBAAC,OAAM;gBAAG,gBAAe;gBAAK,cAAa;YAAI;QAAC;IAAC;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3385, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-resolve-button-type.js"], "sourcesContent": ["import{useMemo as a}from\"react\";function e(t,u){return a(()=>{var n;if(t.type)return t.type;let r=(n=t.as)!=null?n:\"button\";if(typeof r==\"string\"&&r.toLowerCase()===\"button\"||(u==null?void 0:u.tagName)===\"BUTTON\"&&!u.hasAttribute(\"type\"))return\"button\"},[t.type,t.as,u])}export{e as useResolveButtonType};\n"], "names": [], "mappings": ";;;AAAA;;AAAgC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,OAAO,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE;QAAK,IAAI;QAAE,IAAG,EAAE,IAAI,EAAC,OAAO,EAAE,IAAI;QAAC,IAAI,IAAE,CAAC,IAAE,EAAE,EAAE,KAAG,OAAK,IAAE;QAAS,IAAG,OAAO,KAAG,YAAU,EAAE,WAAW,OAAK,YAAU,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,MAAI,YAAU,CAAC,EAAE,YAAY,CAAC,SAAQ,OAAM;IAAQ,GAAE;QAAC,EAAE,IAAI;QAAC,EAAE,EAAE;QAAC;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3409, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-tracked-pointer.js"], "sourcesContent": ["import{useRef as o}from\"react\";function t(e){return[e.screenX,e.screenY]}function u(){let e=o([-1,-1]);return{wasMoved(r){let n=t(r);return e.current[0]===n[0]&&e.current[1]===n[1]?!1:(e.current=n,!0)},update(r){e.current=t(r)}}}export{u as useTrackedPointer};\n"], "names": [], "mappings": ";;;AAAA;;AAA+B,SAAS,EAAE,CAAC;IAAE,OAAM;QAAC,EAAE,OAAO;QAAC,EAAE,OAAO;KAAC;AAAA;AAAC,SAAS;IAAI,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE;QAAC,CAAC;QAAE,CAAC;KAAE;IAAE,OAAM;QAAC,UAAS,CAAC;YAAE,IAAI,IAAE,EAAE;YAAG,OAAO,EAAE,OAAO,CAAC,EAAE,KAAG,CAAC,CAAC,EAAE,IAAE,EAAE,OAAO,CAAC,EAAE,KAAG,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,CAAC,EAAE,OAAO,GAAC,GAAE,CAAC,CAAC;QAAC;QAAE,QAAO,CAAC;YAAE,EAAE,OAAO,GAAC,EAAE;QAAE;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3442, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-tree-walker.js"], "sourcesContent": ["import{useEffect as T,useRef as E}from\"react\";import{getOwnerDocument as d}from'../utils/owner.js';import{useIsoMorphicEffect as N}from'./use-iso-morphic-effect.js';function F(c,{container:e,accept:t,walk:r}){let o=E(t),l=E(r);T(()=>{o.current=t,l.current=r},[t,r]),N(()=>{if(!e||!c)return;let n=d(e);if(!n)return;let f=o.current,p=l.current,i=Object.assign(m=>f(m),{acceptNode:f}),u=n.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,i,!1);for(;u.nextNode();)p(u.currentNode)},[e,c,o,l])}export{F as useTreeWalker};\n"], "names": [], "mappings": ";;;AAAA;AAA8C;AAAqD;;;;AAAkE,SAAS,EAAE,CAAC,EAAC,EAAC,WAAU,CAAC,EAAC,QAAO,CAAC,EAAC,MAAK,CAAC,EAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE;IAAG,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE;QAAK,EAAE,OAAO,GAAC,GAAE,EAAE,OAAO,GAAC;IAAC,GAAE;QAAC;QAAE;KAAE,GAAE,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,CAAC,KAAG,CAAC,GAAE;QAAO,IAAI,IAAE,CAAA,GAAA,iTAAA,CAAA,mBAAC,AAAD,EAAE;QAAG,IAAG,CAAC,GAAE;QAAO,IAAI,IAAE,EAAE,OAAO,EAAC,IAAE,EAAE,OAAO,EAAC,IAAE,OAAO,MAAM,CAAC,CAAA,IAAG,EAAE,IAAG;YAAC,YAAW;QAAC,IAAG,IAAE,EAAE,gBAAgB,CAAC,GAAE,WAAW,YAAY,EAAC,GAAE,CAAC;QAAG,MAAK,EAAE,QAAQ,IAAI,EAAE,EAAE,WAAW;IAAC,GAAE;QAAC;QAAE;QAAE;QAAE;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3480, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/internal/floating.js"], "sourcesContent": ["import{autoUpdate as Z,flip as ee,inner as te,offset as ne,shift as re,size as le,useFloating as oe,useInnerOffset as ie,useInteractions as se}from\"@floating-ui/react\";import*as j from\"react\";import{createContext as _,use<PERSON><PERSON>back as ae,useContext as T,useMemo as R,useRef as ue,useState as v}from\"react\";import{useDisposables as fe}from'../hooks/use-disposables.js';import{useEvent as z}from'../hooks/use-event.js';import{useIsoMorphicEffect as C}from'../hooks/use-iso-morphic-effect.js';import*as pe from'../utils/dom.js';let y=_({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});y.displayName=\"FloatingContext\";let $=_(null);$.displayName=\"PlacementContext\";function ye(e){return R(()=>e?typeof e==\"string\"?{to:e}:e:null,[e])}function Fe(){return T(y).setReference}function be(){return T(y).getReferenceProps}function Te(){let{getFloatingProps:e,slot:t}=T(y);return ae((...n)=>Object.assign({},e(...n),{\"data-anchor\":t.anchor}),[e,t])}function Re(e=null){e===!1&&(e=null),typeof e==\"string\"&&(e={to:e});let t=T($),n=R(()=>e,[JSON.stringify(e,(l,o)=>{var u;return(u=o==null?void 0:o.outerHTML)!=null?u:o})]);C(()=>{t==null||t(n!=null?n:null)},[t,n]);let r=T(y);return R(()=>[r.setFloating,e?r.styles:{}],[r.setFloating,e,r.styles])}let D=4;function Ae({children:e,enabled:t=!0}){let[n,r]=v(null),[l,o]=v(0),u=ue(null),[f,s]=v(null);ce(f);let i=t&&n!==null&&f!==null,{to:F=\"bottom\",gap:E=0,offset:A=0,padding:c=0,inner:h}=ge(n,f),[a,p=\"center\"]=F.split(\" \");C(()=>{i&&o(0)},[i]);let{refs:b,floatingStyles:S,context:g}=oe({open:i,placement:a===\"selection\"?p===\"center\"?\"bottom\":`bottom-${p}`:p===\"center\"?`${a}`:`${a}-${p}`,strategy:\"absolute\",transform:!1,middleware:[ne({mainAxis:a===\"selection\"?0:E,crossAxis:A}),re({padding:c}),a!==\"selection\"&&ee({padding:c}),a===\"selection\"&&h?te({...h,padding:c,overflowRef:u,offset:l,minItemsVisible:D,referenceOverflowThreshold:c,onFallbackChange(P){var L,N;if(!P)return;let d=g.elements.floating;if(!d)return;let M=parseFloat(getComputedStyle(d).scrollPaddingBottom)||0,I=Math.min(D,d.childElementCount),W=0,B=0;for(let m of(N=(L=g.elements.floating)==null?void 0:L.childNodes)!=null?N:[])if(pe.isHTMLElement(m)){let x=m.offsetTop,k=x+m.clientHeight+M,H=d.scrollTop,U=H+d.clientHeight;if(x>=H&&k<=U)I--;else{B=Math.max(0,Math.min(k,U)-Math.max(x,H)),W=m.clientHeight;break}}I>=1&&o(m=>{let x=W*I-B+M;return m>=x?m:x})}}):null,le({padding:c,apply({availableWidth:P,availableHeight:d,elements:M}){Object.assign(M.floating.style,{overflow:\"auto\",maxWidth:`${P}px`,maxHeight:`min(var(--anchor-max-height, 100vh), ${d}px)`})}})].filter(Boolean),whileElementsMounted:Z}),[w=a,V=p]=g.placement.split(\"-\");a===\"selection\"&&(w=\"selection\");let G=R(()=>({anchor:[w,V].filter(Boolean).join(\" \")}),[w,V]),K=ie(g,{overflowRef:u,onChange:o}),{getReferenceProps:Q,getFloatingProps:X}=se([K]),Y=z(P=>{s(P),b.setFloating(P)});return j.createElement($.Provider,{value:r},j.createElement(y.Provider,{value:{setFloating:Y,setReference:b.setReference,styles:S,getReferenceProps:Q,getFloatingProps:X,slot:G}},e))}function ce(e){C(()=>{if(!e)return;let t=new MutationObserver(()=>{let n=window.getComputedStyle(e).maxHeight,r=parseFloat(n);if(isNaN(r))return;let l=parseInt(n);isNaN(l)||r!==l&&(e.style.maxHeight=`${Math.ceil(r)}px`)});return t.observe(e,{attributes:!0,attributeFilter:[\"style\"]}),()=>{t.disconnect()}},[e])}function ge(e,t){var o,u,f;let n=O((o=e==null?void 0:e.gap)!=null?o:\"var(--anchor-gap, 0)\",t),r=O((u=e==null?void 0:e.offset)!=null?u:\"var(--anchor-offset, 0)\",t),l=O((f=e==null?void 0:e.padding)!=null?f:\"var(--anchor-padding, 0)\",t);return{...e,gap:n,offset:r,padding:l}}function O(e,t,n=void 0){let r=fe(),l=z((s,i)=>{if(s==null)return[n,null];if(typeof s==\"number\")return[s,null];if(typeof s==\"string\"){if(!i)return[n,null];let F=J(s,i);return[F,E=>{let A=q(s);{let c=A.map(h=>window.getComputedStyle(i).getPropertyValue(h));r.requestAnimationFrame(function h(){r.nextFrame(h);let a=!1;for(let[b,S]of A.entries()){let g=window.getComputedStyle(i).getPropertyValue(S);if(c[b]!==g){c[b]=g,a=!0;break}}if(!a)return;let p=J(s,i);F!==p&&(E(p),F=p)})}return r.dispose}]}return[n,null]}),o=R(()=>l(e,t)[0],[e,t]),[u=o,f]=v();return C(()=>{let[s,i]=l(e,t);if(f(s),!!i)return i(f)},[e,t]),u}function q(e){let t=/var\\((.*)\\)/.exec(e);if(t){let n=t[1].indexOf(\",\");if(n===-1)return[t[1]];let r=t[1].slice(0,n).trim(),l=t[1].slice(n+1).trim();return l?[r,...q(l)]:[r]}return[]}function J(e,t){let n=document.createElement(\"div\");t.appendChild(n),n.style.setProperty(\"margin-top\",\"0px\",\"important\"),n.style.setProperty(\"margin-top\",e,\"important\");let r=parseFloat(window.getComputedStyle(n).marginTop)||0;return t.removeChild(n),r}export{Ae as FloatingProvider,Re as useFloatingPanel,Te as useFloatingPanelProps,Fe as useFloatingReference,be as useFloatingReferenceProps,ye as useResolvedAnchor};\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAAwK;AAAwI;AAA8D;AAAiD;AAAyE;;;;;;;;AAAmC,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE;IAAC,QAAO,KAAK;IAAE,cAAa,KAAK;IAAE,aAAY,KAAK;IAAE,mBAAkB,IAAI,CAAC,CAAC,CAAC;IAAE,kBAAiB,IAAI,CAAC,CAAC,CAAC;IAAE,MAAK,CAAC;AAAC;AAAG,EAAE,WAAW,GAAC;AAAkB,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAmB,SAAS,GAAG,CAAC;IAAE,OAAO,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,IAAE,OAAO,KAAG,WAAS;YAAC,IAAG;QAAC,IAAE,IAAE,MAAK;QAAC;KAAE;AAAC;AAAC,SAAS;IAAK,OAAO,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE,GAAG,YAAY;AAAA;AAAC,SAAS;IAAK,OAAO,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE,GAAG,iBAAiB;AAAA;AAAC,SAAS;IAAK,IAAG,EAAC,kBAAiB,CAAC,EAAC,MAAK,CAAC,EAAC,GAAC,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;IAAG,OAAO,CAAA,GAAA,oUAAA,CAAA,cAAE,AAAD,EAAE,CAAC,GAAG,IAAI,OAAO,MAAM,CAAC,CAAC,GAAE,KAAK,IAAG;YAAC,eAAc,EAAE,MAAM;QAAA,IAAG;QAAC;QAAE;KAAE;AAAC;AAAC,SAAS,GAAG,IAAE,IAAI;IAAE,MAAI,CAAC,KAAG,CAAC,IAAE,IAAI,GAAE,OAAO,KAAG,YAAU,CAAC,IAAE;QAAC,IAAG;IAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,GAAE;QAAC,KAAK,SAAS,CAAC,GAAE,CAAC,GAAE;YAAK,IAAI;YAAE,OAAM,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,SAAS,KAAG,OAAK,IAAE;QAAC;KAAG;IAAE,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,KAAG,QAAM,EAAE,KAAG,OAAK,IAAE;IAAK,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;IAAG,OAAO,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI;YAAC,EAAE,WAAW;YAAC,IAAE,EAAE,MAAM,GAAC,CAAC;SAAE,EAAC;QAAC,EAAE,WAAW;QAAC;QAAE,EAAE,MAAM;KAAC;AAAC;AAAC,IAAI,IAAE;AAAE,SAAS,GAAG,EAAC,UAAS,CAAC,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,OAAM,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAE,AAAD,EAAE,OAAM,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE;IAAM,GAAG;IAAG,IAAI,IAAE,KAAG,MAAI,QAAM,MAAI,MAAK,EAAC,IAAG,IAAE,QAAQ,EAAC,KAAI,IAAE,CAAC,EAAC,QAAO,IAAE,CAAC,EAAC,SAAQ,IAAE,CAAC,EAAC,OAAM,CAAC,EAAC,GAAC,GAAG,GAAE,IAAG,CAAC,GAAE,IAAE,QAAQ,CAAC,GAAC,EAAE,KAAK,CAAC;IAAK,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,KAAG,EAAE;IAAE,GAAE;QAAC;KAAE;IAAE,IAAG,EAAC,MAAK,CAAC,EAAC,gBAAe,CAAC,EAAC,SAAQ,CAAC,EAAC,GAAC,CAAA,GAAA,qVAAA,CAAA,cAAE,AAAD,EAAE;QAAC,MAAK;QAAE,WAAU,MAAI,cAAY,MAAI,WAAS,WAAS,CAAC,OAAO,EAAE,GAAG,GAAC,MAAI,WAAS,GAAG,GAAG,GAAC,GAAG,EAAE,CAAC,EAAE,GAAG;QAAC,UAAS;QAAW,WAAU,CAAC;QAAE,YAAW;YAAC,CAAA,GAAA,wWAAA,CAAA,SAAE,AAAD,EAAE;gBAAC,UAAS,MAAI,cAAY,IAAE;gBAAE,WAAU;YAAC;YAAG,CAAA,GAAA,wWAAA,CAAA,QAAE,AAAD,EAAE;gBAAC,SAAQ;YAAC;YAAG,MAAI,eAAa,CAAA,GAAA,wWAAA,CAAA,OAAE,AAAD,EAAE;gBAAC,SAAQ;YAAC;YAAG,MAAI,eAAa,IAAE,CAAA,GAAA,qVAAA,CAAA,QAAE,AAAD,EAAE;gBAAC,GAAG,CAAC;gBAAC,SAAQ;gBAAE,aAAY;gBAAE,QAAO;gBAAE,iBAAgB;gBAAE,4BAA2B;gBAAE,kBAAiB,CAAC;oBAAE,IAAI,GAAE;oBAAE,IAAG,CAAC,GAAE;oBAAO,IAAI,IAAE,EAAE,QAAQ,CAAC,QAAQ;oBAAC,IAAG,CAAC,GAAE;oBAAO,IAAI,IAAE,WAAW,iBAAiB,GAAG,mBAAmB,KAAG,GAAE,IAAE,KAAK,GAAG,CAAC,GAAE,EAAE,iBAAiB,GAAE,IAAE,GAAE,IAAE;oBAAE,KAAI,IAAI,KAAI,CAAC,IAAE,CAAC,IAAE,EAAE,QAAQ,CAAC,QAAQ,KAAG,OAAK,KAAK,IAAE,EAAE,UAAU,KAAG,OAAK,IAAE,EAAE,CAAC,IAAG,CAAA,GAAA,+SAAA,CAAA,gBAAgB,AAAD,EAAE,IAAG;wBAAC,IAAI,IAAE,EAAE,SAAS,EAAC,IAAE,IAAE,EAAE,YAAY,GAAC,GAAE,IAAE,EAAE,SAAS,EAAC,IAAE,IAAE,EAAE,YAAY;wBAAC,IAAG,KAAG,KAAG,KAAG,GAAE;6BAAQ;4BAAC,IAAE,KAAK,GAAG,CAAC,GAAE,KAAK,GAAG,CAAC,GAAE,KAAG,KAAK,GAAG,CAAC,GAAE,KAAI,IAAE,EAAE,YAAY;4BAAC;wBAAK;oBAAC;oBAAC,KAAG,KAAG,EAAE,CAAA;wBAAI,IAAI,IAAE,IAAE,IAAE,IAAE;wBAAE,OAAO,KAAG,IAAE,IAAE;oBAAC;gBAAE;YAAC,KAAG;YAAK,CAAA,GAAA,wWAAA,CAAA,OAAE,AAAD,EAAE;gBAAC,SAAQ;gBAAE,OAAM,EAAC,gBAAe,CAAC,EAAC,iBAAgB,CAAC,EAAC,UAAS,CAAC,EAAC;oBAAE,OAAO,MAAM,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAC;wBAAC,UAAS;wBAAO,UAAS,GAAG,EAAE,EAAE,CAAC;wBAAC,WAAU,CAAC,qCAAqC,EAAE,EAAE,GAAG,CAAC;oBAAA;gBAAE;YAAC;SAAG,CAAC,MAAM,CAAC;QAAS,sBAAqB,gQAAA,CAAA,aAAC;IAAA,IAAG,CAAC,IAAE,CAAC,EAAC,IAAE,CAAC,CAAC,GAAC,EAAE,SAAS,CAAC,KAAK,CAAC;IAAK,MAAI,eAAa,CAAC,IAAE,WAAW;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,QAAO;gBAAC;gBAAE;aAAE,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;QAAI,CAAC,GAAE;QAAC;QAAE;KAAE,GAAE,IAAE,CAAA,GAAA,qVAAA,CAAA,iBAAE,AAAD,EAAE,GAAE;QAAC,aAAY;QAAE,UAAS;IAAC,IAAG,EAAC,mBAAkB,CAAC,EAAC,kBAAiB,CAAC,EAAC,GAAC,CAAA,GAAA,qVAAA,CAAA,kBAAE,AAAD,EAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,EAAE,IAAG,EAAE,WAAW,CAAC;IAAE;IAAG,OAAO,CAAA,GAAA,oUAAA,CAAA,gBAAe,AAAD,EAAE,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,CAAA,GAAA,oUAAA,CAAA,gBAAe,AAAD,EAAE,EAAE,QAAQ,EAAC;QAAC,OAAM;YAAC,aAAY;YAAE,cAAa,EAAE,YAAY;YAAC,QAAO;YAAE,mBAAkB;YAAE,kBAAiB;YAAE,MAAK;QAAC;IAAC,GAAE;AAAG;AAAC,SAAS,GAAG,CAAC;IAAE,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE;QAAO,IAAI,IAAE,IAAI,iBAAiB;YAAK,IAAI,IAAE,OAAO,gBAAgB,CAAC,GAAG,SAAS,EAAC,IAAE,WAAW;YAAG,IAAG,MAAM,IAAG;YAAO,IAAI,IAAE,SAAS;YAAG,MAAM,MAAI,MAAI,KAAG,CAAC,EAAE,KAAK,CAAC,SAAS,GAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;QAAC;QAAG,OAAO,EAAE,OAAO,CAAC,GAAE;YAAC,YAAW,CAAC;YAAE,iBAAgB;gBAAC;aAAQ;QAAA,IAAG;YAAK,EAAE,UAAU;QAAE;IAAC,GAAE;QAAC;KAAE;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,GAAE;IAAE,IAAI,IAAE,EAAE,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,GAAG,KAAG,OAAK,IAAE,wBAAuB,IAAG,IAAE,EAAE,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,MAAM,KAAG,OAAK,IAAE,2BAA0B,IAAG,IAAE,EAAE,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,KAAG,OAAK,IAAE,4BAA2B;IAAG,OAAM;QAAC,GAAG,CAAC;QAAC,KAAI;QAAE,QAAO;QAAE,SAAQ;IAAC;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,IAAE,KAAK,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,8TAAA,CAAA,iBAAE,AAAD,KAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAC,GAAE;QAAK,IAAG,KAAG,MAAK,OAAM;YAAC;YAAE;SAAK;QAAC,IAAG,OAAO,KAAG,UAAS,OAAM;YAAC;YAAE;SAAK;QAAC,IAAG,OAAO,KAAG,UAAS;YAAC,IAAG,CAAC,GAAE,OAAM;gBAAC;gBAAE;aAAK;YAAC,IAAI,IAAE,EAAE,GAAE;YAAG,OAAM;gBAAC;gBAAE,CAAA;oBAAI,IAAI,IAAE,EAAE;oBAAG;wBAAC,IAAI,IAAE,EAAE,GAAG,CAAC,CAAA,IAAG,OAAO,gBAAgB,CAAC,GAAG,gBAAgB,CAAC;wBAAI,EAAE,qBAAqB,CAAC,SAAS;4BAAI,EAAE,SAAS,CAAC;4BAAG,IAAI,IAAE,CAAC;4BAAE,KAAI,IAAG,CAAC,GAAE,EAAE,IAAG,EAAE,OAAO,GAAG;gCAAC,IAAI,IAAE,OAAO,gBAAgB,CAAC,GAAG,gBAAgB,CAAC;gCAAG,IAAG,CAAC,CAAC,EAAE,KAAG,GAAE;oCAAC,CAAC,CAAC,EAAE,GAAC,GAAE,IAAE,CAAC;oCAAE;gCAAK;4BAAC;4BAAC,IAAG,CAAC,GAAE;4BAAO,IAAI,IAAE,EAAE,GAAE;4BAAG,MAAI,KAAG,CAAC,EAAE,IAAG,IAAE,CAAC;wBAAC;oBAAE;oBAAC,OAAO,EAAE,OAAO;gBAAA;aAAE;QAAA;QAAC,OAAM;YAAC;YAAE;SAAK;IAAA,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,EAAE,GAAE,EAAE,CAAC,EAAE,EAAC;QAAC;QAAE;KAAE,GAAE,CAAC,IAAE,CAAC,EAAC,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD;IAAI,OAAO,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE,EAAE,GAAC,EAAE,GAAE;QAAG,IAAG,EAAE,IAAG,CAAC,CAAC,GAAE,OAAO,EAAE;IAAE,GAAE;QAAC;QAAE;KAAE,GAAE;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,cAAc,IAAI,CAAC;IAAG,IAAG,GAAE;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;QAAK,IAAG,MAAI,CAAC,GAAE,OAAM;YAAC,CAAC,CAAC,EAAE;SAAC;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAE,GAAG,IAAI,IAAG,IAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAE,GAAG,IAAI;QAAG,OAAO,IAAE;YAAC;eAAK,EAAE;SAAG,GAAC;YAAC;SAAE;IAAA;IAAC,OAAM,EAAE;AAAA;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,SAAS,aAAa,CAAC;IAAO,EAAE,WAAW,CAAC,IAAG,EAAE,KAAK,CAAC,WAAW,CAAC,cAAa,OAAM,cAAa,EAAE,KAAK,CAAC,WAAW,CAAC,cAAa,GAAE;IAAa,IAAI,IAAE,WAAW,OAAO,gBAAgB,CAAC,GAAG,SAAS,KAAG;IAAE,OAAO,EAAE,WAAW,CAAC,IAAG;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3776, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/form.js"], "sourcesContent": ["function e(i={},s=null,t=[]){for(let[r,n]of Object.entries(i))o(t,f(s,r),n);return t}function f(i,s){return i?i+\"[\"+s+\"]\":s}function o(i,s,t){if(Array.isArray(t))for(let[r,n]of t.entries())o(i,f(s,r.toString()),n);else t instanceof Date?i.push([s,t.toISOString()]):typeof t==\"boolean\"?i.push([s,t?\"1\":\"0\"]):typeof t==\"string\"?i.push([s,t]):typeof t==\"number\"?i.push([s,`${t}`]):t==null?i.push([s,\"\"]):e(t,s,i)}function p(i){var t,r;let s=(t=i==null?void 0:i.form)!=null?t:i.closest(\"form\");if(s){for(let n of s.elements)if(n!==i&&(n.tagName===\"INPUT\"&&n.type===\"submit\"||n.tagName===\"BUTTON\"&&n.type===\"submit\"||n.nodeName===\"INPUT\"&&n.type===\"image\")){n.click();return}(r=s.requestSubmit)==null||r.call(s)}}export{p as attemptSubmit,e as objectToFormEntries};\n"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,IAAE,CAAC,CAAC,EAAC,IAAE,IAAI,EAAC,IAAE,EAAE;IAAE,KAAI,IAAG,CAAC,GAAE,EAAE,IAAG,OAAO,OAAO,CAAC,GAAG,EAAE,GAAE,EAAE,GAAE,IAAG;IAAG,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,OAAO,IAAE,IAAE,MAAI,IAAE,MAAI;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAG,MAAM,OAAO,CAAC,IAAG,KAAI,IAAG,CAAC,GAAE,EAAE,IAAG,EAAE,OAAO,GAAG,EAAE,GAAE,EAAE,GAAE,EAAE,QAAQ,KAAI;SAAQ,aAAa,OAAK,EAAE,IAAI,CAAC;QAAC;QAAE,EAAE,WAAW;KAAG,IAAE,OAAO,KAAG,YAAU,EAAE,IAAI,CAAC;QAAC;QAAE,IAAE,MAAI;KAAI,IAAE,OAAO,KAAG,WAAS,EAAE,IAAI,CAAC;QAAC;QAAE;KAAE,IAAE,OAAO,KAAG,WAAS,EAAE,IAAI,CAAC;QAAC;QAAE,GAAG,GAAG;KAAC,IAAE,KAAG,OAAK,EAAE,IAAI,CAAC;QAAC;QAAE;KAAG,IAAE,EAAE,GAAE,GAAE;AAAE;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE;IAAE,IAAI,IAAE,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,KAAG,OAAK,IAAE,EAAE,OAAO,CAAC;IAAQ,IAAG,GAAE;QAAC,KAAI,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAG,MAAI,KAAG,CAAC,EAAE,OAAO,KAAG,WAAS,EAAE,IAAI,KAAG,YAAU,EAAE,OAAO,KAAG,YAAU,EAAE,IAAI,KAAG,YAAU,EAAE,QAAQ,KAAG,WAAS,EAAE,IAAI,KAAG,OAAO,GAAE;YAAC,EAAE,KAAK;YAAG;QAAM;QAAC,CAAC,IAAE,EAAE,aAAa,KAAG,QAAM,EAAE,IAAI,CAAC;IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3824, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/internal/form-fields.js"], "sourcesContent": ["import o,{createContext as H,useContext as E,useEffect as m,useState as u}from\"react\";import{createPortal as g}from\"react-dom\";import{useDisposables as h}from'../hooks/use-disposables.js';import{objectToFormEntries as x}from'../utils/form.js';import{compact as y}from'../utils/render.js';import{Hidden as l,HiddenFeatures as d}from'./hidden.js';let f=H(null);function W(t){let[e,r]=u(null);return o.createElement(f.Provider,{value:{target:e}},t.children,o.createElement(l,{features:d.Hidden,ref:r}))}function c({children:t}){let e=E(f);if(!e)return o.createElement(o.Fragment,null,t);let{target:r}=e;return r?g(o.createElement(o.Fragment,null,t),r):null}function j({data:t,form:e,disabled:r,onReset:n,overrides:F}){let[i,a]=u(null),p=h();return m(()=>{if(n&&i)return p.addEventListener(i,\"reset\",n)},[i,e,n]),o.createElement(c,null,o.createElement(C,{setForm:a,formId:e}),x(t).map(([s,v])=>o.createElement(l,{features:d.Hidden,...y({key:s,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,form:e,disabled:r,name:s,value:v,...F})})))}function C({setForm:t,formId:e}){return m(()=>{if(e){let r=document.getElementById(e);r&&t(r)}},[t,e]),e?null:o.createElement(l,{features:d.Hidden,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,ref:r=>{if(!r)return;let n=r.closest(\"form\");n&&t(n)}})}export{j as FormFields,W as FormFieldsProvider,c as HoistFormFields};\n"], "names": [], "mappings": ";;;;;AAAA;AAAsF;AAAyC;AAA6D;AAAuD;AAA6C;;;;;;;AAAyD,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,SAAS,EAAE,CAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE;IAAM,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;YAAC,QAAO;QAAC;IAAC,GAAE,EAAE,QAAQ,EAAC,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,qTAAA,CAAA,SAAC,EAAC;QAAC,UAAS,qTAAA,CAAA,iBAAC,CAAC,MAAM;QAAC,KAAI;IAAC;AAAG;AAAC,SAAS,EAAE,EAAC,UAAS,CAAC,EAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;IAAG,IAAG,CAAC,GAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,oUAAA,CAAA,UAAC,CAAC,QAAQ,EAAC,MAAK;IAAG,IAAG,EAAC,QAAO,CAAC,EAAC,GAAC;IAAE,OAAO,IAAE,CAAA,GAAA,2UAAA,CAAA,eAAC,AAAD,EAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,oUAAA,CAAA,UAAC,CAAC,QAAQ,EAAC,MAAK,IAAG,KAAG;AAAI;AAAC,SAAS,EAAE,EAAC,MAAK,CAAC,EAAC,MAAK,CAAC,EAAC,UAAS,CAAC,EAAC,SAAQ,CAAC,EAAC,WAAU,CAAC,EAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,8TAAA,CAAA,iBAAC,AAAD;IAAI,OAAO,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE;QAAK,IAAG,KAAG,GAAE,OAAO,EAAE,gBAAgB,CAAC,GAAE,SAAQ;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE,MAAK,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAE;QAAC,SAAQ;QAAE,QAAO;IAAC,IAAG,CAAA,GAAA,gTAAA,CAAA,sBAAC,AAAD,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAE,EAAE,GAAG,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,qTAAA,CAAA,SAAC,EAAC;YAAC,UAAS,qTAAA,CAAA,iBAAC,CAAC,MAAM;YAAC,GAAG,CAAA,GAAA,kTAAA,CAAA,UAAC,AAAD,EAAE;gBAAC,KAAI;gBAAE,IAAG;gBAAQ,MAAK;gBAAS,QAAO,CAAC;gBAAE,UAAS,CAAC;gBAAE,MAAK;gBAAE,UAAS;gBAAE,MAAK;gBAAE,OAAM;gBAAE,GAAG,CAAC;YAAA,EAAE;QAAA;AAAI;AAAC,SAAS,EAAE,EAAC,SAAQ,CAAC,EAAC,QAAO,CAAC,EAAC;IAAE,OAAO,CAAA,GAAA,oUAAA,CAAA,YAAC,AAAD,EAAE;QAAK,IAAG,GAAE;YAAC,IAAI,IAAE,SAAS,cAAc,CAAC;YAAG,KAAG,EAAE;QAAE;IAAC,GAAE;QAAC;QAAE;KAAE,GAAE,IAAE,OAAK,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,qTAAA,CAAA,SAAC,EAAC;QAAC,UAAS,qTAAA,CAAA,iBAAC,CAAC,MAAM;QAAC,IAAG;QAAQ,MAAK;QAAS,QAAO,CAAC;QAAE,UAAS,CAAC;QAAE,KAAI,CAAA;YAAI,IAAG,CAAC,GAAE;YAAO,IAAI,IAAE,EAAE,OAAO,CAAC;YAAQ,KAAG,EAAE;QAAE;IAAC;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3915, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/internal/frozen.js"], "sourcesContent": ["import r,{useState as u}from\"react\";function f({children:o,freeze:e}){let n=l(e,o);return r.createElement(r.Fragment,null,n)}function l(o,e){let[n,t]=u(e);return!o&&n!==e&&t(e),o?n:e}export{f as Frozen,l as useFrozenData};\n"], "names": [], "mappings": ";;;;AAAA;;AAAoC,SAAS,EAAE,EAAC,UAAS,CAAC,EAAC,QAAO,CAAC,EAAC;IAAE,IAAI,IAAE,EAAE,GAAE;IAAG,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,oUAAA,CAAA,UAAC,CAAC,QAAQ,EAAC,MAAK;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE;IAAG,OAAM,CAAC,KAAG,MAAI,KAAG,EAAE,IAAG,IAAE,IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3936, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/internal/id.js"], "sourcesContent": ["import n,{createContext as d,useContext as i}from\"react\";let e=d(void 0);function u(){return i(e)}function f({id:t,children:r}){return n.createElement(e.Provider,{value:t},r)}export{f as IdProvider,u as useProvidedId};\n"], "names": [], "mappings": ";;;;AAAA;;AAAyD,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE,KAAK;AAAG,SAAS;IAAI,OAAO,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;AAAE;AAAC,SAAS,EAAE,EAAC,IAAG,CAAC,EAAC,UAAS,CAAC,EAAC;IAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3958, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/bugs.js"], "sourcesContent": ["import*as n from'./dom.js';function s(l){let e=l.parentElement,t=null;for(;e&&!n.isHTMLFieldSetElement(e);)n.isHTMLLegendElement(e)&&(t=e),e=e.parentElement;let i=(e==null?void 0:e.get<PERSON>ttribute(\"disabled\"))===\"\";return i&&r(t)?!1:i}function r(l){if(!l)return!1;let e=l.previousElementSibling;for(;e!==null;){if(n.isHTMLLegendElement(e))return!1;e=e.previousElementSibling}return!0}export{s as isDisabledReactIssue7711};\n"], "names": [], "mappings": ";;;AAAA;;AAA2B,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,EAAE,aAAa,EAAC,IAAE;IAAK,MAAK,KAAG,CAAC,CAAA,GAAA,+SAAA,CAAA,wBAAuB,AAAD,EAAE,IAAI,CAAA,GAAA,+SAAA,CAAA,sBAAqB,AAAD,EAAE,MAAI,CAAC,IAAE,CAAC,GAAE,IAAE,EAAE,aAAa;IAAC,IAAI,IAAE,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,YAAY,CAAC,WAAW,MAAI;IAAG,OAAO,KAAG,EAAE,KAAG,CAAC,IAAE;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,IAAG,CAAC,GAAE,OAAM,CAAC;IAAE,IAAI,IAAE,EAAE,sBAAsB;IAAC,MAAK,MAAI,MAAM;QAAC,IAAG,CAAA,GAAA,+SAAA,CAAA,sBAAqB,AAAD,EAAE,IAAG,OAAM,CAAC;QAAE,IAAE,EAAE,sBAAsB;IAAA;IAAC,OAAM,CAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3985, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/calculate-active-index.js"], "sourcesContent": ["function u(l){throw new Error(\"Unexpected object: \"+l)}var c=(i=>(i[i.First=0]=\"First\",i[i.Previous=1]=\"Previous\",i[i.Next=2]=\"Next\",i[i.Last=3]=\"Last\",i[i.Specific=4]=\"Specific\",i[i.Nothing=5]=\"Nothing\",i))(c||{});function f(l,n){let t=n.resolveItems();if(t.length<=0)return null;let r=n.resolveActiveIndex(),s=r!=null?r:-1;switch(l.focus){case 0:{for(let e=0;e<t.length;++e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 1:{s===-1&&(s=t.length);for(let e=s-1;e>=0;--e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 2:{for(let e=s+1;e<t.length;++e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 3:{for(let e=t.length-1;e>=0;--e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 4:{for(let e=0;e<t.length;++e)if(n.resolveId(t[e],e,t)===l.id)return e;return r}case 5:return null;default:u(l)}}export{c as Focus,f as calculateActiveIndex};\n"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,MAAM,IAAI,MAAM,wBAAsB;AAAE;AAAC,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,QAAQ,GAAC,EAAE,GAAC,YAAW,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,QAAQ,GAAC,EAAE,GAAC,YAAW,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,EAAE,YAAY;IAAG,IAAG,EAAE,MAAM,IAAE,GAAE,OAAO;IAAK,IAAI,IAAE,EAAE,kBAAkB,IAAG,IAAE,KAAG,OAAK,IAAE,CAAC;IAAE,OAAO,EAAE,KAAK;QAAE,KAAK;YAAE;gBAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,EAAE,EAAE,IAAG,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,EAAC,GAAE,IAAG,OAAO;gBAAE,OAAO;YAAC;QAAC,KAAK;YAAE;gBAAC,MAAI,CAAC,KAAG,CAAC,IAAE,EAAE,MAAM;gBAAE,IAAI,IAAI,IAAE,IAAE,GAAE,KAAG,GAAE,EAAE,EAAE,IAAG,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,EAAC,GAAE,IAAG,OAAO;gBAAE,OAAO;YAAC;QAAC,KAAK;YAAE;gBAAC,IAAI,IAAI,IAAE,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,EAAE,EAAE,IAAG,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,EAAC,GAAE,IAAG,OAAO;gBAAE,OAAO;YAAC;QAAC,KAAK;YAAE;gBAAC,IAAI,IAAI,IAAE,EAAE,MAAM,GAAC,GAAE,KAAG,GAAE,EAAE,EAAE,IAAG,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,EAAC,GAAE,IAAG,OAAO;gBAAE,OAAO;YAAC;QAAC,KAAK;YAAE;gBAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,EAAE,EAAE,IAAG,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,EAAC,GAAE,OAAK,EAAE,EAAE,EAAC,OAAO;gBAAE,OAAO;YAAC;QAAC,KAAK;YAAE,OAAO;QAAK;YAAQ,EAAE;IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4037, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/label/label.js"], "sourcesContent": ["\"use client\";import D,{createContext as k,useContext as v,useMemo as T,useState as _}from\"react\";import{useEvent as P}from'../../hooks/use-event.js';import{useId as A}from'../../hooks/use-id.js';import{useIsoMorphicEffect as B}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as F}from'../../hooks/use-sync-refs.js';import{useDisabled as M}from'../../internal/disabled.js';import{useProvidedId as S}from'../../internal/id.js';import*as m from'../../utils/dom.js';import{forwardRefWithAs as I,useRender as H}from'../../utils/render.js';let L=k(null);L.displayName=\"LabelContext\";function C(){let n=v(L);if(n===null){let l=new Error(\"You used a <Label /> component, but it is not inside a relevant parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(l,C),l}return n}function N(n){var a,e,o;let l=(e=(a=v(L))==null?void 0:a.value)!=null?e:void 0;return((o=n==null?void 0:n.length)!=null?o:0)>0?[l,...n].filter(Boolean).join(\" \"):l}function Q({inherit:n=!1}={}){let l=N(),[a,e]=_([]),o=n?[l,...a].filter(Boolean):a;return[o.length>0?o.join(\" \"):void 0,T(()=>function(t){let p=P(i=>(e(u=>[...u,i]),()=>e(u=>{let d=u.slice(),f=d.indexOf(i);return f!==-1&&d.splice(f,1),d}))),b=T(()=>({register:p,slot:t.slot,name:t.name,props:t.props,value:t.value}),[p,t.slot,t.name,t.props,t.value]);return D.createElement(L.Provider,{value:b},t.children)},[e])]}let G=\"label\";function U(n,l){var E;let a=A(),e=C(),o=S(),y=M(),{id:t=`headlessui-label-${a}`,htmlFor:p=o!=null?o:(E=e.props)==null?void 0:E.htmlFor,passive:b=!1,...i}=n,u=F(l);B(()=>e.register(t),[t,e.register]);let d=P(s=>{let g=s.currentTarget;if(!(s.target!==s.currentTarget&&m.isInteractiveElement(s.target))&&(m.isHTMLLabelElement(g)&&s.preventDefault(),e.props&&\"onClick\"in e.props&&typeof e.props.onClick==\"function\"&&e.props.onClick(s),m.isHTMLLabelElement(g))){let r=document.getElementById(g.htmlFor);if(r){let x=r.getAttribute(\"disabled\");if(x===\"true\"||x===\"\")return;let h=r.getAttribute(\"aria-disabled\");if(h===\"true\"||h===\"\")return;(m.isHTMLInputElement(r)&&(r.type===\"file\"||r.type===\"radio\"||r.type===\"checkbox\")||r.role===\"radio\"||r.role===\"checkbox\"||r.role===\"switch\")&&r.click(),r.focus({preventScroll:!0})}}}),f=y||!1,R=T(()=>({...e.slot,disabled:f}),[e.slot,f]),c={ref:u,...e.props,id:t,htmlFor:p,onClick:d};return b&&(\"onClick\"in c&&(delete c.htmlFor,delete c.onClick),\"onClick\"in i&&delete i.onClick),H()({ourProps:c,theirProps:i,slot:R,defaultTag:p?G:\"div\",name:e.name||\"Label\"})}let j=I(U),V=Object.assign(j,{});export{V as Label,C as useLabelContext,N as useLabelledBy,Q as useLabels};\n"], "names": [], "mappings": ";;;;;;AAAa;AAAoF;AAAkG;AAA4E;AAA2D;AAAyD;AAAqD;AAAqC;AAA7d;;;;;;;;;;AAAqiB,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAe,SAAS;IAAI,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;IAAG,IAAG,MAAI,MAAK;QAAC,IAAI,IAAE,IAAI,MAAM;QAA2E,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG;IAAC;IAAC,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE;IAAE,IAAI,IAAE,CAAC,IAAE,CAAC,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,KAAK,KAAG,OAAK,IAAE,KAAK;IAAE,OAAM,CAAC,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,MAAM,KAAG,OAAK,IAAE,CAAC,IAAE,IAAE;QAAC;WAAK;KAAE,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,OAAK;AAAC;AAAC,SAAS,EAAE,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,GAAC,CAAC,CAAC;IAAE,IAAI,IAAE,KAAI,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAC,AAAD,EAAE,EAAE,GAAE,IAAE,IAAE;QAAC;WAAK;KAAE,CAAC,MAAM,CAAC,WAAS;IAAE,OAAM;QAAC,EAAE,MAAM,GAAC,IAAE,EAAE,IAAI,CAAC,OAAK,KAAK;QAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,SAAS,CAAC;gBAAE,IAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,CAAC,EAAE,CAAA,IAAG;+BAAI;4BAAE;yBAAE,GAAE,IAAI,EAAE,CAAA;4BAAI,IAAI,IAAE,EAAE,KAAK,IAAG,IAAE,EAAE,OAAO,CAAC;4BAAG,OAAO,MAAI,CAAC,KAAG,EAAE,MAAM,CAAC,GAAE,IAAG;wBAAC,EAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;wBAAC,UAAS;wBAAE,MAAK,EAAE,IAAI;wBAAC,MAAK,EAAE,IAAI;wBAAC,OAAM,EAAE,KAAK;wBAAC,OAAM,EAAE,KAAK;oBAAA,CAAC,GAAE;oBAAC;oBAAE,EAAE,IAAI;oBAAC,EAAE,IAAI;oBAAC,EAAE,KAAK;oBAAC,EAAE,KAAK;iBAAC;gBAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;oBAAC,OAAM;gBAAC,GAAE,EAAE,QAAQ;YAAC,GAAE;YAAC;SAAE;KAAE;AAAA;AAAC,IAAI,IAAE;AAAQ,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAC,AAAD,KAAI,IAAE,KAAI,IAAE,CAAA,GAAA,iTAAA,CAAA,gBAAC,AAAD,KAAI,IAAE,CAAA,GAAA,uTAAA,CAAA,cAAC,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,iBAAiB,EAAE,GAAG,EAAC,SAAQ,IAAE,KAAG,OAAK,IAAE,CAAC,IAAE,EAAE,KAAK,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE;IAAG,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAG;QAAC;QAAE,EAAE,QAAQ;KAAC;IAAE,IAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,EAAE,aAAa;QAAC,IAAG,CAAC,CAAC,EAAE,MAAM,KAAG,EAAE,aAAa,IAAE,CAAA,GAAA,+SAAA,CAAA,uBAAsB,AAAD,EAAE,EAAE,MAAM,CAAC,KAAG,CAAC,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,MAAI,EAAE,cAAc,IAAG,EAAE,KAAK,IAAE,aAAY,EAAE,KAAK,IAAE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAE,cAAY,EAAE,KAAK,CAAC,OAAO,CAAC,IAAG,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,EAAE,GAAE;YAAC,IAAI,IAAE,SAAS,cAAc,CAAC,EAAE,OAAO;YAAE,IAAG,GAAE;gBAAC,IAAI,IAAE,EAAE,YAAY,CAAC;gBAAY,IAAG,MAAI,UAAQ,MAAI,IAAG;gBAAO,IAAI,IAAE,EAAE,YAAY,CAAC;gBAAiB,IAAG,MAAI,UAAQ,MAAI,IAAG;gBAAO,CAAC,CAAA,GAAA,+SAAA,CAAA,qBAAoB,AAAD,EAAE,MAAI,CAAC,EAAE,IAAI,KAAG,UAAQ,EAAE,IAAI,KAAG,WAAS,EAAE,IAAI,KAAG,UAAU,KAAG,EAAE,IAAI,KAAG,WAAS,EAAE,IAAI,KAAG,cAAY,EAAE,IAAI,KAAG,QAAQ,KAAG,EAAE,KAAK,IAAG,EAAE,KAAK,CAAC;oBAAC,eAAc,CAAC;gBAAC;YAAE;QAAC;IAAC,IAAG,IAAE,KAAG,CAAC,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,GAAG,EAAE,IAAI;YAAC,UAAS;QAAC,CAAC,GAAE;QAAC,EAAE,IAAI;QAAC;KAAE,GAAE,IAAE;QAAC,KAAI;QAAE,GAAG,EAAE,KAAK;QAAC,IAAG;QAAE,SAAQ;QAAE,SAAQ;IAAC;IAAE,OAAO,KAAG,CAAC,aAAY,KAAG,CAAC,OAAO,EAAE,OAAO,EAAC,OAAO,EAAE,OAAO,GAAE,aAAY,KAAG,OAAO,EAAE,OAAO,GAAE,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD,IAAI;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW,IAAE,IAAE;QAAM,MAAK,EAAE,IAAI,IAAE;IAAO;AAAE;AAAC,IAAI,IAAE,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,IAAG,IAAE,OAAO,MAAM,CAAC,GAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4165, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/mouse.js"], "sourcesContent": ["var g=(f=>(f[f.Left=0]=\"Left\",f[f.Right=2]=\"Right\",f))(g||{});export{g as MouseButton};\n"], "names": [], "mappings": ";;;AAAA,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAG,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4176, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/combobox/combobox-machine.js"], "sourcesContent": ["var S=Object.defineProperty;var I=(t,i,e)=>i in t?S(t,i,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[i]=e;var c=(t,i,e)=>(I(t,typeof i!=\"symbol\"?i+\"\":i,e),e);import{Machine as h}from'../../machine.js';import{ActionTypes as R,stackMachines as A}from'../../machines/stack-machine.js';import{Focus as f,calculateActiveIndex as x}from'../../utils/calculate-active-index.js';import{sortByDomNode as E}from'../../utils/focus-management.js';import{match as g}from'../../utils/match.js';var C=(e=>(e[e.Open=0]=\"Open\",e[e.Closed=1]=\"Closed\",e))(C||{}),M=(e=>(e[e.Single=0]=\"Single\",e[e.Multi=1]=\"Multi\",e))(M||{}),F=(n=>(n[n.Pointer=0]=\"Pointer\",n[n.Focus=1]=\"Focus\",n[n.Other=2]=\"Other\",n))(F||{}),_=(l=>(l[l.OpenCombobox=0]=\"OpenCombobox\",l[l.CloseCombobox=1]=\"CloseCombobox\",l[l.GoToOption=2]=\"GoToOption\",l[l.SetTyping=3]=\"SetTyping\",l[l.RegisterOption=4]=\"RegisterOption\",l[l.UnregisterOption=5]=\"UnregisterOption\",l[l.DefaultToFirstOption=6]=\"DefaultToFirstOption\",l[l.SetActivationTrigger=7]=\"SetActivationTrigger\",l[l.UpdateVirtualConfiguration=8]=\"UpdateVirtualConfiguration\",l[l.SetInputElement=9]=\"SetInputElement\",l[l.SetButtonElement=10]=\"SetButtonElement\",l[l.SetOptionsElement=11]=\"SetOptionsElement\",l))(_||{});function T(t,i=e=>e){let e=t.activeOptionIndex!==null?t.options[t.activeOptionIndex]:null,n=i(t.options.slice()),o=n.length>0&&n[0].dataRef.current.order!==null?n.sort((u,a)=>u.dataRef.current.order-a.dataRef.current.order):E(n,u=>u.dataRef.current.domRef.current),r=e?o.indexOf(e):null;return r===-1&&(r=null),{options:o,activeOptionIndex:r}}let D={[1](t){var i;return(i=t.dataRef.current)!=null&&i.disabled||t.comboboxState===1?t:{...t,activeOptionIndex:null,comboboxState:1,isTyping:!1,activationTrigger:2,__demoMode:!1}},[0](t){var i,e;if((i=t.dataRef.current)!=null&&i.disabled||t.comboboxState===0)return t;if((e=t.dataRef.current)!=null&&e.value){let n=t.dataRef.current.calculateIndex(t.dataRef.current.value);if(n!==-1)return{...t,activeOptionIndex:n,comboboxState:0,__demoMode:!1}}return{...t,comboboxState:0,__demoMode:!1}},[3](t,i){return t.isTyping===i.isTyping?t:{...t,isTyping:i.isTyping}},[2](t,i){var r,u,a,d;if((r=t.dataRef.current)!=null&&r.disabled||t.optionsElement&&!((u=t.dataRef.current)!=null&&u.optionsPropsRef.current.static)&&t.comboboxState===1)return t;if(t.virtual){let{options:p,disabled:s}=t.virtual,b=i.focus===f.Specific?i.idx:x(i,{resolveItems:()=>p,resolveActiveIndex:()=>{var v,m;return(m=(v=t.activeOptionIndex)!=null?v:p.findIndex(y=>!s(y)))!=null?m:null},resolveDisabled:s,resolveId(){throw new Error(\"Function not implemented.\")}}),l=(a=i.trigger)!=null?a:2;return t.activeOptionIndex===b&&t.activationTrigger===l?t:{...t,activeOptionIndex:b,activationTrigger:l,isTyping:!1,__demoMode:!1}}let e=T(t);if(e.activeOptionIndex===null){let p=e.options.findIndex(s=>!s.dataRef.current.disabled);p!==-1&&(e.activeOptionIndex=p)}let n=i.focus===f.Specific?i.idx:x(i,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:p=>p.id,resolveDisabled:p=>p.dataRef.current.disabled}),o=(d=i.trigger)!=null?d:2;return t.activeOptionIndex===n&&t.activationTrigger===o?t:{...t,...e,isTyping:!1,activeOptionIndex:n,activationTrigger:o,__demoMode:!1}},[4]:(t,i)=>{var r,u,a,d;if((r=t.dataRef.current)!=null&&r.virtual)return{...t,options:[...t.options,i.payload]};let e=i.payload,n=T(t,p=>(p.push(e),p));t.activeOptionIndex===null&&(a=(u=t.dataRef.current).isSelected)!=null&&a.call(u,i.payload.dataRef.current.value)&&(n.activeOptionIndex=n.options.indexOf(e));let o={...t,...n,activationTrigger:2};return(d=t.dataRef.current)!=null&&d.__demoMode&&t.dataRef.current.value===void 0&&(o.activeOptionIndex=0),o},[5]:(t,i)=>{var n;if((n=t.dataRef.current)!=null&&n.virtual)return{...t,options:t.options.filter(o=>o.id!==i.id)};let e=T(t,o=>{let r=o.findIndex(u=>u.id===i.id);return r!==-1&&o.splice(r,1),o});return{...t,...e,activationTrigger:2}},[6]:(t,i)=>t.defaultToFirstOption===i.value?t:{...t,defaultToFirstOption:i.value},[7]:(t,i)=>t.activationTrigger===i.trigger?t:{...t,activationTrigger:i.trigger},[8]:(t,i)=>{var n,o;if(t.virtual===null)return{...t,virtual:{options:i.options,disabled:(n=i.disabled)!=null?n:()=>!1}};if(t.virtual.options===i.options&&t.virtual.disabled===i.disabled)return t;let e=t.activeOptionIndex;if(t.activeOptionIndex!==null){let r=i.options.indexOf(t.virtual.options[t.activeOptionIndex]);r!==-1?e=r:e=null}return{...t,activeOptionIndex:e,virtual:{options:i.options,disabled:(o=i.disabled)!=null?o:()=>!1}}},[9]:(t,i)=>t.inputElement===i.element?t:{...t,inputElement:i.element},[10]:(t,i)=>t.buttonElement===i.element?t:{...t,buttonElement:i.element},[11]:(t,i)=>t.optionsElement===i.element?t:{...t,optionsElement:i.element}};class O extends h{constructor(e){super(e);c(this,\"actions\",{onChange:e=>{let{onChange:n,compare:o,mode:r,value:u}=this.state.dataRef.current;return g(r,{[0]:()=>n==null?void 0:n(e),[1]:()=>{let a=u.slice(),d=a.findIndex(p=>o(p,e));return d===-1?a.push(e):a.splice(d,1),n==null?void 0:n(a)}})},registerOption:(e,n)=>(this.send({type:4,payload:{id:e,dataRef:n}}),()=>{this.state.activeOptionIndex===this.state.dataRef.current.calculateIndex(n.current.value)&&this.send({type:6,value:!0}),this.send({type:5,id:e})}),goToOption:(e,n)=>(this.send({type:6,value:!1}),this.send({type:2,...e,trigger:n})),setIsTyping:e=>{this.send({type:3,isTyping:e})},closeCombobox:()=>{var e,n;this.send({type:1}),this.send({type:6,value:!1}),(n=(e=this.state.dataRef.current).onClose)==null||n.call(e)},openCombobox:()=>{this.send({type:0}),this.send({type:6,value:!0})},setActivationTrigger:e=>{this.send({type:7,trigger:e})},selectActiveOption:()=>{let e=this.selectors.activeOptionIndex(this.state);if(e!==null){if(this.actions.setIsTyping(!1),this.state.virtual)this.actions.onChange(this.state.virtual.options[e]);else{let{dataRef:n}=this.state.options[e];this.actions.onChange(n.current.value)}this.actions.goToOption({focus:f.Specific,idx:e})}},setInputElement:e=>{this.send({type:9,element:e})},setButtonElement:e=>{this.send({type:10,element:e})},setOptionsElement:e=>{this.send({type:11,element:e})}});c(this,\"selectors\",{activeDescendantId:e=>{var o,r;let n=this.selectors.activeOptionIndex(e);if(n!==null)return e.virtual?(r=e.options.find(u=>!u.dataRef.current.disabled&&e.dataRef.current.compare(u.dataRef.current.value,e.virtual.options[n])))==null?void 0:r.id:(o=e.options[n])==null?void 0:o.id},activeOptionIndex:e=>{if(e.defaultToFirstOption&&e.activeOptionIndex===null&&(e.virtual?e.virtual.options.length>0:e.options.length>0)){if(e.virtual){let{options:o,disabled:r}=e.virtual,u=o.findIndex(a=>{var d;return!((d=r==null?void 0:r(a))!=null&&d)});if(u!==-1)return u}let n=e.options.findIndex(o=>!o.dataRef.current.disabled);if(n!==-1)return n}return e.activeOptionIndex},activeOption:e=>{var o,r;let n=this.selectors.activeOptionIndex(e);return n===null?null:e.virtual?e.virtual.options[n!=null?n:0]:(r=(o=e.options[n])==null?void 0:o.dataRef.current.value)!=null?r:null},isActive:(e,n,o)=>{var u;let r=this.selectors.activeOptionIndex(e);return r===null?!1:e.virtual?r===e.dataRef.current.calculateIndex(n):((u=e.options[r])==null?void 0:u.id)===o},shouldScrollIntoView:(e,n,o)=>!(e.virtual||e.__demoMode||e.comboboxState!==0||e.activationTrigger===0||!this.selectors.isActive(e,n,o))});{let n=this.state.id,o=A.get(null);this.disposables.add(o.on(R.Push,r=>{!o.selectors.isTop(r,n)&&this.state.comboboxState===0&&this.actions.closeCombobox()})),this.on(0,()=>o.actions.push(n)),this.on(1,()=>o.actions.pop(n))}}static new({id:e,virtual:n=null,__demoMode:o=!1}){var r;return new O({id:e,dataRef:{current:{}},comboboxState:o?0:1,isTyping:!1,options:[],virtual:n?{options:n.options,disabled:(r=n.disabled)!=null?r:()=>!1}:null,activeOptionIndex:null,activationTrigger:2,inputElement:null,buttonElement:null,optionsElement:null,__demoMode:o})}reduce(e,n){return g(n.type,D,e,n)}}export{_ as ActionTypes,F as ActivationTrigger,O as ComboboxMachine,C as ComboboxState,M as ValueMode};\n"], "names": [], "mappings": ";;;;;;;AAAwK;AAA2C;AAAiF;AAAwF;AAAgE;AAA5b,IAAI,IAAE,OAAO,cAAc;AAAC,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,KAAK,IAAE,EAAE,GAAE,GAAE;QAAC,YAAW,CAAC;QAAE,cAAa,CAAC;QAAE,UAAS,CAAC;QAAE,OAAM;IAAC,KAAG,CAAC,CAAC,EAAE,GAAC;AAAE,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,CAAC,EAAE,GAAE,OAAO,KAAG,WAAS,IAAE,KAAG,GAAE,IAAG,CAAC;;;;;;AAAmU,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAG,CAAC,IAAG,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAG,CAAC,IAAG,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAG,CAAC,IAAG,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,YAAY,GAAC,EAAE,GAAC,gBAAe,CAAC,CAAC,EAAE,aAAa,GAAC,EAAE,GAAC,iBAAgB,CAAC,CAAC,EAAE,UAAU,GAAC,EAAE,GAAC,cAAa,CAAC,CAAC,EAAE,SAAS,GAAC,EAAE,GAAC,aAAY,CAAC,CAAC,EAAE,cAAc,GAAC,EAAE,GAAC,kBAAiB,CAAC,CAAC,EAAE,gBAAgB,GAAC,EAAE,GAAC,oBAAmB,CAAC,CAAC,EAAE,oBAAoB,GAAC,EAAE,GAAC,wBAAuB,CAAC,CAAC,EAAE,oBAAoB,GAAC,EAAE,GAAC,wBAAuB,CAAC,CAAC,EAAE,0BAA0B,GAAC,EAAE,GAAC,8BAA6B,CAAC,CAAC,EAAE,eAAe,GAAC,EAAE,GAAC,mBAAkB,CAAC,CAAC,EAAE,gBAAgB,GAAC,GAAG,GAAC,oBAAmB,CAAC,CAAC,EAAE,iBAAiB,GAAC,GAAG,GAAC,qBAAoB,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,CAAC,EAAC,IAAE,CAAA,IAAG,CAAC;IAAE,IAAI,IAAE,EAAE,iBAAiB,KAAG,OAAK,EAAE,OAAO,CAAC,EAAE,iBAAiB,CAAC,GAAC,MAAK,IAAE,EAAE,EAAE,OAAO,CAAC,KAAK,KAAI,IAAE,EAAE,MAAM,GAAC,KAAG,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,KAAG,OAAK,EAAE,IAAI,CAAC,CAAC,GAAE,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,GAAC,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,IAAE,CAAA,GAAA,+TAAA,CAAA,gBAAC,AAAD,EAAE,GAAE,CAAA,IAAG,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,GAAE,IAAE,IAAE,EAAE,OAAO,CAAC,KAAG;IAAK,OAAO,MAAI,CAAC,KAAG,CAAC,IAAE,IAAI,GAAE;QAAC,SAAQ;QAAE,mBAAkB;IAAC;AAAC;AAAC,IAAI,IAAE;IAAC,CAAC,EAAE,EAAC,CAAC;QAAE,IAAI;QAAE,OAAM,CAAC,IAAE,EAAE,OAAO,CAAC,OAAO,KAAG,QAAM,EAAE,QAAQ,IAAE,EAAE,aAAa,KAAG,IAAE,IAAE;YAAC,GAAG,CAAC;YAAC,mBAAkB;YAAK,eAAc;YAAE,UAAS,CAAC;YAAE,mBAAkB;YAAE,YAAW,CAAC;QAAC;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC;QAAE,IAAI,GAAE;QAAE,IAAG,CAAC,IAAE,EAAE,OAAO,CAAC,OAAO,KAAG,QAAM,EAAE,QAAQ,IAAE,EAAE,aAAa,KAAG,GAAE,OAAO;QAAE,IAAG,CAAC,IAAE,EAAE,OAAO,CAAC,OAAO,KAAG,QAAM,EAAE,KAAK,EAAC;YAAC,IAAI,IAAE,EAAE,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK;YAAE,IAAG,MAAI,CAAC,GAAE,OAAM;gBAAC,GAAG,CAAC;gBAAC,mBAAkB;gBAAE,eAAc;gBAAE,YAAW,CAAC;YAAC;QAAC;QAAC,OAAM;YAAC,GAAG,CAAC;YAAC,eAAc;YAAE,YAAW,CAAC;QAAC;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,OAAO,EAAE,QAAQ,KAAG,EAAE,QAAQ,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,UAAS,EAAE,QAAQ;QAAA;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,GAAE,GAAE,GAAE;QAAE,IAAG,CAAC,IAAE,EAAE,OAAO,CAAC,OAAO,KAAG,QAAM,EAAE,QAAQ,IAAE,EAAE,cAAc,IAAE,CAAC,CAAC,CAAC,IAAE,EAAE,OAAO,CAAC,OAAO,KAAG,QAAM,EAAE,eAAe,CAAC,OAAO,CAAC,MAAM,KAAG,EAAE,aAAa,KAAG,GAAE,OAAO;QAAE,IAAG,EAAE,OAAO,EAAC;YAAC,IAAG,EAAC,SAAQ,CAAC,EAAC,UAAS,CAAC,EAAC,GAAC,EAAE,OAAO,EAAC,IAAE,EAAE,KAAK,KAAG,wUAAA,CAAA,QAAC,CAAC,QAAQ,GAAC,EAAE,GAAG,GAAC,CAAA,GAAA,wUAAA,CAAA,uBAAC,AAAD,EAAE,GAAE;gBAAC,cAAa,IAAI;gBAAE,oBAAmB;oBAAK,IAAI,GAAE;oBAAE,OAAM,CAAC,IAAE,CAAC,IAAE,EAAE,iBAAiB,KAAG,OAAK,IAAE,EAAE,SAAS,CAAC,CAAA,IAAG,CAAC,EAAE,GAAG,KAAG,OAAK,IAAE;gBAAI;gBAAE,iBAAgB;gBAAE;oBAAY,MAAM,IAAI,MAAM;gBAA4B;YAAC,IAAG,IAAE,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,IAAE;YAAE,OAAO,EAAE,iBAAiB,KAAG,KAAG,EAAE,iBAAiB,KAAG,IAAE,IAAE;gBAAC,GAAG,CAAC;gBAAC,mBAAkB;gBAAE,mBAAkB;gBAAE,UAAS,CAAC;gBAAE,YAAW,CAAC;YAAC;QAAC;QAAC,IAAI,IAAE,EAAE;QAAG,IAAG,EAAE,iBAAiB,KAAG,MAAK;YAAC,IAAI,IAAE,EAAE,OAAO,CAAC,SAAS,CAAC,CAAA,IAAG,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;YAAE,MAAI,CAAC,KAAG,CAAC,EAAE,iBAAiB,GAAC,CAAC;QAAC;QAAC,IAAI,IAAE,EAAE,KAAK,KAAG,wUAAA,CAAA,QAAC,CAAC,QAAQ,GAAC,EAAE,GAAG,GAAC,CAAA,GAAA,wUAAA,CAAA,uBAAC,AAAD,EAAE,GAAE;YAAC,cAAa,IAAI,EAAE,OAAO;YAAC,oBAAmB,IAAI,EAAE,iBAAiB;YAAC,WAAU,CAAA,IAAG,EAAE,EAAE;YAAC,iBAAgB,CAAA,IAAG,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;QAAA,IAAG,IAAE,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,IAAE;QAAE,OAAO,EAAE,iBAAiB,KAAG,KAAG,EAAE,iBAAiB,KAAG,IAAE,IAAE;YAAC,GAAG,CAAC;YAAC,GAAG,CAAC;YAAC,UAAS,CAAC;YAAE,mBAAkB;YAAE,mBAAkB;YAAE,YAAW,CAAC;QAAC;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,GAAE;QAAK,IAAI,GAAE,GAAE,GAAE;QAAE,IAAG,CAAC,IAAE,EAAE,OAAO,CAAC,OAAO,KAAG,QAAM,EAAE,OAAO,EAAC,OAAM;YAAC,GAAG,CAAC;YAAC,SAAQ;mBAAI,EAAE,OAAO;gBAAC,EAAE,OAAO;aAAC;QAAA;QAAE,IAAI,IAAE,EAAE,OAAO,EAAC,IAAE,EAAE,GAAE,CAAA,IAAG,CAAC,EAAE,IAAI,CAAC,IAAG,CAAC;QAAG,EAAE,iBAAiB,KAAG,QAAM,CAAC,IAAE,CAAC,IAAE,EAAE,OAAO,CAAC,OAAO,EAAE,UAAU,KAAG,QAAM,EAAE,IAAI,CAAC,GAAE,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,KAAG,CAAC,EAAE,iBAAiB,GAAC,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE;QAAE,IAAI,IAAE;YAAC,GAAG,CAAC;YAAC,GAAG,CAAC;YAAC,mBAAkB;QAAC;QAAE,OAAM,CAAC,IAAE,EAAE,OAAO,CAAC,OAAO,KAAG,QAAM,EAAE,UAAU,IAAE,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,KAAG,KAAK,KAAG,CAAC,EAAE,iBAAiB,GAAC,CAAC,GAAE;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,GAAE;QAAK,IAAI;QAAE,IAAG,CAAC,IAAE,EAAE,OAAO,CAAC,OAAO,KAAG,QAAM,EAAE,OAAO,EAAC,OAAM;YAAC,GAAG,CAAC;YAAC,SAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA,IAAG,EAAE,EAAE,KAAG,EAAE,EAAE;QAAC;QAAE,IAAI,IAAE,EAAE,GAAE,CAAA;YAAI,IAAI,IAAE,EAAE,SAAS,CAAC,CAAA,IAAG,EAAE,EAAE,KAAG,EAAE,EAAE;YAAE,OAAO,MAAI,CAAC,KAAG,EAAE,MAAM,CAAC,GAAE,IAAG;QAAC;QAAG,OAAM;YAAC,GAAG,CAAC;YAAC,GAAG,CAAC;YAAC,mBAAkB;QAAC;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,GAAE,IAAI,EAAE,oBAAoB,KAAG,EAAE,KAAK,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,sBAAqB,EAAE,KAAK;QAAA;IAAE,CAAC,EAAE,EAAC,CAAC,GAAE,IAAI,EAAE,iBAAiB,KAAG,EAAE,OAAO,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,mBAAkB,EAAE,OAAO;QAAA;IAAE,CAAC,EAAE,EAAC,CAAC,GAAE;QAAK,IAAI,GAAE;QAAE,IAAG,EAAE,OAAO,KAAG,MAAK,OAAM;YAAC,GAAG,CAAC;YAAC,SAAQ;gBAAC,SAAQ,EAAE,OAAO;gBAAC,UAAS,CAAC,IAAE,EAAE,QAAQ,KAAG,OAAK,IAAE,IAAI,CAAC;YAAC;QAAC;QAAE,IAAG,EAAE,OAAO,CAAC,OAAO,KAAG,EAAE,OAAO,IAAE,EAAE,OAAO,CAAC,QAAQ,KAAG,EAAE,QAAQ,EAAC,OAAO;QAAE,IAAI,IAAE,EAAE,iBAAiB;QAAC,IAAG,EAAE,iBAAiB,KAAG,MAAK;YAAC,IAAI,IAAE,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,iBAAiB,CAAC;YAAE,MAAI,CAAC,IAAE,IAAE,IAAE,IAAE;QAAI;QAAC,OAAM;YAAC,GAAG,CAAC;YAAC,mBAAkB;YAAE,SAAQ;gBAAC,SAAQ,EAAE,OAAO;gBAAC,UAAS,CAAC,IAAE,EAAE,QAAQ,KAAG,OAAK,IAAE,IAAI,CAAC;YAAC;QAAC;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,GAAE,IAAI,EAAE,YAAY,KAAG,EAAE,OAAO,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,cAAa,EAAE,OAAO;QAAA;IAAE,CAAC,GAAG,EAAC,CAAC,GAAE,IAAI,EAAE,aAAa,KAAG,EAAE,OAAO,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,eAAc,EAAE,OAAO;QAAA;IAAE,CAAC,GAAG,EAAC,CAAC,GAAE,IAAI,EAAE,cAAc,KAAG,EAAE,OAAO,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,gBAAe,EAAE,OAAO;QAAA;AAAC;AAAE,MAAM,UAAU,0SAAA,CAAA,UAAC;IAAC,YAAY,CAAC,CAAC;QAAC,KAAK,CAAC;QAAG,EAAE,IAAI,EAAC,WAAU;YAAC,UAAS,CAAA;gBAAI,IAAG,EAAC,UAAS,CAAC,EAAC,SAAQ,CAAC,EAAC,MAAK,CAAC,EAAC,OAAM,CAAC,EAAC,GAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;gBAAC,OAAO,CAAA,GAAA,iTAAA,CAAA,QAAC,AAAD,EAAE,GAAE;oBAAC,CAAC,EAAE,EAAC,IAAI,KAAG,OAAK,KAAK,IAAE,EAAE;oBAAG,CAAC,EAAE,EAAC;wBAAK,IAAI,IAAE,EAAE,KAAK,IAAG,IAAE,EAAE,SAAS,CAAC,CAAA,IAAG,EAAE,GAAE;wBAAI,OAAO,MAAI,CAAC,IAAE,EAAE,IAAI,CAAC,KAAG,EAAE,MAAM,CAAC,GAAE,IAAG,KAAG,OAAK,KAAK,IAAE,EAAE;oBAAE;gBAAC;YAAE;YAAE,gBAAe,CAAC,GAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,SAAQ;wBAAC,IAAG;wBAAE,SAAQ;oBAAC;gBAAC,IAAG;oBAAK,IAAI,CAAC,KAAK,CAAC,iBAAiB,KAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC,KAAK,KAAG,IAAI,CAAC,IAAI,CAAC;wBAAC,MAAK;wBAAE,OAAM,CAAC;oBAAC,IAAG,IAAI,CAAC,IAAI,CAAC;wBAAC,MAAK;wBAAE,IAAG;oBAAC;gBAAE,CAAC;YAAE,YAAW,CAAC,GAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,OAAM,CAAC;gBAAC,IAAG,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,GAAG,CAAC;oBAAC,SAAQ;gBAAC,EAAE;YAAE,aAAY,CAAA;gBAAI,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,UAAS;gBAAC;YAAE;YAAE,eAAc;gBAAK,IAAI,GAAE;gBAAE,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;gBAAC,IAAG,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,OAAM,CAAC;gBAAC,IAAG,CAAC,IAAE,CAAC,IAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,KAAG,QAAM,EAAE,IAAI,CAAC;YAAE;YAAE,cAAa;gBAAK,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;gBAAC,IAAG,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,OAAM,CAAC;gBAAC;YAAE;YAAE,sBAAqB,CAAA;gBAAI,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,SAAQ;gBAAC;YAAE;YAAE,oBAAmB;gBAAK,IAAI,IAAE,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK;gBAAE,IAAG,MAAI,MAAK;oBAAC,IAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;yBAAM;wBAAC,IAAG,EAAC,SAAQ,CAAC,EAAC,GAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;wBAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,KAAK;oBAAC;oBAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;wBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,QAAQ;wBAAC,KAAI;oBAAC;gBAAE;YAAC;YAAE,iBAAgB,CAAA;gBAAI,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,SAAQ;gBAAC;YAAE;YAAE,kBAAiB,CAAA;gBAAI,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAG,SAAQ;gBAAC;YAAE;YAAE,mBAAkB,CAAA;gBAAI,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAG,SAAQ;gBAAC;YAAE;QAAC;QAAG,EAAE,IAAI,EAAC,aAAY;YAAC,oBAAmB,CAAA;gBAAI,IAAI,GAAE;gBAAE,IAAI,IAAE,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;gBAAG,IAAG,MAAI,MAAK,OAAO,EAAE,OAAO,GAAC,CAAC,IAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA,IAAG,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,IAAE,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,EAAC,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE,GAAC,CAAC,IAAE,EAAE,OAAO,CAAC,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE;YAAA;YAAE,mBAAkB,CAAA;gBAAI,IAAG,EAAE,oBAAoB,IAAE,EAAE,iBAAiB,KAAG,QAAM,CAAC,EAAE,OAAO,GAAC,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,GAAC,IAAE,EAAE,OAAO,CAAC,MAAM,GAAC,CAAC,GAAE;oBAAC,IAAG,EAAE,OAAO,EAAC;wBAAC,IAAG,EAAC,SAAQ,CAAC,EAAC,UAAS,CAAC,EAAC,GAAC,EAAE,OAAO,EAAC,IAAE,EAAE,SAAS,CAAC,CAAA;4BAAI,IAAI;4BAAE,OAAM,CAAC,CAAC,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE,KAAG,QAAM,CAAC;wBAAC;wBAAG,IAAG,MAAI,CAAC,GAAE,OAAO;oBAAC;oBAAC,IAAI,IAAE,EAAE,OAAO,CAAC,SAAS,CAAC,CAAA,IAAG,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;oBAAE,IAAG,MAAI,CAAC,GAAE,OAAO;gBAAC;gBAAC,OAAO,EAAE,iBAAiB;YAAA;YAAE,cAAa,CAAA;gBAAI,IAAI,GAAE;gBAAE,IAAI,IAAE,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;gBAAG,OAAO,MAAI,OAAK,OAAK,EAAE,OAAO,GAAC,EAAE,OAAO,CAAC,OAAO,CAAC,KAAG,OAAK,IAAE,EAAE,GAAC,CAAC,IAAE,CAAC,IAAE,EAAE,OAAO,CAAC,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,KAAG,OAAK,IAAE;YAAI;YAAE,UAAS,CAAC,GAAE,GAAE;gBAAK,IAAI;gBAAE,IAAI,IAAE,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;gBAAG,OAAO,MAAI,OAAK,CAAC,IAAE,EAAE,OAAO,GAAC,MAAI,EAAE,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,KAAG,CAAC,CAAC,IAAE,EAAE,OAAO,CAAC,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE,MAAI;YAAC;YAAE,sBAAqB,CAAC,GAAE,GAAE,IAAI,CAAC,CAAC,EAAE,OAAO,IAAE,EAAE,UAAU,IAAE,EAAE,aAAa,KAAG,KAAG,EAAE,iBAAiB,KAAG,KAAG,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAE,GAAE,EAAE;QAAC;QAAG;YAAC,IAAI,IAAE,IAAI,CAAC,KAAK,CAAC,EAAE,EAAC,IAAE,+TAAA,CAAA,gBAAC,CAAC,GAAG,CAAC;YAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,+TAAA,CAAA,cAAC,CAAC,IAAI,EAAC,CAAA;gBAAI,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,GAAE,MAAI,IAAI,CAAC,KAAK,CAAC,aAAa,KAAG,KAAG,IAAI,CAAC,OAAO,CAAC,aAAa;YAAE,KAAI,IAAI,CAAC,EAAE,CAAC,GAAE,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,KAAI,IAAI,CAAC,EAAE,CAAC,GAAE,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC;QAAG;IAAC;IAAC,OAAO,IAAI,EAAC,IAAG,CAAC,EAAC,SAAQ,IAAE,IAAI,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,EAAC;QAAC,IAAI;QAAE,OAAO,IAAI,EAAE;YAAC,IAAG;YAAE,SAAQ;gBAAC,SAAQ,CAAC;YAAC;YAAE,eAAc,IAAE,IAAE;YAAE,UAAS,CAAC;YAAE,SAAQ,EAAE;YAAC,SAAQ,IAAE;gBAAC,SAAQ,EAAE,OAAO;gBAAC,UAAS,CAAC,IAAE,EAAE,QAAQ,KAAG,OAAK,IAAE,IAAI,CAAC;YAAC,IAAE;YAAK,mBAAkB;YAAK,mBAAkB;YAAE,cAAa;YAAK,eAAc;YAAK,gBAAe;YAAK,YAAW;QAAC;IAAE;IAAC,OAAO,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,CAAA,GAAA,iTAAA,CAAA,QAAC,AAAD,EAAE,EAAE,IAAI,EAAC,GAAE,GAAE;IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4538, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/combobox/combobox-machine-glue.js"], "sourcesContent": ["import{createContext as r,useContext as a,useMemo as m}from\"react\";import{useOnUnmount as c}from'../../hooks/use-on-unmount.js';import{ComboboxMachine as i}from'./combobox-machine.js';const u=r(null);function p(n){let o=a(u);if(o===null){let e=new Error(`<${n} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,b),e}return o}function b({id:n,virtual:o=null,__demoMode:e=!1}){let t=m(()=>i.new({id:n,virtual:o,__demoMode:e}),[]);return c(()=>t.dispose()),t}export{u as ComboboxContext,b as useComboboxMachine,p as useComboboxMachineContext};\n"], "names": [], "mappings": ";;;;;AAAA;AAAmE;AAA6D;;;;AAAwD,MAAM,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;IAAG,IAAG,MAAI,MAAK;QAAC,IAAI,IAAE,IAAI,MAAM,CAAC,CAAC,EAAE,EAAE,+CAA+C,CAAC;QAAE,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG;IAAC;IAAC,OAAO;AAAC;AAAC,SAAS,EAAE,EAAC,IAAG,CAAC,EAAC,SAAQ,IAAE,IAAI,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,gVAAA,CAAA,kBAAC,CAAC,GAAG,CAAC;YAAC,IAAG;YAAE,SAAQ;YAAE,YAAW;QAAC,IAAG,EAAE;IAAE,OAAO,CAAA,GAAA,gUAAA,CAAA,eAAC,AAAD,EAAE,IAAI,EAAE,OAAO,KAAI;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4573, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/combobox/combobox.js"], "sourcesContent": ["\"use client\";import{useFocusRing as ve}from\"@react-aria/focus\";import{useHover as Pe}from\"@react-aria/interactions\";import{useVirtualizer as Le}from\"@tanstack/react-virtual\";import F,{Fragment as Ee,create<PERSON>ontex<PERSON> as <PERSON><PERSON>,use<PERSON><PERSON><PERSON> as <PERSON>,use<PERSON>ontex<PERSON> as he,use<PERSON>em<PERSON> as K,useRef as me,useState as xe}from\"react\";import{flushSync as re}from\"react-dom\";import{useActivePress as Ve}from'../../hooks/use-active-press.js';import{useByComparator as we}from'../../hooks/use-by-comparator.js';import{useControllable as Be}from'../../hooks/use-controllable.js';import{useDefaultValue as Ne}from'../../hooks/use-default-value.js';import{useDisposables as ke}from'../../hooks/use-disposables.js';import{useElementSize as Ae}from'../../hooks/use-element-size.js';import{useEvent as O}from'../../hooks/use-event.js';import{useId as le}from'../../hooks/use-id.js';import{useInertOthers as Ue}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as ee}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as He}from'../../hooks/use-latest-value.js';import{useOnDisappear as Ge}from'../../hooks/use-on-disappear.js';import{useOutsideClick as ze}from'../../hooks/use-outside-click.js';import{useOwnerDocument as ge}from'../../hooks/use-owner.js';import{Action as ie,useQuickRelease as Ke}from'../../hooks/use-quick-release.js';import{useRefocusableInput as Ie}from'../../hooks/use-refocusable-input.js';import{useResolveButtonType as We}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as Xe}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as ce}from'../../hooks/use-sync-refs.js';import{useTrackedPointer as $e}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as Je,useTransition as je}from'../../hooks/use-transition.js';import{useTreeWalker as qe}from'../../hooks/use-tree-walker.js';import{useWatch as Re}from'../../hooks/use-watch.js';import{useDisabled as Qe}from'../../internal/disabled.js';import{FloatingProvider as Ye,useFloatingPanel as Ze,useFloatingPanelProps as eo,useFloatingReference as oo,useResolvedAnchor as to}from'../../internal/floating.js';import{FormFields as no}from'../../internal/form-fields.js';import{Frozen as ro,useFrozenData as De}from'../../internal/frozen.js';import{useProvidedId as ao}from'../../internal/id.js';import{OpenClosedProvider as lo,State as fe,useOpenClosed as io}from'../../internal/open-closed.js';import{stackMachines as so}from'../../machines/stack-machine.js';import{useSlice as D}from'../../react-glue.js';import{history as _e}from'../../utils/active-element-history.js';import{isDisabledReactIssue7711 as uo}from'../../utils/bugs.js';import{Focus as L}from'../../utils/calculate-active-index.js';import{disposables as po}from'../../utils/disposables.js';import*as bo from'../../utils/dom.js';import{match as Te}from'../../utils/match.js';import{isMobile as mo}from'../../utils/platform.js';import{RenderFeatures as Fe,forwardRefWithAs as se,mergeProps as ye,useRender as ue}from'../../utils/render.js';import{useDescribedBy as co}from'../description/description.js';import{Keys as V}from'../keyboard.js';import{Label as fo,useLabelledBy as Ce,useLabels as To}from'../label/label.js';import{MouseButton as Se}from'../mouse.js';import{Portal as xo}from'../portal/portal.js';import{ActionTypes as go,ActivationTrigger as oe,ComboboxState as l,ValueMode as N}from'./combobox-machine.js';import{ComboboxContext as yo,useComboboxMachine as Co,useComboboxMachineContext as pe}from'./combobox-machine-glue.js';let de=Oe(null);de.displayName=\"ComboboxDataContext\";function ae(C){let h=he(de);if(h===null){let e=new Error(`<${C} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,ae),e}return h}let Me=Oe(null);function vo(C){let h=pe(\"VirtualProvider\"),e=ae(\"VirtualProvider\"),{options:o}=e.virtual,A=D(h,a=>a.optionsElement),[R,v]=K(()=>{let a=A;if(!a)return[0,0];let u=window.getComputedStyle(a);return[parseFloat(u.paddingBlockStart||u.paddingTop),parseFloat(u.paddingBlockEnd||u.paddingBottom)]},[A]),T=Le({enabled:o.length!==0,scrollPaddingStart:R,scrollPaddingEnd:v,count:o.length,estimateSize(){return 40},getScrollElement(){return h.state.optionsElement},overscan:12}),[I,m]=xe(0);ee(()=>{m(a=>a+1)},[o]);let g=T.getVirtualItems(),n=D(h,a=>a.activationTrigger===oe.Pointer),f=D(h,h.selectors.activeOptionIndex);return g.length===0?null:F.createElement(Me.Provider,{value:T},F.createElement(\"div\",{style:{position:\"relative\",width:\"100%\",height:`${T.getTotalSize()}px`},ref:a=>{a&&(n||f!==null&&o.length>f&&T.scrollToIndex(f))}},g.map(a=>{var u;return F.createElement(Ee,{key:a.key},F.cloneElement((u=C.children)==null?void 0:u.call(C,{...C.slot,option:o[a.index]}),{key:`${I}-${a.key}`,\"data-index\":a.index,\"aria-setsize\":o.length,\"aria-posinset\":a.index+1,style:{position:\"absolute\",top:0,left:0,transform:`translateY(${a.start}px)`,overflowAnchor:\"none\"}}))})))}let Po=Ee;function Eo(C,h){let e=le(),o=Qe(),{value:A,defaultValue:R,onChange:v,form:T,name:I,by:m,invalid:g=!1,disabled:n=o||!1,onClose:f,__demoMode:a=!1,multiple:u=!1,immediate:S=!1,virtual:d=null,nullable:k,...W}=C,y=Ne(R),[x=u?[]:void 0,P]=Be(A,v,y),b=Co({id:e,virtual:d,__demoMode:a}),G=me({static:!1,hold:!1}),_=we(m),z=O(s=>d?m===null?d.options.indexOf(s):d.options.findIndex(c=>_(c,s)):b.state.options.findIndex(c=>_(c.dataRef.current.value,s))),U=Z(s=>Te(r.mode,{[N.Multi]:()=>x.some(c=>_(c,s)),[N.Single]:()=>_(x,s)}),[x]),w=D(b,s=>s.virtual),J=O(()=>f==null?void 0:f()),r=K(()=>({__demoMode:a,immediate:S,optionsPropsRef:G,value:x,defaultValue:y,disabled:n,invalid:g,mode:u?N.Multi:N.Single,virtual:d?w:null,onChange:P,isSelected:U,calculateIndex:z,compare:_,onClose:J}),[x,y,n,g,u,P,U,a,b,d,w,J]);ee(()=>{var s;d&&b.send({type:go.UpdateVirtualConfiguration,options:d.options,disabled:(s=d.disabled)!=null?s:null})},[d,d==null?void 0:d.options,d==null?void 0:d.disabled]),ee(()=>{b.state.dataRef.current=r},[r]);let[M,X,i,H]=D(b,s=>[s.comboboxState,s.buttonElement,s.inputElement,s.optionsElement]),j=so.get(null),q=D(j,Z(s=>j.selectors.isTop(s,e),[j,e]));ze(q,[X,i,H],()=>b.actions.closeCombobox());let Q=D(b,b.selectors.activeOptionIndex),$=D(b,b.selectors.activeOption),be=K(()=>({open:M===l.Open,disabled:n,invalid:g,activeIndex:Q,activeOption:$,value:x}),[r,n,x,g,$,M]),[Y,te]=To(),t=h===null?{}:{ref:h},B=Z(()=>{if(y!==void 0)return P==null?void 0:P(y)},[P,y]),E=ue();return F.createElement(te,{value:Y,props:{htmlFor:i==null?void 0:i.id},slot:{open:M===l.Open,disabled:n}},F.createElement(Ye,null,F.createElement(de.Provider,{value:r},F.createElement(yo.Provider,{value:b},F.createElement(lo,{value:Te(M,{[l.Open]:fe.Open,[l.Closed]:fe.Closed})},I!=null&&F.createElement(no,{disabled:n,data:x!=null?{[I]:x}:{},form:T,onReset:B}),E({ourProps:t,theirProps:W,slot:be,defaultTag:Po,name:\"Combobox\"}))))))}let Oo=\"input\";function ho(C,h){var Y,te;let e=pe(\"Combobox.Input\"),o=ae(\"Combobox.Input\"),A=le(),R=ao(),{id:v=R||`headlessui-combobox-input-${A}`,onChange:T,displayValue:I,disabled:m=o.disabled||!1,autoFocus:g=!1,type:n=\"text\",...f}=C,[a]=D(e,t=>[t.inputElement]),u=me(null),S=ce(u,h,oo(),e.actions.setInputElement),d=ge(a),[k,W]=D(e,t=>[t.comboboxState,t.isTyping]),y=ke(),x=O(()=>{e.actions.onChange(null),e.state.optionsElement&&(e.state.optionsElement.scrollTop=0),e.actions.goToOption({focus:L.Nothing})}),P=K(()=>{var t;return typeof I==\"function\"&&o.value!==void 0?(t=I(o.value))!=null?t:\"\":typeof o.value==\"string\"?o.value:\"\"},[o.value,I]);Re(([t,B],[E,s])=>{if(e.state.isTyping)return;let c=u.current;c&&((s===l.Open&&B===l.Closed||t!==E)&&(c.value=t),requestAnimationFrame(()=>{if(e.state.isTyping||!c||(d==null?void 0:d.activeElement)!==c)return;let{selectionStart:p,selectionEnd:ne}=c;Math.abs((ne!=null?ne:0)-(p!=null?p:0))===0&&p===0&&c.setSelectionRange(c.value.length,c.value.length)}))},[P,k,d,W]),Re(([t],[B])=>{if(t===l.Open&&B===l.Closed){if(e.state.isTyping)return;let E=u.current;if(!E)return;let s=E.value,{selectionStart:c,selectionEnd:p,selectionDirection:ne}=E;E.value=\"\",E.value=s,ne!==null?E.setSelectionRange(c,p,ne):E.setSelectionRange(c,p)}},[k]);let b=me(!1),G=O(()=>{b.current=!0}),_=O(()=>{y.nextFrame(()=>{b.current=!1})}),z=O(t=>{switch(e.actions.setIsTyping(!0),t.key){case V.Enter:if(e.state.comboboxState!==l.Open||b.current)return;if(t.preventDefault(),t.stopPropagation(),e.selectors.activeOptionIndex(e.state)===null){e.actions.closeCombobox();return}e.actions.selectActiveOption(),o.mode===N.Single&&e.actions.closeCombobox();break;case V.ArrowDown:return t.preventDefault(),t.stopPropagation(),Te(e.state.comboboxState,{[l.Open]:()=>e.actions.goToOption({focus:L.Next}),[l.Closed]:()=>e.actions.openCombobox()});case V.ArrowUp:return t.preventDefault(),t.stopPropagation(),Te(e.state.comboboxState,{[l.Open]:()=>e.actions.goToOption({focus:L.Previous}),[l.Closed]:()=>{re(()=>e.actions.openCombobox()),o.value||e.actions.goToOption({focus:L.Last})}});case V.Home:if(t.shiftKey)break;return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:L.First});case V.PageUp:return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:L.First});case V.End:if(t.shiftKey)break;return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:L.Last});case V.PageDown:return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:L.Last});case V.Escape:return e.state.comboboxState!==l.Open?void 0:(t.preventDefault(),e.state.optionsElement&&!o.optionsPropsRef.current.static&&t.stopPropagation(),o.mode===N.Single&&o.value===null&&x(),e.actions.closeCombobox());case V.Tab:if(e.state.comboboxState!==l.Open)return;o.mode===N.Single&&e.state.activationTrigger!==oe.Focus&&e.actions.selectActiveOption(),e.actions.closeCombobox();break}}),U=O(t=>{T==null||T(t),o.mode===N.Single&&t.target.value===\"\"&&x(),e.actions.openCombobox()}),w=O(t=>{var E,s,c;let B=(E=t.relatedTarget)!=null?E:_e.find(p=>p!==t.currentTarget);if(!((s=e.state.optionsElement)!=null&&s.contains(B))&&!((c=e.state.buttonElement)!=null&&c.contains(B))&&e.state.comboboxState===l.Open)return t.preventDefault(),o.mode===N.Single&&o.value===null&&x(),e.actions.closeCombobox()}),J=O(t=>{var E,s,c;let B=(E=t.relatedTarget)!=null?E:_e.find(p=>p!==t.currentTarget);(s=e.state.buttonElement)!=null&&s.contains(B)||(c=e.state.optionsElement)!=null&&c.contains(B)||o.disabled||o.immediate&&e.state.comboboxState!==l.Open&&y.microTask(()=>{re(()=>e.actions.openCombobox()),e.actions.setActivationTrigger(oe.Focus)})}),r=Ce(),M=co(),{isFocused:X,focusProps:i}=ve({autoFocus:g}),{isHovered:H,hoverProps:j}=Pe({isDisabled:m}),q=D(e,t=>t.optionsElement),Q=K(()=>({open:k===l.Open,disabled:m,invalid:o.invalid,hover:H,focus:X,autofocus:g}),[o,H,X,g,m,o.invalid]),$=ye({ref:S,id:v,role:\"combobox\",type:n,\"aria-controls\":q==null?void 0:q.id,\"aria-expanded\":k===l.Open,\"aria-activedescendant\":D(e,e.selectors.activeDescendantId),\"aria-labelledby\":r,\"aria-describedby\":M,\"aria-autocomplete\":\"list\",defaultValue:(te=(Y=C.defaultValue)!=null?Y:o.defaultValue!==void 0?I==null?void 0:I(o.defaultValue):null)!=null?te:o.defaultValue,disabled:m||void 0,autoFocus:g,onCompositionStart:G,onCompositionEnd:_,onKeyDown:z,onChange:U,onFocus:J,onBlur:w},i,j);return ue()({ourProps:$,theirProps:f,slot:Q,defaultTag:Oo,name:\"Combobox.Input\"})}let Ao=\"button\";function Io(C,h){let e=pe(\"Combobox.Button\"),o=ae(\"Combobox.Button\"),[A,R]=xe(null),v=ce(h,R,e.actions.setButtonElement),T=le(),{id:I=`headlessui-combobox-button-${T}`,disabled:m=o.disabled||!1,autoFocus:g=!1,...n}=C,[f,a,u]=D(e,r=>[r.comboboxState,r.inputElement,r.optionsElement]),S=Ie(a),d=f===l.Open;Ke(d,{trigger:A,action:Z(r=>{if(A!=null&&A.contains(r.target))return ie.Ignore;if(a!=null&&a.contains(r.target))return ie.Ignore;let M=r.target.closest('[role=\"option\"]:not([data-disabled])');return bo.isHTMLElement(M)?ie.Select(M):u!=null&&u.contains(r.target)?ie.Ignore:ie.Close},[A,a,u]),close:e.actions.closeCombobox,select:e.actions.selectActiveOption});let k=O(r=>{switch(r.key){case V.Space:case V.Enter:r.preventDefault(),r.stopPropagation(),e.state.comboboxState===l.Closed&&re(()=>e.actions.openCombobox()),S();return;case V.ArrowDown:r.preventDefault(),r.stopPropagation(),e.state.comboboxState===l.Closed&&(re(()=>e.actions.openCombobox()),e.state.dataRef.current.value||e.actions.goToOption({focus:L.First})),S();return;case V.ArrowUp:r.preventDefault(),r.stopPropagation(),e.state.comboboxState===l.Closed&&(re(()=>e.actions.openCombobox()),e.state.dataRef.current.value||e.actions.goToOption({focus:L.Last})),S();return;case V.Escape:if(e.state.comboboxState!==l.Open)return;r.preventDefault(),e.state.optionsElement&&!o.optionsPropsRef.current.static&&r.stopPropagation(),re(()=>e.actions.closeCombobox()),S();return;default:return}}),W=O(r=>{r.preventDefault(),!uo(r.currentTarget)&&(r.button===Se.Left&&(e.state.comboboxState===l.Open?e.actions.closeCombobox():e.actions.openCombobox()),S())}),y=Ce([I]),{isFocusVisible:x,focusProps:P}=ve({autoFocus:g}),{isHovered:b,hoverProps:G}=Pe({isDisabled:m}),{pressed:_,pressProps:z}=Ve({disabled:m}),U=K(()=>({open:f===l.Open,active:_||f===l.Open,disabled:m,invalid:o.invalid,value:o.value,hover:b,focus:x}),[o,b,x,_,m,f]),w=ye({ref:v,id:I,type:We(C,A),tabIndex:-1,\"aria-haspopup\":\"listbox\",\"aria-controls\":u==null?void 0:u.id,\"aria-expanded\":f===l.Open,\"aria-labelledby\":y,disabled:m||void 0,autoFocus:g,onPointerDown:W,onKeyDown:k},P,G,z);return ue()({ourProps:w,theirProps:n,slot:U,defaultTag:Ao,name:\"Combobox.Button\"})}let Ro=\"div\",Do=Fe.RenderStrategy|Fe.Static;function _o(C,h){var E,s,c;let e=le(),{id:o=`headlessui-combobox-options-${e}`,hold:A=!1,anchor:R,portal:v=!1,modal:T=!0,transition:I=!1,...m}=C,g=pe(\"Combobox.Options\"),n=ae(\"Combobox.Options\"),f=to(R);f&&(v=!0);let[a,u]=Ze(f),[S,d]=xe(null),k=eo(),W=ce(h,f?a:null,g.actions.setOptionsElement,d),[y,x,P,b,G]=D(g,p=>[p.comboboxState,p.inputElement,p.buttonElement,p.optionsElement,p.activationTrigger]),_=ge(x||P),z=ge(b),U=io(),[w,J]=je(I,S,U!==null?(U&fe.Open)===fe.Open:y===l.Open);Ge(w,x,g.actions.closeCombobox);let r=n.__demoMode?!1:T&&y===l.Open;Xe(r,z);let M=n.__demoMode?!1:T&&y===l.Open;Ue(M,{allowed:Z(()=>[x,P,b],[x,P,b])}),ee(()=>{var p;n.optionsPropsRef.current.static=(p=C.static)!=null?p:!1},[n.optionsPropsRef,C.static]),ee(()=>{n.optionsPropsRef.current.hold=A},[n.optionsPropsRef,A]),qe(y===l.Open,{container:b,accept(p){return p.getAttribute(\"role\")===\"option\"?NodeFilter.FILTER_REJECT:p.hasAttribute(\"role\")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(p){p.setAttribute(\"role\",\"none\")}});let X=Ce([P==null?void 0:P.id]),i=K(()=>({open:y===l.Open,option:void 0}),[y]),H=O(()=>{g.actions.setActivationTrigger(oe.Pointer)}),j=O(p=>{p.preventDefault(),g.actions.setActivationTrigger(oe.Pointer)}),q=ye(f?k():{},{\"aria-labelledby\":X,role:\"listbox\",\"aria-multiselectable\":n.mode===N.Multi?!0:void 0,id:o,ref:W,style:{...m.style,...u,\"--input-width\":Ae(x,!0).width,\"--button-width\":Ae(P,!0).width},onWheel:G===oe.Pointer?void 0:H,onMouseDown:j,...Je(J)}),Q=w&&y===l.Closed,$=De(Q,(E=n.virtual)==null?void 0:E.options),be=De(Q,n.value),Y=O(p=>n.compare(be,p)),te=K(()=>{if(!n.virtual)return n;if($===void 0)throw new Error(\"Missing `options` in virtual mode\");return $!==n.virtual.options?{...n,virtual:{...n.virtual,options:$}}:n},[n,$,(s=n.virtual)==null?void 0:s.options]);n.virtual&&Object.assign(m,{children:F.createElement(de.Provider,{value:te},F.createElement(vo,{slot:i},m.children))});let t=ue(),B=K(()=>n.mode===N.Multi?n:{...n,isSelected:Y},[n,Y]);return F.createElement(xo,{enabled:v?C.static||w:!1,ownerDocument:_},F.createElement(de.Provider,{value:B},t({ourProps:q,theirProps:{...m,children:F.createElement(ro,{freeze:Q},typeof m.children==\"function\"?(c=m.children)==null?void 0:c.call(m,i):m.children)},slot:i,defaultTag:Ro,features:Do,visible:w,name:\"Combobox.Options\"})))}let Fo=\"div\";function So(C,h){var r,M,X;let e=ae(\"Combobox.Option\"),o=pe(\"Combobox.Option\"),A=le(),{id:R=`headlessui-combobox-option-${A}`,value:v,disabled:T=(X=(M=(r=e.virtual)==null?void 0:r.disabled)==null?void 0:M.call(r,v))!=null?X:!1,order:I=null,...m}=C,[g]=D(o,i=>[i.inputElement]),n=Ie(g),f=D(o,Z(i=>o.selectors.isActive(i,v,R),[v,R])),a=e.isSelected(v),u=me(null),S=He({disabled:T,value:v,domRef:u,order:I}),d=he(Me),k=ce(h,u,d?d.measureElement:null),W=O(()=>{o.actions.setIsTyping(!1),o.actions.onChange(v)});ee(()=>o.actions.registerOption(R,S),[S,R]);let y=D(o,Z(i=>o.selectors.shouldScrollIntoView(i,v,R),[v,R]));ee(()=>{if(y)return po().requestAnimationFrame(()=>{var i,H;(H=(i=u.current)==null?void 0:i.scrollIntoView)==null||H.call(i,{block:\"nearest\"})})},[y,u]);let x=O(i=>{i.preventDefault(),i.button===Se.Left&&(T||(W(),mo()||requestAnimationFrame(()=>n()),e.mode===N.Single&&o.actions.closeCombobox()))}),P=O(()=>{if(T)return o.actions.goToOption({focus:L.Nothing});let i=e.calculateIndex(v);o.actions.goToOption({focus:L.Specific,idx:i})}),b=$e(),G=O(i=>b.update(i)),_=O(i=>{if(!b.wasMoved(i)||T||f)return;let H=e.calculateIndex(v);o.actions.goToOption({focus:L.Specific,idx:H},oe.Pointer)}),z=O(i=>{b.wasMoved(i)&&(T||f&&(e.optionsPropsRef.current.hold||o.actions.goToOption({focus:L.Nothing})))}),U=K(()=>({active:f,focus:f,selected:a,disabled:T}),[f,a,T]),w={id:R,ref:k,role:\"option\",tabIndex:T===!0?void 0:-1,\"aria-disabled\":T===!0?!0:void 0,\"aria-selected\":a,disabled:void 0,onMouseDown:x,onFocus:P,onPointerEnter:G,onMouseEnter:G,onPointerMove:_,onMouseMove:_,onPointerLeave:z,onMouseLeave:z};return ue()({ourProps:w,theirProps:m,slot:U,defaultTag:Fo,name:\"Combobox.Option\"})}let Mo=se(Eo),Lo=se(Io),Vo=se(ho),wo=fo,Bo=se(_o),No=se(So),wt=Object.assign(Mo,{Input:Vo,Button:Lo,Label:wo,Options:Bo,Option:No});export{wt as Combobox,Lo as ComboboxButton,Vo as ComboboxInput,wo as ComboboxLabel,No as ComboboxOption,Bo as ComboboxOptions};\n"], "names": [], "mappings": ";;;;;;;;AAAa;AAAkD;AAAqD;AAA0D;AAAoI;AAAuC;AAAkE;AAAoE;AAAmE;AAAoE;AAAiE;AAAkE;AAAmG;AAAkE;AAA6E;AAAkE;AAAkE;AAAoE;AAA6D;AAAiF;AAA4E;AAA+E;AAAgE;AAA4D;AAAwE;AAA8F;AAAgE;AAAqD;AAA0D;AAAqK;AAA4D;AAAuE;AAAsD;AAAoG;AAAiE;AAA+C;AAAiE;AAAgE;AAA8D;AAA0D;AAAsC;AAA8C;AAAoD;AAAgH;AAAgE;AAAsC;AAA+E;AAA2C;AAA8C;AAA+G;AAA7zG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAo7G,IAAI,KAAG,CAAA,GAAA,oUAAA,CAAA,gBAAE,AAAD,EAAE;AAAM,GAAG,WAAW,GAAC;AAAsB,SAAS,GAAG,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAE,AAAD,EAAE;IAAI,IAAG,MAAI,MAAK;QAAC,IAAI,IAAE,IAAI,MAAM,CAAC,CAAC,EAAE,EAAE,+CAA+C,CAAC;QAAE,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,KAAI;IAAC;IAAC,OAAO;AAAC;AAAC,IAAI,KAAG,CAAA,GAAA,oUAAA,CAAA,gBAAE,AAAD,EAAE;AAAM,SAAS,GAAG,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,wVAAA,CAAA,4BAAE,AAAD,EAAE,oBAAmB,IAAE,GAAG,oBAAmB,EAAC,SAAQ,CAAC,EAAC,GAAC,EAAE,OAAO,EAAC,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG,EAAE,cAAc,GAAE,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE;QAAK,IAAI,IAAE;QAAE,IAAG,CAAC,GAAE,OAAM;YAAC;YAAE;SAAE;QAAC,IAAI,IAAE,OAAO,gBAAgB,CAAC;QAAG,OAAM;YAAC,WAAW,EAAE,iBAAiB,IAAE,EAAE,UAAU;YAAE,WAAW,EAAE,eAAe,IAAE,EAAE,aAAa;SAAE;IAAA,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,mVAAA,CAAA,iBAAE,AAAD,EAAE;QAAC,SAAQ,EAAE,MAAM,KAAG;QAAE,oBAAmB;QAAE,kBAAiB;QAAE,OAAM,EAAE,MAAM;QAAC;YAAe,OAAO;QAAE;QAAE;YAAmB,OAAO,EAAE,KAAK,CAAC,cAAc;QAAA;QAAE,UAAS;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAE,AAAD,EAAE;IAAG,CAAA,GAAA,2UAAA,CAAA,sBAAE,AAAD,EAAE;QAAK,EAAE,CAAA,IAAG,IAAE;IAAE,GAAE;QAAC;KAAE;IAAE,IAAI,IAAE,EAAE,eAAe,IAAG,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG,EAAE,iBAAiB,KAAG,gVAAA,CAAA,oBAAE,CAAC,OAAO,GAAE,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,EAAE,SAAS,CAAC,iBAAiB;IAAE,OAAO,EAAE,MAAM,KAAG,IAAE,OAAK,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAG,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,OAAM;QAAC,OAAM;YAAC,UAAS;YAAW,OAAM;YAAO,QAAO,GAAG,EAAE,YAAY,GAAG,EAAE,CAAC;QAAA;QAAE,KAAI,CAAA;YAAI,KAAG,CAAC,KAAG,MAAI,QAAM,EAAE,MAAM,GAAC,KAAG,EAAE,aAAa,CAAC,EAAE;QAAC;IAAC,GAAE,EAAE,GAAG,CAAC,CAAA;QAAI,IAAI;QAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,oUAAA,CAAA,WAAE,EAAC;YAAC,KAAI,EAAE,GAAG;QAAA,GAAE,oUAAA,CAAA,UAAC,CAAC,YAAY,CAAC,CAAC,IAAE,EAAE,QAAQ,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,GAAE;YAAC,GAAG,EAAE,IAAI;YAAC,QAAO,CAAC,CAAC,EAAE,KAAK,CAAC;QAAA,IAAG;YAAC,KAAI,GAAG,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE;YAAC,cAAa,EAAE,KAAK;YAAC,gBAAe,EAAE,MAAM;YAAC,iBAAgB,EAAE,KAAK,GAAC;YAAE,OAAM;gBAAC,UAAS;gBAAW,KAAI;gBAAE,MAAK;gBAAE,WAAU,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC;gBAAC,gBAAe;YAAM;QAAC;IAAG;AAAI;AAAC,IAAI,KAAG,oUAAA,CAAA,WAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAE,AAAD,KAAI,IAAE,CAAA,GAAA,uTAAA,CAAA,cAAE,AAAD,KAAI,EAAC,OAAM,CAAC,EAAC,cAAa,CAAC,EAAC,UAAS,CAAC,EAAC,MAAK,CAAC,EAAC,MAAK,CAAC,EAAC,IAAG,CAAC,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,UAAS,IAAE,KAAG,CAAC,CAAC,EAAC,SAAQ,CAAC,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,UAAS,IAAE,CAAC,CAAC,EAAC,WAAU,IAAE,CAAC,CAAC,EAAC,SAAQ,IAAE,IAAI,EAAC,UAAS,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,mUAAA,CAAA,kBAAE,AAAD,EAAE,IAAG,CAAC,IAAE,IAAE,EAAE,GAAC,KAAK,CAAC,EAAC,EAAE,GAAC,CAAA,GAAA,+TAAA,CAAA,kBAAE,AAAD,EAAE,GAAE,GAAE,IAAG,IAAE,CAAA,GAAA,wVAAA,CAAA,qBAAE,AAAD,EAAE;QAAC,IAAG;QAAE,SAAQ;QAAE,YAAW;IAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAE,AAAD,EAAE;QAAC,QAAO,CAAC;QAAE,MAAK,CAAC;IAAC,IAAG,IAAE,CAAA,GAAA,mUAAA,CAAA,kBAAE,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,IAAE,MAAI,OAAK,EAAE,OAAO,CAAC,OAAO,CAAC,KAAG,EAAE,OAAO,CAAC,SAAS,CAAC,CAAA,IAAG,EAAE,GAAE,MAAI,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA,IAAG,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,EAAC,MAAK,IAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,CAAA,GAAA,iTAAA,CAAA,QAAE,AAAD,EAAE,EAAE,IAAI,EAAC;YAAC,CAAC,gVAAA,CAAA,YAAC,CAAC,KAAK,CAAC,EAAC,IAAI,EAAE,IAAI,CAAC,CAAA,IAAG,EAAE,GAAE;YAAI,CAAC,gVAAA,CAAA,YAAC,CAAC,MAAM,CAAC,EAAC,IAAI,EAAE,GAAE;QAAE,IAAG;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG,EAAE,OAAO,GAAE,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,IAAI,KAAG,OAAK,KAAK,IAAE,MAAK,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,YAAW;YAAE,WAAU;YAAE,iBAAgB;YAAE,OAAM;YAAE,cAAa;YAAE,UAAS;YAAE,SAAQ;YAAE,MAAK,IAAE,gVAAA,CAAA,YAAC,CAAC,KAAK,GAAC,gVAAA,CAAA,YAAC,CAAC,MAAM;YAAC,SAAQ,IAAE,IAAE;YAAK,UAAS;YAAE,YAAW;YAAE,gBAAe;YAAE,SAAQ;YAAE,SAAQ;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;IAAE,CAAA,GAAA,2UAAA,CAAA,sBAAE,AAAD,EAAE;QAAK,IAAI;QAAE,KAAG,EAAE,IAAI,CAAC;YAAC,MAAK,gVAAA,CAAA,cAAE,CAAC,0BAA0B;YAAC,SAAQ,EAAE,OAAO;YAAC,UAAS,CAAC,IAAE,EAAE,QAAQ,KAAG,OAAK,IAAE;QAAI;IAAE,GAAE;QAAC;QAAE,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO;QAAC,KAAG,OAAK,KAAK,IAAE,EAAE,QAAQ;KAAC,GAAE,CAAA,GAAA,2UAAA,CAAA,sBAAE,AAAD,EAAE;QAAK,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,GAAC;IAAC,GAAE;QAAC;KAAE;IAAE,IAAG,CAAC,GAAE,GAAE,GAAE,EAAE,GAAC,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG;YAAC,EAAE,aAAa;YAAC,EAAE,aAAa;YAAC,EAAE,YAAY;YAAC,EAAE,cAAc;SAAC,GAAE,IAAE,+TAAA,CAAA,gBAAE,CAAC,GAAG,CAAC,OAAM,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,SAAS,CAAC,KAAK,CAAC,GAAE,IAAG;QAAC;QAAE;KAAE;IAAG,CAAA,GAAA,mUAAA,CAAA,kBAAE,AAAD,EAAE,GAAE;QAAC;QAAE;QAAE;KAAE,EAAC,IAAI,EAAE,OAAO,CAAC,aAAa;IAAI,IAAI,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,EAAE,SAAS,CAAC,iBAAiB,GAAE,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,EAAE,SAAS,CAAC,YAAY,GAAE,KAAG,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI;YAAC,UAAS;YAAE,SAAQ;YAAE,aAAY;YAAE,cAAa;YAAE,OAAM;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE,GAAE,CAAC,GAAE,GAAG,GAAC,CAAA,GAAA,+TAAA,CAAA,YAAE,AAAD,KAAI,IAAE,MAAI,OAAK,CAAC,IAAE;QAAC,KAAI;IAAC,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE;QAAK,IAAG,MAAI,KAAK,GAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE;IAAE,GAAE;QAAC;QAAE;KAAE,GAAE,IAAE,CAAA,GAAA,kTAAA,CAAA,YAAE,AAAD;IAAI,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,IAAG;QAAC,OAAM;QAAE,OAAM;YAAC,SAAQ,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE;QAAA;QAAE,MAAK;YAAC,MAAK,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI;YAAC,UAAS;QAAC;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,uTAAA,CAAA,mBAAE,EAAC,MAAK,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAG,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,wVAAA,CAAA,kBAAE,CAAC,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,6TAAA,CAAA,qBAAE,EAAC;QAAC,OAAM,CAAA,GAAA,iTAAA,CAAA,QAAE,AAAD,EAAE,GAAE;YAAC,CAAC,gVAAA,CAAA,gBAAC,CAAC,IAAI,CAAC,EAAC,6TAAA,CAAA,QAAE,CAAC,IAAI;YAAC,CAAC,gVAAA,CAAA,gBAAC,CAAC,MAAM,CAAC,EAAC,6TAAA,CAAA,QAAE,CAAC,MAAM;QAAA;IAAE,GAAE,KAAG,QAAM,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,6TAAA,CAAA,aAAE,EAAC;QAAC,UAAS;QAAE,MAAK,KAAG,OAAK;YAAC,CAAC,EAAE,EAAC;QAAC,IAAE,CAAC;QAAE,MAAK;QAAE,SAAQ;IAAC,IAAG,EAAE;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAG,YAAW;QAAG,MAAK;IAAU;AAAO;AAAC,IAAI,KAAG;AAAQ,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE;IAAG,IAAI,IAAE,CAAA,GAAA,wVAAA,CAAA,4BAAE,AAAD,EAAE,mBAAkB,IAAE,GAAG,mBAAkB,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAE,AAAD,KAAI,IAAE,CAAA,GAAA,iTAAA,CAAA,gBAAE,AAAD,KAAI,EAAC,IAAG,IAAE,KAAG,CAAC,0BAA0B,EAAE,GAAG,EAAC,UAAS,CAAC,EAAC,cAAa,CAAC,EAAC,UAAS,IAAE,EAAE,QAAQ,IAAE,CAAC,CAAC,EAAC,WAAU,IAAE,CAAC,CAAC,EAAC,MAAK,IAAE,MAAM,EAAC,GAAG,GAAE,GAAC,GAAE,CAAC,EAAE,GAAC,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG;YAAC,EAAE,YAAY;SAAC,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAE,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAE,AAAD,EAAE,GAAE,GAAE,CAAA,GAAA,uTAAA,CAAA,uBAAE,AAAD,KAAI,EAAE,OAAO,CAAC,eAAe,GAAE,IAAE,CAAA,GAAA,wTAAA,CAAA,mBAAE,AAAD,EAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG;YAAC,EAAE,aAAa;YAAC,EAAE,QAAQ;SAAC,GAAE,IAAE,CAAA,GAAA,8TAAA,CAAA,iBAAE,AAAD,KAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,OAAM,EAAE,KAAK,CAAC,cAAc,IAAE,CAAC,EAAE,KAAK,CAAC,cAAc,CAAC,SAAS,GAAC,CAAC,GAAE,EAAE,OAAO,CAAC,UAAU,CAAC;YAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,OAAO;QAAA;IAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE;QAAK,IAAI;QAAE,OAAO,OAAO,KAAG,cAAY,EAAE,KAAK,KAAG,KAAK,IAAE,CAAC,IAAE,EAAE,EAAE,KAAK,CAAC,KAAG,OAAK,IAAE,KAAG,OAAO,EAAE,KAAK,IAAE,WAAS,EAAE,KAAK,GAAC;IAAE,GAAE;QAAC,EAAE,KAAK;QAAC;KAAE;IAAE,CAAA,GAAA,wTAAA,CAAA,WAAE,AAAD,EAAE,CAAC,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAE;QAAI,IAAG,EAAE,KAAK,CAAC,QAAQ,EAAC;QAAO,IAAI,IAAE,EAAE,OAAO;QAAC,KAAG,CAAC,CAAC,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI,IAAE,MAAI,gVAAA,CAAA,gBAAC,CAAC,MAAM,IAAE,MAAI,CAAC,KAAG,CAAC,EAAE,KAAK,GAAC,CAAC,GAAE,sBAAsB;YAAK,IAAG,EAAE,KAAK,CAAC,QAAQ,IAAE,CAAC,KAAG,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,MAAI,GAAE;YAAO,IAAG,EAAC,gBAAe,CAAC,EAAC,cAAa,EAAE,EAAC,GAAC;YAAE,KAAK,GAAG,CAAC,CAAC,MAAI,OAAK,KAAG,CAAC,IAAE,CAAC,KAAG,OAAK,IAAE,CAAC,OAAK,KAAG,MAAI,KAAG,EAAE,iBAAiB,CAAC,EAAE,KAAK,CAAC,MAAM,EAAC,EAAE,KAAK,CAAC,MAAM;QAAC,EAAE;IAAC,GAAE;QAAC;QAAE;QAAE;QAAE;KAAE,GAAE,CAAA,GAAA,wTAAA,CAAA,WAAE,AAAD,EAAE,CAAC,CAAC,EAAE,EAAC,CAAC,EAAE;QAAI,IAAG,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI,IAAE,MAAI,gVAAA,CAAA,gBAAC,CAAC,MAAM,EAAC;YAAC,IAAG,EAAE,KAAK,CAAC,QAAQ,EAAC;YAAO,IAAI,IAAE,EAAE,OAAO;YAAC,IAAG,CAAC,GAAE;YAAO,IAAI,IAAE,EAAE,KAAK,EAAC,EAAC,gBAAe,CAAC,EAAC,cAAa,CAAC,EAAC,oBAAmB,EAAE,EAAC,GAAC;YAAE,EAAE,KAAK,GAAC,IAAG,EAAE,KAAK,GAAC,GAAE,OAAK,OAAK,EAAE,iBAAiB,CAAC,GAAE,GAAE,MAAI,EAAE,iBAAiB,CAAC,GAAE;QAAE;IAAC,GAAE;QAAC;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAE,AAAD,EAAE,CAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,EAAE,OAAO,GAAC,CAAC;IAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,EAAE,SAAS,CAAC;YAAK,EAAE,OAAO,GAAC,CAAC;QAAC;IAAE,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,IAAG,EAAE,GAAG;YAAE,KAAK,yTAAA,CAAA,OAAC,CAAC,KAAK;gBAAC,IAAG,EAAE,KAAK,CAAC,aAAa,KAAG,gVAAA,CAAA,gBAAC,CAAC,IAAI,IAAE,EAAE,OAAO,EAAC;gBAAO,IAAG,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,SAAS,CAAC,iBAAiB,CAAC,EAAE,KAAK,MAAI,MAAK;oBAAC,EAAE,OAAO,CAAC,aAAa;oBAAG;gBAAM;gBAAC,EAAE,OAAO,CAAC,kBAAkB,IAAG,EAAE,IAAI,KAAG,gVAAA,CAAA,YAAC,CAAC,MAAM,IAAE,EAAE,OAAO,CAAC,aAAa;gBAAG;YAAM,KAAK,yTAAA,CAAA,OAAC,CAAC,SAAS;gBAAC,OAAO,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,CAAA,GAAA,iTAAA,CAAA,QAAE,AAAD,EAAE,EAAE,KAAK,CAAC,aAAa,EAAC;oBAAC,CAAC,gVAAA,CAAA,gBAAC,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC;4BAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,IAAI;wBAAA;oBAAG,CAAC,gVAAA,CAAA,gBAAC,CAAC,MAAM,CAAC,EAAC,IAAI,EAAE,OAAO,CAAC,YAAY;gBAAE;YAAG,KAAK,yTAAA,CAAA,OAAC,CAAC,OAAO;gBAAC,OAAO,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,CAAA,GAAA,iTAAA,CAAA,QAAE,AAAD,EAAE,EAAE,KAAK,CAAC,aAAa,EAAC;oBAAC,CAAC,gVAAA,CAAA,gBAAC,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC;4BAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,QAAQ;wBAAA;oBAAG,CAAC,gVAAA,CAAA,gBAAC,CAAC,MAAM,CAAC,EAAC;wBAAK,CAAA,GAAA,2UAAA,CAAA,YAAE,AAAD,EAAE,IAAI,EAAE,OAAO,CAAC,YAAY,KAAI,EAAE,KAAK,IAAE,EAAE,OAAO,CAAC,UAAU,CAAC;4BAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,IAAI;wBAAA;oBAAE;gBAAC;YAAG,KAAK,yTAAA,CAAA,OAAC,CAAC,IAAI;gBAAC,IAAG,EAAE,QAAQ,EAAC;gBAAM,OAAO,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,OAAO,CAAC,UAAU,CAAC;oBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,KAAK;gBAAA;YAAG,KAAK,yTAAA,CAAA,OAAC,CAAC,MAAM;gBAAC,OAAO,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,OAAO,CAAC,UAAU,CAAC;oBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,KAAK;gBAAA;YAAG,KAAK,yTAAA,CAAA,OAAC,CAAC,GAAG;gBAAC,IAAG,EAAE,QAAQ,EAAC;gBAAM,OAAO,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,OAAO,CAAC,UAAU,CAAC;oBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,IAAI;gBAAA;YAAG,KAAK,yTAAA,CAAA,OAAC,CAAC,QAAQ;gBAAC,OAAO,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,OAAO,CAAC,UAAU,CAAC;oBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,IAAI;gBAAA;YAAG,KAAK,yTAAA,CAAA,OAAC,CAAC,MAAM;gBAAC,OAAO,EAAE,KAAK,CAAC,aAAa,KAAG,gVAAA,CAAA,gBAAC,CAAC,IAAI,GAAC,KAAK,IAAE,CAAC,EAAE,cAAc,IAAG,EAAE,KAAK,CAAC,cAAc,IAAE,CAAC,EAAE,eAAe,CAAC,OAAO,CAAC,MAAM,IAAE,EAAE,eAAe,IAAG,EAAE,IAAI,KAAG,gVAAA,CAAA,YAAC,CAAC,MAAM,IAAE,EAAE,KAAK,KAAG,QAAM,KAAI,EAAE,OAAO,CAAC,aAAa,EAAE;YAAE,KAAK,yTAAA,CAAA,OAAC,CAAC,GAAG;gBAAC,IAAG,EAAE,KAAK,CAAC,aAAa,KAAG,gVAAA,CAAA,gBAAC,CAAC,IAAI,EAAC;gBAAO,EAAE,IAAI,KAAG,gVAAA,CAAA,YAAC,CAAC,MAAM,IAAE,EAAE,KAAK,CAAC,iBAAiB,KAAG,gVAAA,CAAA,oBAAE,CAAC,KAAK,IAAE,EAAE,OAAO,CAAC,kBAAkB,IAAG,EAAE,OAAO,CAAC,aAAa;gBAAG;QAAK;IAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,KAAG,QAAM,EAAE,IAAG,EAAE,IAAI,KAAG,gVAAA,CAAA,YAAC,CAAC,MAAM,IAAE,EAAE,MAAM,CAAC,KAAK,KAAG,MAAI,KAAI,EAAE,OAAO,CAAC,YAAY;IAAE,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI,GAAE,GAAE;QAAE,IAAI,IAAE,CAAC,IAAE,EAAE,aAAa,KAAG,OAAK,IAAE,wUAAA,CAAA,UAAE,CAAC,IAAI,CAAC,CAAA,IAAG,MAAI,EAAE,aAAa;QAAE,IAAG,CAAC,CAAC,CAAC,IAAE,EAAE,KAAK,CAAC,cAAc,KAAG,QAAM,EAAE,QAAQ,CAAC,EAAE,KAAG,CAAC,CAAC,CAAC,IAAE,EAAE,KAAK,CAAC,aAAa,KAAG,QAAM,EAAE,QAAQ,CAAC,EAAE,KAAG,EAAE,KAAK,CAAC,aAAa,KAAG,gVAAA,CAAA,gBAAC,CAAC,IAAI,EAAC,OAAO,EAAE,cAAc,IAAG,EAAE,IAAI,KAAG,gVAAA,CAAA,YAAC,CAAC,MAAM,IAAE,EAAE,KAAK,KAAG,QAAM,KAAI,EAAE,OAAO,CAAC,aAAa;IAAE,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI,GAAE,GAAE;QAAE,IAAI,IAAE,CAAC,IAAE,EAAE,aAAa,KAAG,OAAK,IAAE,wUAAA,CAAA,UAAE,CAAC,IAAI,CAAC,CAAA,IAAG,MAAI,EAAE,aAAa;QAAE,CAAC,IAAE,EAAE,KAAK,CAAC,aAAa,KAAG,QAAM,EAAE,QAAQ,CAAC,MAAI,CAAC,IAAE,EAAE,KAAK,CAAC,cAAc,KAAG,QAAM,EAAE,QAAQ,CAAC,MAAI,EAAE,QAAQ,IAAE,EAAE,SAAS,IAAE,EAAE,KAAK,CAAC,aAAa,KAAG,gVAAA,CAAA,gBAAC,CAAC,IAAI,IAAE,EAAE,SAAS,CAAC;YAAK,CAAA,GAAA,2UAAA,CAAA,YAAE,AAAD,EAAE,IAAI,EAAE,OAAO,CAAC,YAAY,KAAI,EAAE,OAAO,CAAC,oBAAoB,CAAC,gVAAA,CAAA,oBAAE,CAAC,KAAK;QAAC;IAAE,IAAG,IAAE,CAAA,GAAA,+TAAA,CAAA,gBAAE,AAAD,KAAI,IAAE,CAAA,GAAA,2UAAA,CAAA,iBAAE,AAAD,KAAI,EAAC,WAAU,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,uTAAA,CAAA,eAAE,AAAD,EAAE;QAAC,WAAU;IAAC,IAAG,EAAC,WAAU,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,iUAAA,CAAA,WAAE,AAAD,EAAE;QAAC,YAAW;IAAC,IAAG,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG,EAAE,cAAc,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI;YAAC,UAAS;YAAE,SAAQ,EAAE,OAAO;YAAC,OAAM;YAAE,OAAM;YAAE,WAAU;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE,EAAE,OAAO;KAAC,GAAE,IAAE,CAAA,GAAA,kTAAA,CAAA,aAAE,AAAD,EAAE;QAAC,KAAI;QAAE,IAAG;QAAE,MAAK;QAAW,MAAK;QAAE,iBAAgB,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE;QAAC,iBAAgB,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI;QAAC,yBAAwB,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,EAAE,SAAS,CAAC,kBAAkB;QAAE,mBAAkB;QAAE,oBAAmB;QAAE,qBAAoB;QAAO,cAAa,CAAC,KAAG,CAAC,IAAE,EAAE,YAAY,KAAG,OAAK,IAAE,EAAE,YAAY,KAAG,KAAK,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE,YAAY,IAAE,IAAI,KAAG,OAAK,KAAG,EAAE,YAAY;QAAC,UAAS,KAAG,KAAK;QAAE,WAAU;QAAE,oBAAmB;QAAE,kBAAiB;QAAE,WAAU;QAAE,UAAS;QAAE,SAAQ;QAAE,QAAO;IAAC,GAAE,GAAE;IAAG,OAAO,CAAA,GAAA,kTAAA,CAAA,YAAE,AAAD,IAAI;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAgB;AAAE;AAAC,IAAI,KAAG;AAAS,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,wVAAA,CAAA,4BAAE,AAAD,EAAE,oBAAmB,IAAE,GAAG,oBAAmB,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAE,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAE,AAAD,EAAE,GAAE,GAAE,EAAE,OAAO,CAAC,gBAAgB,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAE,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,2BAA2B,EAAE,GAAG,EAAC,UAAS,IAAE,EAAE,QAAQ,IAAE,CAAC,CAAC,EAAC,WAAU,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,CAAC,GAAE,GAAE,EAAE,GAAC,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG;YAAC,EAAE,aAAa;YAAC,EAAE,YAAY;YAAC,EAAE,cAAc;SAAC,GAAE,IAAE,CAAA,GAAA,uUAAA,CAAA,sBAAE,AAAD,EAAE,IAAG,IAAE,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI;IAAC,CAAA,GAAA,mUAAA,CAAA,kBAAE,AAAD,EAAE,GAAE;QAAC,SAAQ;QAAE,QAAO,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA;YAAI,IAAG,KAAG,QAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAE,OAAO,mUAAA,CAAA,SAAE,CAAC,MAAM;YAAC,IAAG,KAAG,QAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAE,OAAO,mUAAA,CAAA,SAAE,CAAC,MAAM;YAAC,IAAI,IAAE,EAAE,MAAM,CAAC,OAAO,CAAC;YAAwC,OAAO,CAAA,GAAA,+SAAA,CAAA,gBAAgB,AAAD,EAAE,KAAG,mUAAA,CAAA,SAAE,CAAC,MAAM,CAAC,KAAG,KAAG,QAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,IAAE,mUAAA,CAAA,SAAE,CAAC,MAAM,GAAC,mUAAA,CAAA,SAAE,CAAC,KAAK;QAAA,GAAE;YAAC;YAAE;YAAE;SAAE;QAAE,OAAM,EAAE,OAAO,CAAC,aAAa;QAAC,QAAO,EAAE,OAAO,CAAC,kBAAkB;IAAA;IAAG,IAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,OAAO,EAAE,GAAG;YAAE,KAAK,yTAAA,CAAA,OAAC,CAAC,KAAK;YAAC,KAAK,yTAAA,CAAA,OAAC,CAAC,KAAK;gBAAC,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,KAAK,CAAC,aAAa,KAAG,gVAAA,CAAA,gBAAC,CAAC,MAAM,IAAE,CAAA,GAAA,2UAAA,CAAA,YAAE,AAAD,EAAE,IAAI,EAAE,OAAO,CAAC,YAAY,KAAI;gBAAI;YAAO,KAAK,yTAAA,CAAA,OAAC,CAAC,SAAS;gBAAC,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,KAAK,CAAC,aAAa,KAAG,gVAAA,CAAA,gBAAC,CAAC,MAAM,IAAE,CAAC,CAAA,GAAA,2UAAA,CAAA,YAAE,AAAD,EAAE,IAAI,EAAE,OAAO,CAAC,YAAY,KAAI,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAE,EAAE,OAAO,CAAC,UAAU,CAAC;oBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,KAAK;gBAAA,EAAE,GAAE;gBAAI;YAAO,KAAK,yTAAA,CAAA,OAAC,CAAC,OAAO;gBAAC,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,KAAK,CAAC,aAAa,KAAG,gVAAA,CAAA,gBAAC,CAAC,MAAM,IAAE,CAAC,CAAA,GAAA,2UAAA,CAAA,YAAE,AAAD,EAAE,IAAI,EAAE,OAAO,CAAC,YAAY,KAAI,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAE,EAAE,OAAO,CAAC,UAAU,CAAC;oBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,IAAI;gBAAA,EAAE,GAAE;gBAAI;YAAO,KAAK,yTAAA,CAAA,OAAC,CAAC,MAAM;gBAAC,IAAG,EAAE,KAAK,CAAC,aAAa,KAAG,gVAAA,CAAA,gBAAC,CAAC,IAAI,EAAC;gBAAO,EAAE,cAAc,IAAG,EAAE,KAAK,CAAC,cAAc,IAAE,CAAC,EAAE,eAAe,CAAC,OAAO,CAAC,MAAM,IAAE,EAAE,eAAe,IAAG,CAAA,GAAA,2UAAA,CAAA,YAAE,AAAD,EAAE,IAAI,EAAE,OAAO,CAAC,aAAa,KAAI;gBAAI;YAAO;gBAAQ;QAAM;IAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,EAAE,cAAc,IAAG,CAAC,CAAA,GAAA,gTAAA,CAAA,2BAAE,AAAD,EAAE,EAAE,aAAa,KAAG,CAAC,EAAE,MAAM,KAAG,sTAAA,CAAA,cAAE,CAAC,IAAI,IAAE,CAAC,EAAE,KAAK,CAAC,aAAa,KAAG,gVAAA,CAAA,gBAAC,CAAC,IAAI,GAAC,EAAE,OAAO,CAAC,aAAa,KAAG,EAAE,OAAO,CAAC,YAAY,EAAE,GAAE,GAAG;IAAC,IAAG,IAAE,CAAA,GAAA,+TAAA,CAAA,gBAAE,AAAD,EAAE;QAAC;KAAE,GAAE,EAAC,gBAAe,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,uTAAA,CAAA,eAAE,AAAD,EAAE;QAAC,WAAU;IAAC,IAAG,EAAC,WAAU,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,iUAAA,CAAA,WAAE,AAAD,EAAE;QAAC,YAAW;IAAC,IAAG,EAAC,SAAQ,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE;QAAC,UAAS;IAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI;YAAC,QAAO,KAAG,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI;YAAC,UAAS;YAAE,SAAQ,EAAE,OAAO;YAAC,OAAM,EAAE,KAAK;YAAC,OAAM;YAAE,OAAM;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE,GAAE,IAAE,CAAA,GAAA,kTAAA,CAAA,aAAE,AAAD,EAAE;QAAC,KAAI;QAAE,IAAG;QAAE,MAAK,CAAA,GAAA,4UAAA,CAAA,uBAAE,AAAD,EAAE,GAAE;QAAG,UAAS,CAAC;QAAE,iBAAgB;QAAU,iBAAgB,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE;QAAC,iBAAgB,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI;QAAC,mBAAkB;QAAE,UAAS,KAAG,KAAK;QAAE,WAAU;QAAE,eAAc;QAAE,WAAU;IAAC,GAAE,GAAE,GAAE;IAAG,OAAO,CAAA,GAAA,kTAAA,CAAA,YAAE,AAAD,IAAI;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAiB;AAAE;AAAC,IAAI,KAAG,OAAM,KAAG,kTAAA,CAAA,iBAAE,CAAC,cAAc,GAAC,kTAAA,CAAA,iBAAE,CAAC,MAAM;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,GAAE;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAE,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,4BAA4B,EAAE,GAAG,EAAC,MAAK,IAAE,CAAC,CAAC,EAAC,QAAO,CAAC,EAAC,QAAO,IAAE,CAAC,CAAC,EAAC,OAAM,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,wVAAA,CAAA,4BAAE,AAAD,EAAE,qBAAoB,IAAE,GAAG,qBAAoB,IAAE,CAAA,GAAA,uTAAA,CAAA,oBAAE,AAAD,EAAE;IAAG,KAAG,CAAC,IAAE,CAAC,CAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,uTAAA,CAAA,mBAAE,AAAD,EAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAE,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,uTAAA,CAAA,wBAAE,AAAD,KAAI,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAE,AAAD,EAAE,GAAE,IAAE,IAAE,MAAK,EAAE,OAAO,CAAC,iBAAiB,EAAC,IAAG,CAAC,GAAE,GAAE,GAAE,GAAE,EAAE,GAAC,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG;YAAC,EAAE,aAAa;YAAC,EAAE,YAAY;YAAC,EAAE,aAAa;YAAC,EAAE,cAAc;YAAC,EAAE,iBAAiB;SAAC,GAAE,IAAE,CAAA,GAAA,wTAAA,CAAA,mBAAE,AAAD,EAAE,KAAG,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,mBAAE,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,6TAAA,CAAA,gBAAE,AAAD,KAAI,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,6TAAA,CAAA,gBAAE,AAAD,EAAE,GAAE,GAAE,MAAI,OAAK,CAAC,IAAE,6TAAA,CAAA,QAAE,CAAC,IAAI,MAAI,6TAAA,CAAA,QAAE,CAAC,IAAI,GAAC,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI;IAAE,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE,GAAE,GAAE,EAAE,OAAO,CAAC,aAAa;IAAE,IAAI,IAAE,EAAE,UAAU,GAAC,CAAC,IAAE,KAAG,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI;IAAC,CAAA,GAAA,iUAAA,CAAA,gBAAE,AAAD,EAAE,GAAE;IAAG,IAAI,IAAE,EAAE,UAAU,GAAC,CAAC,IAAE,KAAG,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI;IAAC,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE,GAAE;QAAC,SAAQ,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,IAAI;gBAAC;gBAAE;gBAAE;aAAE,EAAC;YAAC;YAAE;YAAE;SAAE;IAAC,IAAG,CAAA,GAAA,2UAAA,CAAA,sBAAE,AAAD,EAAE;QAAK,IAAI;QAAE,EAAE,eAAe,CAAC,OAAO,CAAC,MAAM,GAAC,CAAC,IAAE,EAAE,MAAM,KAAG,OAAK,IAAE,CAAC;IAAC,GAAE;QAAC,EAAE,eAAe;QAAC,EAAE,MAAM;KAAC,GAAE,CAAA,GAAA,2UAAA,CAAA,sBAAE,AAAD,EAAE;QAAK,EAAE,eAAe,CAAC,OAAO,CAAC,IAAI,GAAC;IAAC,GAAE;QAAC,EAAE,eAAe;QAAC;KAAE,GAAE,CAAA,GAAA,iUAAA,CAAA,gBAAE,AAAD,EAAE,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI,EAAC;QAAC,WAAU;QAAE,QAAO,CAAC;YAAE,OAAO,EAAE,YAAY,CAAC,YAAU,WAAS,WAAW,aAAa,GAAC,EAAE,YAAY,CAAC,UAAQ,WAAW,WAAW,GAAC,WAAW,aAAa;QAAA;QAAE,MAAK,CAAC;YAAE,EAAE,YAAY,CAAC,QAAO;QAAO;IAAC;IAAG,IAAI,IAAE,CAAA,GAAA,+TAAA,CAAA,gBAAE,AAAD,EAAE;QAAC,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE;KAAC,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI,gVAAA,CAAA,gBAAC,CAAC,IAAI;YAAC,QAAO,KAAK;QAAC,CAAC,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,EAAE,OAAO,CAAC,oBAAoB,CAAC,gVAAA,CAAA,oBAAE,CAAC,OAAO;IAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,EAAE,cAAc,IAAG,EAAE,OAAO,CAAC,oBAAoB,CAAC,gVAAA,CAAA,oBAAE,CAAC,OAAO;IAAC,IAAG,IAAE,CAAA,GAAA,kTAAA,CAAA,aAAE,AAAD,EAAE,IAAE,MAAI,CAAC,GAAE;QAAC,mBAAkB;QAAE,MAAK;QAAU,wBAAuB,EAAE,IAAI,KAAG,gVAAA,CAAA,YAAC,CAAC,KAAK,GAAC,CAAC,IAAE,KAAK;QAAE,IAAG;QAAE,KAAI;QAAE,OAAM;YAAC,GAAG,EAAE,KAAK;YAAC,GAAG,CAAC;YAAC,iBAAgB,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE,GAAE,CAAC,GAAG,KAAK;YAAC,kBAAiB,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE,GAAE,CAAC,GAAG,KAAK;QAAA;QAAE,SAAQ,MAAI,gVAAA,CAAA,oBAAE,CAAC,OAAO,GAAC,KAAK,IAAE;QAAE,aAAY;QAAE,GAAG,CAAA,GAAA,6TAAA,CAAA,2BAAE,AAAD,EAAE,EAAE;IAAA,IAAG,IAAE,KAAG,MAAI,gVAAA,CAAA,gBAAC,CAAC,MAAM,EAAC,IAAE,CAAA,GAAA,qTAAA,CAAA,gBAAE,AAAD,EAAE,GAAE,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,GAAE,KAAG,CAAA,GAAA,qTAAA,CAAA,gBAAE,AAAD,EAAE,GAAE,EAAE,KAAK,GAAE,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,EAAE,OAAO,CAAC,IAAG,KAAI,KAAG,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE;QAAK,IAAG,CAAC,EAAE,OAAO,EAAC,OAAO;QAAE,IAAG,MAAI,KAAK,GAAE,MAAM,IAAI,MAAM;QAAqC,OAAO,MAAI,EAAE,OAAO,CAAC,OAAO,GAAC;YAAC,GAAG,CAAC;YAAC,SAAQ;gBAAC,GAAG,EAAE,OAAO;gBAAC,SAAQ;YAAC;QAAC,IAAE;IAAC,GAAE;QAAC;QAAE;QAAE,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO;KAAC;IAAE,EAAE,OAAO,IAAE,OAAO,MAAM,CAAC,GAAE;QAAC,UAAS,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAG,QAAQ,EAAC;YAAC,OAAM;QAAE,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,IAAG;YAAC,MAAK;QAAC,GAAE,EAAE,QAAQ;IAAE;IAAG,IAAI,IAAE,CAAA,GAAA,kTAAA,CAAA,YAAE,AAAD,KAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,EAAE,IAAI,KAAG,gVAAA,CAAA,YAAC,CAAC,KAAK,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,YAAW;QAAC,GAAE;QAAC;QAAE;KAAE;IAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,iUAAA,CAAA,SAAE,EAAC;QAAC,SAAQ,IAAE,EAAE,MAAM,IAAE,IAAE,CAAC;QAAE,eAAc;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAG,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,EAAE;QAAC,UAAS;QAAE,YAAW;YAAC,GAAG,CAAC;YAAC,UAAS,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,qTAAA,CAAA,SAAE,EAAC;gBAAC,QAAO;YAAC,GAAE,OAAO,EAAE,QAAQ,IAAE,aAAW,CAAC,IAAE,EAAE,QAAQ,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,GAAE,KAAG,EAAE,QAAQ;QAAC;QAAE,MAAK;QAAE,YAAW;QAAG,UAAS;QAAG,SAAQ;QAAE,MAAK;IAAkB;AAAI;AAAC,IAAI,KAAG;AAAM,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,GAAE;IAAE,IAAI,IAAE,GAAG,oBAAmB,IAAE,CAAA,GAAA,wVAAA,CAAA,4BAAE,AAAD,EAAE,oBAAmB,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAE,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,2BAA2B,EAAE,GAAG,EAAC,OAAM,CAAC,EAAC,UAAS,IAAE,CAAC,IAAE,CAAC,IAAE,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,QAAQ,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,GAAE,EAAE,KAAG,OAAK,IAAE,CAAC,CAAC,EAAC,OAAM,IAAE,IAAI,EAAC,GAAG,GAAE,GAAC,GAAE,CAAC,EAAE,GAAC,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG;YAAC,EAAE,YAAY;SAAC,GAAE,IAAE,CAAA,GAAA,uUAAA,CAAA,sBAAE,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,SAAS,CAAC,QAAQ,CAAC,GAAE,GAAE,IAAG;QAAC;QAAE;KAAE,IAAG,IAAE,EAAE,UAAU,CAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAE,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE;QAAC,UAAS;QAAE,OAAM;QAAE,QAAO;QAAE,OAAM;IAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAE,AAAD,EAAE,KAAI,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAE,AAAD,EAAE,GAAE,GAAE,IAAE,EAAE,cAAc,GAAC,OAAM,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,IAAG,EAAE,OAAO,CAAC,QAAQ,CAAC;IAAE;IAAG,CAAA,GAAA,2UAAA,CAAA,sBAAE,AAAD,EAAE,IAAI,EAAE,OAAO,CAAC,cAAc,CAAC,GAAE,IAAG;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,SAAS,CAAC,oBAAoB,CAAC,GAAE,GAAE,IAAG;QAAC;QAAE;KAAE;IAAG,CAAA,GAAA,2UAAA,CAAA,sBAAE,AAAD,EAAE;QAAK,IAAG,GAAE,OAAO,CAAA,GAAA,uTAAA,CAAA,cAAE,AAAD,IAAI,qBAAqB,CAAC;YAAK,IAAI,GAAE;YAAE,CAAC,IAAE,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,cAAc,KAAG,QAAM,EAAE,IAAI,CAAC,GAAE;gBAAC,OAAM;YAAS;QAAE;IAAE,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,EAAE,cAAc,IAAG,EAAE,MAAM,KAAG,sTAAA,CAAA,cAAE,CAAC,IAAI,IAAE,CAAC,KAAG,CAAC,KAAI,CAAA,GAAA,oTAAA,CAAA,WAAE,AAAD,OAAK,sBAAsB,IAAI,MAAK,EAAE,IAAI,KAAG,gVAAA,CAAA,YAAC,CAAC,MAAM,IAAE,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC;IAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAG,GAAE,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC;YAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,OAAO;QAAA;QAAG,IAAI,IAAE,EAAE,cAAc,CAAC;QAAG,EAAE,OAAO,CAAC,UAAU,CAAC;YAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,QAAQ;YAAC,KAAI;QAAC;IAAE,IAAG,IAAE,CAAA,GAAA,qUAAA,CAAA,oBAAE,AAAD,KAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,EAAE,MAAM,CAAC,KAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAG,CAAC,EAAE,QAAQ,CAAC,MAAI,KAAG,GAAE;QAAO,IAAI,IAAE,EAAE,cAAc,CAAC;QAAG,EAAE,OAAO,CAAC,UAAU,CAAC;YAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,QAAQ;YAAC,KAAI;QAAC,GAAE,gVAAA,CAAA,oBAAE,CAAC,OAAO;IAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,EAAE,QAAQ,CAAC,MAAI,CAAC,KAAG,KAAG,CAAC,EAAE,eAAe,CAAC,OAAO,CAAC,IAAI,IAAE,EAAE,OAAO,CAAC,UAAU,CAAC;YAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,OAAO;QAAA,EAAE,CAAC;IAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,QAAO;YAAE,OAAM;YAAE,UAAS;YAAE,UAAS;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;KAAE,GAAE,IAAE;QAAC,IAAG;QAAE,KAAI;QAAE,MAAK;QAAS,UAAS,MAAI,CAAC,IAAE,KAAK,IAAE,CAAC;QAAE,iBAAgB,MAAI,CAAC,IAAE,CAAC,IAAE,KAAK;QAAE,iBAAgB;QAAE,UAAS,KAAK;QAAE,aAAY;QAAE,SAAQ;QAAE,gBAAe;QAAE,cAAa;QAAE,eAAc;QAAE,aAAY;QAAE,gBAAe;QAAE,cAAa;IAAC;IAAE,OAAO,CAAA,GAAA,kTAAA,CAAA,YAAE,AAAD,IAAI;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAiB;AAAE;AAAC,IAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAE,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAE,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAE,AAAD,EAAE,KAAI,KAAG,+TAAA,CAAA,QAAE,EAAC,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAE,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAE,AAAD,EAAE,KAAI,KAAG,OAAO,MAAM,CAAC,IAAG;IAAC,OAAM;IAAG,QAAO;IAAG,OAAM;IAAG,SAAQ;IAAG,QAAO;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5367, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-did-element-move.js"], "sourcesContent": ["import{useRef as i}from\"react\";import{useIsoMorphicEffect as u}from'./use-iso-morphic-effect.js';function s(n,t){let e=i({left:0,top:0});if(u(()=>{if(!t)return;let r=t.getBoundingClientRect();r&&(e.current=r)},[n,t]),t==null||!n||t===document.activeElement)return!1;let o=t.getBoundingClientRect();return o.top!==e.current.top||o.left!==e.current.left}export{s as useDidElementMove};\n"], "names": [], "mappings": ";;;AAAA;AAA+B;;;AAAkE,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE;QAAC,MAAK;QAAE,KAAI;IAAC;IAAG,IAAG,CAAA,GAAA,2UAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE;QAAO,IAAI,IAAE,EAAE,qBAAqB;QAAG,KAAG,CAAC,EAAE,OAAO,GAAC,CAAC;IAAC,GAAE;QAAC;QAAE;KAAE,GAAE,KAAG,QAAM,CAAC,KAAG,MAAI,SAAS,aAAa,EAAC,OAAM,CAAC;IAAE,IAAI,IAAE,EAAE,qBAAqB;IAAG,OAAO,EAAE,GAAG,KAAG,EAAE,OAAO,CAAC,GAAG,IAAE,EAAE,IAAI,KAAG,EAAE,OAAO,CAAC,IAAI;AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5397, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/utils/get-text-value.js"], "sourcesContent": ["import*as g from'./dom.js';let a=/([\\u2700-\\u27BF]|[\\uE000-\\uF8FF]|\\uD83C[\\uDC00-\\uDFFF]|\\uD83D[\\uDC00-\\uDFFF]|[\\u2011-\\u26FF]|\\uD83E[\\uDD10-\\uDDFF])/g;function o(e){var l,n;let i=(l=e.innerText)!=null?l:\"\",t=e.cloneNode(!0);if(!g.isHTMLElement(t))return i;let u=!1;for(let f of t.querySelectorAll('[hidden],[aria-hidden],[role=\"img\"]'))f.remove(),u=!0;let r=u?(n=t.innerText)!=null?n:\"\":i;return a.test(r)&&(r=r.replace(a,\"\")),r}function F(e){let i=e.getAttribute(\"aria-label\");if(typeof i==\"string\")return i.trim();let t=e.getAttribute(\"aria-labelledby\");if(t){let u=t.split(\" \").map(r=>{let l=document.getElementById(r);if(l){let n=l.getAttribute(\"aria-label\");return typeof n==\"string\"?n.trim():o(l).trim()}return null}).filter(Boolean);if(u.length>0)return u.join(\", \")}return o(e).trim()}export{F as getTextValue};\n"], "names": [], "mappings": ";;;AAAA;;AAA2B,IAAI,IAAE;AAAuH,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE;IAAE,IAAI,IAAE,CAAC,IAAE,EAAE,SAAS,KAAG,OAAK,IAAE,IAAG,IAAE,EAAE,SAAS,CAAC,CAAC;IAAG,IAAG,CAAC,CAAA,GAAA,+SAAA,CAAA,gBAAe,AAAD,EAAE,IAAG,OAAO;IAAE,IAAI,IAAE,CAAC;IAAE,KAAI,IAAI,KAAK,EAAE,gBAAgB,CAAC,uCAAuC,EAAE,MAAM,IAAG,IAAE,CAAC;IAAE,IAAI,IAAE,IAAE,CAAC,IAAE,EAAE,SAAS,KAAG,OAAK,IAAE,KAAG;IAAE,OAAO,EAAE,IAAI,CAAC,MAAI,CAAC,IAAE,EAAE,OAAO,CAAC,GAAE,GAAG,GAAE;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,EAAE,YAAY,CAAC;IAAc,IAAG,OAAO,KAAG,UAAS,OAAO,EAAE,IAAI;IAAG,IAAI,IAAE,EAAE,YAAY,CAAC;IAAmB,IAAG,GAAE;QAAC,IAAI,IAAE,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA;YAAI,IAAI,IAAE,SAAS,cAAc,CAAC;YAAG,IAAG,GAAE;gBAAC,IAAI,IAAE,EAAE,YAAY,CAAC;gBAAc,OAAO,OAAO,KAAG,WAAS,EAAE,IAAI,KAAG,EAAE,GAAG,IAAI;YAAE;YAAC,OAAO;QAAI,GAAG,MAAM,CAAC;QAAS,IAAG,EAAE,MAAM,GAAC,GAAE,OAAO,EAAE,IAAI,CAAC;IAAK;IAAC,OAAO,EAAE,GAAG,IAAI;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5436, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/hooks/use-text-value.js"], "sourcesContent": ["import{useRef as l}from\"react\";import{getTextValue as i}from'../utils/get-text-value.js';import{useEvent as o}from'./use-event.js';function s(c){let t=l(\"\"),r=l(\"\");return o(()=>{let e=c.current;if(!e)return\"\";let u=e.innerText;if(t.current===u)return r.current;let n=i(e).trim().toLowerCase();return t.current=u,r.current=n,n})}export{s as useTextValue};\n"], "names": [], "mappings": ";;;AAAA;AAA+B;AAA0D;;;;AAA0C,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE,KAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAC,AAAD,EAAE;IAAI,OAAO,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAI,IAAE,EAAE,OAAO;QAAC,IAAG,CAAC,GAAE,OAAM;QAAG,IAAI,IAAE,EAAE,SAAS;QAAC,IAAG,EAAE,OAAO,KAAG,GAAE,OAAO,EAAE,OAAO;QAAC,IAAI,IAAE,CAAA,GAAA,gUAAA,CAAA,eAAC,AAAD,EAAE,GAAG,IAAI,GAAG,WAAW;QAAG,OAAO,EAAE,OAAO,GAAC,GAAE,EAAE,OAAO,GAAC,GAAE;IAAC;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5463, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/listbox/listbox-machine.js"], "sourcesContent": ["var T=Object.defineProperty;var m=(e,o,t)=>o in e?T(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t;var v=(e,o,t)=>(m(e,typeof o!=\"symbol\"?o+\"\":o,t),t);import{Machine as y,batch as g}from'../../machine.js';import{ActionTypes as I,stackMachines as R}from'../../machines/stack-machine.js';import{Focus as p,calculateActiveIndex as x}from'../../utils/calculate-active-index.js';import{sortByDomNode as A}from'../../utils/focus-management.js';import{match as S}from'../../utils/match.js';var E=(t=>(t[t.Open=0]=\"Open\",t[t.Closed=1]=\"Closed\",t))(E||{}),L=(t=>(t[t.Single=0]=\"Single\",t[t.Multi=1]=\"Multi\",t))(L||{}),F=(t=>(t[t.Pointer=0]=\"Pointer\",t[t.Other=1]=\"Other\",t))(F||{}),M=(r=>(r[r.OpenListbox=0]=\"OpenListbox\",r[r.CloseListbox=1]=\"CloseListbox\",r[r.GoToOption=2]=\"GoToOption\",r[r.Search=3]=\"Search\",r[r.ClearSearch=4]=\"ClearSearch\",r[r.RegisterOptions=5]=\"RegisterOptions\",r[r.UnregisterOptions=6]=\"UnregisterOptions\",r[r.SetButtonElement=7]=\"SetButtonElement\",r[r.SetOptionsElement=8]=\"SetOptionsElement\",r[r.SortOptions=9]=\"SortOptions\",r))(M||{});function b(e,o=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,n=A(o(e.options.slice()),s=>s.dataRef.current.domRef.current),i=t?n.indexOf(t):null;return i===-1&&(i=null),{options:n,activeOptionIndex:i}}let C={[1](e){return e.dataRef.current.disabled||e.listboxState===1?e:{...e,activeOptionIndex:null,pendingFocus:{focus:p.Nothing},listboxState:1,__demoMode:!1}},[0](e,o){if(e.dataRef.current.disabled||e.listboxState===0)return e;let t=e.activeOptionIndex,{isSelected:n}=e.dataRef.current,i=e.options.findIndex(s=>n(s.dataRef.current.value));return i!==-1&&(t=i),{...e,pendingFocus:o.focus,listboxState:0,activeOptionIndex:t,__demoMode:!1}},[2](e,o){var s,l,u,d,a;if(e.dataRef.current.disabled||e.listboxState===1)return e;let t={...e,searchQuery:\"\",activationTrigger:(s=o.trigger)!=null?s:1,__demoMode:!1};if(o.focus===p.Nothing)return{...t,activeOptionIndex:null};if(o.focus===p.Specific)return{...t,activeOptionIndex:e.options.findIndex(r=>r.id===o.id)};if(o.focus===p.Previous){let r=e.activeOptionIndex;if(r!==null){let O=e.options[r].dataRef.current.domRef,f=x(o,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:c=>c.id,resolveDisabled:c=>c.dataRef.current.disabled});if(f!==null){let c=e.options[f].dataRef.current.domRef;if(((l=O.current)==null?void 0:l.previousElementSibling)===c.current||((u=c.current)==null?void 0:u.previousElementSibling)===null)return{...t,activeOptionIndex:f}}}}else if(o.focus===p.Next){let r=e.activeOptionIndex;if(r!==null){let O=e.options[r].dataRef.current.domRef,f=x(o,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:c=>c.id,resolveDisabled:c=>c.dataRef.current.disabled});if(f!==null){let c=e.options[f].dataRef.current.domRef;if(((d=O.current)==null?void 0:d.nextElementSibling)===c.current||((a=c.current)==null?void 0:a.nextElementSibling)===null)return{...t,activeOptionIndex:f}}}}let n=b(e),i=x(o,{resolveItems:()=>n.options,resolveActiveIndex:()=>n.activeOptionIndex,resolveId:r=>r.id,resolveDisabled:r=>r.dataRef.current.disabled});return{...t,...n,activeOptionIndex:i}},[3]:(e,o)=>{if(e.dataRef.current.disabled||e.listboxState===1)return e;let n=e.searchQuery!==\"\"?0:1,i=e.searchQuery+o.value.toLowerCase(),l=(e.activeOptionIndex!==null?e.options.slice(e.activeOptionIndex+n).concat(e.options.slice(0,e.activeOptionIndex+n)):e.options).find(d=>{var a;return!d.dataRef.current.disabled&&((a=d.dataRef.current.textValue)==null?void 0:a.startsWith(i))}),u=l?e.options.indexOf(l):-1;return u===-1||u===e.activeOptionIndex?{...e,searchQuery:i}:{...e,searchQuery:i,activeOptionIndex:u,activationTrigger:1}},[4](e){return e.dataRef.current.disabled||e.listboxState===1||e.searchQuery===\"\"?e:{...e,searchQuery:\"\"}},[5]:(e,o)=>{let t=e.options.concat(o.options),n=e.activeOptionIndex;if(e.pendingFocus.focus!==p.Nothing&&(n=x(e.pendingFocus,{resolveItems:()=>t,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:i=>i.id,resolveDisabled:i=>i.dataRef.current.disabled})),e.activeOptionIndex===null){let{isSelected:i}=e.dataRef.current;if(i){let s=t.findIndex(l=>i==null?void 0:i(l.dataRef.current.value));s!==-1&&(n=s)}}return{...e,options:t,activeOptionIndex:n,pendingFocus:{focus:p.Nothing},pendingShouldSort:!0}},[6]:(e,o)=>{let t=e.options,n=[],i=new Set(o.options);for(let[s,l]of t.entries())if(i.has(l.id)&&(n.push(s),i.delete(l.id),i.size===0))break;if(n.length>0){t=t.slice();for(let s of n.reverse())t.splice(s,1)}return{...e,options:t,activationTrigger:1}},[7]:(e,o)=>e.buttonElement===o.element?e:{...e,buttonElement:o.element},[8]:(e,o)=>e.optionsElement===o.element?e:{...e,optionsElement:o.element},[9]:e=>e.pendingShouldSort?{...e,...b(e),pendingShouldSort:!1}:e};class h extends y{constructor(t){super(t);v(this,\"actions\",{onChange:t=>{let{onChange:n,compare:i,mode:s,value:l}=this.state.dataRef.current;return S(s,{[0]:()=>n==null?void 0:n(t),[1]:()=>{let u=l.slice(),d=u.findIndex(a=>i(a,t));return d===-1?u.push(t):u.splice(d,1),n==null?void 0:n(u)}})},registerOption:g(()=>{let t=[],n=new Set;return[(i,s)=>{n.has(s)||(n.add(s),t.push({id:i,dataRef:s}))},()=>(n.clear(),this.send({type:5,options:t.splice(0)}))]}),unregisterOption:g(()=>{let t=[];return[n=>t.push(n),()=>{this.send({type:6,options:t.splice(0)})}]}),goToOption:g(()=>{let t=null;return[(n,i)=>{t={type:2,...n,trigger:i}},()=>t&&this.send(t)]}),closeListbox:()=>{this.send({type:1})},openListbox:t=>{this.send({type:0,focus:t})},selectActiveOption:()=>{if(this.state.activeOptionIndex!==null){let{dataRef:t,id:n}=this.state.options[this.state.activeOptionIndex];this.actions.onChange(t.current.value),this.send({type:2,focus:p.Specific,id:n})}},selectOption:t=>{let n=this.state.options.find(i=>i.id===t);n&&this.actions.onChange(n.dataRef.current.value)},search:t=>{this.send({type:3,value:t})},clearSearch:()=>{this.send({type:4})},setButtonElement:t=>{this.send({type:7,element:t})},setOptionsElement:t=>{this.send({type:8,element:t})}});v(this,\"selectors\",{activeDescendantId(t){var s;let n=t.activeOptionIndex,i=t.options;return n===null||(s=i[n])==null?void 0:s.id},isActive(t,n){var l;let i=t.activeOptionIndex,s=t.options;return i!==null?((l=s[i])==null?void 0:l.id)===n:!1},shouldScrollIntoView(t,n){return t.__demoMode||t.listboxState!==0||t.activationTrigger===0?!1:this.isActive(t,n)}});this.on(5,()=>{requestAnimationFrame(()=>{this.send({type:9})})});{let n=this.state.id,i=R.get(null);this.disposables.add(i.on(I.Push,s=>{!i.selectors.isTop(s,n)&&this.state.listboxState===0&&this.actions.closeListbox()})),this.on(0,()=>i.actions.push(n)),this.on(1,()=>i.actions.pop(n))}}static new({id:t,__demoMode:n=!1}){return new h({id:t,dataRef:{current:{}},listboxState:n?0:1,options:[],searchQuery:\"\",activeOptionIndex:null,activationTrigger:1,buttonElement:null,optionsElement:null,pendingShouldSort:!1,pendingFocus:{focus:p.Nothing},__demoMode:n})}reduce(t,n){return S(n.type,C,t,n)}}export{M as ActionTypes,F as ActivationTrigger,h as ListboxMachine,E as ListboxStates,L as ValueMode};\n"], "names": [], "mappings": ";;;;;;;AAAwK;AAAsD;AAAiF;AAAwF;AAAgE;AAAvc,IAAI,IAAE,OAAO,cAAc;AAAC,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,KAAK,IAAE,EAAE,GAAE,GAAE;QAAC,YAAW,CAAC;QAAE,cAAa,CAAC;QAAE,UAAS,CAAC;QAAE,OAAM;IAAC,KAAG,CAAC,CAAC,EAAE,GAAC;AAAE,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,CAAC,EAAE,GAAE,OAAO,KAAG,WAAS,IAAE,KAAG,GAAE,IAAG,CAAC;;;;;;AAA8U,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAG,CAAC,IAAG,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAG,CAAC,IAAG,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAG,CAAC,IAAG,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,WAAW,GAAC,EAAE,GAAC,eAAc,CAAC,CAAC,EAAE,YAAY,GAAC,EAAE,GAAC,gBAAe,CAAC,CAAC,EAAE,UAAU,GAAC,EAAE,GAAC,cAAa,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,WAAW,GAAC,EAAE,GAAC,eAAc,CAAC,CAAC,EAAE,eAAe,GAAC,EAAE,GAAC,mBAAkB,CAAC,CAAC,EAAE,iBAAiB,GAAC,EAAE,GAAC,qBAAoB,CAAC,CAAC,EAAE,gBAAgB,GAAC,EAAE,GAAC,oBAAmB,CAAC,CAAC,EAAE,iBAAiB,GAAC,EAAE,GAAC,qBAAoB,CAAC,CAAC,EAAE,WAAW,GAAC,EAAE,GAAC,eAAc,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,CAAC,EAAC,IAAE,CAAA,IAAG,CAAC;IAAE,IAAI,IAAE,EAAE,iBAAiB,KAAG,OAAK,EAAE,OAAO,CAAC,EAAE,iBAAiB,CAAC,GAAC,MAAK,IAAE,CAAA,GAAA,+TAAA,CAAA,gBAAC,AAAD,EAAE,EAAE,EAAE,OAAO,CAAC,KAAK,KAAI,CAAA,IAAG,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,GAAE,IAAE,IAAE,EAAE,OAAO,CAAC,KAAG;IAAK,OAAO,MAAI,CAAC,KAAG,CAAC,IAAE,IAAI,GAAE;QAAC,SAAQ;QAAE,mBAAkB;IAAC;AAAC;AAAC,IAAI,IAAE;IAAC,CAAC,EAAE,EAAC,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,IAAE,EAAE,YAAY,KAAG,IAAE,IAAE;YAAC,GAAG,CAAC;YAAC,mBAAkB;YAAK,cAAa;gBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,OAAO;YAAA;YAAE,cAAa;YAAE,YAAW,CAAC;QAAC;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,IAAG,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,IAAE,EAAE,YAAY,KAAG,GAAE,OAAO;QAAE,IAAI,IAAE,EAAE,iBAAiB,EAAC,EAAC,YAAW,CAAC,EAAC,GAAC,EAAE,OAAO,CAAC,OAAO,EAAC,IAAE,EAAE,OAAO,CAAC,SAAS,CAAC,CAAA,IAAG,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK;QAAG,OAAO,MAAI,CAAC,KAAG,CAAC,IAAE,CAAC,GAAE;YAAC,GAAG,CAAC;YAAC,cAAa,EAAE,KAAK;YAAC,cAAa;YAAE,mBAAkB;YAAE,YAAW,CAAC;QAAC;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,GAAE,GAAE,GAAE,GAAE;QAAE,IAAG,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,IAAE,EAAE,YAAY,KAAG,GAAE,OAAO;QAAE,IAAI,IAAE;YAAC,GAAG,CAAC;YAAC,aAAY;YAAG,mBAAkB,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,IAAE;YAAE,YAAW,CAAC;QAAC;QAAE,IAAG,EAAE,KAAK,KAAG,wUAAA,CAAA,QAAC,CAAC,OAAO,EAAC,OAAM;YAAC,GAAG,CAAC;YAAC,mBAAkB;QAAI;QAAE,IAAG,EAAE,KAAK,KAAG,wUAAA,CAAA,QAAC,CAAC,QAAQ,EAAC,OAAM;YAAC,GAAG,CAAC;YAAC,mBAAkB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAA,IAAG,EAAE,EAAE,KAAG,EAAE,EAAE;QAAC;QAAE,IAAG,EAAE,KAAK,KAAG,wUAAA,CAAA,QAAC,CAAC,QAAQ,EAAC;YAAC,IAAI,IAAE,EAAE,iBAAiB;YAAC,IAAG,MAAI,MAAK;gBAAC,IAAI,IAAE,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAC,IAAE,CAAA,GAAA,wUAAA,CAAA,uBAAC,AAAD,EAAE,GAAE;oBAAC,cAAa,IAAI,EAAE,OAAO;oBAAC,oBAAmB,IAAI,EAAE,iBAAiB;oBAAC,WAAU,CAAA,IAAG,EAAE,EAAE;oBAAC,iBAAgB,CAAA,IAAG,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;gBAAA;gBAAG,IAAG,MAAI,MAAK;oBAAC,IAAI,IAAE,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM;oBAAC,IAAG,CAAC,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,sBAAsB,MAAI,EAAE,OAAO,IAAE,CAAC,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,sBAAsB,MAAI,MAAK,OAAM;wBAAC,GAAG,CAAC;wBAAC,mBAAkB;oBAAC;gBAAC;YAAC;QAAC,OAAM,IAAG,EAAE,KAAK,KAAG,wUAAA,CAAA,QAAC,CAAC,IAAI,EAAC;YAAC,IAAI,IAAE,EAAE,iBAAiB;YAAC,IAAG,MAAI,MAAK;gBAAC,IAAI,IAAE,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAC,IAAE,CAAA,GAAA,wUAAA,CAAA,uBAAC,AAAD,EAAE,GAAE;oBAAC,cAAa,IAAI,EAAE,OAAO;oBAAC,oBAAmB,IAAI,EAAE,iBAAiB;oBAAC,WAAU,CAAA,IAAG,EAAE,EAAE;oBAAC,iBAAgB,CAAA,IAAG,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;gBAAA;gBAAG,IAAG,MAAI,MAAK;oBAAC,IAAI,IAAE,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM;oBAAC,IAAG,CAAC,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,kBAAkB,MAAI,EAAE,OAAO,IAAE,CAAC,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,kBAAkB,MAAI,MAAK,OAAM;wBAAC,GAAG,CAAC;wBAAC,mBAAkB;oBAAC;gBAAC;YAAC;QAAC;QAAC,IAAI,IAAE,EAAE,IAAG,IAAE,CAAA,GAAA,wUAAA,CAAA,uBAAC,AAAD,EAAE,GAAE;YAAC,cAAa,IAAI,EAAE,OAAO;YAAC,oBAAmB,IAAI,EAAE,iBAAiB;YAAC,WAAU,CAAA,IAAG,EAAE,EAAE;YAAC,iBAAgB,CAAA,IAAG,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;QAAA;QAAG,OAAM;YAAC,GAAG,CAAC;YAAC,GAAG,CAAC;YAAC,mBAAkB;QAAC;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,GAAE;QAAK,IAAG,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,IAAE,EAAE,YAAY,KAAG,GAAE,OAAO;QAAE,IAAI,IAAE,EAAE,WAAW,KAAG,KAAG,IAAE,GAAE,IAAE,EAAE,WAAW,GAAC,EAAE,KAAK,CAAC,WAAW,IAAG,IAAE,CAAC,EAAE,iBAAiB,KAAG,OAAK,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,iBAAiB,GAAC,GAAG,MAAM,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,GAAE,EAAE,iBAAiB,GAAC,MAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;YAAI,IAAI;YAAE,OAAM,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,IAAE,CAAC,CAAC,IAAE,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS,KAAG,OAAK,KAAK,IAAE,EAAE,UAAU,CAAC,EAAE;QAAC,IAAG,IAAE,IAAE,EAAE,OAAO,CAAC,OAAO,CAAC,KAAG,CAAC;QAAE,OAAO,MAAI,CAAC,KAAG,MAAI,EAAE,iBAAiB,GAAC;YAAC,GAAG,CAAC;YAAC,aAAY;QAAC,IAAE;YAAC,GAAG,CAAC;YAAC,aAAY;YAAE,mBAAkB;YAAE,mBAAkB;QAAC;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,IAAE,EAAE,YAAY,KAAG,KAAG,EAAE,WAAW,KAAG,KAAG,IAAE;YAAC,GAAG,CAAC;YAAC,aAAY;QAAE;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,GAAE;QAAK,IAAI,IAAE,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,GAAE,IAAE,EAAE,iBAAiB;QAAC,IAAG,EAAE,YAAY,CAAC,KAAK,KAAG,wUAAA,CAAA,QAAC,CAAC,OAAO,IAAE,CAAC,IAAE,CAAA,GAAA,wUAAA,CAAA,uBAAC,AAAD,EAAE,EAAE,YAAY,EAAC;YAAC,cAAa,IAAI;YAAE,oBAAmB,IAAI,EAAE,iBAAiB;YAAC,WAAU,CAAA,IAAG,EAAE,EAAE;YAAC,iBAAgB,CAAA,IAAG,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;QAAA,EAAE,GAAE,EAAE,iBAAiB,KAAG,MAAK;YAAC,IAAG,EAAC,YAAW,CAAC,EAAC,GAAC,EAAE,OAAO,CAAC,OAAO;YAAC,IAAG,GAAE;gBAAC,IAAI,IAAE,EAAE,SAAS,CAAC,CAAA,IAAG,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK;gBAAG,MAAI,CAAC,KAAG,CAAC,IAAE,CAAC;YAAC;QAAC;QAAC,OAAM;YAAC,GAAG,CAAC;YAAC,SAAQ;YAAE,mBAAkB;YAAE,cAAa;gBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,OAAO;YAAA;YAAE,mBAAkB,CAAC;QAAC;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,GAAE;QAAK,IAAI,IAAE,EAAE,OAAO,EAAC,IAAE,EAAE,EAAC,IAAE,IAAI,IAAI,EAAE,OAAO;QAAE,KAAI,IAAG,CAAC,GAAE,EAAE,IAAG,EAAE,OAAO,GAAG,IAAG,EAAE,GAAG,CAAC,EAAE,EAAE,KAAG,CAAC,EAAE,IAAI,CAAC,IAAG,EAAE,MAAM,CAAC,EAAE,EAAE,GAAE,EAAE,IAAI,KAAG,CAAC,GAAE;QAAM,IAAG,EAAE,MAAM,GAAC,GAAE;YAAC,IAAE,EAAE,KAAK;YAAG,KAAI,IAAI,KAAK,EAAE,OAAO,GAAG,EAAE,MAAM,CAAC,GAAE;QAAE;QAAC,OAAM;YAAC,GAAG,CAAC;YAAC,SAAQ;YAAE,mBAAkB;QAAC;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,GAAE,IAAI,EAAE,aAAa,KAAG,EAAE,OAAO,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,eAAc,EAAE,OAAO;QAAA;IAAE,CAAC,EAAE,EAAC,CAAC,GAAE,IAAI,EAAE,cAAc,KAAG,EAAE,OAAO,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,gBAAe,EAAE,OAAO;QAAA;IAAE,CAAC,EAAE,EAAC,CAAA,IAAG,EAAE,iBAAiB,GAAC;YAAC,GAAG,CAAC;YAAC,GAAG,EAAE,EAAE;YAAC,mBAAkB,CAAC;QAAC,IAAE;AAAC;AAAE,MAAM,UAAU,0SAAA,CAAA,UAAC;IAAC,YAAY,CAAC,CAAC;QAAC,KAAK,CAAC;QAAG,EAAE,IAAI,EAAC,WAAU;YAAC,UAAS,CAAA;gBAAI,IAAG,EAAC,UAAS,CAAC,EAAC,SAAQ,CAAC,EAAC,MAAK,CAAC,EAAC,OAAM,CAAC,EAAC,GAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;gBAAC,OAAO,CAAA,GAAA,iTAAA,CAAA,QAAC,AAAD,EAAE,GAAE;oBAAC,CAAC,EAAE,EAAC,IAAI,KAAG,OAAK,KAAK,IAAE,EAAE;oBAAG,CAAC,EAAE,EAAC;wBAAK,IAAI,IAAE,EAAE,KAAK,IAAG,IAAE,EAAE,SAAS,CAAC,CAAA,IAAG,EAAE,GAAE;wBAAI,OAAO,MAAI,CAAC,IAAE,EAAE,IAAI,CAAC,KAAG,EAAE,MAAM,CAAC,GAAE,IAAG,KAAG,OAAK,KAAK,IAAE,EAAE;oBAAE;gBAAC;YAAE;YAAE,gBAAe,CAAA,GAAA,0SAAA,CAAA,QAAC,AAAD,EAAE;gBAAK,IAAI,IAAE,EAAE,EAAC,IAAE,IAAI;gBAAI,OAAM;oBAAC,CAAC,GAAE;wBAAK,EAAE,GAAG,CAAC,MAAI,CAAC,EAAE,GAAG,CAAC,IAAG,EAAE,IAAI,CAAC;4BAAC,IAAG;4BAAE,SAAQ;wBAAC,EAAE;oBAAC;oBAAE,IAAI,CAAC,EAAE,KAAK,IAAG,IAAI,CAAC,IAAI,CAAC;4BAAC,MAAK;4BAAE,SAAQ,EAAE,MAAM,CAAC;wBAAE,EAAE;iBAAE;YAAA;YAAG,kBAAiB,CAAA,GAAA,0SAAA,CAAA,QAAC,AAAD,EAAE;gBAAK,IAAI,IAAE,EAAE;gBAAC,OAAM;oBAAC,CAAA,IAAG,EAAE,IAAI,CAAC;oBAAG;wBAAK,IAAI,CAAC,IAAI,CAAC;4BAAC,MAAK;4BAAE,SAAQ,EAAE,MAAM,CAAC;wBAAE;oBAAE;iBAAE;YAAA;YAAG,YAAW,CAAA,GAAA,0SAAA,CAAA,QAAC,AAAD,EAAE;gBAAK,IAAI,IAAE;gBAAK,OAAM;oBAAC,CAAC,GAAE;wBAAK,IAAE;4BAAC,MAAK;4BAAE,GAAG,CAAC;4BAAC,SAAQ;wBAAC;oBAAC;oBAAE,IAAI,KAAG,IAAI,CAAC,IAAI,CAAC;iBAAG;YAAA;YAAG,cAAa;gBAAK,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;gBAAC;YAAE;YAAE,aAAY,CAAA;gBAAI,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,OAAM;gBAAC;YAAE;YAAE,oBAAmB;gBAAK,IAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,KAAG,MAAK;oBAAC,IAAG,EAAC,SAAQ,CAAC,EAAC,IAAG,CAAC,EAAC,GAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;oBAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,KAAK,GAAE,IAAI,CAAC,IAAI,CAAC;wBAAC,MAAK;wBAAE,OAAM,wUAAA,CAAA,QAAC,CAAC,QAAQ;wBAAC,IAAG;oBAAC;gBAAE;YAAC;YAAE,cAAa,CAAA;gBAAI,IAAI,IAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,IAAG,EAAE,EAAE,KAAG;gBAAG,KAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK;YAAC;YAAE,QAAO,CAAA;gBAAI,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,OAAM;gBAAC;YAAE;YAAE,aAAY;gBAAK,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;gBAAC;YAAE;YAAE,kBAAiB,CAAA;gBAAI,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,SAAQ;gBAAC;YAAE;YAAE,mBAAkB,CAAA;gBAAI,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;oBAAE,SAAQ;gBAAC;YAAE;QAAC;QAAG,EAAE,IAAI,EAAC,aAAY;YAAC,oBAAmB,CAAC;gBAAE,IAAI;gBAAE,IAAI,IAAE,EAAE,iBAAiB,EAAC,IAAE,EAAE,OAAO;gBAAC,OAAO,MAAI,QAAM,CAAC,IAAE,CAAC,CAAC,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE;YAAA;YAAE,UAAS,CAAC,EAAC,CAAC;gBAAE,IAAI;gBAAE,IAAI,IAAE,EAAE,iBAAiB,EAAC,IAAE,EAAE,OAAO;gBAAC,OAAO,MAAI,OAAK,CAAC,CAAC,IAAE,CAAC,CAAC,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE,MAAI,IAAE,CAAC;YAAC;YAAE,sBAAqB,CAAC,EAAC,CAAC;gBAAE,OAAO,EAAE,UAAU,IAAE,EAAE,YAAY,KAAG,KAAG,EAAE,iBAAiB,KAAG,IAAE,CAAC,IAAE,IAAI,CAAC,QAAQ,CAAC,GAAE;YAAE;QAAC;QAAG,IAAI,CAAC,EAAE,CAAC,GAAE;YAAK,sBAAsB;gBAAK,IAAI,CAAC,IAAI,CAAC;oBAAC,MAAK;gBAAC;YAAE;QAAE;QAAG;YAAC,IAAI,IAAE,IAAI,CAAC,KAAK,CAAC,EAAE,EAAC,IAAE,+TAAA,CAAA,gBAAC,CAAC,GAAG,CAAC;YAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,+TAAA,CAAA,cAAC,CAAC,IAAI,EAAC,CAAA;gBAAI,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,GAAE,MAAI,IAAI,CAAC,KAAK,CAAC,YAAY,KAAG,KAAG,IAAI,CAAC,OAAO,CAAC,YAAY;YAAE,KAAI,IAAI,CAAC,EAAE,CAAC,GAAE,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,KAAI,IAAI,CAAC,EAAE,CAAC,GAAE,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC;QAAG;IAAC;IAAC,OAAO,IAAI,EAAC,IAAG,CAAC,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,EAAC;QAAC,OAAO,IAAI,EAAE;YAAC,IAAG;YAAE,SAAQ;gBAAC,SAAQ,CAAC;YAAC;YAAE,cAAa,IAAE,IAAE;YAAE,SAAQ,EAAE;YAAC,aAAY;YAAG,mBAAkB;YAAK,mBAAkB;YAAE,eAAc;YAAK,gBAAe;YAAK,mBAAkB,CAAC;YAAE,cAAa;gBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,OAAO;YAAA;YAAE,YAAW;QAAC;IAAE;IAAC,OAAO,CAAC,EAAC,CAAC,EAAC;QAAC,OAAO,CAAA,GAAA,iTAAA,CAAA,QAAC,AAAD,EAAE,EAAE,IAAI,EAAC,GAAE,GAAE;IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5819, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/listbox/listbox-machine-glue.js"], "sourcesContent": ["import{createContext as n,useContext as r,useMemo as i}from\"react\";import{useOnUnmount as s}from'../../hooks/use-on-unmount.js';import{ListboxMachine as a}from'./listbox-machine.js';const c=n(null);function p(o){let e=r(c);if(e===null){let t=new Error(`<${o} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}return e}function u({id:o,__demoMode:e=!1}){let t=i(()=>a.new({id:o,__demoMode:e}),[]);return s(()=>t.dispose()),t}export{c as ListboxContext,u as useListboxMachine,p as useListboxMachineContext};\n"], "names": [], "mappings": ";;;;;AAAA;AAAmE;AAA6D;;;;AAAsD,MAAM,IAAE,CAAA,GAAA,oUAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAC,AAAD,EAAE;IAAG,IAAG,MAAI,MAAK;QAAC,IAAI,IAAE,IAAI,MAAM,CAAC,CAAC,EAAE,EAAE,8CAA8C,CAAC;QAAE,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG;IAAC;IAAC,OAAO;AAAC;AAAC,SAAS,EAAE,EAAC,IAAG,CAAC,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,8UAAA,CAAA,iBAAC,CAAC,GAAG,CAAC;YAAC,IAAG;YAAE,YAAW;QAAC,IAAG,EAAE;IAAE,OAAO,CAAA,GAAA,gUAAA,CAAA,eAAC,AAAD,EAAE,IAAI,EAAE,OAAO,KAAI;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5853, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/node_modules/.pnpm/%40headlessui%2Breact%402.2.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40headlessui/react/dist/components/listbox/listbox.js"], "sourcesContent": ["\"use client\";import{useFocusRing as he}from\"@react-aria/focus\";import{useHover as De}from\"@react-aria/interactions\";import F,{Fragment as ce,createContext as fe,useCallback as j,useContext as Te,useEffect as Ae,useMemo as V,useRef as pe,useState as Se}from\"react\";import{flushSync as J}from\"react-dom\";import{useActivePress as Re}from'../../hooks/use-active-press.js';import{useByComparator as _e}from'../../hooks/use-by-comparator.js';import{useControllable as Fe}from'../../hooks/use-controllable.js';import{useDefaultValue as Ce}from'../../hooks/use-default-value.js';import{useDidElementMove as Me}from'../../hooks/use-did-element-move.js';import{useDisposables as we}from'../../hooks/use-disposables.js';import{useElementSize as Ie}from'../../hooks/use-element-size.js';import{useEvent as C}from'../../hooks/use-event.js';import{useId as ee}from'../../hooks/use-id.js';import{useInertOthers as Be}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as ue}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as ke}from'../../hooks/use-latest-value.js';import{useOnDisappear as Ue}from'../../hooks/use-on-disappear.js';import{useOutsideClick as Ne}from'../../hooks/use-outside-click.js';import{useOwnerDocument as be}from'../../hooks/use-owner.js';import{Action as te,useQuickRelease as He}from'../../hooks/use-quick-release.js';import{useResolveButtonType as Ge}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as Ve}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as Q}from'../../hooks/use-sync-refs.js';import{useTextValue as Ke}from'../../hooks/use-text-value.js';import{useTrackedPointer as ze}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as We,useTransition as Xe}from'../../hooks/use-transition.js';import{useDisabled as je}from'../../internal/disabled.js';import{FloatingProvider as Je,useFloatingPanel as Qe,useFloatingPanelProps as $e,useFloatingReference as qe,useFloatingReferenceProps as Ye,useResolvedAnchor as Ze}from'../../internal/floating.js';import{FormFields as et}from'../../internal/form-fields.js';import{useFrozenData as tt}from'../../internal/frozen.js';import{useProvidedId as ot}from'../../internal/id.js';import{OpenClosedProvider as nt,State as oe,useOpenClosed as rt}from'../../internal/open-closed.js';import{stackMachines as at}from'../../machines/stack-machine.js';import{useSlice as M}from'../../react-glue.js';import{isDisabledReactIssue7711 as lt}from'../../utils/bugs.js';import{Focus as P}from'../../utils/calculate-active-index.js';import{disposables as it}from'../../utils/disposables.js';import*as st from'../../utils/dom.js';import{Focus as me,FocusableMode as pt,focusFrom as ut,isFocusableElement as dt}from'../../utils/focus-management.js';import{attemptSubmit as ct}from'../../utils/form.js';import{match as ne}from'../../utils/match.js';import{getOwnerDocument as ft}from'../../utils/owner.js';import{RenderFeatures as ye,forwardRefWithAs as $,mergeProps as xe,useRender as q}from'../../utils/render.js';import{useDescribedBy as Tt}from'../description/description.js';import{Keys as f}from'../keyboard.js';import{Label as bt,useLabelledBy as mt,useLabels as yt}from'../label/label.js';import{Portal as xt}from'../portal/portal.js';import{ActionTypes as Ot,ActivationTrigger as Oe,ListboxStates as T,ValueMode as k}from'./listbox-machine.js';import{ListboxContext as Lt,useListboxMachine as Pt,useListboxMachineContext as de}from'./listbox-machine-glue.js';let re=fe(null);re.displayName=\"ListboxDataContext\";function Y(g){let D=Te(re);if(D===null){let x=new Error(`<${g} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(x,Y),x}return D}let gt=ce;function vt(g,D){let x=ee(),u=je(),{value:l,defaultValue:p,form:R,name:i,onChange:b,by:o,invalid:d=!1,disabled:m=u||!1,horizontal:a=!1,multiple:t=!1,__demoMode:s=!1,...A}=g;const v=a?\"horizontal\":\"vertical\";let U=Q(D),w=Ce(p),[c=t?[]:void 0,O]=Fe(l,b,w),y=Pt({id:x,__demoMode:s}),I=pe({static:!1,hold:!1}),N=pe(new Map),_=_e(o),H=j(h=>ne(n.mode,{[k.Multi]:()=>c.some(W=>_(W,h)),[k.Single]:()=>_(c,h)}),[c]),n=V(()=>({value:c,disabled:m,invalid:d,mode:t?k.Multi:k.Single,orientation:v,onChange:O,compare:_,isSelected:H,optionsPropsRef:I,listRef:N}),[c,m,d,t,v,O,_,H,I,N]);ue(()=>{y.state.dataRef.current=n},[n]);let L=M(y,h=>h.listboxState),G=at.get(null),K=M(G,j(h=>G.selectors.isTop(h,x),[G,x])),[E,z]=M(y,h=>[h.buttonElement,h.optionsElement]);Ne(K,[E,z],(h,W)=>{y.send({type:Ot.CloseListbox}),dt(W,pt.Loose)||(h.preventDefault(),E==null||E.focus())});let r=V(()=>({open:L===T.Open,disabled:m,invalid:d,value:c}),[L,m,d,c]),[B,ae]=yt({inherit:!0}),le={ref:U},ie=j(()=>{if(w!==void 0)return O==null?void 0:O(w)},[O,w]),Z=q();return F.createElement(ae,{value:B,props:{htmlFor:E==null?void 0:E.id},slot:{open:L===T.Open,disabled:m}},F.createElement(Je,null,F.createElement(Lt.Provider,{value:y},F.createElement(re.Provider,{value:n},F.createElement(nt,{value:ne(L,{[T.Open]:oe.Open,[T.Closed]:oe.Closed})},i!=null&&c!=null&&F.createElement(et,{disabled:m,data:{[i]:c},form:R,onReset:ie}),Z({ourProps:le,theirProps:A,slot:r,defaultTag:gt,name:\"Listbox\"}))))))}let Et=\"button\";function ht(g,D){let x=ee(),u=ot(),l=Y(\"Listbox.Button\"),p=de(\"Listbox.Button\"),{id:R=u||`headlessui-listbox-button-${x}`,disabled:i=l.disabled||!1,autoFocus:b=!1,...o}=g,d=Q(D,qe(),p.actions.setButtonElement),m=Ye(),[a,t,s]=M(p,r=>[r.listboxState,r.buttonElement,r.optionsElement]),A=a===T.Open;He(A,{trigger:t,action:j(r=>{if(t!=null&&t.contains(r.target))return te.Ignore;let B=r.target.closest('[role=\"option\"]:not([data-disabled])');return st.isHTMLElement(B)?te.Select(B):s!=null&&s.contains(r.target)?te.Ignore:te.Close},[t,s]),close:p.actions.closeListbox,select:p.actions.selectActiveOption});let v=C(r=>{switch(r.key){case f.Enter:ct(r.currentTarget);break;case f.Space:case f.ArrowDown:r.preventDefault(),p.actions.openListbox({focus:l.value?P.Nothing:P.First});break;case f.ArrowUp:r.preventDefault(),p.actions.openListbox({focus:l.value?P.Nothing:P.Last});break}}),U=C(r=>{switch(r.key){case f.Space:r.preventDefault();break}}),w=C(r=>{var B;if(r.button===0){if(lt(r.currentTarget))return r.preventDefault();p.state.listboxState===T.Open?(J(()=>p.actions.closeListbox()),(B=p.state.buttonElement)==null||B.focus({preventScroll:!0})):(r.preventDefault(),p.actions.openListbox({focus:P.Nothing}))}}),c=C(r=>r.preventDefault()),O=mt([R]),y=Tt(),{isFocusVisible:I,focusProps:N}=he({autoFocus:b}),{isHovered:_,hoverProps:H}=De({isDisabled:i}),{pressed:n,pressProps:L}=Re({disabled:i}),G=V(()=>({open:a===T.Open,active:n||a===T.Open,disabled:i,invalid:l.invalid,value:l.value,hover:_,focus:I,autofocus:b}),[a,l.value,i,_,I,n,l.invalid,b]),K=M(p,r=>r.listboxState===T.Open),E=xe(m(),{ref:d,id:R,type:Ge(g,t),\"aria-haspopup\":\"listbox\",\"aria-controls\":s==null?void 0:s.id,\"aria-expanded\":K,\"aria-labelledby\":O,\"aria-describedby\":y,disabled:i||void 0,autoFocus:b,onKeyDown:v,onKeyUp:U,onKeyPress:c,onPointerDown:w},N,H,L);return q()({ourProps:E,theirProps:o,slot:G,defaultTag:Et,name:\"Listbox.Button\"})}let Le=fe(!1),Dt=\"div\",At=ye.RenderStrategy|ye.Static;function St(g,D){let x=ee(),{id:u=`headlessui-listbox-options-${x}`,anchor:l,portal:p=!1,modal:R=!0,transition:i=!1,...b}=g,o=Ze(l),[d,m]=Se(null);o&&(p=!0);let a=Y(\"Listbox.Options\"),t=de(\"Listbox.Options\"),[s,A,v,U]=M(t,e=>[e.listboxState,e.buttonElement,e.optionsElement,e.__demoMode]),w=be(A),c=be(v),O=rt(),[y,I]=Xe(i,d,O!==null?(O&oe.Open)===oe.Open:s===T.Open);Ue(y,A,t.actions.closeListbox);let N=U?!1:R&&s===T.Open;Ve(N,c);let _=U?!1:R&&s===T.Open;Be(_,{allowed:j(()=>[A,v],[A,v])});let H=s!==T.Open,L=Me(H,A)?!1:y,G=y&&s===T.Closed,K=tt(G,a.value),E=C(e=>a.compare(K,e)),z=M(t,e=>{var X;if(o==null||!((X=o==null?void 0:o.to)!=null&&X.includes(\"selection\")))return null;let S=e.options.findIndex(se=>E(se.dataRef.current.value));return S===-1&&(S=0),S}),r=(()=>{if(o==null)return;if(z===null)return{...o,inner:void 0};let e=Array.from(a.listRef.current.values());return{...o,inner:{listRef:{current:e},index:z}}})(),[B,ae]=Qe(r),le=$e(),ie=Q(D,o?B:null,t.actions.setOptionsElement,m),Z=we();Ae(()=>{var S;let e=v;e&&s===T.Open&&e!==((S=ft(e))==null?void 0:S.activeElement)&&(e==null||e.focus({preventScroll:!0}))},[s,v]);let h=C(e=>{var S,X;switch(Z.dispose(),e.key){case f.Space:if(t.state.searchQuery!==\"\")return e.preventDefault(),e.stopPropagation(),t.actions.search(e.key);case f.Enter:if(e.preventDefault(),e.stopPropagation(),t.state.activeOptionIndex!==null){let{dataRef:se}=t.state.options[t.state.activeOptionIndex];t.actions.onChange(se.current.value)}a.mode===k.Single&&(J(()=>t.actions.closeListbox()),(S=t.state.buttonElement)==null||S.focus({preventScroll:!0}));break;case ne(a.orientation,{vertical:f.ArrowDown,horizontal:f.ArrowRight}):return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:P.Next});case ne(a.orientation,{vertical:f.ArrowUp,horizontal:f.ArrowLeft}):return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:P.Previous});case f.Home:case f.PageUp:return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:P.First});case f.End:case f.PageDown:return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:P.Last});case f.Escape:e.preventDefault(),e.stopPropagation(),J(()=>t.actions.closeListbox()),(X=t.state.buttonElement)==null||X.focus({preventScroll:!0});return;case f.Tab:e.preventDefault(),e.stopPropagation(),J(()=>t.actions.closeListbox()),ut(t.state.buttonElement,e.shiftKey?me.Previous:me.Next);break;default:e.key.length===1&&(t.actions.search(e.key),Z.setTimeout(()=>t.actions.clearSearch(),350));break}}),W=M(t,e=>{var S;return(S=e.buttonElement)==null?void 0:S.id}),Pe=V(()=>({open:s===T.Open}),[s]),ge=xe(o?le():{},{id:u,ref:ie,\"aria-activedescendant\":M(t,t.selectors.activeDescendantId),\"aria-multiselectable\":a.mode===k.Multi?!0:void 0,\"aria-labelledby\":W,\"aria-orientation\":a.orientation,onKeyDown:h,role:\"listbox\",tabIndex:s===T.Open?0:void 0,style:{...b.style,...ae,\"--button-width\":Ie(A,!0).width},...We(I)}),ve=q(),Ee=V(()=>a.mode===k.Multi?a:{...a,isSelected:E},[a,E]);return F.createElement(xt,{enabled:p?g.static||y:!1,ownerDocument:w},F.createElement(re.Provider,{value:Ee},ve({ourProps:ge,theirProps:b,slot:Pe,defaultTag:Dt,features:At,visible:L,name:\"Listbox.Options\"})))}let Rt=\"div\";function _t(g,D){let x=ee(),{id:u=`headlessui-listbox-option-${x}`,disabled:l=!1,value:p,...R}=g,i=Te(Le)===!0,b=Y(\"Listbox.Option\"),o=de(\"Listbox.Option\"),d=M(o,n=>o.selectors.isActive(n,u)),m=b.isSelected(p),a=pe(null),t=Ke(a),s=ke({disabled:l,value:p,domRef:a,get textValue(){return t()}}),A=Q(D,a,n=>{n?b.listRef.current.set(u,n):b.listRef.current.delete(u)}),v=M(o,n=>o.selectors.shouldScrollIntoView(n,u));ue(()=>{if(v)return it().requestAnimationFrame(()=>{var n,L;(L=(n=a.current)==null?void 0:n.scrollIntoView)==null||L.call(n,{block:\"nearest\"})})},[v,a]),ue(()=>{if(!i)return o.actions.registerOption(u,s),()=>o.actions.unregisterOption(u)},[s,u,i]);let U=C(n=>{var L;if(l)return n.preventDefault();o.actions.onChange(p),b.mode===k.Single&&(J(()=>o.actions.closeListbox()),(L=o.state.buttonElement)==null||L.focus({preventScroll:!0}))}),w=C(()=>{if(l)return o.actions.goToOption({focus:P.Nothing});o.actions.goToOption({focus:P.Specific,id:u})}),c=ze(),O=C(n=>{c.update(n),!l&&(d||o.actions.goToOption({focus:P.Specific,id:u},Oe.Pointer))}),y=C(n=>{c.wasMoved(n)&&(l||d||o.actions.goToOption({focus:P.Specific,id:u},Oe.Pointer))}),I=C(n=>{c.wasMoved(n)&&(l||d&&o.actions.goToOption({focus:P.Nothing}))}),N=V(()=>({active:d,focus:d,selected:m,disabled:l,selectedOption:m&&i}),[d,m,l,i]),_=i?{}:{id:u,ref:A,role:\"option\",tabIndex:l===!0?void 0:-1,\"aria-disabled\":l===!0?!0:void 0,\"aria-selected\":m,disabled:void 0,onClick:U,onFocus:w,onPointerEnter:O,onMouseEnter:O,onPointerMove:y,onMouseMove:y,onPointerLeave:I,onMouseLeave:I},H=q();return!m&&i?null:H({ourProps:_,theirProps:R,slot:N,defaultTag:Rt,name:\"Listbox.Option\"})}let Ft=ce;function Ct(g,D){let{options:x,placeholder:u,...l}=g,R={ref:Q(D)},i=Y(\"ListboxSelectedOption\"),b=V(()=>({}),[]),o=i.value===void 0||i.value===null||i.mode===k.Multi&&Array.isArray(i.value)&&i.value.length===0,d=q();return F.createElement(Le.Provider,{value:!0},d({ourProps:R,theirProps:{...l,children:F.createElement(F.Fragment,null,u&&o?u:x)},slot:b,defaultTag:Ft,name:\"ListboxSelectedOption\"}))}let Mt=$(vt),wt=$(ht),It=bt,Bt=$(St),kt=$(_t),Ut=$(Ct),wo=Object.assign(Mt,{Button:wt,Label:It,Options:Bt,Option:kt,SelectedOption:Ut});export{wo as Listbox,wt as ListboxButton,It as ListboxLabel,kt as ListboxOption,Bt as ListboxOptions,Ut as ListboxSelectedOption};\n"], "names": [], "mappings": ";;;;;;;;AAAa;AAAkD;AAAqD;AAAoJ;AAAsC;AAAkE;AAAoE;AAAmE;AAAoE;AAAyE;AAAiE;AAAkE;AAAmG;AAAkE;AAA6E;AAAkE;AAAkE;AAAoE;AAA6D;AAAiF;AAA+E;AAAgE;AAA2D;AAA8D;AAAwE;AAA8F;AAA0D;AAAqM;AAA4D;AAA0D;AAAsD;AAAoG;AAAiE;AAA+C;AAAgE;AAA8D;AAA0D;AAAsC;AAAsH;AAAqD;AAA8C;AAAyD;AAA8G;AAAgE;AAAsC;AAA+E;AAA8C;AAA8G;AAA3yG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAA85G,IAAI,KAAG,CAAA,GAAA,oUAAA,CAAA,gBAAE,AAAD,EAAE;AAAM,GAAG,WAAW,GAAC;AAAqB,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAE,AAAD,EAAE;IAAI,IAAG,MAAI,MAAK;QAAC,IAAI,IAAE,IAAI,MAAM,CAAC,CAAC,EAAE,EAAE,8CAA8C,CAAC;QAAE,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG;IAAC;IAAC,OAAO;AAAC;AAAC,IAAI,KAAG,oUAAA,CAAA,WAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAE,AAAD,KAAI,IAAE,CAAA,GAAA,uTAAA,CAAA,cAAE,AAAD,KAAI,EAAC,OAAM,CAAC,EAAC,cAAa,CAAC,EAAC,MAAK,CAAC,EAAC,MAAK,CAAC,EAAC,UAAS,CAAC,EAAC,IAAG,CAAC,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,UAAS,IAAE,KAAG,CAAC,CAAC,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,UAAS,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC;IAAE,MAAM,IAAE,IAAE,eAAa;IAAW,IAAI,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,mUAAA,CAAA,kBAAE,AAAD,EAAE,IAAG,CAAC,IAAE,IAAE,EAAE,GAAC,KAAK,CAAC,EAAC,EAAE,GAAC,CAAA,GAAA,+TAAA,CAAA,kBAAE,AAAD,EAAE,GAAE,GAAE,IAAG,IAAE,CAAA,GAAA,sVAAA,CAAA,oBAAE,AAAD,EAAE;QAAC,IAAG;QAAE,YAAW;IAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAE,AAAD,EAAE;QAAC,QAAO,CAAC;QAAE,MAAK,CAAC;IAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAE,AAAD,EAAE,IAAI,MAAK,IAAE,CAAA,GAAA,mUAAA,CAAA,kBAAE,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,CAAA,GAAA,iTAAA,CAAA,QAAE,AAAD,EAAE,EAAE,IAAI,EAAC;YAAC,CAAC,8UAAA,CAAA,YAAC,CAAC,KAAK,CAAC,EAAC,IAAI,EAAE,IAAI,CAAC,CAAA,IAAG,EAAE,GAAE;YAAI,CAAC,8UAAA,CAAA,YAAC,CAAC,MAAM,CAAC,EAAC,IAAI,EAAE,GAAE;QAAE,IAAG;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,OAAM;YAAE,UAAS;YAAE,SAAQ;YAAE,MAAK,IAAE,8UAAA,CAAA,YAAC,CAAC,KAAK,GAAC,8UAAA,CAAA,YAAC,CAAC,MAAM;YAAC,aAAY;YAAE,UAAS;YAAE,SAAQ;YAAE,YAAW;YAAE,iBAAgB;YAAE,SAAQ;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;IAAE,CAAA,GAAA,2UAAA,CAAA,sBAAE,AAAD,EAAE;QAAK,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,GAAC;IAAC,GAAE;QAAC;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG,EAAE,YAAY,GAAE,IAAE,+TAAA,CAAA,gBAAE,CAAC,GAAG,CAAC,OAAM,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,SAAS,CAAC,KAAK,CAAC,GAAE,IAAG;QAAC;QAAE;KAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG;YAAC,EAAE,aAAa;YAAC,EAAE,cAAc;SAAC;IAAE,CAAA,GAAA,mUAAA,CAAA,kBAAE,AAAD,EAAE,GAAE;QAAC;QAAE;KAAE,EAAC,CAAC,GAAE;QAAK,EAAE,IAAI,CAAC;YAAC,MAAK,8UAAA,CAAA,cAAE,CAAC,YAAY;QAAA,IAAG,CAAA,GAAA,+TAAA,CAAA,qBAAE,AAAD,EAAE,GAAE,+TAAA,CAAA,gBAAE,CAAC,KAAK,KAAG,CAAC,EAAE,cAAc,IAAG,KAAG,QAAM,EAAE,KAAK,EAAE;IAAC;IAAG,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI,8UAAA,CAAA,gBAAC,CAAC,IAAI;YAAC,UAAS;YAAE,SAAQ;YAAE,OAAM;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;KAAE,GAAE,CAAC,GAAE,GAAG,GAAC,CAAA,GAAA,+TAAA,CAAA,YAAE,AAAD,EAAE;QAAC,SAAQ,CAAC;IAAC,IAAG,KAAG;QAAC,KAAI;IAAC,GAAE,KAAG,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE;QAAK,IAAG,MAAI,KAAK,GAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE;IAAE,GAAE;QAAC;QAAE;KAAE,GAAE,IAAE,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD;IAAI,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,IAAG;QAAC,OAAM;QAAE,OAAM;YAAC,SAAQ,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE;QAAA;QAAE,MAAK;YAAC,MAAK,MAAI,8UAAA,CAAA,gBAAC,CAAC,IAAI;YAAC,UAAS;QAAC;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,uTAAA,CAAA,mBAAE,EAAC,MAAK,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,sVAAA,CAAA,iBAAE,CAAC,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAG,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,6TAAA,CAAA,qBAAE,EAAC;QAAC,OAAM,CAAA,GAAA,iTAAA,CAAA,QAAE,AAAD,EAAE,GAAE;YAAC,CAAC,8UAAA,CAAA,gBAAC,CAAC,IAAI,CAAC,EAAC,6TAAA,CAAA,QAAE,CAAC,IAAI;YAAC,CAAC,8UAAA,CAAA,gBAAC,CAAC,MAAM,CAAC,EAAC,6TAAA,CAAA,QAAE,CAAC,MAAM;QAAA;IAAE,GAAE,KAAG,QAAM,KAAG,QAAM,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,6TAAA,CAAA,aAAE,EAAC;QAAC,UAAS;QAAE,MAAK;YAAC,CAAC,EAAE,EAAC;QAAC;QAAE,MAAK;QAAE,SAAQ;IAAE,IAAG,EAAE;QAAC,UAAS;QAAG,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAS;AAAO;AAAC,IAAI,KAAG;AAAS,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAE,AAAD,KAAI,IAAE,CAAA,GAAA,iTAAA,CAAA,gBAAE,AAAD,KAAI,IAAE,EAAE,mBAAkB,IAAE,CAAA,GAAA,sVAAA,CAAA,2BAAE,AAAD,EAAE,mBAAkB,EAAC,IAAG,IAAE,KAAG,CAAC,0BAA0B,EAAE,GAAG,EAAC,UAAS,IAAE,EAAE,QAAQ,IAAE,CAAC,CAAC,EAAC,WAAU,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE,GAAE,CAAA,GAAA,uTAAA,CAAA,uBAAE,AAAD,KAAI,EAAE,OAAO,CAAC,gBAAgB,GAAE,IAAE,CAAA,GAAA,uTAAA,CAAA,4BAAE,AAAD,KAAI,CAAC,GAAE,GAAE,EAAE,GAAC,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG;YAAC,EAAE,YAAY;YAAC,EAAE,aAAa;YAAC,EAAE,cAAc;SAAC,GAAE,IAAE,MAAI,8UAAA,CAAA,gBAAC,CAAC,IAAI;IAAC,CAAA,GAAA,mUAAA,CAAA,kBAAE,AAAD,EAAE,GAAE;QAAC,SAAQ;QAAE,QAAO,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,CAAA;YAAI,IAAG,KAAG,QAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAE,OAAO,mUAAA,CAAA,SAAE,CAAC,MAAM;YAAC,IAAI,IAAE,EAAE,MAAM,CAAC,OAAO,CAAC;YAAwC,OAAO,CAAA,GAAA,+SAAA,CAAA,gBAAgB,AAAD,EAAE,KAAG,mUAAA,CAAA,SAAE,CAAC,MAAM,CAAC,KAAG,KAAG,QAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,IAAE,mUAAA,CAAA,SAAE,CAAC,MAAM,GAAC,mUAAA,CAAA,SAAE,CAAC,KAAK;QAAA,GAAE;YAAC;YAAE;SAAE;QAAE,OAAM,EAAE,OAAO,CAAC,YAAY;QAAC,QAAO,EAAE,OAAO,CAAC,kBAAkB;IAAA;IAAG,IAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,OAAO,EAAE,GAAG;YAAE,KAAK,yTAAA,CAAA,OAAC,CAAC,KAAK;gBAAC,CAAA,GAAA,gTAAA,CAAA,gBAAE,AAAD,EAAE,EAAE,aAAa;gBAAE;YAAM,KAAK,yTAAA,CAAA,OAAC,CAAC,KAAK;YAAC,KAAK,yTAAA,CAAA,OAAC,CAAC,SAAS;gBAAC,EAAE,cAAc,IAAG,EAAE,OAAO,CAAC,WAAW,CAAC;oBAAC,OAAM,EAAE,KAAK,GAAC,wUAAA,CAAA,QAAC,CAAC,OAAO,GAAC,wUAAA,CAAA,QAAC,CAAC,KAAK;gBAAA;gBAAG;YAAM,KAAK,yTAAA,CAAA,OAAC,CAAC,OAAO;gBAAC,EAAE,cAAc,IAAG,EAAE,OAAO,CAAC,WAAW,CAAC;oBAAC,OAAM,EAAE,KAAK,GAAC,wUAAA,CAAA,QAAC,CAAC,OAAO,GAAC,wUAAA,CAAA,QAAC,CAAC,IAAI;gBAAA;gBAAG;QAAK;IAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,OAAO,EAAE,GAAG;YAAE,KAAK,yTAAA,CAAA,OAAC,CAAC,KAAK;gBAAC,EAAE,cAAc;gBAAG;QAAK;IAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI;QAAE,IAAG,EAAE,MAAM,KAAG,GAAE;YAAC,IAAG,CAAA,GAAA,gTAAA,CAAA,2BAAE,AAAD,EAAE,EAAE,aAAa,GAAE,OAAO,EAAE,cAAc;YAAG,EAAE,KAAK,CAAC,YAAY,KAAG,8UAAA,CAAA,gBAAC,CAAC,IAAI,GAAC,CAAC,CAAA,GAAA,2UAAA,CAAA,YAAC,AAAD,EAAE,IAAI,EAAE,OAAO,CAAC,YAAY,KAAI,CAAC,IAAE,EAAE,KAAK,CAAC,aAAa,KAAG,QAAM,EAAE,KAAK,CAAC;gBAAC,eAAc,CAAC;YAAC,EAAE,IAAE,CAAC,EAAE,cAAc,IAAG,EAAE,OAAO,CAAC,WAAW,CAAC;gBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,OAAO;YAAA,EAAE;QAAC;IAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,EAAE,cAAc,KAAI,IAAE,CAAA,GAAA,+TAAA,CAAA,gBAAE,AAAD,EAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,2UAAA,CAAA,iBAAE,AAAD,KAAI,EAAC,gBAAe,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,uTAAA,CAAA,eAAE,AAAD,EAAE;QAAC,WAAU;IAAC,IAAG,EAAC,WAAU,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,iUAAA,CAAA,WAAE,AAAD,EAAE;QAAC,YAAW;IAAC,IAAG,EAAC,SAAQ,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE;QAAC,UAAS;IAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI,8UAAA,CAAA,gBAAC,CAAC,IAAI;YAAC,QAAO,KAAG,MAAI,8UAAA,CAAA,gBAAC,CAAC,IAAI;YAAC,UAAS;YAAE,SAAQ,EAAE,OAAO;YAAC,OAAM,EAAE,KAAK;YAAC,OAAM;YAAE,OAAM;YAAE,WAAU;QAAC,CAAC,GAAE;QAAC;QAAE,EAAE,KAAK;QAAC;QAAE;QAAE;QAAE;QAAE,EAAE,OAAO;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG,EAAE,YAAY,KAAG,8UAAA,CAAA,gBAAC,CAAC,IAAI,GAAE,IAAE,CAAA,GAAA,kTAAA,CAAA,aAAE,AAAD,EAAE,KAAI;QAAC,KAAI;QAAE,IAAG;QAAE,MAAK,CAAA,GAAA,4UAAA,CAAA,uBAAE,AAAD,EAAE,GAAE;QAAG,iBAAgB;QAAU,iBAAgB,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE;QAAC,iBAAgB;QAAE,mBAAkB;QAAE,oBAAmB;QAAE,UAAS,KAAG,KAAK;QAAE,WAAU;QAAE,WAAU;QAAE,SAAQ;QAAE,YAAW;QAAE,eAAc;IAAC,GAAE,GAAE,GAAE;IAAG,OAAO,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD,IAAI;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAgB;AAAE;AAAC,IAAI,KAAG,CAAA,GAAA,oUAAA,CAAA,gBAAE,AAAD,EAAE,CAAC,IAAG,KAAG,OAAM,KAAG,kTAAA,CAAA,iBAAE,CAAC,cAAc,GAAC,kTAAA,CAAA,iBAAE,CAAC,MAAM;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAE,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,2BAA2B,EAAE,GAAG,EAAC,QAAO,CAAC,EAAC,QAAO,IAAE,CAAC,CAAC,EAAC,OAAM,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,uTAAA,CAAA,oBAAE,AAAD,EAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,oUAAA,CAAA,WAAE,AAAD,EAAE;IAAM,KAAG,CAAC,IAAE,CAAC,CAAC;IAAE,IAAI,IAAE,EAAE,oBAAmB,IAAE,CAAA,GAAA,sVAAA,CAAA,2BAAE,AAAD,EAAE,oBAAmB,CAAC,GAAE,GAAE,GAAE,EAAE,GAAC,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG;YAAC,EAAE,YAAY;YAAC,EAAE,aAAa;YAAC,EAAE,cAAc;YAAC,EAAE,UAAU;SAAC,GAAE,IAAE,CAAA,GAAA,wTAAA,CAAA,mBAAE,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,mBAAE,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,6TAAA,CAAA,gBAAE,AAAD,KAAI,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,6TAAA,CAAA,gBAAE,AAAD,EAAE,GAAE,GAAE,MAAI,OAAK,CAAC,IAAE,6TAAA,CAAA,QAAE,CAAC,IAAI,MAAI,6TAAA,CAAA,QAAE,CAAC,IAAI,GAAC,MAAI,8UAAA,CAAA,gBAAC,CAAC,IAAI;IAAE,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE,GAAE,GAAE,EAAE,OAAO,CAAC,YAAY;IAAE,IAAI,IAAE,IAAE,CAAC,IAAE,KAAG,MAAI,8UAAA,CAAA,gBAAC,CAAC,IAAI;IAAC,CAAA,GAAA,iUAAA,CAAA,gBAAE,AAAD,EAAE,GAAE;IAAG,IAAI,IAAE,IAAE,CAAC,IAAE,KAAG,MAAI,8UAAA,CAAA,gBAAC,CAAC,IAAI;IAAC,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE,GAAE;QAAC,SAAQ,CAAA,GAAA,oUAAA,CAAA,cAAC,AAAD,EAAE,IAAI;gBAAC;gBAAE;aAAE,EAAC;YAAC;YAAE;SAAE;IAAC;IAAG,IAAI,IAAE,MAAI,8UAAA,CAAA,gBAAC,CAAC,IAAI,EAAC,IAAE,CAAA,GAAA,yUAAA,CAAA,oBAAE,AAAD,EAAE,GAAE,KAAG,CAAC,IAAE,GAAE,IAAE,KAAG,MAAI,8UAAA,CAAA,gBAAC,CAAC,MAAM,EAAC,IAAE,CAAA,GAAA,qTAAA,CAAA,gBAAE,AAAD,EAAE,GAAE,EAAE,KAAK,GAAE,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA,IAAG,EAAE,OAAO,CAAC,GAAE,KAAI,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA;QAAI,IAAI;QAAE,IAAG,KAAG,QAAM,CAAC,CAAC,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE,KAAG,QAAM,EAAE,QAAQ,CAAC,YAAY,GAAE,OAAO;QAAK,IAAI,IAAE,EAAE,OAAO,CAAC,SAAS,CAAC,CAAA,KAAI,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK;QAAG,OAAO,MAAI,CAAC,KAAG,CAAC,IAAE,CAAC,GAAE;IAAC,IAAG,IAAE,CAAC;QAAK,IAAG,KAAG,MAAK;QAAO,IAAG,MAAI,MAAK,OAAM;YAAC,GAAG,CAAC;YAAC,OAAM,KAAK;QAAC;QAAE,IAAI,IAAE,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM;QAAI,OAAM;YAAC,GAAG,CAAC;YAAC,OAAM;gBAAC,SAAQ;oBAAC,SAAQ;gBAAC;gBAAE,OAAM;YAAC;QAAC;IAAC,CAAC,KAAI,CAAC,GAAE,GAAG,GAAC,CAAA,GAAA,uTAAA,CAAA,mBAAE,AAAD,EAAE,IAAG,KAAG,CAAA,GAAA,uTAAA,CAAA,wBAAE,AAAD,KAAI,KAAG,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE,GAAE,IAAE,IAAE,MAAK,EAAE,OAAO,CAAC,iBAAiB,EAAC,IAAG,IAAE,CAAA,GAAA,8TAAA,CAAA,iBAAE,AAAD;IAAI,CAAA,GAAA,oUAAA,CAAA,YAAE,AAAD,EAAE;QAAK,IAAI;QAAE,IAAI,IAAE;QAAE,KAAG,MAAI,8UAAA,CAAA,gBAAC,CAAC,IAAI,IAAE,MAAI,CAAC,CAAC,IAAE,CAAA,GAAA,iTAAA,CAAA,mBAAE,AAAD,EAAE,EAAE,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,KAAG,CAAC,KAAG,QAAM,EAAE,KAAK,CAAC;YAAC,eAAc,CAAC;QAAC,EAAE;IAAC,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI,GAAE;QAAE,OAAO,EAAE,OAAO,IAAG,EAAE,GAAG;YAAE,KAAK,yTAAA,CAAA,OAAC,CAAC,KAAK;gBAAC,IAAG,EAAE,KAAK,CAAC,WAAW,KAAG,IAAG,OAAO,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG;YAAE,KAAK,yTAAA,CAAA,OAAC,CAAC,KAAK;gBAAC,IAAG,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,KAAK,CAAC,iBAAiB,KAAG,MAAK;oBAAC,IAAG,EAAC,SAAQ,EAAE,EAAC,GAAC,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,iBAAiB,CAAC;oBAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,KAAK;gBAAC;gBAAC,EAAE,IAAI,KAAG,8UAAA,CAAA,YAAC,CAAC,MAAM,IAAE,CAAC,CAAA,GAAA,2UAAA,CAAA,YAAC,AAAD,EAAE,IAAI,EAAE,OAAO,CAAC,YAAY,KAAI,CAAC,IAAE,EAAE,KAAK,CAAC,aAAa,KAAG,QAAM,EAAE,KAAK,CAAC;oBAAC,eAAc,CAAC;gBAAC,EAAE;gBAAE;YAAM,KAAK,CAAA,GAAA,iTAAA,CAAA,QAAE,AAAD,EAAE,EAAE,WAAW,EAAC;gBAAC,UAAS,yTAAA,CAAA,OAAC,CAAC,SAAS;gBAAC,YAAW,yTAAA,CAAA,OAAC,CAAC,UAAU;YAAA;gBAAG,OAAO,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,OAAO,CAAC,UAAU,CAAC;oBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,IAAI;gBAAA;YAAG,KAAK,CAAA,GAAA,iTAAA,CAAA,QAAE,AAAD,EAAE,EAAE,WAAW,EAAC;gBAAC,UAAS,yTAAA,CAAA,OAAC,CAAC,OAAO;gBAAC,YAAW,yTAAA,CAAA,OAAC,CAAC,SAAS;YAAA;gBAAG,OAAO,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,OAAO,CAAC,UAAU,CAAC;oBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,QAAQ;gBAAA;YAAG,KAAK,yTAAA,CAAA,OAAC,CAAC,IAAI;YAAC,KAAK,yTAAA,CAAA,OAAC,CAAC,MAAM;gBAAC,OAAO,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,OAAO,CAAC,UAAU,CAAC;oBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,KAAK;gBAAA;YAAG,KAAK,yTAAA,CAAA,OAAC,CAAC,GAAG;YAAC,KAAK,yTAAA,CAAA,OAAC,CAAC,QAAQ;gBAAC,OAAO,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE,OAAO,CAAC,UAAU,CAAC;oBAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,IAAI;gBAAA;YAAG,KAAK,yTAAA,CAAA,OAAC,CAAC,MAAM;gBAAC,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,CAAA,GAAA,2UAAA,CAAA,YAAC,AAAD,EAAE,IAAI,EAAE,OAAO,CAAC,YAAY,KAAI,CAAC,IAAE,EAAE,KAAK,CAAC,aAAa,KAAG,QAAM,EAAE,KAAK,CAAC;oBAAC,eAAc,CAAC;gBAAC;gBAAG;YAAO,KAAK,yTAAA,CAAA,OAAC,CAAC,GAAG;gBAAC,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,CAAA,GAAA,2UAAA,CAAA,YAAC,AAAD,EAAE,IAAI,EAAE,OAAO,CAAC,YAAY,KAAI,CAAA,GAAA,+TAAA,CAAA,YAAE,AAAD,EAAE,EAAE,KAAK,CAAC,aAAa,EAAC,EAAE,QAAQ,GAAC,+TAAA,CAAA,QAAE,CAAC,QAAQ,GAAC,+TAAA,CAAA,QAAE,CAAC,IAAI;gBAAE;YAAM;gBAAQ,EAAE,GAAG,CAAC,MAAM,KAAG,KAAG,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,GAAE,EAAE,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,WAAW,IAAG,IAAI;gBAAE;QAAK;IAAC,IAAG,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA;QAAI,IAAI;QAAE,OAAM,CAAC,IAAE,EAAE,aAAa,KAAG,OAAK,KAAK,IAAE,EAAE,EAAE;IAAA,IAAG,KAAG,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI,8UAAA,CAAA,gBAAC,CAAC,IAAI;QAAA,CAAC,GAAE;QAAC;KAAE,GAAE,KAAG,CAAA,GAAA,kTAAA,CAAA,aAAE,AAAD,EAAE,IAAE,OAAK,CAAC,GAAE;QAAC,IAAG;QAAE,KAAI;QAAG,yBAAwB,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,EAAE,SAAS,CAAC,kBAAkB;QAAE,wBAAuB,EAAE,IAAI,KAAG,8UAAA,CAAA,YAAC,CAAC,KAAK,GAAC,CAAC,IAAE,KAAK;QAAE,mBAAkB;QAAE,oBAAmB,EAAE,WAAW;QAAC,WAAU;QAAE,MAAK;QAAU,UAAS,MAAI,8UAAA,CAAA,gBAAC,CAAC,IAAI,GAAC,IAAE,KAAK;QAAE,OAAM;YAAC,GAAG,EAAE,KAAK;YAAC,GAAG,EAAE;YAAC,kBAAiB,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE,GAAE,CAAC,GAAG,KAAK;QAAA;QAAE,GAAG,CAAA,GAAA,6TAAA,CAAA,2BAAE,AAAD,EAAE,EAAE;IAAA,IAAG,KAAG,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD,KAAI,KAAG,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,EAAE,IAAI,KAAG,8UAAA,CAAA,YAAC,CAAC,KAAK,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,YAAW;QAAC,GAAE;QAAC;QAAE;KAAE;IAAE,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,iUAAA,CAAA,SAAE,EAAC;QAAC,SAAQ,IAAE,EAAE,MAAM,IAAE,IAAE,CAAC;QAAE,eAAc;IAAC,GAAE,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAG,QAAQ,EAAC;QAAC,OAAM;IAAE,GAAE,GAAG;QAAC,UAAS;QAAG,YAAW;QAAE,MAAK;QAAG,YAAW;QAAG,UAAS;QAAG,SAAQ;QAAE,MAAK;IAAiB;AAAI;AAAC,IAAI,KAAG;AAAM,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,oUAAA,CAAA,QAAE,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,0BAA0B,EAAE,GAAG,EAAC,UAAS,IAAE,CAAC,CAAC,EAAC,OAAM,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,oUAAA,CAAA,aAAE,AAAD,EAAE,QAAM,CAAC,GAAE,IAAE,EAAE,mBAAkB,IAAE,CAAA,GAAA,sVAAA,CAAA,2BAAE,AAAD,EAAE,mBAAkB,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG,EAAE,SAAS,CAAC,QAAQ,CAAC,GAAE,KAAI,IAAE,EAAE,UAAU,CAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,SAAE,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,gUAAA,CAAA,eAAE,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,kUAAA,CAAA,iBAAE,AAAD,EAAE;QAAC,UAAS;QAAE,OAAM;QAAE,QAAO;QAAE,IAAI,aAAW;YAAC,OAAO;QAAG;IAAC,IAAG,IAAE,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE,GAAE,GAAE,CAAA;QAAI,IAAE,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAE,KAAG,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;IAAE,IAAG,IAAE,CAAA,GAAA,gTAAA,CAAA,WAAC,AAAD,EAAE,GAAE,CAAA,IAAG,EAAE,SAAS,CAAC,oBAAoB,CAAC,GAAE;IAAI,CAAA,GAAA,2UAAA,CAAA,sBAAE,AAAD,EAAE;QAAK,IAAG,GAAE,OAAO,CAAA,GAAA,uTAAA,CAAA,cAAE,AAAD,IAAI,qBAAqB,CAAC;YAAK,IAAI,GAAE;YAAE,CAAC,IAAE,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,cAAc,KAAG,QAAM,EAAE,IAAI,CAAC,GAAE;gBAAC,OAAM;YAAS;QAAE;IAAE,GAAE;QAAC;QAAE;KAAE,GAAE,CAAA,GAAA,2UAAA,CAAA,sBAAE,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE,OAAO,EAAE,OAAO,CAAC,cAAc,CAAC,GAAE,IAAG,IAAI,EAAE,OAAO,CAAC,gBAAgB,CAAC;IAAE,GAAE;QAAC;QAAE;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI;QAAE,IAAG,GAAE,OAAO,EAAE,cAAc;QAAG,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAG,EAAE,IAAI,KAAG,8UAAA,CAAA,YAAC,CAAC,MAAM,IAAE,CAAC,CAAA,GAAA,2UAAA,CAAA,YAAC,AAAD,EAAE,IAAI,EAAE,OAAO,CAAC,YAAY,KAAI,CAAC,IAAE,EAAE,KAAK,CAAC,aAAa,KAAG,QAAM,EAAE,KAAK,CAAC;YAAC,eAAc,CAAC;QAAC,EAAE;IAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE;QAAK,IAAG,GAAE,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC;YAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,OAAO;QAAA;QAAG,EAAE,OAAO,CAAC,UAAU,CAAC;YAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,QAAQ;YAAC,IAAG;QAAC;IAAE,IAAG,IAAE,CAAA,GAAA,qUAAA,CAAA,oBAAE,AAAD,KAAI,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,EAAE,MAAM,CAAC,IAAG,CAAC,KAAG,CAAC,KAAG,EAAE,OAAO,CAAC,UAAU,CAAC;YAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,QAAQ;YAAC,IAAG;QAAC,GAAE,8UAAA,CAAA,oBAAE,CAAC,OAAO,CAAC;IAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,EAAE,QAAQ,CAAC,MAAI,CAAC,KAAG,KAAG,EAAE,OAAO,CAAC,UAAU,CAAC;YAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,QAAQ;YAAC,IAAG;QAAC,GAAE,8UAAA,CAAA,oBAAE,CAAC,OAAO,CAAC;IAAC,IAAG,IAAE,CAAA,GAAA,wTAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,EAAE,QAAQ,CAAC,MAAI,CAAC,KAAG,KAAG,EAAE,OAAO,CAAC,UAAU,CAAC;YAAC,OAAM,wUAAA,CAAA,QAAC,CAAC,OAAO;QAAA,EAAE;IAAC,IAAG,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,QAAO;YAAE,OAAM;YAAE,UAAS;YAAE,UAAS;YAAE,gBAAe,KAAG;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;KAAE,GAAE,IAAE,IAAE,CAAC,IAAE;QAAC,IAAG;QAAE,KAAI;QAAE,MAAK;QAAS,UAAS,MAAI,CAAC,IAAE,KAAK,IAAE,CAAC;QAAE,iBAAgB,MAAI,CAAC,IAAE,CAAC,IAAE,KAAK;QAAE,iBAAgB;QAAE,UAAS,KAAK;QAAE,SAAQ;QAAE,SAAQ;QAAE,gBAAe;QAAE,cAAa;QAAE,eAAc;QAAE,aAAY;QAAE,gBAAe;QAAE,cAAa;IAAC,GAAE,IAAE,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD;IAAI,OAAM,CAAC,KAAG,IAAE,OAAK,EAAE;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAgB;AAAE;AAAC,IAAI,KAAG,oUAAA,CAAA,WAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,SAAQ,CAAC,EAAC,aAAY,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE;QAAC,KAAI,CAAA,GAAA,+TAAA,CAAA,cAAC,AAAD,EAAE;IAAE,GAAE,IAAE,EAAE,0BAAyB,IAAE,CAAA,GAAA,oUAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,CAAC,CAAC,GAAE,EAAE,GAAE,IAAE,EAAE,KAAK,KAAG,KAAK,KAAG,EAAE,KAAK,KAAG,QAAM,EAAE,IAAI,KAAG,8UAAA,CAAA,YAAC,CAAC,KAAK,IAAE,MAAM,OAAO,CAAC,EAAE,KAAK,KAAG,EAAE,KAAK,CAAC,MAAM,KAAG,GAAE,IAAE,CAAA,GAAA,kTAAA,CAAA,YAAC,AAAD;IAAI,OAAO,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,GAAG,QAAQ,EAAC;QAAC,OAAM,CAAC;IAAC,GAAE,EAAE;QAAC,UAAS;QAAE,YAAW;YAAC,GAAG,CAAC;YAAC,UAAS,oUAAA,CAAA,UAAC,CAAC,aAAa,CAAC,oUAAA,CAAA,UAAC,CAAC,QAAQ,EAAC,MAAK,KAAG,IAAE,IAAE;QAAE;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAuB;AAAG;AAAC,IAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,+TAAA,CAAA,QAAE,EAAC,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,kTAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,OAAO,MAAM,CAAC,IAAG;IAAC,QAAO;IAAG,OAAM;IAAG,SAAQ;IAAG,QAAO;IAAG,gBAAe;AAAE", "ignoreList": [0], "debugId": null}}]}