{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/loading.tsx"], "sourcesContent": ["import cn from 'classnames'\nimport Spinner from './spinner'\n\nexport default function Loading({ className }: { className?: string }) {\n  return (\n    <div className={cn('flex h-screen items-center justify-center', className)}>\n      <div className='flex flex-col items-center gap-3'>\n        <Spinner className='!h-[40px] !w-[40px]' />\n        <div className='text-gray-500'>加载中...</div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS,QAAQ,EAAE,SAAS,EAA0B;IACnE,qBACE,oWAAC;QAAI,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAE,AAAD,EAAE,6CAA6C;kBAC9D,cAAA,oWAAC;YAAI,WAAU;;8BACb,oWAAC,iJAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,oWAAC;oBAAI,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAIvC;KATwB", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/pdf-preview.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport { createPortal } from 'react-dom'\nimport 'react-pdf-highlighter/dist/style.css'\nimport { Pdf<PERSON><PERSON><PERSON><PERSON>, PdfLoader } from 'react-pdf-highlighter'\nimport { t } from 'i18next'\nimport { Ri<PERSON>loseLine, RiZoomInLine, RiZoomOutLine } from '@remixicon/react'\nimport React, { useState } from 'react'\nimport { useHotkeys } from 'react-hotkeys-hook'\nimport Loading from '@/components/base/loading'\nimport useBreakpoints, { MediaType } from '@/hooks/use-breakpoints'\nimport Tooltip from '@/components/base/tooltip'\nimport { noop } from 'lodash-es'\n\ntype PdfPreviewProps = {\n  url: string\n  onCancel: () => void\n}\n\nconst PdfPreview: FC<PdfPreviewProps> = ({\n  url,\n  onCancel,\n}) => {\n  const media = useBreakpoints()\n  const [scale, setScale] = useState(1)\n  const [position, setPosition] = useState({ x: 0, y: 0 })\n  const isMobile = media === MediaType.mobile\n\n  const zoomIn = () => {\n    setScale(prevScale => Math.min(prevScale * 1.2, 15))\n    setPosition({ x: position.x - 50, y: position.y - 50 })\n  }\n\n  const zoomOut = () => {\n    setScale((prevScale) => {\n      const newScale = Math.max(prevScale / 1.2, 0.5)\n      if (newScale === 1)\n        setPosition({ x: 0, y: 0 })\n      else\n        setPosition({ x: position.x + 50, y: position.y + 50 })\n\n      return newScale\n    })\n  }\n\n  useHotkeys('esc', onCancel)\n  useHotkeys('up', zoomIn)\n  useHotkeys('down', zoomOut)\n\n  return createPortal(\n    <div\n      className={`fixed inset-0 z-[1000] flex items-center justify-center bg-black/80 ${!isMobile && 'p-8'}`}\n      onClick={e => e.stopPropagation()}\n      tabIndex={-1}\n    >\n      <div\n        className='h-[95vh] max-h-full w-[100vw] max-w-full overflow-hidden'\n        style={{ transform: `scale(${scale})`, transformOrigin: 'center', scrollbarWidth: 'none', msOverflowStyle: 'none' }}\n      >\n        <PdfLoader\n          workerSrc='/pdf.worker.min.mjs'\n          url={url}\n          beforeLoad={<div className='flex h-64 items-center justify-center'><Loading type='app' /></div>}\n        >\n          {(pdfDocument) => {\n            return (\n              <PdfHighlighter\n                pdfDocument={pdfDocument}\n                enableAreaSelection={event => event.altKey}\n                scrollRef={noop}\n                onScrollChange={noop}\n                onSelectionFinished={() => null}\n                highlightTransform={() => { return <div/> }}\n                highlights={[]}\n              />\n            )\n          }}\n        </PdfLoader>\n      </div>\n      <Tooltip popupContent={t('common.operation.zoomOut')}>\n        <div className='absolute right-24 top-6 flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg'\n          onClick={zoomOut}>\n          <RiZoomOutLine className='h-4 w-4 text-gray-500'/>\n        </div>\n      </Tooltip>\n      <Tooltip popupContent={t('common.operation.zoomIn')}>\n        <div className='absolute right-16 top-6 flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg'\n          onClick={zoomIn}>\n          <RiZoomInLine className='h-4 w-4 text-gray-500'/>\n        </div>\n      </Tooltip>\n      <Tooltip popupContent={t('common.operation.cancel')}>\n        <div\n          className='absolute right-6 top-6 flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg bg-white/8 backdrop-blur-[2px]'\n          onClick={onCancel}>\n          <RiCloseLine className='h-4 w-4 text-gray-500'/>\n        </div>\n      </Tooltip>\n    </div>,\n    document.body,\n  )\n}\n\nexport default PdfPreview\n"], "names": [], "mappings": ";;;;AACA;;;;;;;;;;;;;;;;AAIA;AACA;;;;;;AAEA;;;;;;AAEA;AACA;;;;;;;;;;;;;;AAOA,MAAM,aAAkC,CAAC,EACvC,GAAG,EACH,QAAQ,EACT;;IACC,MAAM,QAAQ;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACtD,MAAM,WAAW,UAAU,UAAU,MAAM;IAE3C,MAAM,SAAS;QACb,SAAS,CAAA,YAAa,KAAK,GAAG,CAAC,YAAY,KAAK;QAChD,YAAY;YAAE,GAAG,SAAS,CAAC,GAAG;YAAI,GAAG,SAAS,CAAC,GAAG;QAAG;IACvD;IAEA,MAAM,UAAU;QACd,SAAS,CAAC;YACR,MAAM,WAAW,KAAK,GAAG,CAAC,YAAY,KAAK;YAC3C,IAAI,aAAa,GACf,YAAY;gBAAE,GAAG;gBAAG,GAAG;YAAE;iBAEzB,YAAY;gBAAE,GAAG,SAAS,CAAC,GAAG;gBAAI,GAAG,SAAS,CAAC,GAAG;YAAG;YAEvD,OAAO;QACT;IACF;IAEA,WAAW,OAAO;IAClB,WAAW,MAAM;IACjB,WAAW,QAAQ;IAEnB,qBAAO,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,gBAChB,oWAAC;QACC,WAAW,CAAC,oEAAoE,EAAE,CAAC,YAAY,OAAO;QACtG,SAAS,CAAA,IAAK,EAAE,eAAe;QAC/B,UAAU,CAAC;;0BAEX,oWAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;oBAAE,iBAAiB;oBAAU,gBAAgB;oBAAQ,iBAAiB;gBAAO;0BAElH,cAAA,oWAAC;oBACC,WAAU;oBACV,KAAK;oBACL,0BAAY,oWAAC;wBAAI,WAAU;kCAAwC,cAAA,oWAAC,wIAAA,CAAA,UAAO;4BAAC,MAAK;;;;;;;;;;;8BAEhF,CAAC;wBACA,qBACE,oWAAC;4BACC,aAAa;4BACb,qBAAqB,CAAA,QAAS,MAAM,MAAM;4BAC1C,WAAW,qOAAA,CAAA,OAAI;4BACf,gBAAgB,qOAAA,CAAA,OAAI;4BACpB,qBAAqB,IAAM;4BAC3B,oBAAoB;gCAAQ,qBAAO,oWAAC;;;;;4BAAM;4BAC1C,YAAY,EAAE;;;;;;oBAGpB;;;;;;;;;;;0BAGJ,oWAAC,iJAAA,CAAA,UAAO;gBAAC,cAAc,EAAE;0BACvB,cAAA,oWAAC;oBAAI,WAAU;oBACb,SAAS;8BACT,cAAA,oWAAC,wOAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAG7B,oWAAC,iJAAA,CAAA,UAAO;gBAAC,cAAc,EAAE;0BACvB,cAAA,oWAAC;oBAAI,WAAU;oBACb,SAAS;8BACT,cAAA,oWAAC,wOAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAG5B,oWAAC,iJAAA,CAAA,UAAO;gBAAC,cAAc,EAAE;0BACvB,cAAA,oWAAC;oBACC,WAAU;oBACV,SAAS;8BACT,cAAA,oWAAC,wOAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;cAI7B,SAAS,IAAI;AAEjB;GAlFM;;QAIU;QAsBd;QACA;QACA;;;KA5BI;uCAoFS", "debugId": null}}]}