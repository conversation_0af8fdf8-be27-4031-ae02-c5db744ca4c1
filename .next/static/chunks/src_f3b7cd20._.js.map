{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/context.tsx"], "sourcesContent": ["'use client'\n\nimport type { RefObject } from 'react'\nimport { createContext, useContext } from 'use-context-selector'\nimport type {\n  Callback,\n  ChatConfig,\n  ChatItemInTree,\n  Feedback,\n} from '../types'\nimport type { ThemeBuilder } from '../embedded-chatbot/theme/theme-context'\nimport type {\n  AppConversationData,\n  AppData,\n  AppMeta,\n  ConversationItem,\n  EmbedSource,\n} from '@/models/share'\nimport { noop } from 'lodash-es'\n\nexport type ChatWithHistoryContextValue = {\n  appInfoError?: any\n  appInfoLoading?: boolean\n  appMeta?: AppMeta\n  appData?: AppData\n  appParams?: ChatConfig\n  appChatListDataLoading?: boolean\n  currentConversationId: string\n  currentConversationItem?: ConversationItem\n  appPrevChatTree: ChatItemInTree[]\n  pinnedConversationList: AppConversationData['data']\n  conversationList: AppConversationData['data']\n  newConversationInputs: Record<string, any>\n  newConversationInputsRef: RefObject<Record<string, any>>\n  handleNewConversationInputsChange: (v: Record<string, any>) => void\n  inputsForms: any[]\n  handleNewConversation: () => void\n  handleStartChat: (callback?: any) => void\n  handleChangeConversation: (conversationId: string) => void\n  handlePinConversation: (conversationId: string) => void\n  handleUnpinConversation: (conversationId: string) => void\n  handleDeleteConversation: (conversationId: string, callback: Callback) => void\n  conversationRenaming: boolean\n  handleRenameConversation: (conversationId: string, newName: string, callback: Callback) => void\n  handleNewConversationCompleted: (newConversationId: string) => void\n  chatShouldReloadKey: string\n  isMobile: boolean\n  isInstalledApp: boolean\n  appId?: string\n  handleFeedback: (messageId: string, feedback: Feedback) => void\n  currentChatInstanceRef: RefObject<{ handleStop: () => void }>\n  themeBuilder?: ThemeBuilder\n  sidebarCollapseState?: boolean\n  handleSidebarCollapse: (state: boolean) => void\n  clearChatList?: boolean\n  setClearChatList: (state: boolean) => void\n  isResponding?: boolean\n  setIsResponding: (state: boolean) => void,\n  currentConversationInputs: Record<string, any> | null,\n  setCurrentConversationInputs: (v: Record<string, any>) => void,\n  isFold?: boolean\n  setIsFold?: (bool: boolean) => void\n  embedSource?: EmbedSource\n  refreshRenderKey?: number\n  larkInfo?: any\n  handleClearAllConversations?: (callback: Callback) => void\n  rightSideInfo?: null | any// 右侧面板信息\n  setRightSideInfo?: (info: any) => void // 右侧面板信息\n}\n\nexport const ChatWithHistoryContext = createContext<ChatWithHistoryContextValue>({\n  currentConversationId: '',\n  appPrevChatTree: [],\n  pinnedConversationList: [],\n  conversationList: [],\n  newConversationInputs: {},\n  newConversationInputsRef: { current: {} },\n  handleNewConversationInputsChange: noop,\n  inputsForms: [],\n  handleNewConversation: noop,\n  handleStartChat: noop,\n  handleChangeConversation: noop,\n  handlePinConversation: noop,\n  handleUnpinConversation: noop,\n  handleDeleteConversation: noop,\n  conversationRenaming: false,\n  handleRenameConversation: noop,\n  handleNewConversationCompleted: noop,\n  chatShouldReloadKey: '',\n  isMobile: false,\n  isInstalledApp: false,\n  handleFeedback: noop,\n  currentChatInstanceRef: { current: { handleStop: noop } },\n  sidebarCollapseState: false,\n  handleSidebarCollapse: noop,\n  clearChatList: false,\n  setClearChatList: noop,\n  isResponding: false,\n  setIsResponding: noop,\n  currentConversationInputs: {},\n  setCurrentConversationInputs: noop,\n  isFold: false,\n  setIsFold: noop,\n  embedSource: '',\n  refreshRenderKey: -1,\n  larkInfo: {},\n  handleClearAllConversations: noop,\n  rightSideInfo: null,\n  setRightSideInfo: noop,\n})\nexport const useChatWithHistoryContext = () => useContext(ChatWithHistoryContext)\n"], "names": [], "mappings": ";;;;AAGA;AAeA;;AAlBA;;;AAsEO,MAAM,yBAAyB,CAAA,GAAA,qRAAA,CAAA,gBAAa,AAAD,EAA+B;IAC/E,uBAAuB;IACvB,iBAAiB,EAAE;IACnB,wBAAwB,EAAE;IAC1B,kBAAkB,EAAE;IACpB,uBAAuB,CAAC;IACxB,0BAA0B;QAAE,SAAS,CAAC;IAAE;IACxC,mCAAmC,qOAAA,CAAA,OAAI;IACvC,aAAa,EAAE;IACf,uBAAuB,qOAAA,CAAA,OAAI;IAC3B,iBAAiB,qOAAA,CAAA,OAAI;IACrB,0BAA0B,qOAAA,CAAA,OAAI;IAC9B,uBAAuB,qOAAA,CAAA,OAAI;IAC3B,yBAAyB,qOAAA,CAAA,OAAI;IAC7B,0BAA0B,qOAAA,CAAA,OAAI;IAC9B,sBAAsB;IACtB,0BAA0B,qOAAA,CAAA,OAAI;IAC9B,gCAAgC,qOAAA,CAAA,OAAI;IACpC,qBAAqB;IACrB,UAAU;IACV,gBAAgB;IAChB,gBAAgB,qOAAA,CAAA,OAAI;IACpB,wBAAwB;QAAE,SAAS;YAAE,YAAY,qOAAA,CAAA,OAAI;QAAC;IAAE;IACxD,sBAAsB;IACtB,uBAAuB,qOAAA,CAAA,OAAI;IAC3B,eAAe;IACf,kBAAkB,qOAAA,CAAA,OAAI;IACtB,cAAc;IACd,iBAAiB,qOAAA,CAAA,OAAI;IACrB,2BAA2B,CAAC;IAC5B,8BAA8B,qOAAA,CAAA,OAAI;IAClC,QAAQ;IACR,WAAW,qOAAA,CAAA,OAAI;IACf,aAAa;IACb,kBAAkB,CAAC;IACnB,UAAU,CAAC;IACX,6BAA6B,qOAAA,CAAA,OAAI;IACjC,eAAe;IACf,kBAAkB,qOAAA,CAAA,OAAI;AACxB;AACO,MAAM,4BAA4B;;IAAM,OAAA,CAAA,GAAA,qRAAA,CAAA,aAAU,AAAD,EAAE;AAAsB;GAAnE", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/utils/classnames.ts"], "sourcesContent": ["import { twMerge } from 'tailwind-merge'\nimport cn from 'classnames'\n\nconst classNames = (...cls: cn.ArgumentArray) => {\n  return twMerge(cn(cls))\n}\n\nexport default classNames\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,aAAa,CAAC,GAAG;IACrB,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,6LAAA,CAAA,UAAE,AAAD,EAAE;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/input/index.tsx"], "sourcesContent": ["import type { CSSProperties } from 'react'\nimport React from 'react'\nimport { useTranslation } from 'react-i18next'\nimport { RiCloseCircleFill, RiErrorWarningLine, RiSearchLine } from '@remixicon/react'\nimport { type VariantProps, cva } from 'class-variance-authority'\nimport cn from '@/utils/classnames'\nimport { noop } from 'lodash-es'\n\nexport const inputVariants = cva(\n  '',\n  {\n    variants: {\n      size: {\n        regular: 'px-3 radius-md system-sm-regular',\n        large: 'px-4 radius-lg system-md-regular',\n      },\n    },\n    defaultVariants: {\n      size: 'regular',\n    },\n  },\n)\n\nexport type InputProps = {\n  showLeftIcon?: boolean\n  showClearIcon?: boolean\n  onClear?: () => void\n  disabled?: boolean\n  destructive?: boolean\n  wrapperClassName?: string\n  styleCss?: CSSProperties\n  unit?: string\n} & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> & VariantProps<typeof inputVariants>\n\nconst Input = ({\n  size,\n  disabled,\n  destructive,\n  showLeftIcon,\n  showClearIcon,\n  onClear,\n  wrapperClassName,\n  className,\n  styleCss,\n  value,\n  placeholder,\n  onChange = noop,\n  unit,\n  ...props\n}: InputProps) => {\n  const { t } = useTranslation()\n  return (\n    <div className={cn('relative w-full', wrapperClassName)}>\n      {showLeftIcon && <RiSearchLine className={cn('absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-components-input-text-placeholder')} />}\n      <input\n        style={styleCss}\n        className={cn(\n          'w-full appearance-none border border-transparent bg-components-input-bg-normal py-[7px] text-components-input-text-filled caret-primary-600 outline-none placeholder:text-components-input-text-placeholder hover:border-components-input-border-hover hover:bg-components-input-bg-hover focus:border-components-input-border-active focus:bg-components-input-bg-active focus:shadow-xs',\n          inputVariants({ size }),\n          showLeftIcon && 'pl-[26px]',\n          showLeftIcon && size === 'large' && 'pl-7',\n          showClearIcon && value && 'pr-[26px]',\n          showClearIcon && value && size === 'large' && 'pr-7',\n          destructive && 'pr-[26px]',\n          destructive && size === 'large' && 'pr-7',\n          disabled && 'cursor-not-allowed border-transparent bg-components-input-bg-disabled text-components-input-text-filled-disabled hover:border-transparent hover:bg-components-input-bg-disabled',\n          destructive && 'border-components-input-border-destructive bg-components-input-bg-destructive text-components-input-text-filled hover:border-components-input-border-destructive hover:bg-components-input-bg-destructive focus:border-components-input-border-destructive focus:bg-components-input-bg-destructive',\n          className,\n        )}\n        placeholder={placeholder ?? (showLeftIcon\n          ? (t('common.operation.search') || '')\n          : (t('common.placeholder.input') || ''))}\n        value={value}\n        onChange={onChange}\n        disabled={disabled}\n        {...props}\n      />\n      {showClearIcon && value && !disabled && !destructive && (\n        <div className={cn('group absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer p-[1px]')} onClick={onClear}>\n          <RiCloseCircleFill className='h-3.5 w-3.5 cursor-pointer text-text-quaternary group-hover:text-text-tertiary' />\n        </div>\n      )}\n      {destructive && (\n        <RiErrorWarningLine className='absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 text-text-destructive-secondary' />\n      )}\n      {\n        unit && (\n          <div className='system-sm-regular absolute right-2 top-1/2 -translate-y-1/2 text-text-tertiary'>\n            {unit}\n          </div>\n        )\n      }\n    </div>\n  )\n}\n\nexport default Input\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAEO,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EAC7B,IACA;IACE,UAAU;QACR,MAAM;YACJ,SAAS;YACT,OAAO;QACT;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAcF,MAAM,QAAQ,CAAC,EACb,IAAI,EACJ,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,aAAa,EACb,OAAO,EACP,gBAAgB,EAChB,SAAS,EACT,QAAQ,EACR,KAAK,EACL,WAAW,EACX,WAAW,qOAAA,CAAA,OAAI,EACf,IAAI,EACJ,GAAG,OACQ;;IACX,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,qBACE,oWAAC;QAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,mBAAmB;;YACnC,8BAAgB,oWAAC,wOAAA,CAAA,eAAY;gBAAC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE;;;;;;0BAC7C,oWAAC;gBACC,OAAO;gBACP,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EACV,6XACA,cAAc;oBAAE;gBAAK,IACrB,gBAAgB,aAChB,gBAAgB,SAAS,WAAW,QACpC,iBAAiB,SAAS,aAC1B,iBAAiB,SAAS,SAAS,WAAW,QAC9C,eAAe,aACf,eAAe,SAAS,WAAW,QACnC,YAAY,mLACZ,eAAe,uSACf;gBAEF,aAAa,eAAe,CAAC,eACxB,EAAE,8BAA8B,KAChC,EAAE,+BAA+B,EAAG;gBACzC,OAAO;gBACP,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;;;;;YAEV,iBAAiB,SAAS,CAAC,YAAY,CAAC,6BACvC,oWAAC;gBAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE;gBAA2E,SAAS;0BACrG,cAAA,oWAAC,wOAAA,CAAA,oBAAiB;oBAAC,WAAU;;;;;;;;;;;YAGhC,6BACC,oWAAC,wOAAA,CAAA,qBAAkB;gBAAC,WAAU;;;;;;YAG9B,sBACE,oWAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAMb;GA5DM;;QAgBU,8XAAA,CAAA,iBAAc;;;KAhBxB;uCA8DS", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/spinner/index.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport React from 'react'\n\ntype Props = {\n  loading?: boolean\n  className?: string\n  children?: React.ReactNode | string\n}\n\nconst Spinner: FC<Props> = ({ loading = false, children, className }) => {\n  return (\n    <div\n      className={`inline-block h-4 w-4 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] text-gray-200 ${loading ? 'motion-reduce:animate-[spin_1.5s_linear_infinite]' : 'hidden'} ${className ?? ''}`}\n      role=\"status\"\n    >\n      <span\n        className=\"!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]\"\n      >Loading...</span>\n      {children}\n    </div>\n  )\n}\n\nexport default Spinner\n"], "names": [], "mappings": ";;;;;AASA,MAAM,UAAqB,CAAC,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE;IAClE,qBACE,oWAAC;QACC,WAAW,CAAC,wIAAwI,EAAE,UAAU,sDAAsD,SAAS,CAAC,EAAE,aAAa,IAAI;QACnP,MAAK;;0BAEL,oWAAC;gBACC,WAAU;0BACX;;;;;;YACA;;;;;;;AAGP;KAZM;uCAcS", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/button/index.tsx"], "sourcesContent": ["import type { CSSProperties } from 'react'\nimport React from 'react'\nimport { type VariantProps, cva } from 'class-variance-authority'\nimport Spinner from '../spinner'\nimport classNames from '@/utils/classnames'\n\nconst buttonVariants = cva(\n  'btn disabled:btn-disabled',\n  {\n    variants: {\n      variant: {\n        'primary': 'btn-primary',\n        'warning': 'btn-warning',\n        'secondary': 'btn-secondary',\n        'secondary-accent': 'btn-secondary-accent',\n        'ghost': 'btn-ghost',\n        'ghost-accent': 'btn-ghost-accent',\n        'tertiary': 'btn-tertiary',\n      },\n      size: {\n        small: 'btn-small',\n        medium: 'btn-medium',\n        large: 'btn-large',\n      },\n    },\n    defaultVariants: {\n      variant: 'secondary',\n      size: 'medium',\n    },\n  },\n)\n\nexport type ButtonProps = {\n  destructive?: boolean\n  loading?: boolean\n  styleCss?: CSSProperties\n  spinnerClassName?: string\n} & React.ButtonHTMLAttributes<HTMLButtonElement> & VariantProps<typeof buttonVariants>\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, destructive, loading, styleCss, children, spinnerClassName, ...props }, ref) => {\n    return (\n      <button\n        type='button'\n        className={classNames(\n          buttonVariants({ variant, size, className }),\n          destructive && 'btn-destructive',\n        )}\n        ref={ref}\n        style={styleCss}\n        {...props}\n      >\n        {children}\n        {loading && <Spinner loading={loading} className={classNames('!text-white !h-3 !w-3 !border-2 !ml-1', spinnerClassName)} />}\n      </button>\n    )\n  },\n)\nButton.displayName = 'Button'\n\nexport default Button\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,6BACA;IACE,UAAU;QACR,SAAS;YACP,WAAW;YACX,WAAW;YACX,aAAa;YACb,oBAAoB;YACpB,SAAS;YACT,gBAAgB;YAChB,YAAY;QACd;QACA,MAAM;YACJ,OAAO;YACP,QAAQ;YACR,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,oUAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG,OAAO,EAAE;IACnG,qBACE,oWAAC;QACC,MAAK;QACL,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAClB,eAAe;YAAE;YAAS;YAAM;QAAU,IAC1C,eAAe;QAEjB,KAAK;QACL,OAAO;QACN,GAAG,KAAK;;YAER;YACA,yBAAW,oWAAC,iJAAA,CAAA,UAAO;gBAAC,SAAS;gBAAS,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE,yCAAyC;;;;;;;;;;;;AAG5G;;AAEF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/btn-fold.tsx"], "sourcesContent": ["import cls from 'classnames'\nimport Button from '../../button'\nconst IconFold = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" className={className}>\n    <g stroke=\"#1E6AFF\" stroke-width=\"1.4\" fill=\"none\" fill-rule=\"evenodd\">\n      <rect x=\".7\" y=\".7\" width=\"18.6\" height=\"18.6\" rx=\"4\" />\n      <path d=\"M13.5 0L13.5 20\" />\n      <path d=\"M6,7 L9,10 L6,13\" transform=\"matrix(-1 0 0 1 15 0)\" />\n    </g>\n  </svg>\n)\nconst IconUnfold = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" className={className}>\n    <g stroke=\"#1E6AFF\" stroke-width=\"1.4\" fill=\"none\" fill-rule=\"evenodd\">\n      <rect x=\".7\" y=\".7\" width=\"18.6\" height=\"18.6\" rx=\"4\" />\n      <path d=\"M13.5 0L13.5 20\" />\n      <path d=\"M6,7 L9,10 L6,13\" />\n    </g>\n  </svg>\n)\n\nconst SvgClass = 'w-[20px] h-[20px]'\n\ntype Props = {\n  className?: string\n  isFold?: boolean\n  onClick?: () => void\n}\n\nconst BtnFold = ({ className, isFold, onClick }: Props) => {\n  return (\n    <div className={cls(className, 'cursor-pointer')} onClick={onClick}>\n      {isFold\n        ? <Button\n          variant='secondary-accent'\n          className={'h-[40px] rounded-[20px] border-[#356CFF] text-[#434B5B]'}\n        >\n          <IconUnfold className=\"w-[18px] mr-[8px]\" />\n        历史对话\n        </Button>\n        : <IconFold className={SvgClass} />}\n    </div>\n  )\n}\n\nexport default BtnFold\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AACA,MAAM,WAAW,CAAC,EAAE,SAAS,EAAyB,iBACpD,oWAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,oWAAC;YAAE,QAAO;YAAU,gBAAa;YAAM,MAAK;YAAO,aAAU;;8BAC3D,oWAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAO,QAAO;oBAAO,IAAG;;;;;;8BAClD,oWAAC;oBAAK,GAAE;;;;;;8BACR,oWAAC;oBAAK,GAAE;oBAAmB,WAAU;;;;;;;;;;;;;;;;;KALrC;AASN,MAAM,aAAa,CAAC,EAAE,SAAS,EAAyB,iBACtD,oWAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,oWAAC;YAAE,QAAO;YAAU,gBAAa;YAAM,MAAK;YAAO,aAAU;;8BAC3D,oWAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAO,QAAO;oBAAO,IAAG;;;;;;8BAClD,oWAAC;oBAAK,GAAE;;;;;;8BACR,oWAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;;MALR;AAUN,MAAM,WAAW;AAQjB,MAAM,UAAU,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAS;IACpD,qBACE,oWAAC;QAAI,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAG,AAAD,EAAE,WAAW;QAAmB,SAAS;kBACxD,uBACG,oWAAC,gJAAA,CAAA,UAAM;YACP,SAAQ;YACR,WAAW;;8BAEX,oWAAC;oBAAW,WAAU;;;;;;gBAAsB;;;;;;iCAG5C,oWAAC;YAAS,WAAW;;;;;;;;;;;AAG/B;MAdM;uCAgBS", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/config/index.ts"], "sourcesContent": ["export const DEFAULT_APP_ICON = process.env.NEXT_PUBLIC_SITE_LOGO_ICON // '/logo/default-robot-logo.svg' // APP默认图标\n\nexport const WEBSITE_LOGO = process.env.NEXT_PUBLIC_SITE_LOGO_ICON // 网站logo\n\nexport const appDefaultIconBackground = '#D5F5F6'\n"], "names": [], "mappings": ";;;;;AAAgC;AAAzB,MAAM,mBAAmB,uUAAA,CAAA,UAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,4CAA4C;;AAE5G,MAAM,eAAe,uUAAA,CAAA,UAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,SAAS;;AAErE,MAAM,2BAA2B", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/app-icon/index.tsx"], "sourcesContent": ["'use client'\n\nimport type { FC } from 'react'\nimport { init } from 'emoji-mart'\nimport data from '@emoji-mart/data'\nimport { cva } from 'class-variance-authority'\nimport type { AppIconType } from '@/types/app'\nimport classNames from '@/utils/classnames'\nimport { DEFAULT_APP_ICON } from '@/config'\n\ninit({ data })\n\nexport type AppIconProps = {\n  size?: 'xs' | 'tiny' | 'small' | 'medium' | 'large' | 'xl' | 'xxl'\n  rounded?: boolean\n  iconType?: AppIconType | null\n  icon?: string\n  background?: string | null\n  imageUrl?: string | null\n  className?: string\n  innerIcon?: React.ReactNode\n  onClick?: () => void\n}\nconst appIconVariants = cva(\n  'flex items-center justify-center relative text-lg rounded-lg grow-0 shrink-0 overflow-hidden leading-none',\n  {\n    variants: {\n      size: {\n        xs: 'w-4 h-4 text-xs',\n        tiny: 'w-6 h-6 text-base',\n        small: 'w-8 h-8 text-xl',\n        medium: 'w-9 h-9 text-[22px]',\n        large: 'w-10 h-10 text-[24px]',\n        xl: 'w-12 h-12 text-[28px]',\n        xxl: 'w-14 h-14 text-[32px]',\n      },\n      rounded: {\n        true: 'rounded-full',\n      },\n    },\n    defaultVariants: {\n      size: 'medium',\n      rounded: false,\n    },\n  })\nconst AppIcon: FC<AppIconProps> = ({\n  size = 'medium',\n  rounded = false,\n  iconType,\n  icon,\n  background,\n  imageUrl,\n  className,\n  innerIcon,\n  onClick,\n}) => {\n  const isDefaultIcon = icon === 'default-icon'\n  const isValidImageIcon = (iconType === 'image' && imageUrl)|| isDefaultIcon\n\n  return <span\n    className={classNames(appIconVariants({ size, rounded }), className)}\n    style={{ background: isValidImageIcon ? undefined : (background || '#FFEAD5') }}\n    onClick={onClick}\n  >\n    {isValidImageIcon\n\n      ? <img src={isDefaultIcon ? DEFAULT_APP_ICON : (imageUrl as string)} className=\"h-full w-full\" alt=\"app icon\" />\n      : (innerIcon || ((icon && icon !== '') ? <em-emoji id={icon} /> : <em-emoji id='🤖' />))\n    }\n  </span>\n}\n\nexport default AppIcon\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AACA;AARA;;;;;;;AAUA,CAAA,GAAA,4MAAA,CAAA,OAAI,AAAD,EAAE;IAAE,MAAA,2MAAA,CAAA,UAAI;AAAC;AAaZ,MAAM,kBAAkB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACxB,6GACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,OAAO;YACP,IAAI;YACJ,KAAK;QACP;QACA,SAAS;YACP,MAAM;QACR;IACF;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;AACF;AACF,MAAM,UAA4B,CAAC,EACjC,OAAO,QAAQ,EACf,UAAU,KAAK,EACf,QAAQ,EACR,IAAI,EACJ,UAAU,EACV,QAAQ,EACR,SAAS,EACT,SAAS,EACT,OAAO,EACR;IACC,MAAM,gBAAgB,SAAS;IAC/B,MAAM,mBAAmB,AAAC,aAAa,WAAW,YAAY;IAE9D,qBAAO,oWAAC;QACN,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE,gBAAgB;YAAE;YAAM;QAAQ,IAAI;QAC1D,OAAO;YAAE,YAAY,mBAAmB,YAAa,cAAc;QAAW;QAC9E,SAAS;kBAER,iCAEG,oWAAC;YAAI,KAAK,gBAAgB,yHAAA,CAAA,mBAAgB,GAAI;YAAqB,WAAU;YAAgB,KAAI;;;;;mBAChG,aAAa,CAAC,AAAC,QAAQ,SAAS,mBAAM,oWAAC;YAAS,IAAI;;;;;iCAAW,oWAAC;YAAS,IAAG;;;;;gBAAO;;;;;;AAG5F;KAzBM;uCA2BS", "debugId": null}}, {"offset": {"line": 594, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/action-button/index.tsx"], "sourcesContent": ["import type { CSSProperties } from 'react'\nimport React from 'react'\nimport { type VariantProps, cva } from 'class-variance-authority'\nimport classNames from '@/utils/classnames'\n\nenum ActionButtonState {\n  Destructive = 'destructive',\n  Active = 'active',\n  Disabled = 'disabled',\n  Default = '',\n  Hover = 'hover',\n}\n\nconst actionButtonVariants = cva(\n  'action-btn',\n  {\n    variants: {\n      size: {\n        xs: 'action-btn-xs',\n        m: 'action-btn-m',\n        l: 'action-btn-l',\n        xl: 'action-btn-xl',\n      },\n    },\n    defaultVariants: {\n      size: 'm',\n    },\n  },\n)\n\nexport type ActionButtonProps = {\n  size?: 'xs' | 's' | 'm' | 'l' | 'xl'\n  state?: ActionButtonState\n  styleCss?: CSSProperties\n} & React.ButtonHTMLAttributes<HTMLButtonElement> & VariantProps<typeof actionButtonVariants>\n\nfunction getActionButtonState(state: ActionButtonState) {\n  switch (state) {\n    case ActionButtonState.Destructive:\n      return 'action-btn-destructive'\n    case ActionButtonState.Active:\n      return 'action-btn-active'\n    case ActionButtonState.Disabled:\n      return 'action-btn-disabled'\n    case ActionButtonState.Hover:\n      return 'action-btn-hover'\n    default:\n      return ''\n  }\n}\n\nconst ActionButton = React.forwardRef<HTMLButtonElement, ActionButtonProps>(\n  ({ className, size, state = ActionButtonState.Default, styleCss, children, ...props }, ref) => {\n    return (\n      <button\n        type='button'\n        className={classNames(\n          actionButtonVariants({ className, size }),\n          getActionButtonState(state),\n        )}\n        ref={ref}\n        style={styleCss}\n        {...props}\n      >\n        {children}\n      </button>\n    )\n  },\n)\nActionButton.displayName = 'ActionButton'\n\nexport default ActionButton\nexport { ActionButton, ActionButtonState, actionButtonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AACA;;;;;AAEA,IAAA,AAAK,2CAAA;;;;;;WAAA;EAAA;AAQL,MAAM,uBAAuB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EAC7B,cACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,GAAG;YACH,GAAG;YACH,IAAI;QACN;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AASF,SAAS,qBAAqB,KAAwB;IACpD,OAAQ;QACN;YACE,OAAO;QACT;YACE,OAAO;QACT;YACE,OAAO;QACT;YACE,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,6BAAe,oUAAA,CAAA,UAAK,CAAC,UAAU,MACnC,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,UAAiC,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACrF,qBACE,oWAAC;QACC,MAAK;QACL,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAClB,qBAAqB;YAAE;YAAW;QAAK,IACvC,qBAAqB;QAEvB,KAAK;QACL,OAAO;QACN,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEF,aAAa,WAAW,GAAG;uCAEZ", "debugId": null}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/portal-to-follow-elem/index.tsx"], "sourcesContent": ["'use client'\nimport React from 'react'\nimport {\n  FloatingPortal,\n  autoUpdate,\n  flip,\n  offset,\n  shift,\n  size,\n  useDismiss,\n  useFloating,\n  useFocus,\n  useHover,\n  useInteractions,\n  useMergeRefs,\n  useRole,\n} from '@floating-ui/react'\n\nimport type { OffsetOptions, Placement } from '@floating-ui/react'\nimport cn from '@/utils/classnames'\nexport type PortalToFollowElemOptions = {\n  /*\n  * top, bottom, left, right\n  * start, end. Default is middle\n  * combine: top-start, top-end\n  */\n  placement?: Placement\n  open?: boolean\n  offset?: number | OffsetOptions\n  onOpenChange?: (open: boolean) => void\n  triggerPopupSameWidth?: boolean\n}\n\nexport function usePortalToFollowElem({\n  placement = 'bottom',\n  open,\n  offset: offsetValue = 0,\n  onOpenChange: setControlledOpen,\n  triggerPopupSameWidth,\n}: PortalToFollowElemOptions = {}) {\n  const setOpen = setControlledOpen\n\n  const data = useFloating({\n    placement,\n    open,\n    onOpenChange: setOpen,\n    whileElementsMounted: autoUpdate,\n    middleware: [\n      offset(offsetValue),\n      flip({\n        crossAxis: placement.includes('-'),\n        fallbackAxisSideDirection: 'start',\n        padding: 5,\n      }),\n      shift({ padding: 5 }),\n      size({\n        apply({ rects, elements }) {\n          if (triggerPopupSameWidth)\n            elements.floating.style.width = `${rects.reference.width}px`\n        },\n      }),\n    ],\n  })\n\n  const context = data.context\n\n  const hover = useHover(context, {\n    move: false,\n    enabled: open == null,\n  })\n  const focus = useFocus(context, {\n    enabled: open == null,\n  })\n  const dismiss = useDismiss(context)\n  const role = useRole(context, { role: 'tooltip' })\n\n  const interactions = useInteractions([hover, focus, dismiss, role])\n\n  return React.useMemo(\n    () => ({\n      open,\n      setOpen,\n      ...interactions,\n      ...data,\n    }),\n    [open, setOpen, interactions, data],\n  )\n}\n\ntype ContextType = ReturnType<typeof usePortalToFollowElem> | null\n\nconst PortalToFollowElemContext = React.createContext<ContextType>(null)\n\nexport function usePortalToFollowElemContext() {\n  const context = React.useContext(PortalToFollowElemContext)\n\n  if (context == null)\n    throw new Error('PortalToFollowElem components must be wrapped in <PortalToFollowElem />')\n\n  return context\n}\n\nexport function PortalToFollowElem({\n  children,\n  ...options\n}: { children: React.ReactNode } & PortalToFollowElemOptions) {\n  // This can accept any props as options, e.g. `placement`,\n  // or other positioning options.\n  const tooltip = usePortalToFollowElem(options)\n  return (\n    <PortalToFollowElemContext.Provider value={tooltip}>\n      {children}\n    </PortalToFollowElemContext.Provider>\n  )\n}\n\nexport const PortalToFollowElemTrigger = (\n  {\n    ref: propRef,\n    children,\n    asChild = false,\n    ...props\n  }: React.HTMLProps<HTMLElement> & { ref?: React.RefObject<HTMLElement>, asChild?: boolean },\n) => {\n  const context = usePortalToFollowElemContext()\n  const childrenRef = (children as any).props?.ref\n  const ref = useMergeRefs([context.refs.setReference, propRef, childrenRef])\n\n  // `asChild` allows the user to pass any element as the anchor\n  if (asChild && React.isValidElement(children)) {\n    return React.cloneElement(\n      children,\n      context.getReferenceProps({\n        ref,\n        ...props,\n        ...children.props,\n        'data-state': context.open ? 'open' : 'closed',\n      }),\n    )\n  }\n\n  return (\n    <div\n      ref={ref}\n      className={cn('inline-block', props.className)}\n      // The user can style the trigger based on the state\n      data-state={context.open ? 'open' : 'closed'}\n      {...context.getReferenceProps(props)}\n    >\n      {children}\n    </div>\n  )\n}\nPortalToFollowElemTrigger.displayName = 'PortalToFollowElemTrigger'\n\nexport const PortalToFollowElemContent = (\n  {\n    ref: propRef,\n    style,\n    ...props\n  }: React.HTMLProps<HTMLDivElement> & {\n    ref?: React.RefObject<HTMLDivElement>;\n  },\n) => {\n  const context = usePortalToFollowElemContext()\n  const ref = useMergeRefs([context.refs.setFloating, propRef])\n\n  if (!context.open)\n    return null\n\n  const body = document.body\n\n  return (\n    <FloatingPortal root={body}>\n      <div\n        ref={ref}\n        style={{\n          ...context.floatingStyles,\n          ...style,\n        }}\n        {...context.getFloatingProps(props)}\n      />\n    </FloatingPortal>\n  )\n}\n\nPortalToFollowElemContent.displayName = 'PortalToFollowElemContent'\n"], "names": [], "mappings": ";;;;;;;;AACA;AACA;AAAA;AAAA;AAiBA;;;AAnBA;;;;AAiCO,SAAS,sBAAsB,EACpC,YAAY,QAAQ,EACpB,IAAI,EACJ,QAAQ,cAAc,CAAC,EACvB,cAAc,iBAAiB,EAC/B,qBAAqB,EACK,GAAG,CAAC,CAAC;;IAC/B,MAAM,UAAU;IAEhB,MAAM,OAAO,CAAA,GAAA,qVAAA,CAAA,cAAW,AAAD,EAAE;QACvB;QACA;QACA,cAAc;QACd,sBAAsB,gQAAA,CAAA,aAAU;QAChC,YAAY;YACV,CAAA,GAAA,wWAAA,CAAA,SAAM,AAAD,EAAE;YACP,CAAA,GAAA,wWAAA,CAAA,OAAI,AAAD,EAAE;gBACH,WAAW,UAAU,QAAQ,CAAC;gBAC9B,2BAA2B;gBAC3B,SAAS;YACX;YACA,CAAA,GAAA,wWAAA,CAAA,QAAK,AAAD,EAAE;gBAAE,SAAS;YAAE;YACnB,CAAA,GAAA,wWAAA,CAAA,OAAI,AAAD,EAAE;gBACH,OAAM,EAAE,KAAK,EAAE,QAAQ,EAAE;oBACvB,IAAI,uBACF,SAAS,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,MAAM,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChE;YACF;SACD;IACH;IAEA,MAAM,UAAU,KAAK,OAAO;IAE5B,MAAM,QAAQ,CAAA,GAAA,qVAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;QAC9B,MAAM;QACN,SAAS,QAAQ;IACnB;IACA,MAAM,QAAQ,CAAA,GAAA,qVAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;QAC9B,SAAS,QAAQ;IACnB;IACA,MAAM,UAAU,CAAA,GAAA,qVAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,MAAM,OAAO,CAAA,GAAA,qVAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,MAAM;IAAU;IAEhD,MAAM,eAAe,CAAA,GAAA,qVAAA,CAAA,kBAAe,AAAD,EAAE;QAAC;QAAO;QAAO;QAAS;KAAK;IAElE,OAAO,oUAAA,CAAA,UAAK,CAAC,OAAO;yCAClB,IAAM,CAAC;gBACL;gBACA;gBACA,GAAG,YAAY;gBACf,GAAG,IAAI;YACT,CAAC;wCACD;QAAC;QAAM;QAAS;QAAc;KAAK;AAEvC;GAtDgB;;QASD,qVAAA,CAAA,cAAW;QAwBV,qVAAA,CAAA,WAAQ;QAIR,qVAAA,CAAA,WAAQ;QAGN,qVAAA,CAAA,aAAU;QACb,qVAAA,CAAA,UAAO;QAEC,qVAAA,CAAA,kBAAe;;;AAetC,MAAM,0CAA4B,oUAAA,CAAA,UAAK,CAAC,aAAa,CAAc;AAE5D,SAAS;;IACd,MAAM,UAAU,oUAAA,CAAA,UAAK,CAAC,UAAU,CAAC;IAEjC,IAAI,WAAW,MACb,MAAM,IAAI,MAAM;IAElB,OAAO;AACT;IAPgB;AAST,SAAS,mBAAmB,EACjC,QAAQ,EACR,GAAG,SACuD;;IAC1D,0DAA0D;IAC1D,gCAAgC;IAChC,MAAM,UAAU,sBAAsB;IACtC,qBACE,oWAAC,0BAA0B,QAAQ;QAAC,OAAO;kBACxC;;;;;;AAGP;IAZgB;;QAME;;;KANF;AAcT,MAAM,4BAA4B,CACvC,EACE,KAAK,OAAO,EACZ,QAAQ,EACR,UAAU,KAAK,EACf,GAAG,OACsF;;IAE3F,MAAM,UAAU;IAChB,MAAM,cAAc,AAAC,SAAiB,KAAK,EAAE;IAC7C,MAAM,MAAM,CAAA,GAAA,qVAAA,CAAA,eAAY,AAAD,EAAE;QAAC,QAAQ,IAAI,CAAC,YAAY;QAAE;QAAS;KAAY;IAE1E,8DAA8D;IAC9D,IAAI,yBAAW,oUAAA,CAAA,UAAK,CAAC,cAAc,CAAC,WAAW;QAC7C,qBAAO,oUAAA,CAAA,UAAK,CAAC,YAAY,CACvB,UACA,QAAQ,iBAAiB,CAAC;YACxB;YACA,GAAG,KAAK;YACR,GAAG,SAAS,KAAK;YACjB,cAAc,QAAQ,IAAI,GAAG,SAAS;QACxC;IAEJ;IAEA,qBACE,oWAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,gBAAgB,MAAM,SAAS;QAC7C,oDAAoD;QACpD,cAAY,QAAQ,IAAI,GAAG,SAAS;QACnC,GAAG,QAAQ,iBAAiB,CAAC,MAAM;kBAEnC;;;;;;AAGP;IApCa;;QAQK;QAEJ,qVAAA,CAAA,eAAY;;;MAVb;AAqCb,0BAA0B,WAAW,GAAG;AAEjC,MAAM,4BAA4B,CACvC,EACE,KAAK,OAAO,EACZ,KAAK,EACL,GAAG,OAGJ;;IAED,MAAM,UAAU;IAChB,MAAM,MAAM,CAAA,GAAA,qVAAA,CAAA,eAAY,AAAD,EAAE;QAAC,QAAQ,IAAI,CAAC,WAAW;QAAE;KAAQ;IAE5D,IAAI,CAAC,QAAQ,IAAI,EACf,OAAO;IAET,MAAM,OAAO,SAAS,IAAI;IAE1B,qBACE,oWAAC,qVAAA,CAAA,iBAAc;QAAC,MAAM;kBACpB,cAAA,oWAAC;YACC,KAAK;YACL,OAAO;gBACL,GAAG,QAAQ,cAAc;gBACzB,GAAG,KAAK;YACV;YACC,GAAG,QAAQ,gBAAgB,CAAC,MAAM;;;;;;;;;;;AAI3C;IA7Ba;;QASK;QACJ,qVAAA,CAAA,eAAY;;;MAVb;AA+Bb,0BAA0B,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 879, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/sidebar/operation.tsx"], "sourcesContent": ["'use client'\nimport type { FC } from 'react'\nimport React, { useEffect, useRef, useState } from 'react'\nimport {\n  RiDeleteBinLine,\n  RiEditLine,\n  RiMoreFill,\n  RiPushpinLine,\n  RiUnpinLine,\n} from '@remixicon/react'\nimport { useTranslation } from 'react-i18next'\nimport { useBoolean } from 'ahooks'\nimport { PortalToFollowElem, PortalToFollowElemContent, PortalToFollowElemTrigger } from '@/components/base/portal-to-follow-elem'\nimport ActionButton, { ActionButtonState } from '@/components/base/action-button'\nimport cn from '@/utils/classnames'\nimport { EmbedSource } from '@/models/share'\n\nconst LarkRenameIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 14 14\" className={className}>\n    <g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" fill-rule=\"evenodd\">\n      <path d=\"M9.33333333,4.66666667 L9.33333333,8.33333333 C9.33333333,8.88561808 8.88561808,9.33333333 8.33333333,9.33333333 L1,9.33333333 C0.44771525,9.33333333 3.38176876e-17,8.88561808 0,8.33333333 L0,1 C-6.76353751e-17,0.44771525 0.44771525,6.76353751e-17 1,0 L6.99195588,0 L6.99195588,0\" transform=\"translate(2.3333 2.3333)\" />\n      <path transform=\"rotate(42 4.83572284 7.99757716)\" d=\"M6.90262064 0.08444366L6.51404603 7.49888968\" />\n    </g>\n  </svg>\n\n)\nconst LarkPinIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 14 14\" className={className}>\n    <g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" fill-rule=\"evenodd\">\n      <path d=\"M3,8 L3,1 M3,1 L0,3.8 M3,1 L6,3.8\" transform=\"translate(4 3)\" />\n      <path d=\"M0 0.5L6 0.5\" transform=\"translate(4 3)\" />\n    </g>\n  </svg>\n\n)\nconst LarkDeleteIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 14 14\" className={className}>\n    <g stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" fill-rule=\"evenodd\">\n      <path d=\"M8.75,2.04166667 L8.22889873,9.85818568 C8.19387888,10.3834835 7.75757745,10.7916667 7.23111357,10.7916667 L2.10221976,10.7916667 C1.57575588,10.7916667 1.13945446,10.3834835 1.1044346,9.85818568 L0.583333333,2.04166667 L0.583333333,2.04166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M0 2.04166667L9.33333333 2.04166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M3.5 0.29166667L5.83333333 0.29166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M3.5 7.875L3.5 4.95833333\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M5.83333333 7.875L5.83333333 4.95833333\" transform=\"translate(2.3333 1.4583)\" />\n    </g>\n  </svg>\n)\n\ntype Props = {\n  isActive?: boolean\n  isItemHovering?: boolean\n  isPinned: boolean\n  isShowRenameConversation?: boolean\n  onRenameConversation?: () => void\n  isShowDelete: boolean\n  togglePin: () => void\n  onDelete: () => void\n  embedSource?: EmbedSource\n  isMobile?: boolean\n}\n\nconst Operation: FC<Props> = ({\n  isActive,\n  isItemHovering,\n  isPinned,\n  togglePin,\n  isShowRenameConversation,\n  onRenameConversation,\n  isShowDelete,\n  onDelete,\n  embedSource,\n  isMobile\n}) => {\n  const { t } = useTranslation()\n  const [open, setOpen] = useState(false)\n  const ref = useRef(null)\n  const [isHovering, { setTrue: setIsHovering, setFalse: setNotHovering }] = useBoolean(false)\n  const isEmbedMobile = embedSource && isMobile\n\n  useEffect(() => {\n    if (!isItemHovering && !isHovering)\n      setOpen(false)\n  }, [isItemHovering, isHovering])\n\n  return (\n    <PortalToFollowElem\n      open={open}\n      onOpenChange={setOpen}\n      placement='bottom-end'\n      offset={4}\n    >\n      <PortalToFollowElemTrigger\n        onClick={() => setOpen(v => !v)}\n      >\n        <ActionButton\n          className={cn((isItemHovering || open) ? 'opacity-100' : 'opacity-0')}\n          state={\n            isActive\n              ? ActionButtonState.Active\n              : open\n                ? ActionButtonState.Hover\n                : ActionButtonState.Default\n          }\n        >\n          <RiMoreFill className='h-4 w-4' />\n        </ActionButton>\n      </PortalToFollowElemTrigger>\n      <PortalToFollowElemContent className=\"z-50\">\n        <div\n          ref={ref}\n          className={`min-w-[120px] rounded-xl border-[0.5px] border-components-panel-border bg-components-panel-bg-blur p-1 shadow-lg backdrop-blur-sm ${isEmbedMobile && 'flex !bg-[#262933] gap-[10px]'}`}\n          onMouseEnter={setIsHovering}\n          onMouseLeave={setNotHovering}\n          onClick={(e) => {\n            e.stopPropagation()\n          }}\n        >\n          <div className={cn('system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-2 py-1.5 text-text-secondary hover:bg-state-base-hover',isEmbedMobile && 'flex-col !h-auto !gap-[0px] !px-[0px]')} onClick={togglePin}>\n            {isPinned && <RiUnpinLine className='h-4 w-4 shrink-0 text-text-tertiary' />}\n            {!isPinned && <RiPushpinLine className='h-4 w-4 shrink-0 text-text-tertiary' />}\n            <span className={cn('grow',isEmbedMobile && '!text-[#fff] !text-[12px]')}>{isPinned ? t('explore.sidebar.action.unpin') : t('explore.sidebar.action.pin')}</span>\n          </div>\n          {isShowRenameConversation && (\n            <div className={cn('system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-2 py-1.5 text-text-secondary hover:bg-state-base-hover',isEmbedMobile && 'flex-col !h-auto !gap-[0px] !px-[0px]')} onClick={onRenameConversation}>\n              <RiEditLine className='h-4 w-4 shrink-0 text-text-tertiary' />\n              <span className={cn('grow',isEmbedMobile && '!text-[#fff] !text-[12px]')}>{t('explore.sidebar.action.rename')}</span>\n            </div>\n          )}\n          {isShowDelete && (\n            <div className={cn('system-md-regular group flex cursor-pointer items-center space-x-1 rounded-lg px-2 py-1.5 text-text-secondary hover:bg-state-destructive-hover hover:text-text-destructive',isEmbedMobile && 'flex-col !h-auto !gap-[0px] !px-[0px]')} onClick={onDelete} >\n              <RiDeleteBinLine className={cn('h-4 w-4 shrink-0 text-text-tertiary group-hover:text-text-destructive')} />\n              <span className={cn('grow',isEmbedMobile && '!text-[#fff] !text-[12px]')}>{t('explore.sidebar.action.delete')}</span>\n            </div>\n          )}\n        </div>\n      </PortalToFollowElemContent>\n    </PortalToFollowElem>\n  )\n}\nexport default React.memo(Operation)\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AAAA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;AAiBA,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAyB,iBAC1D,oWAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,oWAAC;YAAE,QAAO;YAAe,kBAAe;YAAQ,mBAAgB;YAAQ,MAAK;YAAO,aAAU;;8BAC5F,oWAAC;oBAAK,GAAE;oBAA0R,WAAU;;;;;;8BAC5S,oWAAC;oBAAK,WAAU;oBAAmC,GAAE;;;;;;;;;;;;;;;;;KAJrD;AASN,MAAM,cAAc,CAAC,EAAE,SAAS,EAAyB,iBACvD,oWAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,oWAAC;YAAE,QAAO;YAAe,kBAAe;YAAQ,mBAAgB;YAAQ,MAAK;YAAO,aAAU;;8BAC5F,oWAAC;oBAAK,GAAE;oBAAoC,WAAU;;;;;;8BACtD,oWAAC;oBAAK,GAAE;oBAAe,WAAU;;;;;;;;;;;;;;;;;MAJjC;AASN,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAyB,iBAC1D,oWAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,oWAAC;YAAE,QAAO;YAAe,kBAAe;YAAQ,mBAAgB;YAAQ,MAAK;YAAO,aAAU;;8BAC5F,oWAAC;oBAAK,GAAE;oBAAsP,WAAU;;;;;;8BACxQ,oWAAC;oBAAK,GAAE;oBAAsC,WAAU;;;;;;8BACxD,oWAAC;oBAAK,GAAE;oBAAwC,WAAU;;;;;;8BAC1D,oWAAC;oBAAK,GAAE;oBAA4B,WAAU;;;;;;8BAC9C,oWAAC;oBAAK,GAAE;oBAA0C,WAAU;;;;;;;;;;;;;;;;;MAP5D;AAyBN,MAAM,YAAuB,CAAC,EAC5B,QAAQ,EACR,cAAc,EACd,QAAQ,EACR,SAAS,EACT,wBAAwB,EACxB,oBAAoB,EACpB,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,QAAQ,EACT;;IACC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,MAAM,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,CAAC,YAAY,EAAE,SAAS,aAAa,EAAE,UAAU,cAAc,EAAE,CAAC,GAAG,CAAA,GAAA,+TAAA,CAAA,aAAU,AAAD,EAAE;IACtF,MAAM,gBAAgB,eAAe;IAErC,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,kBAAkB,CAAC,YACtB,QAAQ;QACZ;8BAAG;QAAC;QAAgB;KAAW;IAE/B,qBACE,oWAAC,wKAAA,CAAA,qBAAkB;QACjB,MAAM;QACN,cAAc;QACd,WAAU;QACV,QAAQ;;0BAER,oWAAC,wKAAA,CAAA,4BAAyB;gBACxB,SAAS,IAAM,QAAQ,CAAA,IAAK,CAAC;0BAE7B,cAAA,oWAAC,0JAAA,CAAA,UAAY;oBACX,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,AAAC,kBAAkB,OAAQ,gBAAgB;oBACzD,OACE,WACI,0JAAA,CAAA,oBAAiB,CAAC,MAAM,GACxB,OACE,0JAAA,CAAA,oBAAiB,CAAC,KAAK,GACvB,0JAAA,CAAA,oBAAiB,CAAC,OAAO;8BAGjC,cAAA,oWAAC,wOAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAG1B,oWAAC,wKAAA,CAAA,4BAAyB;gBAAC,WAAU;0BACnC,cAAA,oWAAC;oBACC,KAAK;oBACL,WAAW,CAAC,kIAAkI,EAAE,iBAAiB,iCAAiC;oBAClM,cAAc;oBACd,cAAc;oBACd,SAAS,CAAC;wBACR,EAAE,eAAe;oBACnB;;sCAEA,oWAAC;4BAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,qIAAoI,iBAAiB;4BAA0C,SAAS;;gCACxN,0BAAY,oWAAC,wOAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACnC,CAAC,0BAAY,oWAAC,wOAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACvC,oWAAC;oCAAK,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,QAAO,iBAAiB;8CAA+B,WAAW,EAAE,kCAAkC,EAAE;;;;;;;;;;;;wBAE7H,0CACC,oWAAC;4BAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,qIAAoI,iBAAiB;4BAA0C,SAAS;;8CACzN,oWAAC,wOAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,oWAAC;oCAAK,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,QAAO,iBAAiB;8CAA+B,EAAE;;;;;;;;;;;;wBAGhF,8BACC,oWAAC;4BAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,8KAA6K,iBAAiB;4BAA0C,SAAS;;8CAClQ,oWAAC,wOAAA,CAAA,kBAAe;oCAAC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE;;;;;;8CAC/B,oWAAC;oCAAK,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,QAAO,iBAAiB;8CAA+B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3F;GA7EM;;QAYU,8XAAA,CAAA,iBAAc;QAG+C,+TAAA,CAAA,aAAU;;;MAfjF;2DA8ES,oUAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 1219, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/sidebar/item.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport {\n  memo,\n  useRef,\n} from 'react'\nimport { useHover } from 'ahooks'\nimport type { ConversationItem } from '@/models/share'\nimport Operation from '@/components/base/chat/chat-with-history/sidebar/operation'\nimport cn from '@/utils/classnames'\nimport type { ConversationItem, EmbedSource } from '@/models/share'\nimport { MessageDotsCircle } from '@/components/base/icons/src/vender/solid/communication'\nimport ItemOperation from '@/components/explore/item-operation'\n\ntype ItemProps = {\n  isPin?: boolean\n  item: ConversationItem\n  embedSource?: EmbedSource\n  isMobile?: boolean\n  onOperate: (type: string, item: ConversationItem) => void\n  onChangeConversation: (conversationId: string) => void\n  currentConversationId: string\n}\n\nconst LarkChatIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 16 16\" className={className}>\n    <path d=\"M4.58035669,13.2913263 L5.50680349,12.4266908 L5.51361828,12.4205913 C5.70676076,12.2403023 5.8042226,12.1493067 5.91296138,12.084553 C6.01052049,12.0263953 6.11454787,11.9840536 6.22185803,11.9583082 C6.34281601,11.9293711 6.46881365,11.9293711 6.72173297,11.9293711 L11.9991107,11.9293711 C12.6787052,11.9293711 13.0188976,11.9293711 13.2787229,11.7748985 C13.5074835,11.638937 13.6936288,11.4216966 13.8101672,11.1548093 C13.9425723,10.8516798 13.9425723,10.4552142 13.9425723,9.66235387 L13.9425723,4.26737899 C13.9425723,3.47450452 13.9425723,3.07747862 13.8101672,2.7743491 C13.6936288,2.50744051 13.5071187,2.29059723 13.2783581,2.1546074 C13.0182897,2 12.6782796,2 11.9973477,2 L4.94546777,2 C4.26452973,2 3.92380843,2 3.6637278,2.1546074 C3.43494901,2.29059723 3.24908334,2.50744051 3.13252063,2.7743491 C3,3.0777765 3,3.47528469 3,4.2697124 L3,12.4054136 C3,13.1612515 3,13.5390641 3.13280635,13.7331833 C3.2483052,13.9019826 3.42338028,14.0002124 3.60851644,14 C3.82139203,13.999716 4.07446333,13.7635388 4.58035669,13.2913263 Z\" stroke=\"#B9C2CB\" stroke-width=\"1.2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-dasharray=\"1\" fill=\"none\" fill-rule=\"evenodd\" />\n  </svg>\n\n)\n\nconst Item: FC<ItemProps> = ({\n  isPin,\n  item,\n  embedSource,\n  isMobile,\n  onOperate,\n  onChangeConversation,\n  currentConversationId,\n}) => {\n  const ref = useRef(null)\n  const isHovering = useHover(ref)\n  const isSelected = currentConversationId === item.id\n\n  return (\n    <div\n      ref={ref}\n      key={item.id}\n      className={cn(\n        'system-sm-medium group flex cursor-pointer rounded-lg p-1 pl-3 text-components-menu-item-text hover:bg-state-base-hover',\n        isSelected && 'bg-state-accent-active text-text-accent hover:bg-state-accent-active',\n        (currentConversationId === item.id && !embedSource) && 'text-primary-600 bg-primary-50',\n        (currentConversationId === item.id && embedSource) && 'bg-[#ECECEC]'\n      )}\n      onClick={() => onChangeConversation(item.id)}\n    >\n      {embedSource && !isMobile && <LarkChatIcon className=\"w-[16px] mr-[6px] shrink-0\" />}\n      <div className={`grow truncate p-1 pl-0 ${embedSource && 'overflow-hidden text-ellipsis text-nowrap'}`} title={item.name}>{item.name}</div>\n      {item.id !== '' && (\n        <div className='shrink-0' onClick={e => e.stopPropagation()}>\n          <Operation\n            embedSource={embedSource}\n            isMobile={isMobile}\n            isActive={isSelected}\n            isPinned={!!isPin}\n            isItemHovering={isHovering}\n            togglePin={() => onOperate(isPin ? 'unpin' : 'pin', item)}\n            isShowDelete\n            isShowRenameConversation\n            onRenameConversation={() => onOperate('rename', item)}\n            onDelete={() => onOperate('delete', item)}\n          />\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default memo(Item)\n"], "names": [], "mappings": ";;;;AACA;AAIA;AAEA;AACA;;;;;;;AAeA,MAAM,eAAe,CAAC,EAAE,SAAS,EAAyB,iBACxD,oWAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,oWAAC;YAAK,GAAE;YAAkhC,QAAO;YAAU,gBAAa;YAAM,kBAAe;YAAQ,mBAAgB;YAAQ,oBAAiB;YAAI,MAAK;YAAO,aAAU;;;;;;;;;;;KAFtpC;AAON,MAAM,OAAsB,CAAC,EAC3B,KAAK,EACL,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,SAAS,EACT,oBAAoB,EACpB,qBAAqB,EACtB;;IACC,MAAM,MAAM,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,aAAa,CAAA,GAAA,2TAAA,CAAA,WAAQ,AAAD,EAAE;IAC5B,MAAM,aAAa,0BAA0B,KAAK,EAAE;IAEpD,qBACE,oWAAC;QACC,KAAK;QAEL,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EACV,2HACA,cAAc,wEACd,AAAC,0BAA0B,KAAK,EAAE,IAAI,CAAC,eAAgB,kCACvD,AAAC,0BAA0B,KAAK,EAAE,IAAI,eAAgB;QAExD,SAAS,IAAM,qBAAqB,KAAK,EAAE;;YAE1C,eAAe,CAAC,0BAAY,oWAAC;gBAAa,WAAU;;;;;;0BACrD,oWAAC;gBAAI,WAAW,CAAC,uBAAuB,EAAE,eAAe,6CAA6C;gBAAE,OAAO,KAAK,IAAI;0BAAG,KAAK,IAAI;;;;;;YACnI,KAAK,EAAE,KAAK,oBACX,oWAAC;gBAAI,WAAU;gBAAW,SAAS,CAAA,IAAK,EAAE,eAAe;0BACvD,cAAA,oWAAC,wLAAA,CAAA,UAAS;oBACR,aAAa;oBACb,UAAU;oBACV,UAAU;oBACV,UAAU,CAAC,CAAC;oBACZ,gBAAgB;oBAChB,WAAW,IAAM,UAAU,QAAQ,UAAU,OAAO;oBACpD,YAAY;oBACZ,wBAAwB;oBACxB,sBAAsB,IAAM,UAAU,UAAU;oBAChD,UAAU,IAAM,UAAU,UAAU;;;;;;;;;;;;OAvBrC,KAAK,EAAE;;;;;AA6BlB;GA7CM;;QAUe,2TAAA,CAAA,WAAQ;;;MAVvB;2DA+CS,CAAA,GAAA,oUAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/sidebar/list.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport Item from './item'\nimport type { ConversationItem, EmbedSource } from '@/models/share'\n\ntype ListProps = {\n  isPin?: boolean\n  title?: string\n  list: ConversationItem[]\n  embedSource?: EmbedSource\n  isMobile?: boolean\n  onOperate: (type: string, item: ConversationItem) => void\n  onChangeConversation: (conversationId: string) => void\n  currentConversationId: string\n}\nconst List: FC<ListProps> = ({\n  isPin,\n  title,\n  list,\n  embedSource,\n  isMobile,\n  onOperate,\n  onChangeConversation,\n  currentConversationId,\n}) => {\n  return (\n    <div className='space-y-0.5'>\n      {title && (\n        <div className='system-xs-medium-uppercase px-3 pb-1 pt-2 text-text-tertiary'>{title}</div>\n      )}\n      {list.map(item => (\n        <Item\n          isMobile={isMobile}\n          embedSource={embedSource}\n          key={item.id}\n          isPin={isPin}\n          item={item}\n          onOperate={onOperate}\n          onChangeConversation={onChangeConversation}\n          currentConversationId={currentConversationId}\n        />\n      ))}\n    </div>\n  )\n}\n\nexport default List\n"], "names": [], "mappings": ";;;;AACA;;;AAaA,MAAM,OAAsB,CAAC,EAC3B,KAAK,EACL,KAAK,EACL,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,SAAS,EACT,oBAAoB,EACpB,qBAAqB,EACtB;IACC,qBACE,oWAAC;QAAI,WAAU;;YACZ,uBACC,oWAAC;gBAAI,WAAU;0BAAgE;;;;;;YAEhF,KAAK,GAAG,CAAC,CAAA,qBACR,oWAAC,mLAAA,CAAA,UAAI;oBACH,UAAU;oBACV,aAAa;oBAEb,OAAO;oBACP,MAAM;oBACN,WAAW;oBACX,sBAAsB;oBACtB,uBAAuB;mBALlB,KAAK,EAAE;;;;;;;;;;;AAUtB;KA7BM;uCA+BS", "debugId": null}}, {"offset": {"line": 1386, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/modal/index.tsx"], "sourcesContent": ["import { Dialog, DialogPanel, DialogTitle, Transition, TransitionChild } from '@headlessui/react'\nimport { Fragment } from 'react'\nimport { RiCloseLine } from '@remixicon/react'\nimport classNames from '@/utils/classnames'\nimport { noop } from 'lodash-es'\n// https://headlessui.com/react/dialog\n\ntype IModal = {\n  className?: string\n  wrapperClassName?: string\n  isShow: boolean\n  onClose?: () => void\n  title?: React.ReactNode\n  description?: React.ReactNode\n  children?: React.ReactNode\n  closable?: boolean\n  isStatic?: boolean\n  overflowVisible?: boolean\n}\n\nexport default function Modal({\n  className,\n  wrapperClassName,\n  isShow,\n  onClose = noop,\n  title,\n  description,\n  children,\n  closable = false,\n  isStatic = false,\n  overflowVisible = false,\n}: IModal) {\n  return (\n    <Transition appear show={isShow} as={Fragment}>\n      <Dialog as=\"div\" className={classNames('relative z-[60]', wrapperClassName)} onClose={() => {\n        // 禁止点击遮罩层关闭弹窗\n        !isStatic && onClose()\n      }}>\n        <TransitionChild>\n          <div className={classNames(\n            'fixed inset-0 bg-background-overlay',\n            'duration-300 ease-in data-[closed]:opacity-0',\n            'data-[enter]:opacity-100',\n            'data-[leave]:opacity-0',\n          )} />\n        </TransitionChild>\n\n        <div\n          className=\"fixed inset-0 overflow-y-auto\"\n          onClick={(e) => {\n            e.preventDefault()\n            e.stopPropagation()\n          }}\n        >\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <TransitionChild>\n              <DialogPanel className={classNames(\n                'w-full max-w-[480px] transform rounded-2xl bg-components-panel-bg p-6 text-left align-middle shadow-xl transition-all',\n                overflowVisible ? 'overflow-visible' : 'overflow-hidden',\n                'duration-100 ease-in data-[closed]:opacity-0 data-[closed]:scale-95',\n                'data-[enter]:opacity-100 data-[enter]:scale-100',\n                'data-[leave]:opacity-0 data-[enter]:scale-95',\n                className,\n              )}>\n                {title && <DialogTitle\n                  as=\"h3\"\n                  className=\"title-2xl-semi-bold text-text-primary\"\n                >\n                  {title}\n                </DialogTitle>}\n                {description && <div className='body-md-regular mt-2 text-text-secondary'>\n                  {description}\n                </div>}\n                {closable\n                  && <div className='absolute right-6 top-6 z-10 flex h-5 w-5 items-center justify-center rounded-2xl hover:cursor-pointer hover:bg-state-base-hover'>\n                    <RiCloseLine className='h-4 w-4 text-text-tertiary' onClick={\n                      (e) => {\n                        e.stopPropagation()\n                        onClose()\n                      }\n                    } />\n                  </div>}\n                {children}\n              </DialogPanel>\n            </TransitionChild>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAgBe,SAAS,MAAM,EAC5B,SAAS,EACT,gBAAgB,EAChB,MAAM,EACN,UAAU,qOAAA,CAAA,OAAI,EACd,KAAK,EACL,WAAW,EACX,QAAQ,EACR,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,kBAAkB,KAAK,EAChB;IACP,qBACE,oWAAC,yUAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,oUAAA,CAAA,WAAQ;kBAC3C,cAAA,oWAAC,iUAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE,mBAAmB;YAAmB,SAAS;gBACpF,cAAc;gBACd,CAAC,YAAY;YACf;;8BACE,oWAAC,yUAAA,CAAA,kBAAe;8BACd,cAAA,oWAAC;wBAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EACvB,uCACA,gDACA,4BACA;;;;;;;;;;;8BAIJ,oWAAC;oBACC,WAAU;oBACV,SAAS,CAAC;wBACR,EAAE,cAAc;wBAChB,EAAE,eAAe;oBACnB;8BAEA,cAAA,oWAAC;wBAAI,WAAU;kCACb,cAAA,oWAAC,yUAAA,CAAA,kBAAe;sCACd,cAAA,oWAAC,iUAAA,CAAA,cAAW;gCAAC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAC/B,yHACA,kBAAkB,qBAAqB,mBACvC,uEACA,mDACA,gDACA;;oCAEC,uBAAS,oWAAC,iUAAA,CAAA,cAAW;wCACpB,IAAG;wCACH,WAAU;kDAET;;;;;;oCAEF,6BAAe,oWAAC;wCAAI,WAAU;kDAC5B;;;;;;oCAEF,0BACI,oWAAC;wCAAI,WAAU;kDAChB,cAAA,oWAAC,wOAAA,CAAA,cAAW;4CAAC,WAAU;4CAA6B,SAClD,CAAC;gDACC,EAAE,eAAe;gDACjB;4CACF;;;;;;;;;;;oCAGL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;KAtEwB", "debugId": null}}, {"offset": {"line": 1521, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/share/text-generation/info-modal.tsx"], "sourcesContent": ["import React from 'react'\nimport Modal from '@/components/base/modal'\nimport AppIcon from '@/components/base/app-icon'\nimport type { SiteInfo } from '@/models/share'\nimport { appDefaultIconBackground } from '@/config'\nimport cn from 'classnames'\n\ntype Props = {\n  data?: SiteInfo\n  isShow: boolean\n  onClose: () => void\n}\n\nconst InfoModal = ({\n  isShow,\n  onClose,\n  data,\n}: Props) => {\n  return (\n    <Modal\n      isShow={isShow}\n      onClose={onClose}\n      className='min-w-[400px] max-w-[400px] !p-0'\n      closable\n    >\n      <div className={cn('flex flex-col items-center gap-4 px-4 pb-8 pt-10')}>\n        <AppIcon\n          size='xxl'\n          iconType={data?.icon_type}\n          icon={data?.icon}\n          background={data?.icon_background || appDefaultIconBackground}\n          imageUrl={data?.icon_url}\n        />\n        <div className='system-xl-semibold text-text-secondary'>{data?.title}</div>\n        <div className='system-xs-regular text-text-tertiary'>\n          {/* copyright */}\n          {data?.copyright && (\n            <div>© {(new Date()).getFullYear()} {data?.copyright}</div>\n          )}\n          {data?.custom_disclaimer && (\n            <div className='mt-2'>{data.custom_disclaimer}</div>\n          )}\n        </div>\n      </div>\n    </Modal>\n  )\n}\n\nexport default InfoModal\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AACA;;;;;;AAQA,MAAM,YAAY,CAAC,EACjB,MAAM,EACN,OAAO,EACP,IAAI,EACE;IACN,qBACE,oWAAC,+IAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;QACV,QAAQ;kBAER,cAAA,oWAAC;YAAI,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAE,AAAD,EAAE;;8BACjB,oWAAC,qJAAA,CAAA,UAAO;oBACN,MAAK;oBACL,UAAU,MAAM;oBAChB,MAAM,MAAM;oBACZ,YAAY,MAAM,mBAAmB,yHAAA,CAAA,2BAAwB;oBAC7D,UAAU,MAAM;;;;;;8BAElB,oWAAC;oBAAI,WAAU;8BAA0C,MAAM;;;;;;8BAC/D,oWAAC;oBAAI,WAAU;;wBAEZ,MAAM,2BACL,oWAAC;;gCAAI;gCAAI,IAAI,OAAQ,WAAW;gCAAG;gCAAE,MAAM;;;;;;;wBAE5C,MAAM,mCACL,oWAAC;4BAAI,WAAU;sCAAQ,KAAK,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;AAMzD;KAjCM;uCAmCS", "debugId": null}}, {"offset": {"line": 1616, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/share/text-generation/menu-dropdown.tsx"], "sourcesContent": ["'use client'\nimport type { FC } from 'react'\nimport React, { useCallback, useRef, useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport type { Placement } from '@floating-ui/react'\nimport {\n  RiEqualizer2Line,\n} from '@remixicon/react'\nimport ActionButton from '@/components/base/action-button'\nimport {\n  PortalToFollowElem,\n  PortalToFollowElemContent,\n  PortalToFollowElemTrigger,\n} from '@/components/base/portal-to-follow-elem'\nimport InfoModal from './info-modal'\nimport type { SiteInfo } from '@/models/share'\nimport cn from '@/utils/classnames'\n\ntype Props = {\n  data?: SiteInfo\n  placement?: Placement\n}\n\nconst MenuDropdown: FC<Props> = ({\n  data,\n  placement,\n}) => {\n  const { t } = useTranslation()\n  const [open, doSetOpen] = useState(false)\n  const openRef = useRef(open)\n  const setOpen = useCallback((v: boolean) => {\n    doSetOpen(v)\n    openRef.current = v\n  }, [doSetOpen])\n\n  const handleTrigger = useCallback(() => {\n    setOpen(!openRef.current)\n  }, [setOpen])\n\n  const [show, setShow] = useState(false)\n\n  return (\n    <>\n      <PortalToFollowElem\n        open={open}\n        onOpenChange={setOpen}\n        placement={placement || 'bottom-end'}\n        offset={{\n          mainAxis: 4,\n          crossAxis: -4,\n        }}\n      >\n        <PortalToFollowElemTrigger onClick={handleTrigger}>\n          <div>\n            <ActionButton size='l' className={cn(open && 'bg-state-base-hover')}>\n              <RiEqualizer2Line className='h-[18px] w-[18px]' />\n            </ActionButton>\n          </div>\n        </PortalToFollowElemTrigger>\n        <PortalToFollowElemContent className='z-50'>\n          <div className='w-[224px] rounded-xl border-[0.5px] border-components-panel-border bg-components-panel-bg-blur shadow-lg backdrop-blur-sm'>\n            <div className='p-1'>\n              {data?.privacy_policy && (\n                <a href={data.privacy_policy} target='_blank' className='system-md-regular flex cursor-pointer items-center rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-base-hover'>\n                  <span className='grow'>{t('share.chat.privacyPolicyMiddle')}</span>\n                </a>\n              )}\n              <div\n                onClick={() => {\n                  handleTrigger()\n                  setShow(true)\n                }}\n                className='system-md-regular cursor-pointer rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-base-hover'\n              >{t('common.userProfile.about')}</div>\n            </div>\n          </div>\n        </PortalToFollowElemContent>\n      </PortalToFollowElem>\n      {show && (\n        <InfoModal\n          isShow={show}\n          onClose={() => {\n            setShow(false)\n          }}\n          data={data}\n        />\n      )}\n    </>\n  )\n}\nexport default React.memo(MenuDropdown)\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAGA;AACA;AAKA;AAEA;;;AAhBA;;;;;;;;AAuBA,MAAM,eAA0B,CAAC,EAC/B,IAAI,EACJ,SAAS,EACV;;IACC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,MAAM,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,UAAU,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,UAAU,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;6CAAE,CAAC;YAC3B,UAAU;YACV,QAAQ,OAAO,GAAG;QACpB;4CAAG;QAAC;KAAU;IAEd,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;mDAAE;YAChC,QAAQ,CAAC,QAAQ,OAAO;QAC1B;kDAAG;QAAC;KAAQ;IAEZ,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,qBACE;;0BACE,oWAAC,wKAAA,CAAA,qBAAkB;gBACjB,MAAM;gBACN,cAAc;gBACd,WAAW,aAAa;gBACxB,QAAQ;oBACN,UAAU;oBACV,WAAW,CAAC;gBACd;;kCAEA,oWAAC,wKAAA,CAAA,4BAAyB;wBAAC,SAAS;kCAClC,cAAA,oWAAC;sCACC,cAAA,oWAAC,0JAAA,CAAA,UAAY;gCAAC,MAAK;gCAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,QAAQ;0CAC3C,cAAA,oWAAC,wOAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;kCAIlC,oWAAC,wKAAA,CAAA,4BAAyB;wBAAC,WAAU;kCACnC,cAAA,oWAAC;4BAAI,WAAU;sCACb,cAAA,oWAAC;gCAAI,WAAU;;oCACZ,MAAM,gCACL,oWAAC;wCAAE,MAAM,KAAK,cAAc;wCAAE,QAAO;wCAAS,WAAU;kDACtD,cAAA,oWAAC;4CAAK,WAAU;sDAAQ,EAAE;;;;;;;;;;;kDAG9B,oWAAC;wCACC,SAAS;4CACP;4CACA,QAAQ;wCACV;wCACA,WAAU;kDACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAKX,sBACC,oWAAC,qKAAA,CAAA,UAAS;gBACR,QAAQ;gBACR,SAAS;oBACP,QAAQ;gBACV;gBACA,MAAM;;;;;;;;AAKhB;GAlEM;;QAIU,8XAAA,CAAA,iBAAc;;;KAJxB;2DAmES,oUAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 1789, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/confirm/index.tsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react'\nimport { createPortal } from 'react-dom'\nimport { useTranslation } from 'react-i18next'\nimport Button from '../button'\n\nexport type IConfirm = {\n  className?: string\n  isEmbedMobile?: boolean\n  isShow: boolean\n  type?: 'info' | 'warning'\n  title: string\n  content?: React.ReactNode\n  confirmText?: string | null\n  onConfirm: () => void\n  cancelText?: string\n  onCancel: () => void\n  isLoading?: boolean\n  isDisabled?: boolean\n  showConfirm?: boolean\n  showCancel?: boolean\n  maskClosable?: boolean\n  footerRender?: () => React.ReactNode\n}\n\nfunction Confirm({\n  isShow,\n  isEmbedMobile,\n  type = 'warning',\n  title,\n  content,\n  confirmText,\n  cancelText,\n  onConfirm,\n  onCancel,\n  showConfirm = true,\n  showCancel = true,\n  isLoading = false,\n  isDisabled = false,\n  maskClosable = true,\n  footerRender\n}: IConfirm) {\n  const { t } = useTranslation()\n  const dialogRef = useRef<HTMLDivElement>(null)\n  const [isVisible, setIsVisible] = useState(isShow)\n\n  const confirmTxt = confirmText || `${t('common.operation.confirm')}`\n  const cancelTxt = cancelText || `${t('common.operation.cancel')}`\n\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape')\n        onCancel()\n      if (event.key === 'Enter' && isShow) {\n        event.preventDefault()\n        onConfirm()\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown)\n    }\n  }, [onCancel, onConfirm, isShow])\n\n  const handleClickOutside = (event: MouseEvent) => {\n    if (maskClosable && dialogRef.current && !dialogRef.current.contains(event.target as Node))\n      onCancel()\n  }\n\n  useEffect(() => {\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [maskClosable])\n\n  useEffect(() => {\n    if (isShow) {\n      setIsVisible(true)\n    }\n    else {\n      const timer = setTimeout(() => setIsVisible(false), 200)\n      return () => clearTimeout(timer)\n    }\n  }, [isShow])\n\n  if (!isVisible)\n    return null\n\n  return createPortal(\n    <div className={'fixed inset-0 z-[10000000] flex items-center justify-center bg-background-overlay'}\n      onClick={(e) => {\n        e.preventDefault()\n        e.stopPropagation()\n      }}>\n      <div ref={dialogRef} className={`relative w-full max-w-[480px] overflow-hidden ${isEmbedMobile && 'mx-[32px]'}`}>\n        <div className='shadows-shadow-lg flex max-w-full flex-col items-start rounded-2xl border-[0.5px] border-solid border-components-panel-border bg-components-panel-bg'>\n          <div className='flex flex-col items-start gap-2 self-stretch pb-4 pl-6 pr-6 pt-6'>\n            <div className={`title-2xl-semi-bold text-text-primary ${isEmbedMobile && 'text-center w-full mb-[34px]'}`}>{title}</div>\n            <div className={`system-md-regular w-full text-text-tertiary ${isEmbedMobile && 'text-[#8C97A4]'}`}>{content}</div>\n          </div>\n          {footerRender ? footerRender() : <div className='flex items-start justify-end gap-2 self-stretch p-6'>\n            {showCancel && <Button onClick={onCancel}>{cancelTxt}</Button>}\n            {showConfirm && <Button variant={'primary'} destructive={type !== 'info'} loading={isLoading} disabled={isDisabled} onClick={onConfirm}>{confirmTxt}</Button>}\n          </div>}\n        </div>\n      </div>\n    </div>, document.body,\n  )\n}\n\nexport default React.memo(Confirm)\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;;;;;;;AAqBA,SAAS,QAAQ,EACf,MAAM,EACN,aAAa,EACb,OAAO,SAAS,EAChB,KAAK,EACL,OAAO,EACP,WAAW,EACX,UAAU,EACV,SAAS,EACT,QAAQ,EACR,cAAc,IAAI,EAClB,aAAa,IAAI,EACjB,YAAY,KAAK,EACjB,aAAa,KAAK,EAClB,eAAe,IAAI,EACnB,YAAY,EACH;;IACT,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,YAAY,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa,eAAe,GAAG,EAAE,6BAA6B;IACpE,MAAM,YAAY,cAAc,GAAG,EAAE,4BAA4B;IAEjE,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;mDAAgB,CAAC;oBACrB,IAAI,MAAM,GAAG,KAAK,UAChB;oBACF,IAAI,MAAM,GAAG,KAAK,WAAW,QAAQ;wBACnC,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;qCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;gBAC1C;;QACF;4BAAG;QAAC;QAAU;QAAW;KAAO;IAEhC,MAAM,qBAAqB,CAAC;QAC1B,IAAI,gBAAgB,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC/E;IACJ;IAEA,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD;6BAAE;YACR,SAAS,gBAAgB,CAAC,aAAa;YACvC;qCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;4BAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,QAAQ;gBACV,aAAa;YACf,OACK;gBACH,MAAM,QAAQ;+CAAW,IAAM,aAAa;8CAAQ;gBACpD;yCAAO,IAAM,aAAa;;YAC5B;QACF;4BAAG;QAAC;KAAO;IAEX,IAAI,CAAC,WACH,OAAO;IAET,qBAAO,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,gBAChB,oWAAC;QAAI,WAAW;QACd,SAAS,CAAC;YACR,EAAE,cAAc;YAChB,EAAE,eAAe;QACnB;kBACA,cAAA,oWAAC;YAAI,KAAK;YAAW,WAAW,CAAC,8CAA8C,EAAE,iBAAiB,aAAa;sBAC7G,cAAA,oWAAC;gBAAI,WAAU;;kCACb,oWAAC;wBAAI,WAAU;;0CACb,oWAAC;gCAAI,WAAW,CAAC,sCAAsC,EAAE,iBAAiB,gCAAgC;0CAAG;;;;;;0CAC7G,oWAAC;gCAAI,WAAW,CAAC,4CAA4C,EAAE,iBAAiB,kBAAkB;0CAAG;;;;;;;;;;;;oBAEtG,eAAe,+BAAiB,oWAAC;wBAAI,WAAU;;4BAC7C,4BAAc,oWAAC,gJAAA,CAAA,UAAM;gCAAC,SAAS;0CAAW;;;;;;4BAC1C,6BAAe,oWAAC,gJAAA,CAAA,UAAM;gCAAC,SAAS;gCAAW,aAAa,SAAS;gCAAQ,SAAS;gCAAW,UAAU;gCAAY,SAAS;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;cAIzI,SAAS,IAAI;AAEzB;GArFS;;QAiBO,8XAAA,CAAA,iBAAc;;;KAjBrB;2DAuFM,oUAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 1968, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/sidebar/rename-modal.tsx"], "sourcesContent": ["'use client'\nimport type { FC } from 'react'\nimport React, { useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport Modal from '@/components/base/modal'\nimport Button from '@/components/base/button'\nimport Input from '@/components/base/input'\n\nexport type IRenameModalProps = {\n  isShow: boolean\n  isEmbedMobile?: boolean\n  saveLoading: boolean\n  name: string\n  onClose: () => void\n  onSave: (name: string) => void\n}\n\nconst RenameModal: FC<IRenameModalProps> = ({\n  isShow,\n  isEmbedMobile,\n  saveLoading,\n  name,\n  onClose,\n  onSave,\n}) => {\n  const { t } = useTranslation()\n  const [tempName, setTempName] = useState(name)\n\n  return (\n    <Modal\n      className={isEmbedMobile ? '!px-[0px] !pb-[0px] text-center' : ''}\n      title={isEmbedMobile ? '重命名' : t('common.chat.renameConversation')}\n      isShow={isShow}\n      onClose={onClose}\n    >\n      {!isEmbedMobile && <div className={'mt-6 text-sm font-medium leading-[21px] text-text-primary'}>{t('common.chat.conversationName')}</div>}\n      <Input className={`mt-2 h-10 w-full  ${isEmbedMobile && 'outline-[#E2E2FF]'}`}\n        value={tempName}\n        onChange={e => setTempName(e.target.value)}\n        placeholder={t('common.chat.conversationNamePlaceholder') || ''}\n      />\n\n      <div className={`mt-10 flex justify-end ${isEmbedMobile && 'border-t-[1px] border-t-[#EEF0F2]'}`}>\n        <Button className={`mr-2 shrink-0 ${isEmbedMobile && 'border-[0px] bg-[#fff] flex-1 h-[56px] mr-[0px] shadow-none border-r-[1px] border-r-[#EEF0F2] rounded-[0px] hover:bg-[#fff] !text-[16px]'}`} onClick={onClose}>{t('common.operation.cancel')}</Button>\n        <Button variant='primary' className={`shrink-0 ${isEmbedMobile && 'border-[0px] bg-[#fff] flex-1 h-[56px] text-[#1E86FF] shadow-none rounded-[0px] hover:bg-[#fff] !text-[16px]'}`} onClick={() => onSave(tempName)} loading={saveLoading}>{t('common.operation.save')}</Button>\n      </div>\n    </Modal>\n  )\n}\nexport default React.memo(RenameModal)\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAiBA,MAAM,cAAqC,CAAC,EAC1C,MAAM,EACN,aAAa,EACb,WAAW,EACX,IAAI,EACJ,OAAO,EACP,MAAM,EACP;;IACC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qBACE,oWAAC,+IAAA,CAAA,UAAK;QACJ,WAAW,gBAAgB,oCAAoC;QAC/D,OAAO,gBAAgB,QAAQ,EAAE;QACjC,QAAQ;QACR,SAAS;;YAER,CAAC,+BAAiB,oWAAC;gBAAI,WAAW;0BAA8D,EAAE;;;;;;0BACnG,oWAAC,+<PERSON>AA<PERSON>,CAAA,UAAK;gBAAC,WAAW,CAAC,kBAAkB,EAAE,iBAAiB,qBAAqB;gBAC3E,OAAO;gBACP,UAAU,CAAA,IAAK,YAAY,EAAE,MAAM,CAAC,KAAK;gBACzC,aAAa,EAAE,8CAA8C;;;;;;0BAG/D,oWAAC;gBAAI,WAAW,CAAC,uBAAuB,EAAE,iBAAiB,qCAAqC;;kCAC9F,oWAAC,gJAAA,CAAA,UAAM;wBAAC,WAAW,CAAC,cAAc,EAAE,iBAAiB,4IAA4I;wBAAE,SAAS;kCAAU,EAAE;;;;;;kCACxN,oWAAC,gJAAA,CAAA,UAAM;wBAAC,SAAQ;wBAAU,WAAW,CAAC,SAAS,EAAE,iBAAiB,gHAAgH;wBAAE,SAAS,IAAM,OAAO;wBAAW,SAAS;kCAAc,EAAE;;;;;;;;;;;;;;;;;;AAItP;GA/BM;;QAQU,8XAAA,CAAA,iBAAc;;;KARxB;2DAgCS,oUAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 2069, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/logo/logo-site.tsx"], "sourcesContent": ["'use client'\nimport type { <PERSON> } from 'react'\nimport classNames from '@/utils/classnames'\nimport { WEBSITE_LOGO } from '@/config'\n\ntype LogoSiteProps = {\n  className?: string\n}\n\nconst LogoSite: FC<LogoSiteProps> = ({\n  className,\n}) => {\n  return (\n    WEBSITE_LOGO && (\n      <picture>\n        <img\n          src={WEBSITE_LOGO}\n          className={classNames('block w-auto h-[22px]', className)}\n          alt='logo'\n        />\n      </picture>\n    )\n  )\n}\n\nexport default LogoSite\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASA,MAAM,WAA8B,CAAC,EACnC,SAAS,EACV;IACC,OACE,yHAAA,CAAA,eAAY,kBACV,oWAAC;kBACC,cAAA,oWAAC;YACC,KAAK,yHAAA,CAAA,eAAY;YACjB,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE,yBAAyB;YAC/C,KAAI;;;;;;;;;;;AAKd;KAdM;uCAgBS", "debugId": null}}, {"offset": {"line": 2108, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/base/chat/chat-with-history/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"bg\": \"index-module__guMYKq__bg\",\n  \"errorPng\": \"index-module__guMYKq__errorPng\",\n  \"mobileNavTitle\": \"index-module__guMYKq__mobileNavTitle\",\n  \"navBarBg\": \"index-module__guMYKq__navBarBg\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2125, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/assets/lark-app-robot.gif.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 661, height: 661, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,8HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 2147, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/sidebar/index.tsx"], "sourcesContent": ["import type { ChangeEvent } from 'react'\nimport {\n  useCallback,\n  useState,\n} from 'react'\nimport { useTranslation } from 'react-i18next'\nimport {\n  RiEditBoxLine,\n  RiExpandRightLine,\n  RiLayoutLeft2Line,\n} from '@remixicon/react'\nimport { throttle } from 'lodash-es'\nimport { useChatWithHistoryContext } from '../context'\nimport Input from '../../../input'\nimport BtnFold from '../btn-fold'\nimport AppIcon from '@/components/base/app-icon'\nimport ActionButton from '@/components/base/action-button'\nimport Button from '@/components/base/button'\nimport List from '@/components/base/chat/chat-with-history/sidebar/list'\nimport MenuDropdown from '@/components/share/text-generation/menu-dropdown'\nimport Confirm from '@/components/base/confirm'\nimport RenameModal from '@/components/base/chat/chat-with-history/sidebar/rename-modal'\nimport LogoSite from '@/components/base/logo/logo-site'\nimport type { ConversationItem } from '@/models/share'\nimport cn from '@/utils/classnames'\nimport styles from '../index.module.css'\nimport Robot from '@/assets/lark-app-robot.gif'\nimport Image from 'next/image'\n\nconst LarkDeleteIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 14 14\" className={className}>\n    <g stroke=\"#A3AFBB\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\" fill-rule=\"evenodd\">\n      <path d=\"M8.75,2.04166667 L8.22889873,9.85818568 C8.19387888,10.3834835 7.75757745,10.7916667 7.23111357,10.7916667 L2.10221976,10.7916667 C1.57575588,10.7916667 1.13945446,10.3834835 1.1044346,9.85818568 L0.583333333,2.04166667 L0.583333333,2.04166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M0 2.04166667L9.33333333 2.04166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M3.5 0.29166667L5.83333333 0.29166667\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M3.5 7.875L3.5 4.95833333\" transform=\"translate(2.3333 1.4583)\" />\n      <path d=\"M5.83333333 7.875L5.83333333 4.95833333\" transform=\"translate(2.3333 1.4583)\" />\n    </g>\n  </svg>\n)\n\ntype Props = {\n  isPanel?: boolean\n}\n\nconst Sidebar = ({ isPanel }: Props) => {\n  const { t } = useTranslation()\n  const {\n    appData,\n    handleNewConversation,\n    pinnedConversationList,\n    conversationList,\n    currentConversationId,\n    handleChangeConversation,\n    handlePinConversation,\n    handleUnpinConversation,\n    conversationRenaming,\n    handleRenameConversation,\n    handleDeleteConversation,\n    sidebarCollapseState,\n    handleSidebarCollapse,\n    isMobile,\n    isResponding,\n    embedSource,\n    isFold,\n    setIsFold,\n    handleClearAllConversations\n  } = useChatWithHistoryContext()\n  const isSidebarCollapsed = sidebarCollapseState\n\n  const [showConfirm, setShowConfirm] = useState<ConversationItem | null>(null)\n  const [showClearAll, setShowClearAll] = useState<boolean | null>(null)\n  const [showRename, setShowRename] = useState<ConversationItem | null>(null)\n  const [keyword, setKeyword] = useState<string>('')\n  const isEmbedMobile = isMobile && embedSource\n\n  const handleOperate = useCallback((type: string, item: ConversationItem) => {\n    if (type === 'pin')\n      handlePinConversation(item.id)\n\n    if (type === 'unpin')\n      handleUnpinConversation(item.id)\n\n    if (type === 'delete')\n      setShowConfirm(item)\n\n    if (type === 'rename')\n      setShowRename(item)\n  }, [handlePinConversation, handleUnpinConversation])\n  const handleCancelConfirm = useCallback(() => {\n    setShowConfirm(null)\n  }, [])\n  const handleDelete = useCallback(() => {\n    if (showConfirm)\n      handleDeleteConversation(showConfirm.id, { onSuccess: handleCancelConfirm })\n  }, [showConfirm, handleDeleteConversation, handleCancelConfirm])\n  const handleCancelRename = useCallback(() => {\n    setShowRename(null)\n  }, [])\n  const handleRename = useCallback((newName: string) => {\n    if (showRename)\n      handleRenameConversation(showRename.id, newName, { onSuccess: handleCancelRename })\n  }, [showRename, handleRenameConversation, handleCancelRename])\n\n  const handleSearch = throttle(({ target }: ChangeEvent<HTMLInputElement>) => {\n    setKeyword(target?.value)\n  }, 100)\n\n  const handleClearAll = () => {\n    setShowClearAll(true)\n  }\n\n  const handleClearAllConfirm = () => {\n    handleClearAllConversations?.({ onSuccess: () => setShowClearAll(false) })\n  }\n\n  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}\n  const aiRobotGifUrl = chatPageConfigData?.aiRobotGifUrl || Robot // AI机器人动画\n\n  return (\n    <div className={cn(\n      'flex w-full grow flex-col',\n      isPanel && 'rounded-xl border-[0.5px] border-components-panel-border-subtle bg-components-panel-bg shadow-lg',\n      isEmbedMobile && styles.bg,\n      isEmbedMobile && 'bg-[#f5f6f8] w-[74vw] !border-none'\n    )}>\n      <div className={cn(\n        'flex shrink-0 items-center gap-3 p-3 pr-2',\n      )}>\n        <div className='shrink-0'>\n          {\n            embedSource ? (\n              !isMobile && <div className=\"flex items-center\">\n                <Input\n                  className='h-[40px] rounded-[8px] border-[1px] border-solid border-[#DFE4E8]'\n                  value={keyword}\n                  placeholder=\"搜索对话...\"\n                  showLeftIcon\n                  showClearIcon\n                  onChange={handleSearch}\n                  onClear={() => setKeyword('')}\n                />\n                <BtnFold className=\"ml-[11px]\" isFold={isFold} onClick={() => setIsFold?.(!isFold)} />\n              </div>\n            ) : (\n              <>\n                <AppIcon\n                  size='large'\n                  iconType={appData?.site.icon_type}\n                  icon={appData?.site.icon}\n                  background={appData?.site.icon_background}\n                  imageUrl={appData?.site.icon_url}\n                />\n                <div className={cn('system-md-semibold grow truncate text-text-secondary')}>{appData?.site.title}</div>\n                {!isMobile && isSidebarCollapsed && (\n                  <ActionButton size='l' onClick={() => handleSidebarCollapse(false)}>\n                    <RiExpandRightLine className='h-[18px] w-[18px]' />\n                  </ActionButton>\n                )}\n                {!isMobile && !isSidebarCollapsed && (\n                  <ActionButton size='l' onClick={() => handleSidebarCollapse(true)}>\n                    <RiLayoutLeft2Line className='h-[18px] w-[18px]' />\n                  </ActionButton>\n                )}\n              </>\n            )\n          }\n        </div>\n      </div>\n      {\n        !embedSource && (\n          <div className='shrink-0 px-3 py-4'>\n            <Button variant='secondary-accent' disabled={isResponding} className='w-full justify-center' onClick={handleNewConversation}>\n              <RiEditBoxLine className='mr-1 h-4 w-4' />\n              {t('share.chat.newChat')}\n            </Button>\n          </div>\n        )\n      }\n      {embedSource && isMobile && <div className=\"mb-[30px]\">\n        <Image src={aiRobotGifUrl} width={100} height={100} className=\"w-[100px] height-[100px] mt-[64px] mx-auto mb-[6px]\" alt=\"\" />\n        <p className=\"text-center text-[18px] text-[#242933] mb-[20px] font-semibold\">Hi～我是{appData?.site.title}</p>\n        <div className=\"px-[12px]\">\n          <Input\n            className='h-[40px] rounded-[8px] border-[1px] border-solid bg-[#fff] hover:bg-[#fff]'\n            value={keyword}\n            placeholder=\"搜索对话...\"\n            showLeftIcon\n            showClearIcon\n            onChange={handleSearch}\n            onClear={() => setKeyword('')}\n          />\n        </div>\n      </div>}\n      <div className=\"flex items-center justify-between px-[20px] mb-[10px]\">\n        <p className=\"text-[16px] text-[#242933] font-medium\">对话记录</p>\n        <button onClick={handleClearAll}><LarkDeleteIcon className=\"w-[20px] h-[20px]\" /></button>\n      </div>\n      <div className='h-0 grow space-y-2 overflow-y-auto px-3 pt-4'>\n        {/* pinned list */}\n        {!!pinnedConversationList.length && (\n          <div className='mb-4'>\n            <List\n              embedSource={embedSource}\n              isMobile={isMobile}\n              isPin\n              title={t('share.chat.pinnedTitle') || ''}\n              list={pinnedConversationList.filter(item => item.name.includes(keyword))}\n              onChangeConversation={handleChangeConversation}\n              onOperate={handleOperate}\n              currentConversationId={currentConversationId}\n            />\n          </div>\n        )}\n        {!!conversationList.length && (\n          <List\n            embedSource={embedSource}\n            isMobile={isMobile}\n            title={(pinnedConversationList.length && t('share.chat.unpinnedTitle')) || ''}\n            list={conversationList.filter(item => item.name.includes(keyword))}\n            onChangeConversation={handleChangeConversation}\n            onOperate={handleOperate}\n            currentConversationId={currentConversationId}\n          />\n        )}\n      </div>\n      <div className='flex shrink-0 items-center justify-between p-3'>\n        <MenuDropdown placement='top-start' data={appData?.site} />\n        {/* powered by */}\n        <div className='shrink-0'>\n          {!appData?.custom_config?.remove_webapp_brand && (\n            <div className={cn(\n              'flex shrink-0 items-center gap-1.5 px-2',\n            )}>\n              <div className='system-2xs-medium-uppercase text-text-tertiary'>{t('share.chat.poweredBy')}</div>\n              {appData?.custom_config?.replace_webapp_logo && (\n                <img src={appData?.custom_config?.replace_webapp_logo} alt='logo' className='block h-5 w-auto' />\n              )}\n              {!appData?.custom_config?.replace_webapp_logo && (\n                <LogoSite className='!h-5' />\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n      {!!showConfirm && (\n        <Confirm\n          title={embedSource && isMobile ? ' 确定要删除吗？' : t('share.chat.deleteConversation.title')}\n          content={embedSource && isMobile ? \"删除后无法撤销\" : t('share.chat.deleteConversation.content') || ''}\n          isShow\n          isEmbedMobile={Boolean(embedSource && isMobile)}\n          onCancel={handleCancelConfirm}\n          onConfirm={handleDelete}\n          footerRender={embedSource && isMobile ? (() => <div className='flex w-full border-t-[1px] border-t-[#EEF0F2] mt-[23px]'>\n            <p className=\"py-[15px] text-[#A3AFBB] flex-1 text-center border-r-[1px] border-r-[#EEF0F2] text-[16px]\" onClick={handleCancelConfirm}>取消</p>\n            <p className=\"py-[15px] text-[#FF4C4C] flex-1 text-center text-[16px]\" onClick={handleDelete}>删除</p>\n          </div>) : undefined}\n        />\n      )}\n      {showClearAll && (\n        <Confirm\n          title={'确定要删除所有对话记录吗？'}\n          content={\"删除后无法撤销\"}\n          isShow\n          isEmbedMobile={Boolean(embedSource && isMobile)}\n          onCancel={() => setShowClearAll(false)}\n          onConfirm={handleClearAllConfirm}\n          footerRender={embedSource && isMobile ? (() => <div className='flex w-full border-t-[1px] border-t-[#EEF0F2] mt-[23px]'>\n            <p className=\"py-[15px] text-[#A3AFBB] flex-1 text-center border-r-[1px] border-r-[#EEF0F2] text-[16px]\" onClick={() => setShowClearAll(false)}>取消</p>\n            <p className=\"py-[15px] text-[#FF4C4C] flex-1 text-center text-[16px]\" onClick={handleClearAllConfirm}>删除</p>\n          </div>) : undefined}\n        />\n      )}\n      {showRename && (\n        <RenameModal\n          isEmbedMobile={Boolean(embedSource && isMobile)}\n          isShow\n          onClose={handleCancelRename}\n          saveLoading={conversationRenaming}\n          name={showRename?.name || ''}\n          onSave={handleRename}\n        />\n      )}\n    </div>\n  )\n}\n\nexport default Sidebar\n"], "names": [], "mappings": ";;;;AACA;AAIA;AAAA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAyB,iBAC1D,oWAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,oWAAC;YAAE,QAAO;YAAU,kBAAe;YAAQ,mBAAgB;YAAQ,MAAK;YAAO,aAAU;;8BACvF,oWAAC;oBAAK,GAAE;oBAAsP,WAAU;;;;;;8BACxQ,oWAAC;oBAAK,GAAE;oBAAsC,WAAU;;;;;;8BACxD,oWAAC;oBAAK,GAAE;oBAAwC,WAAU;;;;;;8BAC1D,oWAAC;oBAAK,GAAE;oBAA4B,WAAU;;;;;;8BAC9C,oWAAC;oBAAK,GAAE;oBAA0C,WAAU;;;;;;;;;;;;;;;;;KAP5D;AAgBN,MAAM,UAAU,CAAC,EAAE,OAAO,EAAS;;IACjC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,EACJ,OAAO,EACP,qBAAqB,EACrB,sBAAsB,EACtB,gBAAgB,EAChB,qBAAqB,EACrB,wBAAwB,EACxB,qBAAqB,EACrB,uBAAuB,EACvB,oBAAoB,EACpB,wBAAwB,EACxB,wBAAwB,EACxB,oBAAoB,EACpB,qBAAqB,EACrB,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,MAAM,EACN,SAAS,EACT,2BAA2B,EAC5B,GAAG,CAAA,GAAA,2KAAA,CAAA,4BAAyB,AAAD;IAC5B,MAAM,qBAAqB;IAE3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA2B;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA2B;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,gBAAgB,YAAY;IAElC,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;8CAAE,CAAC,MAAc;YAC/C,IAAI,SAAS,OACX,sBAAsB,KAAK,EAAE;YAE/B,IAAI,SAAS,SACX,wBAAwB,KAAK,EAAE;YAEjC,IAAI,SAAS,UACX,eAAe;YAEjB,IAAI,SAAS,UACX,cAAc;QAClB;6CAAG;QAAC;QAAuB;KAAwB;IACnD,MAAM,sBAAsB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;oDAAE;YACtC,eAAe;QACjB;mDAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;6CAAE;YAC/B,IAAI,aACF,yBAAyB,YAAY,EAAE,EAAE;gBAAE,WAAW;YAAoB;QAC9E;4CAAG;QAAC;QAAa;QAA0B;KAAoB;IAC/D,MAAM,qBAAqB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;mDAAE;YACrC,cAAc;QAChB;kDAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;6CAAE,CAAC;YAChC,IAAI,YACF,yBAAyB,WAAW,EAAE,EAAE,SAAS;gBAAE,WAAW;YAAmB;QACrF;4CAAG;QAAC;QAAY;QAA0B;KAAmB;IAE7D,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EAAE,MAAM,EAAiC;QACtE,WAAW,QAAQ;IACrB,GAAG;IAEH,MAAM,iBAAiB;QACrB,gBAAgB;IAClB;IAEA,MAAM,wBAAwB;QAC5B,8BAA8B;YAAE,WAAW,IAAM,gBAAgB;QAAO;IAC1E;IAEA,MAAM,qBAAqB,SAAS,MAAM,iBAAiB,KAAK,KAAK,CAAC,SAAS,MAAM,kBAAkB,CAAC;IACxG,MAAM,gBAAgB,oBAAoB,iBAAiB,8SAAM,UAAU;IAAhB,CAAA,UAAK;IAEhE,qBACE,oWAAC;QAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EACf,6BACA,WAAW,oGACX,iBAAiB,oLAAA,CAAA,UAAM,CAAC,EAAE,EAC1B,iBAAiB;;0BAEjB,oWAAC;gBAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EACf;0BAEA,cAAA,oWAAC;oBAAI,WAAU;8BAEX,cACE,CAAC,0BAAY,oWAAC;wBAAI,WAAU;;0CAC1B,oWAAC,+IAAA,CAAA,UAAK;gCACJ,WAAU;gCACV,OAAO;gCACP,aAAY;gCACZ,YAAY;gCACZ,aAAa;gCACb,UAAU;gCACV,SAAS,IAAM,WAAW;;;;;;0CAE5B,oWAAC,+KAAA,CAAA,UAAO;gCAAC,WAAU;gCAAY,QAAQ;gCAAQ,SAAS,IAAM,YAAY,CAAC;;;;;;;;;;;6CAG7E;;0CACE,oWAAC,qJAAA,CAAA,UAAO;gCACN,MAAK;gCACL,UAAU,SAAS,KAAK;gCACxB,MAAM,SAAS,KAAK;gCACpB,YAAY,SAAS,KAAK;gCAC1B,UAAU,SAAS,KAAK;;;;;;0CAE1B,oWAAC;gCAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE;0CAA0D,SAAS,KAAK;;;;;;4BAC1F,CAAC,YAAY,oCACZ,oWAAC,0JAAA,CAAA,UAAY;gCAAC,MAAK;gCAAI,SAAS,IAAM,sBAAsB;0CAC1D,cAAA,oWAAC,wOAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;;;;;;4BAGhC,CAAC,YAAY,CAAC,oCACb,oWAAC,0JAAA,CAAA,UAAY;gCAAC,MAAK;gCAAI,SAAS,IAAM,sBAAsB;0CAC1D,cAAA,oWAAC,wOAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YASzC,CAAC,6BACC,oWAAC;gBAAI,WAAU;0BACb,cAAA,oWAAC,gJAAA,CAAA,UAAM;oBAAC,SAAQ;oBAAmB,UAAU;oBAAc,WAAU;oBAAwB,SAAS;;sCACpG,oWAAC,wOAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;wBACxB,EAAE;;;;;;;;;;;;YAKV,eAAe,0BAAY,oWAAC;gBAAI,WAAU;;kCACzC,oWAAC,uSAAA,CAAA,UAAK;wBAAC,KAAK;wBAAe,OAAO;wBAAK,QAAQ;wBAAK,WAAU;wBAAsD,KAAI;;;;;;kCACxH,oWAAC;wBAAE,WAAU;;4BAAiE;4BAAM,SAAS,KAAK;;;;;;;kCAClG,oWAAC;wBAAI,WAAU;kCACb,cAAA,oWAAC,+IAAA,CAAA,UAAK;4BACJ,WAAU;4BACV,OAAO;4BACP,aAAY;4BACZ,YAAY;4BACZ,aAAa;4BACb,UAAU;4BACV,SAAS,IAAM,WAAW;;;;;;;;;;;;;;;;;0BAIhC,oWAAC;gBAAI,WAAU;;kCACb,oWAAC;wBAAE,WAAU;kCAAyC;;;;;;kCACtD,oWAAC;wBAAO,SAAS;kCAAgB,cAAA,oWAAC;4BAAe,WAAU;;;;;;;;;;;;;;;;;0BAE7D,oWAAC;gBAAI,WAAU;;oBAEZ,CAAC,CAAC,uBAAuB,MAAM,kBAC9B,oWAAC;wBAAI,WAAU;kCACb,cAAA,oWAAC,mLAAA,CAAA,UAAI;4BACH,aAAa;4BACb,UAAU;4BACV,KAAK;4BACL,OAAO,EAAE,6BAA6B;4BACtC,MAAM,uBAAuB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC;4BAC/D,sBAAsB;4BACtB,WAAW;4BACX,uBAAuB;;;;;;;;;;;oBAI5B,CAAC,CAAC,iBAAiB,MAAM,kBACxB,oWAAC,mLAAA,CAAA,UAAI;wBACH,aAAa;wBACb,UAAU;wBACV,OAAO,AAAC,uBAAuB,MAAM,IAAI,EAAE,+BAAgC;wBAC3E,MAAM,iBAAiB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC;wBACzD,sBAAsB;wBACtB,WAAW;wBACX,uBAAuB;;;;;;;;;;;;0BAI7B,oWAAC;gBAAI,WAAU;;kCACb,oWAAC,wKAAA,CAAA,UAAY;wBAAC,WAAU;wBAAY,MAAM,SAAS;;;;;;kCAEnD,oWAAC;wBAAI,WAAU;kCACZ,CAAC,SAAS,eAAe,qCACxB,oWAAC;4BAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EACf;;8CAEA,oWAAC;oCAAI,WAAU;8CAAkD,EAAE;;;;;;gCAClE,SAAS,eAAe,qCACvB,oWAAC;oCAAI,KAAK,SAAS,eAAe;oCAAqB,KAAI;oCAAO,WAAU;;;;;;gCAE7E,CAAC,SAAS,eAAe,qCACxB,oWAAC,qJAAA,CAAA,UAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAM7B,CAAC,CAAC,6BACD,oWAAC,iJAAA,CAAA,UAAO;gBACN,OAAO,eAAe,WAAW,aAAa,EAAE;gBAChD,SAAS,eAAe,WAAW,YAAY,EAAE,4CAA4C;gBAC7F,MAAM;gBACN,eAAe,QAAQ,eAAe;gBACtC,UAAU;gBACV,WAAW;gBACX,cAAc,eAAe,WAAY,kBAAM,oWAAC;wBAAI,WAAU;;0CAC5D,oWAAC;gCAAE,WAAU;gCAA4F,SAAS;0CAAqB;;;;;;0CACvI,oWAAC;gCAAE,WAAU;gCAA0D,SAAS;0CAAc;;;;;;;;;;;iCACtF;;;;;;YAGb,8BACC,oWAAC,iJAAA,CAAA,UAAO;gBACN,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,eAAe,QAAQ,eAAe;gBACtC,UAAU,IAAM,gBAAgB;gBAChC,WAAW;gBACX,cAAc,eAAe,WAAY,kBAAM,oWAAC;wBAAI,WAAU;;0CAC5D,oWAAC;gCAAE,WAAU;gCAA4F,SAAS,IAAM,gBAAgB;0CAAQ;;;;;;0CAChJ,oWAAC;gCAAE,WAAU;gCAA0D,SAAS;0CAAuB;;;;;;;;;;;iCAC/F;;;;;;YAGb,4BACC,oWAAC,8LAAA,CAAA,UAAW;gBACV,eAAe,QAAQ,eAAe;gBACtC,MAAM;gBACN,SAAS;gBACT,aAAa;gBACb,MAAM,YAAY,QAAQ;gBAC1B,QAAQ;;;;;;;;;;;;AAKlB;GAhPM;;QACU,8XAAA,CAAA,iBAAc;QAqBxB,2KAAA,CAAA,4BAAyB;;;MAtBzB;uCAkPS", "debugId": null}}, {"offset": {"line": 2748, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/header/operation.tsx"], "sourcesContent": ["'use client'\nimport type { FC } from 'react'\nimport React, { useState } from 'react'\nimport type { Placement } from '@floating-ui/react'\nimport {\n  RiArrowDownSLine,\n} from '@remixicon/react'\nimport { useTranslation } from 'react-i18next'\nimport { PortalToFollowElem, PortalToFollowElemContent, PortalToFollowElemTrigger } from '@/components/base/portal-to-follow-elem'\nimport cn from '@/utils/classnames'\n\ntype Props = {\n  title: string\n  isPinned: boolean\n  isShowRenameConversation?: boolean\n  onRenameConversation?: () => void\n  isShowDelete: boolean\n  togglePin: () => void\n  onDelete: () => void\n  placement?: Placement\n}\n\nconst Operation: FC<Props> = ({\n  title,\n  isPinned,\n  togglePin,\n  isShowRenameConversation,\n  onRenameConversation,\n  isShowDelete,\n  onDelete,\n  placement = 'bottom-start',\n}) => {\n  const { t } = useTranslation()\n  const [open, setOpen] = useState(false)\n\n  return (\n    <PortalToFollowElem\n      open={open}\n      onOpenChange={setOpen}\n      placement={placement}\n      offset={4}\n    >\n      <PortalToFollowElemTrigger\n        onClick={() => setOpen(v => !v)}\n      >\n        <div className={cn('flex cursor-pointer items-center rounded-lg p-1.5 pl-2 text-text-secondary hover:bg-state-base-hover', open && 'bg-state-base-hover')}>\n          <div className='system-md-semibold'>{title}</div>\n          <RiArrowDownSLine className='h-4 w-4 ' />\n        </div>\n      </PortalToFollowElemTrigger>\n      <PortalToFollowElemContent className=\"z-50\">\n        <div\n          className={'min-w-[120px] rounded-xl border-[0.5px] border-components-panel-border bg-components-panel-bg-blur p-1 shadow-lg backdrop-blur-sm'}\n        >\n          <div className={cn('system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-base-hover')} onClick={togglePin}>\n            <span className='grow'>{isPinned ? t('explore.sidebar.action.unpin') : t('explore.sidebar.action.pin')}</span>\n          </div>\n          {isShowRenameConversation && (\n            <div className={cn('system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-base-hover')} onClick={onRenameConversation}>\n              <span className='grow'>{t('explore.sidebar.action.rename')}</span>\n            </div>\n          )}\n          {isShowDelete && (\n            <div className={cn('system-md-regular group flex cursor-pointer items-center space-x-1 rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-destructive-hover hover:text-text-destructive')} onClick={onDelete} >\n              <span className='grow'>{t('explore.sidebar.action.delete')}</span>\n            </div>\n          )}\n        </div>\n      </PortalToFollowElemContent>\n    </PortalToFollowElem>\n  )\n}\nexport default React.memo(Operation)\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAGA;AAAA;AACA;AACA;;;AATA;;;;;;AAsBA,MAAM,YAAuB,CAAC,EAC5B,KAAK,EACL,QAAQ,EACR,SAAS,EACT,wBAAwB,EACxB,oBAAoB,EACpB,YAAY,EACZ,QAAQ,EACR,YAAY,cAAc,EAC3B;;IACC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,qBACE,oWAAC,wKAAA,CAAA,qBAAkB;QACjB,MAAM;QACN,cAAc;QACd,WAAW;QACX,QAAQ;;0BAER,oWAAC,wKAAA,CAAA,4BAAyB;gBACxB,SAAS,IAAM,QAAQ,CAAA,IAAK,CAAC;0BAE7B,cAAA,oWAAC;oBAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,wGAAwG,QAAQ;;sCACjI,oWAAC;4BAAI,WAAU;sCAAsB;;;;;;sCACrC,oWAAC,wOAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAGhC,oWAAC,wKAAA,CAAA,4BAAyB;gBAAC,WAAU;0BACnC,cAAA,oWAAC;oBACC,WAAW;;sCAEX,oWAAC;4BAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE;4BAAsI,SAAS;sCAChK,cAAA,oWAAC;gCAAK,WAAU;0CAAQ,WAAW,EAAE,kCAAkC,EAAE;;;;;;;;;;;wBAE1E,0CACC,oWAAC;4BAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE;4BAAsI,SAAS;sCAChK,cAAA,oWAAC;gCAAK,WAAU;0CAAQ,EAAE;;;;;;;;;;;wBAG7B,8BACC,oWAAC;4BAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE;4BAA+K,SAAS;sCACzM,cAAA,oWAAC;gCAAK,WAAU;0CAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;GAjDM;;QAUU,8XAAA,CAAA,iBAAc;;;KAVxB;2DAkDS,oUAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 2897, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/tooltip/index.tsx"], "sourcesContent": ["'use client'\nimport type { FC } from 'react'\nimport React, { useEffect, useRef, useState } from 'react'\nimport { useBoolean } from 'ahooks'\nimport type { OffsetOptions, Placement } from '@floating-ui/react'\nimport { RiQuestionLine } from '@remixicon/react'\nimport cn from '@/utils/classnames'\nimport { PortalToFollowElem, PortalToFollowElemContent, PortalToFollowElemTrigger } from '@/components/base/portal-to-follow-elem'\nexport type TooltipProps = {\n  position?: Placement\n  triggerMethod?: 'hover' | 'click'\n  triggerClassName?: string\n  triggerTestId?: string\n  disabled?: boolean\n  popupContent?: React.ReactNode\n  children?: React.ReactNode\n  popupClassName?: string\n  noDecoration?: boolean\n  offset?: OffsetOptions\n  needsDelay?: boolean\n  asChild?: boolean\n}\n\nconst Tooltip: FC<TooltipProps> = ({\n  position = 'top',\n  triggerMethod = 'hover',\n  triggerClassName,\n  triggerTestId,\n  disabled = false,\n  popupContent,\n  children,\n  popupClassName,\n  noDecoration,\n  offset,\n  asChild = true,\n  needsDelay = false,\n}) => {\n  const [open, setOpen] = useState(false)\n  const [isHoverPopup, {\n    setTrue: setHoverPopup,\n    setFalse: setNotHoverPopup,\n  }] = useBoolean(false)\n\n  const isHoverPopupRef = useRef(isHoverPopup)\n  useEffect(() => {\n    isHoverPopupRef.current = isHoverPopup\n  }, [isHoverPopup])\n\n  const [isHoverTrigger, {\n    setTrue: setHoverTrigger,\n    setFalse: setNotHoverTrigger,\n  }] = useBoolean(false)\n\n  const isHoverTriggerRef = useRef(isHoverTrigger)\n  useEffect(() => {\n    isHoverTriggerRef.current = isHoverTrigger\n  }, [isHoverTrigger])\n\n  const handleLeave = (isTrigger: boolean) => {\n    if (isTrigger)\n      setNotHoverTrigger()\n\n    else\n      setNotHoverPopup()\n\n    // give time to move to the popup\n    if (needsDelay) {\n      setTimeout(() => {\n        if (!isHoverPopupRef.current && !isHoverTriggerRef.current)\n          setOpen(false)\n      }, 500)\n    }\n    else {\n      setOpen(false)\n    }\n  }\n\n  return (\n    <PortalToFollowElem\n      open={disabled ? false : open}\n      onOpenChange={setOpen}\n      placement={position}\n      offset={offset ?? 8}\n    >\n      <PortalToFollowElemTrigger\n        onClick={() => triggerMethod === 'click' && setOpen(v => !v)}\n        onMouseEnter={() => {\n          if (triggerMethod === 'hover') {\n            setHoverTrigger()\n            setOpen(true)\n          }\n        }}\n        onMouseLeave={() => triggerMethod === 'hover' && handleLeave(true)}\n        asChild={asChild}\n      >\n        {children || <div data-testid={triggerTestId} className={triggerClassName || 'h-3.5 w-3.5 shrink-0 p-[1px]'}><RiQuestionLine className='h-full w-full text-text-quaternary hover:text-text-tertiary' /></div>}\n      </PortalToFollowElemTrigger>\n      <PortalToFollowElemContent\n        className=\"z-[9999]\"\n      >\n        {popupContent && (<div\n          className={cn(\n            !noDecoration && 'system-xs-regular relative break-words rounded-md bg-components-panel-bg px-3 py-2 text-text-tertiary shadow-lg',\n            popupClassName,\n          )}\n          onMouseEnter={() => triggerMethod === 'hover' && setHoverPopup()}\n          onMouseLeave={() => triggerMethod === 'hover' && handleLeave(false)}\n        >\n          {popupContent}\n        </div>)}\n      </PortalToFollowElemContent>\n    </PortalToFollowElem>\n  )\n}\n\nexport default React.memo(Tooltip)\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;;;AAPA;;;;;;AAuBA,MAAM,UAA4B,CAAC,EACjC,WAAW,KAAK,EAChB,gBAAgB,OAAO,EACvB,gBAAgB,EAChB,aAAa,EACb,WAAW,KAAK,EAChB,YAAY,EACZ,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,MAAM,EACN,UAAU,IAAI,EACd,aAAa,KAAK,EACnB;;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,cAAc,EACnB,SAAS,aAAa,EACtB,UAAU,gBAAgB,EAC3B,CAAC,GAAG,CAAA,GAAA,+TAAA,CAAA,aAAU,AAAD,EAAE;IAEhB,MAAM,kBAAkB,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD;6BAAE;YACR,gBAAgB,OAAO,GAAG;QAC5B;4BAAG;QAAC;KAAa;IAEjB,MAAM,CAAC,gBAAgB,EACrB,SAAS,eAAe,EACxB,UAAU,kBAAkB,EAC7B,CAAC,GAAG,CAAA,GAAA,+TAAA,CAAA,aAAU,AAAD,EAAE;IAEhB,MAAM,oBAAoB,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAE;IACjC,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD;6BAAE;YACR,kBAAkB,OAAO,GAAG;QAC9B;4BAAG;QAAC;KAAe;IAEnB,MAAM,cAAc,CAAC;QACnB,IAAI,WACF;aAGA;QAEF,iCAAiC;QACjC,IAAI,YAAY;YACd,WAAW;gBACT,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,kBAAkB,OAAO,EACxD,QAAQ;YACZ,GAAG;QACL,OACK;YACH,QAAQ;QACV;IACF;IAEA,qBACE,oWAAC,wKAAA,CAAA,qBAAkB;QACjB,MAAM,WAAW,QAAQ;QACzB,cAAc;QACd,WAAW;QACX,QAAQ,UAAU;;0BAElB,oWAAC,wKAAA,CAAA,4BAAyB;gBACxB,SAAS,IAAM,kBAAkB,WAAW,QAAQ,CAAA,IAAK,CAAC;gBAC1D,cAAc;oBACZ,IAAI,kBAAkB,SAAS;wBAC7B;wBACA,QAAQ;oBACV;gBACF;gBACA,cAAc,IAAM,kBAAkB,WAAW,YAAY;gBAC7D,SAAS;0BAER,0BAAY,oWAAC;oBAAI,eAAa;oBAAe,WAAW,oBAAoB;8BAAgC,cAAA,oWAAC,wOAAA,CAAA,iBAAc;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAEzI,oWAAC,wKAAA,CAAA,4BAAyB;gBACxB,WAAU;0BAET,8BAAiB,oWAAC;oBACjB,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EACV,CAAC,gBAAgB,mHACjB;oBAEF,cAAc,IAAM,kBAAkB,WAAW;oBACjD,cAAc,IAAM,kBAAkB,WAAW,YAAY;8BAE5D;;;;;;;;;;;;;;;;;AAKX;GA1FM;;QAkBC,+TAAA,CAAA,aAAU;QAUV,+TAAA,CAAA,aAAU;;;KA5BX;2DA4FS,oUAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 3032, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/utils.ts"], "sourcesContent": ["import React from 'react'\n\nexport type AbstractNode = {\n  name: string\n  attributes: {\n    [key: string]: string\n  }\n  children?: AbstractNode[]\n}\n\nexport type Attrs = {\n  [key: string]: string\n}\n\nexport function normalizeAttrs(attrs: Attrs = {}): Attrs {\n  return Object.keys(attrs).reduce((acc: Attrs, key) => {\n    const val = attrs[key]\n    key = key.replace(/([-]\\w)/g, (g: string) => g[1].toUpperCase())\n    key = key.replace(/([:]\\w)/g, (g: string) => g[1].toUpperCase())\n    switch (key) {\n      case 'class':\n        acc.className = val\n        delete acc.class\n        break\n      case 'style':\n        (acc.style as any) = val.split(';').reduce((prev, next) => {\n          const pairs = next?.split(':')\n\n          if (pairs[0] && pairs[1]) {\n            const k = pairs[0].replace(/([-]\\w)/g, (g: string) => g[1].toUpperCase())\n            prev[k] = pairs[1]\n          }\n\n          return prev\n        }, {} as Attrs)\n        break\n      default:\n        acc[key] = val\n    }\n    return acc\n  }, {})\n}\n\nexport function generate(\n  node: AbstractNode,\n  key: string,\n  rootProps?: { [key: string]: any } | false,\n): any {\n  if (!rootProps) {\n    return React.createElement(\n      node.name,\n      { key, ...normalizeAttrs(node.attributes) },\n      (node.children || []).map((child, index) => generate(child, `${key}-${node.name}-${index}`)),\n    )\n  }\n\n  return React.createElement(\n    node.name,\n    {\n      key,\n      ...normalizeAttrs(node.attributes),\n      ...rootProps,\n    },\n    (node.children || []).map((child, index) => generate(child, `${key}-${node.name}-${index}`)),\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAcO,SAAS,eAAe,QAAe,CAAC,CAAC;IAC9C,OAAO,OAAO,IAAI,CAAC,OAAO,MAAM,CAAC,CAAC,KAAY;QAC5C,MAAM,MAAM,KAAK,CAAC,IAAI;QACtB,MAAM,IAAI,OAAO,CAAC,YAAY,CAAC,IAAc,CAAC,CAAC,EAAE,CAAC,WAAW;QAC7D,MAAM,IAAI,OAAO,CAAC,YAAY,CAAC,IAAc,CAAC,CAAC,EAAE,CAAC,WAAW;QAC7D,OAAQ;YACN,KAAK;gBACH,IAAI,SAAS,GAAG;gBAChB,OAAO,IAAI,KAAK;gBAChB;YACF,KAAK;gBACF,IAAI,KAAK,GAAW,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,MAAM;oBAChD,MAAM,QAAQ,MAAM,MAAM;oBAE1B,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,EAAE;wBACxB,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,IAAc,CAAC,CAAC,EAAE,CAAC,WAAW;wBACtE,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;oBACpB;oBAEA,OAAO;gBACT,GAAG,CAAC;gBACJ;YACF;gBACE,GAAG,CAAC,IAAI,GAAG;QACf;QACA,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,SACd,IAAkB,EAClB,GAAW,EACX,SAA0C;IAE1C,IAAI,CAAC,WAAW;QACd,qBAAO,oUAAA,CAAA,UAAK,CAAC,aAAa,CACxB,KAAK,IAAI,EACT;YAAE;YAAK,GAAG,eAAe,KAAK,UAAU,CAAC;QAAC,GAC1C,CAAC,KAAK,QAAQ,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,OAAO,QAAU,SAAS,OAAO,GAAG,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;IAE9F;IAEA,qBAAO,oUAAA,CAAA,UAAK,CAAC,aAAa,CACxB,KAAK,IAAI,EACT;QACE;QACA,GAAG,eAAe,KAAK,UAAU,CAAC;QAClC,GAAG,SAAS;IACd,GACA,CAAC,KAAK,QAAQ,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,OAAO,QAAU,SAAS,OAAO,GAAG,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;AAE9F", "debugId": null}}, {"offset": {"line": 3086, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/IconBase.tsx"], "sourcesContent": ["import { generate } from './utils'\nimport type { AbstractNode } from './utils'\n\nexport type IconData = {\n  name: string\n  icon: AbstractNode\n}\n\nexport type IconBaseProps = {\n  data: IconData\n  className?: string\n  onClick?: React.MouseEventHandler<SVGElement>\n  style?: React.CSSProperties\n}\n\nconst IconBase = (\n  {\n    ref,\n    ...props\n  }: IconBaseProps & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => {\n  const { data, className, onClick, style, ...restProps } = props\n\n  return generate(data.icon, `svg-${data.name}`, {\n    className,\n    onClick,\n    style,\n    'data-icon': data.name,\n    'aria-hidden': 'true',\n    ...restProps,\n    'ref': ref,\n  })\n}\n\nIconBase.displayName = 'IconBase'\n\nexport default IconBase\n"], "names": [], "mappings": ";;;AAAA;;AAeA,MAAM,WAAW,CACf,EACE,GAAG,EACH,GAAG,OAGJ;IAED,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,WAAW,GAAG;IAE1D,OAAO,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE,EAAE;QAC7C;QACA;QACA;QACA,aAAa,KAAK,IAAI;QACtB,eAAe;QACf,GAAG,SAAS;QACZ,OAAO;IACT;AACF;KAnBM;AAqBN,SAAS,WAAW,GAAG;uCAER", "debugId": null}}, {"offset": {"line": 3117, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/public/other/Icon3Dots.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './Icon3Dots.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'Icon3Dots'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;AACA;;;;AAGA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,oWAAC,kJAAA,CAAA,UAAQ;QAAE,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,iJAAA,CAAA,UAAI;;;;;;KAPxC;AASN,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 3156, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/public/other/DefaultToolIcon.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './DefaultToolIcon.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'DefaultToolIcon'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;AACA;;;;AAGA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,oWAAC,kJAAA,CAAA,UAAQ;QAAE,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,uJAAA,CAAA,UAAI;;;;;;KAPxC;AASN,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 3195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/public/other/Message3Fill.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './Message3Fill.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'Message3Fill'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;AACA;;;;AAGA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,oWAAC,kJAAA,CAAA,UAAQ;QAAE,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,oJAAA,CAAA,UAAI;;;;;;KAPxC;AASN,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 3234, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/public/other/RowStruct.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './RowStruct.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'RowStruct'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;AACA;;;;AAGA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,oWAAC,kJAAA,CAAA,UAAQ;QAAE,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,iJAAA,CAAA,UAAI;;;;;;KAPxC;AASN,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 3268, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/public/other/index.ts"], "sourcesContent": ["export { default as Icon3Dots } from './Icon3Dots'\nexport { default as DefaultToolIcon } from './DefaultToolIcon'\nexport { default as Message3Fill } from './Message3Fill'\nexport { default as RowStruct } from './RowStruct'\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3308, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/textarea/index.tsx"], "sourcesContent": ["import type { CSSProperties } from 'react'\nimport React from 'react'\nimport { type VariantProps, cva } from 'class-variance-authority'\nimport cn from '@/utils/classnames'\n\nconst textareaVariants = cva(\n  '',\n  {\n    variants: {\n      size: {\n        small: 'py-1 rounded-md system-xs-regular',\n        regular: 'px-3 rounded-md system-sm-regular',\n        large: 'px-4 rounded-lg system-md-regular',\n      },\n    },\n    defaultVariants: {\n      size: 'regular',\n    },\n  },\n)\n\nexport type TextareaProps = {\n  value: string\n  disabled?: boolean\n  destructive?: boolean\n  styleCss?: CSSProperties\n} & React.TextareaHTMLAttributes<HTMLTextAreaElement> & VariantProps<typeof textareaVariants>\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, value, onChange, disabled, size, destructive, styleCss, ...props }, ref) => {\n    return (\n      <textarea\n        ref={ref}\n        style={styleCss}\n        className={cn(\n          'min-h-20 w-full appearance-none border border-transparent bg-components-input-bg-normal p-2 text-components-input-text-filled caret-primary-600 outline-none placeholder:text-components-input-text-placeholder hover:border-components-input-border-hover hover:bg-components-input-bg-hover focus:border-components-input-border-active focus:bg-components-input-bg-active focus:shadow-xs',\n          textareaVariants({ size }),\n          disabled && 'cursor-not-allowed border-transparent bg-components-input-bg-disabled text-components-input-text-filled-disabled hover:border-transparent hover:bg-components-input-bg-disabled',\n          destructive && 'border-components-input-border-destructive bg-components-input-bg-destructive text-components-input-text-filled hover:border-components-input-border-destructive hover:bg-components-input-bg-destructive focus:border-components-input-border-destructive focus:bg-components-input-bg-destructive',\n          className,\n        )}\n        value={value}\n        onChange={onChange}\n        disabled={disabled}\n        {...props}\n      >\n      </textarea>\n    )\n  },\n)\nTextarea.displayName = 'Textarea'\n\nexport default Textarea\nexport { Textarea, textareaVariants }\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;;;;;AAEA,MAAM,mBAAmB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACzB,IACA;IACE,UAAU;QACR,MAAM;YACJ,OAAO;YACP,SAAS;YACT,OAAO;QACT;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAUF,MAAM,yBAAW,oUAAA,CAAA,UAAK,CAAC,UAAU,MAC/B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAChF,qBACE,oWAAC;QACC,KAAK;QACL,OAAO;QACP,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EACV,iYACA,iBAAiB;YAAE;QAAK,IACxB,YAAY,mLACZ,eAAe,uSACf;QAEF,OAAO;QACP,UAAU;QACV,UAAU;QACT,GAAG,KAAK;;;;;;AAIf;;AAEF,SAAS,WAAW,GAAG;uCAER", "debugId": null}}, {"offset": {"line": 3366, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/badge/index.tsx"], "sourcesContent": ["import type { CSSProperties, ReactNode } from 'react'\nimport React from 'react'\nimport { type VariantProps, cva } from 'class-variance-authority'\nimport classNames from '@/utils/classnames'\nimport './index.css'\n\nenum BadgeState {\n  Warning = 'warning',\n  Accent = 'accent',\n  Default = '',\n}\n\nconst BadgeVariants = cva(\n  'badge',\n  {\n    variants: {\n      size: {\n        s: 'badge-s',\n        m: 'badge-m',\n        l: 'badge-l',\n      },\n    },\n    defaultVariants: {\n      size: 'm',\n    },\n  },\n)\n\ntype BadgeProps = {\n  size?: 's' | 'm' | 'l'\n  iconOnly?: boolean\n  uppercase?: boolean\n  state?: BadgeState\n  styleCss?: CSSProperties\n  children?: ReactNode\n} & React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof BadgeVariants>\n\nfunction getBadgeState(state: BadgeState) {\n  switch (state) {\n    case BadgeState.Warning:\n      return 'badge-warning'\n    case BadgeState.Accent:\n      return 'badge-accent'\n    default:\n      return ''\n  }\n}\n\nconst Badge: React.FC<BadgeProps> = ({\n  className,\n  size,\n  state = BadgeState.Default,\n  iconOnly = false,\n  uppercase = false,\n  styleCss,\n  children,\n  ...props\n}) => {\n  return (\n    <div\n      className={classNames(\n        BadgeVariants({ size, className }),\n        getBadgeState(state),\n        size === 's'\n          ? (iconOnly ? 'p-[3px]' : 'px-[5px] py-[3px]')\n          : size === 'l'\n            ? (iconOnly ? 'p-1.5' : 'px-2 py-1')\n            : (iconOnly ? 'p-1' : 'px-[5px] py-[2px]'),\n        uppercase ? 'system-2xs-medium-uppercase' : 'system-2xs-medium',\n      )}\n      style={styleCss}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n}\nBadge.displayName = 'Badge'\n\nexport default Badge\nexport { Badge, BadgeState, BadgeVariants }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;;;AAGA,IAAA,AAAK,oCAAA;;;;WAAA;EAAA;AAML,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,SACA;IACE,UAAU;QACR,MAAM;YACJ,GAAG;YACH,GAAG;YACH,GAAG;QACL;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAYF,SAAS,cAAc,KAAiB;IACtC,OAAQ;QACN;YACE,OAAO;QACT;YACE,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,QAA8B,CAAC,EACnC,SAAS,EACT,IAAI,EACJ,UAA0B,EAC1B,WAAW,KAAK,EAChB,YAAY,KAAK,EACjB,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IACC,qBACE,oWAAC;QACC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAClB,cAAc;YAAE;YAAM;QAAU,IAChC,cAAc,QACd,SAAS,MACJ,WAAW,YAAY,sBACxB,SAAS,MACN,WAAW,UAAU,cACrB,WAAW,QAAQ,qBAC1B,YAAY,gCAAgC;QAE9C,OAAO;QACN,GAAG,KAAK;kBAER;;;;;;AAGP;KA5BM;AA6BN,MAAM,WAAW,GAAG;uCAEL", "debugId": null}}, {"offset": {"line": 3437, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/select/index.tsx"], "sourcesContent": ["'use client'\nimport type { FC } from 'react'\nimport React, { useEffect, useState } from 'react'\nimport { Combobox, ComboboxButton, ComboboxInput, ComboboxOption, ComboboxOptions, Listbox, ListboxButton, ListboxOption, ListboxOptions } from '@headlessui/react'\nimport { ChevronDownIcon, ChevronUpIcon, XMarkIcon } from '@heroicons/react/20/solid'\nimport Badge from '../badge/index'\nimport { RiCheckLine } from '@remixicon/react'\nimport { useTranslation } from 'react-i18next'\nimport classNames from '@/utils/classnames'\nimport {\n  PortalToFollowElem,\n  PortalToFollowElemContent,\n  PortalToFollowElemTrigger,\n} from '@/components/base/portal-to-follow-elem'\n\nconst defaultItems = [\n  { value: 1, name: 'option1' },\n  { value: 2, name: 'option2' },\n  { value: 3, name: 'option3' },\n  { value: 4, name: 'option4' },\n  { value: 5, name: 'option5' },\n  { value: 6, name: 'option6' },\n  { value: 7, name: 'option7' },\n]\n\nexport type Item = {\n  value: number | string\n  name: string\n} & Record<string, any>\n\nexport type ISelectProps = {\n  className?: string\n  wrapperClassName?: string\n  renderTrigger?: (value: Item | null) => React.JSX.Element | null\n  items?: Item[]\n  defaultValue?: number | string\n  disabled?: boolean\n  onSelect: (value: Item) => void\n  allowSearch?: boolean\n  bgClassName?: string\n  placeholder?: string\n  overlayClassName?: string\n  optionWrapClassName?: string\n  optionClassName?: string\n  hideChecked?: boolean\n  notClearable?: boolean\n  renderOption?: ({\n    item,\n    selected,\n  }: {\n    item: Item\n    selected: boolean\n  }) => React.ReactNode\n}\nconst Select: FC<ISelectProps> = ({\n  className,\n  items = defaultItems,\n  defaultValue = 1,\n  disabled = false,\n  onSelect,\n  allowSearch = true,\n  bgClassName = 'bg-components-input-bg-normal',\n  overlayClassName,\n  optionClassName,\n  renderOption,\n}) => {\n  const [query, setQuery] = useState('')\n  const [open, setOpen] = useState(false)\n\n  const [selectedItem, setSelectedItem] = useState<Item | null>(null)\n  useEffect(() => {\n    let defaultSelect = null\n    const existed = items.find((item: Item) => item.value === defaultValue)\n    if (existed)\n      defaultSelect = existed\n\n    setSelectedItem(defaultSelect)\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [defaultValue])\n\n  const filteredItems: Item[]\n    = query === ''\n      ? items\n      : items.filter((item) => {\n        return item.name.toLowerCase().includes(query.toLowerCase())\n      })\n\n  return (\n    <Combobox\n      as=\"div\"\n      disabled={disabled}\n      value={selectedItem}\n      className={className}\n      onChange={(value: Item) => {\n        if (!disabled) {\n          setSelectedItem(value)\n          setOpen(false)\n          onSelect(value)\n        }\n      }}>\n      <div className={classNames('relative')}>\n        <div className='group text-text-secondary'>\n          {allowSearch\n            ? <ComboboxInput\n              className={`w-full rounded-lg border-0 ${bgClassName} py-1.5 pl-3 pr-10 shadow-sm focus-visible:bg-state-base-hover focus-visible:outline-none group-hover:bg-state-base-hover sm:text-sm sm:leading-6 ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}\n              onChange={(event) => {\n                if (!disabled)\n                  setQuery(event.target.value)\n              }}\n              displayValue={(item: Item) => item?.name}\n            />\n            : <ComboboxButton onClick={\n              () => {\n                if (!disabled)\n                  setOpen(!open)\n              }\n            } className={classNames(`flex items-center h-9 w-full rounded-lg border-0 ${bgClassName} py-1.5 pl-3 pr-10 shadow-sm sm:text-sm sm:leading-6 focus-visible:outline-none focus-visible:bg-state-base-hover group-hover:bg-state-base-hover`, optionClassName)}>\n              <div className='w-0 grow truncate text-left' title={selectedItem?.name}>{selectedItem?.name}</div>\n            </ComboboxButton>}\n          <ComboboxButton className=\"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none\" onClick={\n            () => {\n              if (!disabled)\n                setOpen(!open)\n            }\n          }>\n            {open ? <ChevronUpIcon className=\"h-5 w-5\" /> : <ChevronDownIcon className=\"h-5 w-5\" />}\n          </ComboboxButton>\n        </div>\n\n        {(filteredItems.length > 0 && open) && (\n          <ComboboxOptions className={`absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md border-[0.5px] border-components-panel-border bg-components-panel-bg-blur px-1 py-1 text-base shadow-lg backdrop-blur-sm focus:outline-none sm:text-sm ${overlayClassName}`}>\n            {filteredItems.map((item: Item) => (\n              <ComboboxOption\n                key={item.value}\n                value={item}\n                className={({ active }: { active: boolean }) =>\n                  classNames(\n                    'relative cursor-default select-none py-2 pl-3 pr-9 rounded-lg hover:bg-state-base-hover text-text-secondary',\n                    active ? 'bg-state-base-hover' : '',\n                    optionClassName,\n                  )\n                }\n              >\n                {({ /* active, */ selected }) => (\n                  <>\n                    {renderOption\n                      ? renderOption({ item, selected })\n                      : (\n                        <>\n                          <span className={classNames('block', selected && 'font-normal')}>{item.name}</span>\n                          {selected && (\n                            <span\n                              className={classNames(\n                                'absolute inset-y-0 right-0 flex items-center pr-4 text-text-secondary',\n                              )}\n                            >\n                              <RiCheckLine className=\"h-4 w-4\" aria-hidden=\"true\" />\n                            </span>\n                          )}\n                        </>\n                      )}\n                  </>\n                )}\n              </ComboboxOption>\n            ))}\n          </ComboboxOptions>\n        )}\n      </div>\n    </Combobox >\n  )\n}\n\nconst SimpleSelect: FC<ISelectProps> = ({\n  className,\n  wrapperClassName = '',\n  renderTrigger,\n  items = defaultItems,\n  defaultValue = 1,\n  disabled = false,\n  onSelect,\n  placeholder,\n  optionWrapClassName,\n  optionClassName,\n  hideChecked,\n  notClearable,\n  renderOption,\n}) => {\n  const { t } = useTranslation()\n  const localPlaceholder = placeholder || t('common.placeholder.select')\n\n  const [selectedItem, setSelectedItem] = useState<Item | null>(null)\n  useEffect(() => {\n    let defaultSelect = null\n    const existed = items.find((item: Item) => item.value === defaultValue)\n    if (existed)\n      defaultSelect = existed\n\n    setSelectedItem(defaultSelect)\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [defaultValue])\n\n  return (\n    <Listbox\n      value={selectedItem}\n      onChange={(value: Item) => {\n        if (!disabled) {\n          setSelectedItem(value)\n          onSelect(value)\n        }\n      }}\n    >\n      <div className={classNames('group/simple-select relative h-9', wrapperClassName)}>\n        {renderTrigger && <ListboxButton className='w-full'>{renderTrigger(selectedItem)}</ListboxButton>}\n        {!renderTrigger && (\n          <ListboxButton className={classNames(`flex items-center w-full h-full rounded-lg border-0 bg-components-input-bg-normal pl-3 pr-10 sm:text-sm sm:leading-6 focus-visible:outline-none focus-visible:bg-state-base-hover-alt group-hover/simple-select:bg-state-base-hover-alt ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`, className)}>\n            <span className={classNames('block truncate text-left system-sm-regular text-components-input-text-filled', !selectedItem?.name && 'text-components-input-text-placeholder')}>{selectedItem?.name ?? localPlaceholder}</span>\n            <span className=\"absolute inset-y-0 right-0 flex items-center pr-2\">\n              {(selectedItem && !notClearable)\n                ? (\n                  <XMarkIcon\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      setSelectedItem(null)\n                      onSelect({ name: '', value: '' })\n                    }}\n                    className=\"h-4 w-4 cursor-pointer text-text-quaternary\"\n                    aria-hidden=\"false\"\n                  />\n                )\n                : (\n                  <ChevronDownIcon\n                    className=\"h-4 w-4 text-text-quaternary group-hover/simple-select:text-text-secondary\"\n                    aria-hidden=\"true\"\n                  />\n                )}\n            </span>\n          </ListboxButton>\n        )}\n\n        {!disabled && (\n          <ListboxOptions className={classNames('absolute z-10 mt-1 px-1 max-h-60 w-full overflow-auto rounded-xl bg-components-panel-bg-blur backdrop-blur-sm py-1 text-base shadow-lg border-components-panel-border border-[0.5px] focus:outline-none sm:text-sm', optionWrapClassName)}>\n            {items.map((item: Item) => (\n              <ListboxOption\n                key={item.value}\n                className={\n                  classNames(\n                    'relative cursor-pointer select-none py-2 pl-3 pr-9 rounded-lg hover:bg-state-base-hover text-text-secondary',\n                    optionClassName,\n                  )\n                }\n                value={item}\n                disabled={disabled}\n              >\n                {({ /* active, */ selected }) => (\n                  <>\n                    {renderOption\n                      ? renderOption({ item, selected })\n                      : (<>\n                        <span className={classNames('block', selected && 'font-normal')}>{item.name}</span>\n                        {selected && !hideChecked && (\n                          <span\n                            className={classNames(\n                              'absolute inset-y-0 right-0 flex items-center pr-4 text-text-accent',\n                            )}\n                          >\n                            <RiCheckLine className=\"h-4 w-4\" aria-hidden=\"true\" />\n                          </span>\n                        )}\n                      </>)}\n                  </>\n                )}\n              </ListboxOption>\n            ))}\n          </ListboxOptions>\n        )}\n      </div>\n    </Listbox>\n  )\n}\n\ntype PortalSelectProps = {\n  value: string | number\n  onSelect: (value: Item) => void\n  items: Item[]\n  placeholder?: string\n  installedValue?: string | number\n  renderTrigger?: (value?: Item) => React.JSX.Element | null\n  triggerClassName?: string\n  triggerClassNameFn?: (open: boolean) => string\n  popupClassName?: string\n  popupInnerClassName?: string\n  readonly?: boolean\n  hideChecked?: boolean\n}\nconst PortalSelect: FC<PortalSelectProps> = ({\n  value,\n  onSelect,\n  items,\n  placeholder,\n  installedValue,\n  renderTrigger,\n  triggerClassName,\n  triggerClassNameFn,\n  popupClassName,\n  popupInnerClassName,\n  readonly,\n  hideChecked,\n}) => {\n  const { t } = useTranslation()\n  const [open, setOpen] = useState(false)\n  const localPlaceholder = placeholder || t('common.placeholder.select')\n  const selectedItem = value ? items.find(item => item.value === value) : undefined\n\n  return (\n    <PortalToFollowElem\n      open={open}\n      onOpenChange={setOpen}\n      placement='bottom-start'\n      offset={4}\n    >\n      <PortalToFollowElemTrigger onClick={() => !readonly && setOpen(v => !v)} className='w-full'>\n        {renderTrigger\n          ? renderTrigger(selectedItem)\n          : (\n            <div\n              className={classNames(`\n            group flex items-center justify-between px-2.5 h-9 rounded-lg border-0 bg-components-input-bg-normal hover:bg-state-base-hover-alt text-sm ${readonly ? 'cursor-not-allowed' : 'cursor-pointer'} \n          `, triggerClassName, triggerClassNameFn?.(open))}\n              title={selectedItem?.name}\n            >\n              <span\n                className={`\n              grow truncate\n              ${!selectedItem?.name && 'text-components-input-text-placeholder'}\n            `}\n              >\n                {selectedItem?.name ?? localPlaceholder}\n              </span>\n              <div className='mx-0.5'>{installedValue && selectedItem && selectedItem.value !== installedValue && <Badge>{installedValue} {'->'} {selectedItem.value} </Badge>}</div>\n              <ChevronDownIcon className='h-4 w-4 shrink-0 text-text-quaternary group-hover:text-text-secondary' />\n            </div>\n          )}\n\n      </PortalToFollowElemTrigger>\n      <PortalToFollowElemContent className={`z-20 ${popupClassName}`}>\n        <div\n          className={classNames('px-1 py-1 max-h-60 overflow-auto rounded-md text-base shadow-lg border-components-panel-border bg-components-panel-bg border-[0.5px] focus:outline-none sm:text-sm', popupInnerClassName)}\n        >\n          {items.map((item: Item) => (\n            <div\n              key={item.value}\n              className={`\n                flex h-9 cursor-pointer items-center justify-between rounded-lg px-2.5 text-text-secondary hover:bg-state-base-hover\n                ${item.value === value && 'bg-state-base-hover'}\n              `}\n              title={item.name}\n              onClick={() => {\n                onSelect(item)\n                setOpen(false)\n              }}\n            >\n              <span\n                className='w-0 grow truncate'\n                title={item.name}\n              >\n                <span className='truncate'>{item.name}</span>\n                {item.value === installedValue && (\n                  <Badge uppercase={true} className='ml-1 shrink-0'>INSTALLED</Badge>\n                )}\n              </span>\n              {!hideChecked && item.value === value && (\n                <RiCheckLine className='h-4 w-4 shrink-0 text-text-accent' />\n              )}\n            </div>\n          ))}\n        </div>\n      </PortalToFollowElemContent>\n    </PortalToFollowElem>\n  )\n}\nexport { SimpleSelect, PortalSelect }\nexport default React.memo(Select)\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;;;;;;AAEA;AACA;AACA;AAAA;AACA;AACA;;;AATA;;;;;;;;;AAeA,MAAM,eAAe;IACnB;QAAE,OAAO;QAAG,MAAM;IAAU;IAC5B;QAAE,OAAO;QAAG,MAAM;IAAU;IAC5B;QAAE,OAAO;QAAG,MAAM;IAAU;IAC5B;QAAE,OAAO;QAAG,MAAM;IAAU;IAC5B;QAAE,OAAO;QAAG,MAAM;IAAU;IAC5B;QAAE,OAAO;QAAG,MAAM;IAAU;IAC5B;QAAE,OAAO;QAAG,MAAM;IAAU;CAC7B;AA+BD,MAAM,SAA2B,CAAC,EAChC,SAAS,EACT,QAAQ,YAAY,EACpB,eAAe,CAAC,EAChB,WAAW,KAAK,EAChB,QAAQ,EACR,cAAc,IAAI,EAClB,cAAc,+BAA+B,EAC7C,gBAAgB,EAChB,eAAe,EACf,YAAY,EACb;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,gBAAgB;YACpB,MAAM,UAAU,MAAM,IAAI;4CAAC,CAAC,OAAe,KAAK,KAAK,KAAK;;YAC1D,IAAI,SACF,gBAAgB;YAElB,gBAAgB;QAChB,uDAAuD;QACzD;2BAAG;QAAC;KAAa;IAEjB,MAAM,gBACF,UAAU,KACR,QACA,MAAM,MAAM,CAAC,CAAC;QACd,OAAO,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;IAC3D;IAEJ,qBACE,oWAAC,qUAAA,CAAA,WAAQ;QACP,IAAG;QACH,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU,CAAC;YACT,IAAI,CAAC,UAAU;gBACb,gBAAgB;gBAChB,QAAQ;gBACR,SAAS;YACX;QACF;kBACA,cAAA,oWAAC;YAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE;;8BACzB,oWAAC;oBAAI,WAAU;;wBACZ,4BACG,oWAAC,qUAAA,CAAA,gBAAa;4BACd,WAAW,CAAC,2BAA2B,EAAE,YAAY,kJAAkJ,EAAE,WAAW,uBAAuB,kBAAkB;4BAC7P,UAAU,CAAC;gCACT,IAAI,CAAC,UACH,SAAS,MAAM,MAAM,CAAC,KAAK;4BAC/B;4BACA,cAAc,CAAC,OAAe,MAAM;;;;;iDAEpC,oWAAC,qUAAA,CAAA,iBAAc;4BAAC,SAChB;gCACE,IAAI,CAAC,UACH,QAAQ,CAAC;4BACb;4BACA,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE,CAAC,iDAAiD,EAAE,YAAY,iJAAiJ,CAAC,EAAE;sCAC1O,cAAA,oWAAC;gCAAI,WAAU;gCAA8B,OAAO,cAAc;0CAAO,cAAc;;;;;;;;;;;sCAE3F,oWAAC,qUAAA,CAAA,iBAAc;4BAAC,WAAU;4BAAoF,SAC5G;gCACE,IAAI,CAAC,UACH,QAAQ,CAAC;4BACb;sCAEC,qBAAO,oWAAC;gCAAc,WAAU;;;;;qDAAe,oWAAC;gCAAgB,WAAU;;;;;;;;;;;;;;;;;gBAI7E,cAAc,MAAM,GAAG,KAAK,sBAC5B,oWAAC,qUAAA,CAAA,kBAAe;oBAAC,WAAW,CAAC,mNAAmN,EAAE,kBAAkB;8BACjQ,cAAc,GAAG,CAAC,CAAC,qBAClB,oWAAC,qUAAA,CAAA,iBAAc;4BAEb,OAAO;4BACP,WAAW,CAAC,EAAE,MAAM,EAAuB,GACzC,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EACP,+GACA,SAAS,wBAAwB,IACjC;sCAIH,CAAC,EAAgB,QAAQ,EAAE,iBAC1B;8CACG,eACG,aAAa;wCAAE;wCAAM;oCAAS,mBAE9B;;0DACE,oWAAC;gDAAK,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE,SAAS,YAAY;0DAAiB,KAAK,IAAI;;;;;;4CAC1E,0BACC,oWAAC;gDACC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAClB;0DAGF,cAAA,oWAAC,wOAAA,CAAA,cAAW;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;2BAvBtD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;AAqC/B;GApHM;KAAA;AAsHN,MAAM,eAAiC,CAAC,EACtC,SAAS,EACT,mBAAmB,EAAE,EACrB,aAAa,EACb,QAAQ,YAAY,EACpB,eAAe,CAAC,EAChB,WAAW,KAAK,EAChB,QAAQ,EACR,WAAW,EACX,mBAAmB,EACnB,eAAe,EACf,WAAW,EACX,YAAY,EACZ,YAAY,EACb;;IACC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,mBAAmB,eAAe,EAAE;IAE1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,gBAAgB;YACpB,MAAM,UAAU,MAAM,IAAI;kDAAC,CAAC,OAAe,KAAK,KAAK,KAAK;;YAC1D,IAAI,SACF,gBAAgB;YAElB,gBAAgB;QAChB,uDAAuD;QACzD;iCAAG;QAAC;KAAa;IAEjB,qBACE,oWAAC,mUAAA,CAAA,UAAO;QACN,OAAO;QACP,UAAU,CAAC;YACT,IAAI,CAAC,UAAU;gBACb,gBAAgB;gBAChB,SAAS;YACX;QACF;kBAEA,cAAA,oWAAC;YAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE,oCAAoC;;gBAC5D,+BAAiB,oWAAC,mUAAA,CAAA,gBAAa;oBAAC,WAAU;8BAAU,cAAc;;;;;;gBAClE,CAAC,+BACA,oWAAC,mUAAA,CAAA,gBAAa;oBAAC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE,CAAC,wOAAwO,EAAE,WAAW,uBAAuB,kBAAkB,EAAE;;sCACpU,oWAAC;4BAAK,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE,gFAAgF,CAAC,cAAc,QAAQ;sCAA4C,cAAc,QAAQ;;;;;;sCACrM,oWAAC;4BAAK,WAAU;sCACb,AAAC,gBAAgB,CAAC,6BAEf,oWAAC;gCACC,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,gBAAgB;oCAChB,SAAS;wCAAE,MAAM;wCAAI,OAAO;oCAAG;gCACjC;gCACA,WAAU;gCACV,eAAY;;;;;qDAId,oWAAC;gCACC,WAAU;gCACV,eAAY;;;;;;;;;;;;;;;;;gBAOvB,CAAC,0BACA,oWAAC,mUAAA,CAAA,iBAAc;oBAAC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE,sNAAsN;8BACzP,MAAM,GAAG,CAAC,CAAC,qBACV,oWAAC,mUAAA,CAAA,gBAAa;4BAEZ,WACE,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EACP,+GACA;4BAGJ,OAAO;4BACP,UAAU;sCAET,CAAC,EAAgB,QAAQ,EAAE,iBAC1B;8CACG,eACG,aAAa;wCAAE;wCAAM;oCAAS,mBAC7B;;0DACD,oWAAC;gDAAK,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE,SAAS,YAAY;0DAAiB,KAAK,IAAI;;;;;;4CAC1E,YAAY,CAAC,6BACZ,oWAAC;gDACC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAClB;0DAGF,cAAA,oWAAC,wOAAA,CAAA,cAAW;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;2BAtBpD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;AAmC/B;IA1GM;;QAeU,8XAAA,CAAA,iBAAc;;;MAfxB;AA0HN,MAAM,eAAsC,CAAC,EAC3C,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,EACX,cAAc,EACd,aAAa,EACb,gBAAgB,EAChB,kBAAkB,EAClB,cAAc,EACd,mBAAmB,EACnB,QAAQ,EACR,WAAW,EACZ;;IACC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,mBAAmB,eAAe,EAAE;IAC1C,MAAM,eAAe,QAAQ,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,SAAS;IAExE,qBACE,oWAAC,wKAAA,CAAA,qBAAkB;QACjB,MAAM;QACN,cAAc;QACd,WAAU;QACV,QAAQ;;0BAER,oWAAC,wKAAA,CAAA,4BAAyB;gBAAC,SAAS,IAAM,CAAC,YAAY,QAAQ,CAAA,IAAK,CAAC;gBAAI,WAAU;0BAChF,gBACG,cAAc,8BAEd,oWAAC;oBACC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE,CAAC;uJACkH,EAAE,WAAW,uBAAuB,iBAAiB;UAClM,CAAC,EAAE,kBAAkB,qBAAqB;oBACtC,OAAO,cAAc;;sCAErB,oWAAC;4BACC,WAAW,CAAC;;cAEd,EAAE,CAAC,cAAc,QAAQ,yCAAyC;YACpE,CAAC;sCAEI,cAAc,QAAQ;;;;;;sCAEzB,oWAAC;4BAAI,WAAU;sCAAU,kBAAkB,gBAAgB,aAAa,KAAK,KAAK,gCAAkB,oWAAC,+IAAA,CAAA,UAAK;;oCAAE;oCAAe;oCAAE;oCAAK;oCAAE,aAAa,KAAK;oCAAC;;;;;;;;;;;;sCACvJ,oWAAC;4BAAgB,WAAU;;;;;;;;;;;;;;;;;0BAKnC,oWAAC,wKAAA,CAAA,4BAAyB;gBAAC,WAAW,CAAC,KAAK,EAAE,gBAAgB;0BAC5D,cAAA,oWAAC;oBACC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAAE,sKAAsK;8BAE3L,MAAM,GAAG,CAAC,CAAC,qBACV,oWAAC;4BAEC,WAAW,CAAC;;gBAEV,EAAE,KAAK,KAAK,KAAK,SAAS,sBAAsB;cAClD,CAAC;4BACD,OAAO,KAAK,IAAI;4BAChB,SAAS;gCACP,SAAS;gCACT,QAAQ;4BACV;;8CAEA,oWAAC;oCACC,WAAU;oCACV,OAAO,KAAK,IAAI;;sDAEhB,oWAAC;4CAAK,WAAU;sDAAY,KAAK,IAAI;;;;;;wCACpC,KAAK,KAAK,KAAK,gCACd,oWAAC,+IAAA,CAAA,UAAK;4CAAC,WAAW;4CAAM,WAAU;sDAAgB;;;;;;;;;;;;gCAGrD,CAAC,eAAe,KAAK,KAAK,KAAK,uBAC9B,oWAAC,wOAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;2BArBpB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;AA6B7B;IArFM;;QAcU,8XAAA,CAAA,iBAAc;;;MAdxB;;2DAuFS,oUAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 3971, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/store.tsx"], "sourcesContent": ["import {\n  createContext,\n  useContext,\n  useRef,\n} from 'react'\nimport {\n  create,\n  useStore as useZustandStore,\n} from 'zustand'\nimport type {\n  FileEntity,\n} from './types'\n\ntype Shape = {\n  files: FileEntity[]\n  setFiles: (files: FileEntity[]) => void\n}\n\nexport const createFileStore = (\n  value: FileEntity[] = [],\n  onChange?: (files: FileEntity[]) => void,\n) => {\n  return create<Shape>(set => ({\n    files: value ? [...value] : [],\n    setFiles: (files) => {\n      set({ files })\n      onChange?.(files)\n    },\n  }))\n}\n\ntype FileStore = ReturnType<typeof createFileStore>\nexport const FileContext = createContext<FileStore | null>(null)\n\nexport function useStore<T>(selector: (state: Shape) => T): T {\n  const store = useContext(FileContext)\n  if (!store)\n    throw new Error('Missing FileContext.Provider in the tree')\n\n  return useZustandStore(store, selector)\n}\n\nexport const useFileStore = () => {\n  return useContext(FileContext)!\n}\n\ntype FileProviderProps = {\n  children: React.ReactNode\n  value?: FileEntity[]\n  onChange?: (files: FileEntity[]) => void\n}\nexport const FileContextProvider = ({\n  children,\n  value,\n  onChange,\n}: FileProviderProps) => {\n  const storeRef = useRef<FileStore | undefined>(undefined)\n\n  if (!storeRef.current)\n    storeRef.current = createFileStore(value, onChange)\n\n  return (\n    <FileContext.Provider value={storeRef.current}>\n      {children}\n    </FileContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAKA;;;;;AAaO,MAAM,kBAAkB,CAC7B,QAAsB,EAAE,EACxB;IAEA,OAAO,CAAA,GAAA,qVAAA,CAAA,SAAM,AAAD,EAAS,CAAA,MAAO,CAAC;YAC3B,OAAO,QAAQ;mBAAI;aAAM,GAAG,EAAE;YAC9B,UAAU,CAAC;gBACT,IAAI;oBAAE;gBAAM;gBACZ,WAAW;YACb;QACF,CAAC;AACH;AAGO,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,gBAAa,AAAD,EAAoB;AAEpD,SAAS,SAAY,QAA6B;;IACvD,MAAM,QAAQ,CAAA,GAAA,oUAAA,CAAA,aAAU,AAAD,EAAE;IACzB,IAAI,CAAC,OACH,MAAM,IAAI,MAAM;IAElB,OAAO,CAAA,GAAA,qVAAA,CAAA,WAAe,AAAD,EAAE,OAAO;AAChC;GANgB;;QAKP,qVAAA,CAAA,WAAe;;;AAGjB,MAAM,eAAe;;IAC1B,OAAO,CAAA,GAAA,oUAAA,CAAA,aAAU,AAAD,EAAE;AACpB;IAFa;AASN,MAAM,sBAAsB,CAAC,EAClC,QAAQ,EACR,KAAK,EACL,QAAQ,EACU;;IAClB,MAAM,WAAW,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAyB;IAE/C,IAAI,CAAC,SAAS,OAAO,EACnB,SAAS,OAAO,GAAG,gBAAgB,OAAO;IAE5C,qBACE,oWAAC,YAAY,QAAQ;QAAC,OAAO,SAAS,OAAO;kBAC1C;;;;;;AAGP;IAfa;KAAA", "debugId": null}}, {"offset": {"line": 4041, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/types.ts"], "sourcesContent": ["import type { TransferMethod } from '@/types/app'\n\nexport enum FileAppearanceTypeEnum {\n  image = 'image',\n  video = 'video',\n  audio = 'audio',\n  document = 'document',\n  code = 'code',\n  pdf = 'pdf',\n  markdown = 'markdown',\n  excel = 'excel',\n  word = 'word',\n  ppt = 'ppt',\n  gif = 'gif',\n  custom = 'custom',\n}\n\nexport type FileAppearanceType = keyof typeof FileAppearanceTypeEnum\n\nexport type FileEntity = {\n  id: string\n  name: string\n  size: number\n  type: string\n  progress: number\n  transferMethod: TransferMethod\n  supportFileType: string\n  originalFile?: File\n  uploadedId?: string\n  base64Url?: string\n  url?: string\n  isRemote?: boolean\n}\n"], "names": [], "mappings": ";;;AAEO,IAAA,AAAK,gDAAA;;;;;;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 4068, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/prompt-editor/constants.tsx"], "sourcesContent": ["import { SupportUploadFileTypes, type ValueSelector } from '../../workflow/types'\n\nexport const CONTEXT_PLACEHOLDER_TEXT = '{{#context#}}'\nexport const HISTORY_PLACEHOLDER_TEXT = '{{#histories#}}'\nexport const QUERY_PLACEHOLDER_TEXT = '{{#query#}}'\nexport const PRE_PROMPT_PLACEHOLDER_TEXT = '{{#pre_prompt#}}'\nexport const UPDATE_DATASETS_EVENT_EMITTER = 'prompt-editor-context-block-update-datasets'\nexport const UPDATE_HISTORY_EVENT_EMITTER = 'prompt-editor-history-block-update-role'\n\nexport const checkHasContextBlock = (text: string) => {\n  if (!text)\n    return false\n  return text.includes(CONTEXT_PLACEHOLDER_TEXT)\n}\n\nexport const checkHasHistoryBlock = (text: string) => {\n  if (!text)\n    return false\n  return text.includes(HISTORY_PLACEHOLDER_TEXT)\n}\n\nexport const checkHasQueryBlock = (text: string) => {\n  if (!text)\n    return false\n  return text.includes(QUERY_PLACEHOLDER_TEXT)\n}\n\n/*\n* {{#1711617514996.name#}} => [1711617514996, name]\n* {{#1711617514996.sys.query#}} => [sys, query]\n*/\nexport const getInputVars = (text: string): ValueSelector[] => {\n  if (!text)\n    return []\n\n  const allVars = text.match(/{{#([^#]*)#}}/g)\n  if (allVars && allVars?.length > 0) {\n    // {{#context#}}, {{#query#}} is not input vars\n    const inputVars = allVars\n      .filter(item => item.includes('.'))\n      .map((item) => {\n        const valueSelector = item.replace('{{#', '').replace('#}}', '').split('.')\n        if (valueSelector[1] === 'sys' && /^\\d+$/.test(valueSelector[0]))\n          return valueSelector.slice(1)\n\n        return valueSelector\n      })\n    return inputVars\n  }\n  return []\n}\n\nexport const FILE_EXTS: Record<string, string[]> = {\n  [SupportUploadFileTypes.image]: ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],\n  [SupportUploadFileTypes.document]: ['TXT', 'MD', 'MDX', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOC', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],\n  [SupportUploadFileTypes.audio]: ['MP3', 'M4A', 'WAV', 'AMR', 'MPGA'],\n  [SupportUploadFileTypes.video]: ['MP4', 'MOV', 'MPEG', 'WEBM'],\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAEO,MAAM,2BAA2B;AACjC,MAAM,2BAA2B;AACjC,MAAM,yBAAyB;AAC/B,MAAM,8BAA8B;AACpC,MAAM,gCAAgC;AACtC,MAAM,+BAA+B;AAErC,MAAM,uBAAuB,CAAC;IACnC,IAAI,CAAC,MACH,OAAO;IACT,OAAO,KAAK,QAAQ,CAAC;AACvB;AAEO,MAAM,uBAAuB,CAAC;IACnC,IAAI,CAAC,MACH,OAAO;IACT,OAAO,KAAK,QAAQ,CAAC;AACvB;AAEO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,MACH,OAAO;IACT,OAAO,KAAK,QAAQ,CAAC;AACvB;AAMO,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,MACH,OAAO,EAAE;IAEX,MAAM,UAAU,KAAK,KAAK,CAAC;IAC3B,IAAI,WAAW,SAAS,SAAS,GAAG;QAClC,+CAA+C;QAC/C,MAAM,YAAY,QACf,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,MAC7B,GAAG,CAAC,CAAC;YACJ,MAAM,gBAAgB,KAAK,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC;YACvE,IAAI,aAAa,CAAC,EAAE,KAAK,SAAS,QAAQ,IAAI,CAAC,aAAa,CAAC,EAAE,GAC7D,OAAO,cAAc,KAAK,CAAC;YAE7B,OAAO;QACT;QACF,OAAO;IACT;IACA,OAAO,EAAE;AACX;AAEO,MAAM,YAAsC;IACjD,CAAC,uBAAuB,KAAK,CAAC,EAAE;QAAC;QAAO;QAAQ;QAAO;QAAO;QAAQ;KAAM;IAC5E,CAAC,uBAAuB,QAAQ,CAAC,EAAE;QAAC;QAAO;QAAM;QAAO;QAAY;QAAO;QAAQ;QAAQ;QAAO;QAAO;QAAQ;QAAO;QAAO;QAAO;QAAQ;QAAO;QAAO;KAAO;IACnK,CAAC,uBAAuB,KAAK,CAAC,EAAE;QAAC;QAAO;QAAO;QAAO;QAAO;KAAO;IACpE,CAAC,uBAAuB,KAAK,CAAC,EAAE;QAAC;QAAO;QAAO;QAAQ;KAAO;AAChE", "debugId": null}}, {"offset": {"line": 4170, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/utils.ts"], "sourcesContent": ["import mime from 'mime'\nimport { FileAppearanceTypeEnum } from './types'\nimport type { FileEntity } from './types'\nimport { upload } from '@/service/base'\nimport { FILE_EXTS } from '@/components/base/prompt-editor/constants'\nimport { SupportUploadFileTypes } from '@/components/workflow/types'\nimport type { FileResponse } from '@/types/workflow'\nimport { TransferMethod } from '@/types/app'\n\ntype FileUploadParams = {\n  file: File\n  onProgressCallback: (progress: number) => void\n  onSuccessCallback: (res: { id: string }) => void\n  onErrorCallback: () => void\n}\ntype FileUpload = (v: FileUploadParams, isPublic?: boolean, url?: string) => void\nexport const fileUpload: FileUpload = ({\n  file,\n  onProgressCallback,\n  onSuccessCallback,\n  onErrorCallback,\n}, isPublic, url) => {\n  const formData = new FormData()\n  formData.append('file', file)\n  const onProgress = (e: ProgressEvent) => {\n    if (e.lengthComputable) {\n      const percent = Math.floor(e.loaded / e.total * 100)\n      onProgressCallback(percent)\n    }\n  }\n\n  upload({\n    xhr: new XMLHttpRequest(),\n    data: formData,\n    onprogress: onProgress,\n  }, isPublic, url)\n    .then((res: { id: string }) => {\n      onSuccessCallback(res)\n    })\n    .catch(() => {\n      onErrorCallback()\n    })\n}\n\nexport const getFileExtension = (fileName: string, fileMimetype: string, isRemote?: boolean) => {\n  let extension = ''\n  if (fileMimetype)\n    extension = mime.getExtension(fileMimetype) || ''\n\n  if (fileName && !extension) {\n    const fileNamePair = fileName.split('.')\n    const fileNamePairLength = fileNamePair.length\n\n    if (fileNamePairLength > 1)\n      extension = fileNamePair[fileNamePairLength - 1]\n    else\n      extension = ''\n  }\n\n  if (isRemote)\n    extension = ''\n\n  return extension\n}\n\nexport const getFileAppearanceType = (fileName: string, fileMimetype: string) => {\n  const extension = getFileExtension(fileName, fileMimetype)\n\n  if (extension === 'gif')\n    return FileAppearanceTypeEnum.gif\n\n  if (FILE_EXTS.image.includes(extension.toUpperCase()))\n    return FileAppearanceTypeEnum.image\n\n  if (FILE_EXTS.video.includes(extension.toUpperCase()))\n    return FileAppearanceTypeEnum.video\n\n  if (FILE_EXTS.audio.includes(extension.toUpperCase()))\n    return FileAppearanceTypeEnum.audio\n\n  if (extension === 'html')\n    return FileAppearanceTypeEnum.code\n\n  if (extension === 'pdf')\n    return FileAppearanceTypeEnum.pdf\n\n  if (extension === 'md' || extension === 'markdown' || extension === 'mdx')\n    return FileAppearanceTypeEnum.markdown\n\n  if (extension === 'xlsx' || extension === 'xls')\n    return FileAppearanceTypeEnum.excel\n\n  if (extension === 'docx' || extension === 'doc')\n    return FileAppearanceTypeEnum.word\n\n  if (extension === 'pptx' || extension === 'ppt')\n    return FileAppearanceTypeEnum.ppt\n\n  if (FILE_EXTS.document.includes(extension.toUpperCase()))\n    return FileAppearanceTypeEnum.document\n\n  return FileAppearanceTypeEnum.custom\n}\n\nexport const getSupportFileType = (fileName: string, fileMimetype: string, isCustom?: boolean) => {\n  if (isCustom)\n    return SupportUploadFileTypes.custom\n\n  const extension = getFileExtension(fileName, fileMimetype)\n  for (const key in FILE_EXTS) {\n    if ((FILE_EXTS[key]).includes(extension.toUpperCase()))\n      return key\n  }\n\n  return ''\n}\n\nexport const getProcessedFiles = (files: FileEntity[]) => {\n  return files.filter(file => file.progress !== -1).map(fileItem => ({\n    type: fileItem.supportFileType,\n    transfer_method: fileItem.transferMethod,\n    url: fileItem.url || '',\n    upload_file_id: fileItem.uploadedId || '',\n  }))\n}\n\nexport const getProcessedFilesFromResponse = (files: FileResponse[]) => {\n  return files.map((fileItem) => {\n    return {\n      id: fileItem.related_id,\n      name: fileItem.filename,\n      size: fileItem.size || 0,\n      type: fileItem.mime_type,\n      progress: 100,\n      transferMethod: fileItem.transfer_method,\n      supportFileType: fileItem.type,\n      uploadedId: fileItem.upload_file_id || fileItem.related_id,\n      url: fileItem.url,\n    }\n  })\n}\n\nexport const getFileNameFromUrl = (url: string) => {\n  const urlParts = url.split('/')\n  return urlParts[urlParts.length - 1] || ''\n}\n\nexport const getSupportFileExtensionList = (allowFileTypes: string[], allowFileExtensions: string[]) => {\n  if (allowFileTypes.includes(SupportUploadFileTypes.custom))\n    return allowFileExtensions.map(item => item.slice(1).toUpperCase())\n\n  return allowFileTypes.map(type => FILE_EXTS[type]).flat()\n}\n\nexport const isAllowedFileExtension = (fileName: string, fileMimetype: string, allowFileTypes: string[], allowFileExtensions: string[]) => {\n  return getSupportFileExtensionList(allowFileTypes, allowFileExtensions).includes(getFileExtension(fileName, fileMimetype).toUpperCase())\n}\n\nexport const getFilesInLogs = (rawData: any) => {\n  const result = Object.keys(rawData || {}).map((key) => {\n    if (typeof rawData[key] === 'object' && rawData[key]?.dify_model_identity === '__dify__file__') {\n      return {\n        varName: key,\n        list: getProcessedFilesFromResponse([rawData[key]]),\n      }\n    }\n    if (Array.isArray(rawData[key]) && rawData[key].some(item => item?.dify_model_identity === '__dify__file__')) {\n      return {\n        varName: key,\n        list: getProcessedFilesFromResponse(rawData[key]),\n      }\n    }\n    return undefined\n  }).filter(Boolean)\n  return result\n}\n\nexport const fileIsUploaded = (file: FileEntity) => {\n  if (file.uploadedId)\n    return true\n\n  if (file.transferMethod === TransferMethod.remote_url && file.progress === 100)\n    return true\n}\n\nexport const downloadFile = (url: string, filename: string) => {\n  const anchor = document.createElement('a')\n  anchor.href = url\n  anchor.download = filename\n  anchor.style.display = 'none'\n  anchor.target = '_blank'\n  anchor.title = filename\n  document.body.appendChild(anchor)\n  anchor.click()\n  document.body.removeChild(anchor)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AACA;;;;;;AAGA;;;;;;;;;;;;;;;;;AAYO,MAAM,aAAyB,CAAC,EACrC,IAAI,EACJ,kBAAkB,EAClB,iBAAiB,EACjB,eAAe,EAChB,EAAE,UAAU;IACX,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,QAAQ;IACxB,MAAM,aAAa,CAAC;QAClB,IAAI,EAAE,gBAAgB,EAAE;YACtB,MAAM,UAAU,KAAK,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,GAAG;YAChD,mBAAmB;QACrB;IACF;IAEA,OAAO;QACL,KAAK,IAAI;QACT,MAAM;QACN,YAAY;IACd,GAAG,UAAU,KACV,IAAI,CAAC,CAAC;QACL,kBAAkB;IACpB,GACC,KAAK,CAAC;QACL;IACF;AACJ;AAEO,MAAM,mBAAmB,CAAC,UAAkB,cAAsB;IACvE,IAAI,YAAY;IAChB,IAAI,cACF,YAAY,KAAK,YAAY,CAAC,iBAAiB;IAEjD,IAAI,YAAY,CAAC,WAAW;QAC1B,MAAM,eAAe,SAAS,KAAK,CAAC;QACpC,MAAM,qBAAqB,aAAa,MAAM;QAE9C,IAAI,qBAAqB,GACvB,YAAY,YAAY,CAAC,qBAAqB,EAAE;aAEhD,YAAY;IAChB;IAEA,IAAI,UACF,YAAY;IAEd,OAAO;AACT;AAEO,MAAM,wBAAwB,CAAC,UAAkB;IACtD,MAAM,YAAY,iBAAiB,UAAU;IAE7C,IAAI,cAAc,OAChB,OAAO,yJAAA,CAAA,yBAAsB,CAAC,GAAG;IAEnC,IAAI,8JAAA,CAAA,YAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,WAAW,KAChD,OAAO,yJAAA,CAAA,yBAAsB,CAAC,KAAK;IAErC,IAAI,8JAAA,CAAA,YAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,WAAW,KAChD,OAAO,yJAAA,CAAA,yBAAsB,CAAC,KAAK;IAErC,IAAI,8JAAA,CAAA,YAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,WAAW,KAChD,OAAO,yJAAA,CAAA,yBAAsB,CAAC,KAAK;IAErC,IAAI,cAAc,QAChB,OAAO,yJAAA,CAAA,yBAAsB,CAAC,IAAI;IAEpC,IAAI,cAAc,OAChB,OAAO,yJAAA,CAAA,yBAAsB,CAAC,GAAG;IAEnC,IAAI,cAAc,QAAQ,cAAc,cAAc,cAAc,OAClE,OAAO,yJAAA,CAAA,yBAAsB,CAAC,QAAQ;IAExC,IAAI,cAAc,UAAU,cAAc,OACxC,OAAO,yJAAA,CAAA,yBAAsB,CAAC,KAAK;IAErC,IAAI,cAAc,UAAU,cAAc,OACxC,OAAO,yJAAA,CAAA,yBAAsB,CAAC,IAAI;IAEpC,IAAI,cAAc,UAAU,cAAc,OACxC,OAAO,yJAAA,CAAA,yBAAsB,CAAC,GAAG;IAEnC,IAAI,8JAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,WAAW,KACnD,OAAO,yJAAA,CAAA,yBAAsB,CAAC,QAAQ;IAExC,OAAO,yJAAA,CAAA,yBAAsB,CAAC,MAAM;AACtC;AAEO,MAAM,qBAAqB,CAAC,UAAkB,cAAsB;IACzE,IAAI,UACF,OAAO,uBAAuB,MAAM;IAEtC,MAAM,YAAY,iBAAiB,UAAU;IAC7C,IAAK,MAAM,OAAO,8JAAA,CAAA,YAAS,CAAE;QAC3B,IAAI,AAAC,8JAAA,CAAA,YAAS,CAAC,IAAI,CAAE,QAAQ,CAAC,UAAU,WAAW,KACjD,OAAO;IACX;IAEA,OAAO;AACT;AAEO,MAAM,oBAAoB,CAAC;IAChC,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,CAAA,WAAY,CAAC;YACjE,MAAM,SAAS,eAAe;YAC9B,iBAAiB,SAAS,cAAc;YACxC,KAAK,SAAS,GAAG,IAAI;YACrB,gBAAgB,SAAS,UAAU,IAAI;QACzC,CAAC;AACH;AAEO,MAAM,gCAAgC,CAAC;IAC5C,OAAO,MAAM,GAAG,CAAC,CAAC;QAChB,OAAO;YACL,IAAI,SAAS,UAAU;YACvB,MAAM,SAAS,QAAQ;YACvB,MAAM,SAAS,IAAI,IAAI;YACvB,MAAM,SAAS,SAAS;YACxB,UAAU;YACV,gBAAgB,SAAS,eAAe;YACxC,iBAAiB,SAAS,IAAI;YAC9B,YAAY,SAAS,cAAc,IAAI,SAAS,UAAU;YAC1D,KAAK,SAAS,GAAG;QACnB;IACF;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,WAAW,IAAI,KAAK,CAAC;IAC3B,OAAO,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,IAAI;AAC1C;AAEO,MAAM,8BAA8B,CAAC,gBAA0B;IACpE,IAAI,eAAe,QAAQ,CAAC,uBAAuB,MAAM,GACvD,OAAO,oBAAoB,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,GAAG,WAAW;IAElE,OAAO,eAAe,GAAG,CAAC,CAAA,OAAQ,8JAAA,CAAA,YAAS,CAAC,KAAK,EAAE,IAAI;AACzD;AAEO,MAAM,yBAAyB,CAAC,UAAkB,cAAsB,gBAA0B;IACvG,OAAO,4BAA4B,gBAAgB,qBAAqB,QAAQ,CAAC,iBAAiB,UAAU,cAAc,WAAW;AACvI;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,SAAS,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC;QAC7C,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,YAAY,OAAO,CAAC,IAAI,EAAE,wBAAwB,kBAAkB;YAC9F,OAAO;gBACL,SAAS;gBACT,MAAM,8BAA8B;oBAAC,OAAO,CAAC,IAAI;iBAAC;YACpD;QACF;QACA,IAAI,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,OAAQ,MAAM,wBAAwB,mBAAmB;YAC5G,OAAO;gBACL,SAAS;gBACT,MAAM,8BAA8B,OAAO,CAAC,IAAI;YAClD;QACF;QACA,OAAO;IACT,GAAG,MAAM,CAAC;IACV,OAAO;AACT;AAEO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,KAAK,UAAU,EACjB,OAAO;IAET,IAAI,KAAK,cAAc,KAAK,eAAe,UAAU,IAAI,KAAK,QAAQ,KAAK,KACzE,OAAO;AACX;AAEO,MAAM,eAAe,CAAC,KAAa;IACxC,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,OAAO,IAAI,GAAG;IACd,OAAO,QAAQ,GAAG;IAClB,OAAO,KAAK,CAAC,OAAO,GAAG;IACvB,OAAO,MAAM,GAAG;IAChB,OAAO,KAAK,GAAG;IACf,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,OAAO,KAAK;IACZ,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 4344, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/constants.ts"], "sourcesContent": ["// fallback for file size limit of dify_config\nexport const IMG_SIZE_LIMIT = 10 * 1024 * 1024\nexport const FILE_SIZE_LIMIT = 15 * 1024 * 1024\nexport const AUDIO_SIZE_LIMIT = 50 * 1024 * 1024\nexport const VIDEO_SIZE_LIMIT = 100 * 1024 * 1024\nexport const MAX_FILE_UPLOAD_LIMIT = 10\n\nexport const FILE_URL_REGEX = /^(https?|ftp):\\/\\//\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;;;;AACvC,MAAM,iBAAiB,KAAK,OAAO;AACnC,MAAM,kBAAkB,KAAK,OAAO;AACpC,MAAM,mBAAmB,KAAK,OAAO;AACrC,MAAM,mBAAmB,MAAM,OAAO;AACtC,MAAM,wBAAwB;AAE9B,MAAM,iBAAiB", "debugId": null}}, {"offset": {"line": 4368, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/toast/index.tsx"], "sourcesContent": ["'use client'\nimport type { ReactNode } from 'react'\nimport React, { useEffect, useState } from 'react'\nimport { createRoot } from 'react-dom/client'\nimport {\n  Ri<PERSON>lertFill,\n  RiCheckboxCircleFill,\n  RiCloseLine,\n  RiErrorWarningFill,\n  RiInformation2Fill,\n} from '@remixicon/react'\nimport { createContext, useContext } from 'use-context-selector'\nimport ActionButton from '@/components/base/action-button'\nimport classNames from '@/utils/classnames'\nimport { noop } from 'lodash-es'\n\nexport type IToastProps = {\n  type?: 'success' | 'error' | 'warning' | 'info'\n  size?: 'md' | 'sm'\n  duration?: number\n  message: string\n  children?: ReactNode\n  onClose?: () => void\n  className?: string\n  customComponent?: ReactNode\n}\ntype IToastContext = {\n  notify: (props: IToastProps) => void\n  close: () => void\n}\n\nexport const ToastContext = createContext<IToastContext>({} as IToastContext)\nexport const useToastContext = () => useContext(ToastContext)\nconst Toast = ({\n  type = 'info',\n  size = 'md',\n  message,\n  children,\n  className,\n  customComponent,\n}: IToastProps) => {\n  const { close } = useToastContext()\n  // sometimes message is react node array. Not handle it.\n  if (typeof message !== 'string')\n    return null\n\n  return <div className={classNames(\n    className,\n    'fixed w-[360px] rounded-xl my-4 mx-8 flex-grow z-[9999] overflow-hidden',\n    size === 'md' ? 'p-3' : 'p-2',\n    'border border-components-panel-border-subtle bg-components-panel-bg-blur shadow-sm',\n    'top-0',\n    'right-0',\n  )}>\n    <div className={`absolute inset-0 -z-10 opacity-40 ${\n      (type === 'success' && 'bg-toast-success-bg')\n      || (type === 'warning' && 'bg-toast-warning-bg')\n      || (type === 'error' && 'bg-toast-error-bg')\n      || (type === 'info' && 'bg-toast-info-bg')\n    }`}\n    />\n    <div className={`flex ${size === 'md' ? 'gap-1' : 'gap-0.5'}`}>\n      <div className={`flex items-center justify-center ${size === 'md' ? 'p-0.5' : 'p-1'}`}>\n        {type === 'success' && <RiCheckboxCircleFill className={`${size === 'md' ? 'h-5 w-5' : 'h-4 w-4'} text-text-success`} aria-hidden=\"true\" />}\n        {type === 'error' && <RiErrorWarningFill className={`${size === 'md' ? 'h-5 w-5' : 'h-4 w-4'} text-text-destructive`} aria-hidden=\"true\" />}\n        {type === 'warning' && <RiAlertFill className={`${size === 'md' ? 'h-5 w-5' : 'h-4 w-4'} text-text-warning-secondary`} aria-hidden=\"true\" />}\n        {type === 'info' && <RiInformation2Fill className={`${size === 'md' ? 'h-5 w-5' : 'h-4 w-4'} text-text-accent`} aria-hidden=\"true\" />}\n      </div>\n      <div className={`flex py-1 ${size === 'md' ? 'px-1' : 'px-0.5'} grow flex-col items-start gap-1`}>\n        <div className='flex items-center gap-1'>\n          <div className='system-sm-semibold text-text-primary [word-break:break-word]'>{message}</div>\n          {customComponent}\n        </div>\n        {children && <div className='system-xs-regular text-text-secondary'>\n          {children}\n        </div>\n        }\n      </div>\n      {close\n        && (<ActionButton className='z-[1000]' onClick={close}>\n          <RiCloseLine className='h-4 w-4 shrink-0 text-text-tertiary' />\n        </ActionButton>)\n      }\n    </div>\n  </div>\n}\n\nexport const ToastProvider = ({\n  children,\n}: {\n  children: ReactNode\n}) => {\n  const placeholder: IToastProps = {\n    type: 'info',\n    message: 'Toast message',\n    duration: 6000,\n  }\n  const [params, setParams] = React.useState<IToastProps>(placeholder)\n  const defaultDuring = (params.type === 'success' || params.type === 'info') ? 3000 : 6000\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    if (mounted) {\n      setTimeout(() => {\n        setMounted(false)\n      }, params.duration || defaultDuring)\n    }\n  }, [defaultDuring, mounted, params.duration])\n\n  return <ToastContext.Provider value={{\n    notify: (props) => {\n      setMounted(true)\n      setParams(props)\n    },\n    close: () => setMounted(false),\n  }}>\n    {mounted && <Toast {...params} />}\n    {children}\n  </ToastContext.Provider>\n}\n\nToast.notify = ({\n  type,\n  size = 'md',\n  message,\n  duration,\n  className,\n  customComponent,\n  onClose,\n}: Pick<IToastProps, 'type' | 'size' | 'message' | 'duration' | 'className' | 'customComponent' | 'onClose'>) => {\n  const defaultDuring = (type === 'success' || type === 'info') ? 3000 : 6000\n  if (typeof window === 'object') {\n    const holder = document.createElement('div')\n    const root = createRoot(holder)\n\n    root.render(\n      <ToastContext.Provider value={{\n        notify: noop,\n        close: () => {\n          if (holder) {\n            root.unmount()\n            holder.remove()\n          }\n          onClose?.()\n        },\n      }}>\n        <Toast type={type} size={size} message={message} duration={duration} className={className} customComponent={customComponent} />\n      </ToastContext.Provider>,\n    )\n    document.body.appendChild(holder)\n    setTimeout(() => {\n      if (holder) {\n        root.unmount()\n        holder.remove()\n      }\n      onClose?.()\n    }, duration || defaultDuring)\n  }\n}\n\nexport default Toast\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAOA;AACA;AACA;AACA;;;AAdA;;;;;;;;AA+BO,MAAM,eAAe,CAAA,GAAA,qRAAA,CAAA,gBAAa,AAAD,EAAiB,CAAC;AACnD,MAAM,kBAAkB;;IAAM,OAAA,CAAA,GAAA,qRAAA,CAAA,aAAU,AAAD,EAAE;AAAY;GAA/C;AACb,MAAM,QAAQ,CAAC,EACb,OAAO,MAAM,EACb,OAAO,IAAI,EACX,OAAO,EACP,QAAQ,EACR,SAAS,EACT,eAAe,EACH;;IACZ,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,wDAAwD;IACxD,IAAI,OAAO,YAAY,UACrB,OAAO;IAET,qBAAO,oWAAC;QAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAU,AAAD,EAC9B,WACA,2EACA,SAAS,OAAO,QAAQ,OACxB,sFACA,SACA;;0BAEA,oWAAC;gBAAI,WAAW,CAAC,kCAAkC,EACjD,AAAC,SAAS,aAAa,yBACnB,SAAS,aAAa,yBACtB,SAAS,WAAW,uBACpB,SAAS,UAAU,oBACvB;;;;;;0BAEF,oWAAC;gBAAI,WAAW,CAAC,KAAK,EAAE,SAAS,OAAO,UAAU,WAAW;;kCAC3D,oWAAC;wBAAI,WAAW,CAAC,iCAAiC,EAAE,SAAS,OAAO,UAAU,OAAO;;4BAClF,SAAS,2BAAa,oWAAC,wOAAA,CAAA,uBAAoB;gCAAC,WAAW,GAAG,SAAS,OAAO,YAAY,UAAU,kBAAkB,CAAC;gCAAE,eAAY;;;;;;4BACjI,SAAS,yBAAW,oWAAC,wOAAA,CAAA,qBAAkB;gCAAC,WAAW,GAAG,SAAS,OAAO,YAAY,UAAU,sBAAsB,CAAC;gCAAE,eAAY;;;;;;4BACjI,SAAS,2BAAa,oWAAC,wOAAA,CAAA,cAAW;gCAAC,WAAW,GAAG,SAAS,OAAO,YAAY,UAAU,4BAA4B,CAAC;gCAAE,eAAY;;;;;;4BAClI,SAAS,wBAAU,oWAAC,wOAAA,CAAA,qBAAkB;gCAAC,WAAW,GAAG,SAAS,OAAO,YAAY,UAAU,iBAAiB,CAAC;gCAAE,eAAY;;;;;;;;;;;;kCAE9H,oWAAC;wBAAI,WAAW,CAAC,UAAU,EAAE,SAAS,OAAO,SAAS,SAAS,gCAAgC,CAAC;;0CAC9F,oWAAC;gCAAI,WAAU;;kDACb,oWAAC;wCAAI,WAAU;kDAAgE;;;;;;oCAC9E;;;;;;;4BAEF,0BAAY,oWAAC;gCAAI,WAAU;0CACzB;;;;;;;;;;;;oBAIJ,uBACK,oWAAC,0JAAA,CAAA,UAAY;wBAAC,WAAU;wBAAW,SAAS;kCAC9C,cAAA,oWAAC,wOAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKjC;IApDM;;QAQc;;;KARd;AAsDC,MAAM,gBAAgB,CAAC,EAC5B,QAAQ,EAGT;;IACC,MAAM,cAA2B;QAC/B,MAAM;QACN,SAAS;QACT,UAAU;IACZ;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,oUAAA,CAAA,UAAK,CAAC,QAAQ,CAAc;IACxD,MAAM,gBAAgB,AAAC,OAAO,IAAI,KAAK,aAAa,OAAO,IAAI,KAAK,SAAU,OAAO;IACrF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,SAAS;gBACX;+CAAW;wBACT,WAAW;oBACb;8CAAG,OAAO,QAAQ,IAAI;YACxB;QACF;kCAAG;QAAC;QAAe;QAAS,OAAO,QAAQ;KAAC;IAE5C,qBAAO,oWAAC,aAAa,QAAQ;QAAC,OAAO;YACnC,QAAQ,CAAC;gBACP,WAAW;gBACX,UAAU;YACZ;YACA,OAAO,IAAM,WAAW;QAC1B;;YACG,yBAAW,oWAAC;gBAAO,GAAG,MAAM;;;;;;YAC5B;;;;;;;AAEL;IAhCa;MAAA;AAkCb,MAAM,MAAM,GAAG,CAAC,EACd,IAAI,EACJ,OAAO,IAAI,EACX,OAAO,EACP,QAAQ,EACR,SAAS,EACT,eAAe,EACf,OAAO,EACmG;IAC1G,MAAM,gBAAgB,AAAC,SAAS,aAAa,SAAS,SAAU,OAAO;IACvE,wCAAgC;QAC9B,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,MAAM,OAAO,CAAA,GAAA,4UAAA,CAAA,aAAU,AAAD,EAAE;QAExB,KAAK,MAAM,eACT,oWAAC,aAAa,QAAQ;YAAC,OAAO;gBAC5B,QAAQ,qOAAA,CAAA,OAAI;gBACZ,OAAO;oBACL,IAAI,QAAQ;wBACV,KAAK,OAAO;wBACZ,OAAO,MAAM;oBACf;oBACA;gBACF;YACF;sBACE,cAAA,oWAAC;gBAAM,MAAM;gBAAM,MAAM;gBAAM,SAAS;gBAAS,UAAU;gBAAU,WAAW;gBAAW,iBAAiB;;;;;;;;;;;QAGhH,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,WAAW;YACT,IAAI,QAAQ;gBACV,KAAK,OAAO;gBACZ,OAAO,MAAM;YACf;YACA;QACF,GAAG,YAAY;IACjB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 4633, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/hooks.ts"], "sourcesContent": ["import type { ClipboardEvent } from 'react'\nimport {\n  useCallback,\n  useState,\n} from 'react'\nimport { useParams } from 'next/navigation'\nimport produce from 'immer'\nimport { v4 as uuid4 } from 'uuid'\nimport { useTranslation } from 'react-i18next'\nimport type { FileEntity } from './types'\nimport { useFileStore } from './store'\nimport {\n  fileUpload,\n  getSupportFileType,\n  isAllowedFileExtension,\n} from './utils'\nimport {\n  AUDIO_SIZE_LIMIT,\n  FILE_SIZE_LIMIT,\n  IMG_SIZE_LIMIT,\n  MAX_FILE_UPLOAD_LIMIT,\n  VIDEO_SIZE_LIMIT,\n} from '@/components/base/file-uploader/constants'\nimport { useToastContext } from '@/components/base/toast'\nimport { TransferMethod } from '@/types/app'\nimport { SupportUploadFileTypes } from '@/components/workflow/types'\nimport type { FileUpload } from '@/components/base/features/types'\nimport { formatFileSize } from '@/utils/format'\nimport { uploadRemoteFileInfo } from '@/service/common'\nimport type { FileUploadConfigResponse } from '@/models/common'\nimport { noop } from 'lodash-es'\n\nexport const useFileSizeLimit = (fileUploadConfig?: FileUploadConfigResponse) => {\n  const imgSizeLimit = Number(fileUploadConfig?.image_file_size_limit) * 1024 * 1024 || IMG_SIZE_LIMIT\n  const docSizeLimit = Number(fileUploadConfig?.file_size_limit) * 1024 * 1024 || FILE_SIZE_LIMIT\n  const audioSizeLimit = Number(fileUploadConfig?.audio_file_size_limit) * 1024 * 1024 || AUDIO_SIZE_LIMIT\n  const videoSizeLimit = Number(fileUploadConfig?.video_file_size_limit) * 1024 * 1024 || VIDEO_SIZE_LIMIT\n  const maxFileUploadLimit = Number(fileUploadConfig?.workflow_file_upload_limit) || MAX_FILE_UPLOAD_LIMIT\n\n  return {\n    imgSizeLimit,\n    docSizeLimit,\n    audioSizeLimit,\n    videoSizeLimit,\n    maxFileUploadLimit,\n  }\n}\n\nexport const useFile = (fileConfig: FileUpload) => {\n  const { t } = useTranslation()\n  const { notify } = useToastContext()\n  const fileStore = useFileStore()\n  const params = useParams()\n  const { imgSizeLimit, docSizeLimit, audioSizeLimit, videoSizeLimit } = useFileSizeLimit(fileConfig.fileUploadConfig)\n\n  const checkSizeLimit = useCallback((fileType: string, fileSize: number) => {\n    switch (fileType) {\n      case SupportUploadFileTypes.image: {\n        if (fileSize > imgSizeLimit) {\n          notify({\n            type: 'error',\n            message: t('common.fileUploader.uploadFromComputerLimit', {\n              type: SupportUploadFileTypes.image,\n              size: formatFileSize(imgSizeLimit),\n            }),\n          })\n          return false\n        }\n        return true\n      }\n      case SupportUploadFileTypes.document: {\n        if (fileSize > docSizeLimit) {\n          notify({\n            type: 'error',\n            message: t('common.fileUploader.uploadFromComputerLimit', {\n              type: SupportUploadFileTypes.document,\n              size: formatFileSize(docSizeLimit),\n            }),\n          })\n          return false\n        }\n        return true\n      }\n      case SupportUploadFileTypes.audio: {\n        if (fileSize > audioSizeLimit) {\n          notify({\n            type: 'error',\n            message: t('common.fileUploader.uploadFromComputerLimit', {\n              type: SupportUploadFileTypes.audio,\n              size: formatFileSize(audioSizeLimit),\n            }),\n          })\n          return false\n        }\n        return true\n      }\n      case SupportUploadFileTypes.video: {\n        if (fileSize > videoSizeLimit) {\n          notify({\n            type: 'error',\n            message: t('common.fileUploader.uploadFromComputerLimit', {\n              type: SupportUploadFileTypes.video,\n              size: formatFileSize(videoSizeLimit),\n            }),\n          })\n          return false\n        }\n        return true\n      }\n      case SupportUploadFileTypes.custom: {\n        if (fileSize > docSizeLimit) {\n          notify({\n            type: 'error',\n            message: t('common.fileUploader.uploadFromComputerLimit', {\n              type: SupportUploadFileTypes.document,\n              size: formatFileSize(docSizeLimit),\n            }),\n          })\n          return false\n        }\n        return true\n      }\n      default: {\n        return true\n      }\n    }\n  }, [audioSizeLimit, docSizeLimit, imgSizeLimit, notify, t, videoSizeLimit])\n\n  const handleAddFile = useCallback((newFile: FileEntity) => {\n    const {\n      files,\n      setFiles,\n    } = fileStore.getState()\n\n    const newFiles = produce(files, (draft) => {\n      draft.push(newFile)\n    })\n    setFiles(newFiles)\n  }, [fileStore])\n\n  const handleUpdateFile = useCallback((newFile: FileEntity) => {\n    const {\n      files,\n      setFiles,\n    } = fileStore.getState()\n\n    const newFiles = produce(files, (draft) => {\n      const index = draft.findIndex(file => file.id === newFile.id)\n\n      if (index > -1)\n        draft[index] = newFile\n    })\n    setFiles(newFiles)\n  }, [fileStore])\n\n  const handleRemoveFile = useCallback((fileId: string) => {\n    const {\n      files,\n      setFiles,\n    } = fileStore.getState()\n\n    const newFiles = files.filter(file => file.id !== fileId)\n    setFiles(newFiles)\n  }, [fileStore])\n\n  const handleReUploadFile = useCallback((fileId: string) => {\n    const {\n      files,\n      setFiles,\n    } = fileStore.getState()\n    const index = files.findIndex(file => file.id === fileId)\n\n    if (index > -1) {\n      const uploadingFile = files[index]\n      const newFiles = produce(files, (draft) => {\n        draft[index].progress = 0\n      })\n      setFiles(newFiles)\n      fileUpload({\n        file: uploadingFile.originalFile!,\n        onProgressCallback: (progress) => {\n          handleUpdateFile({ ...uploadingFile, progress })\n        },\n        onSuccessCallback: (res) => {\n          handleUpdateFile({ ...uploadingFile, uploadedId: res.id, progress: 100 })\n        },\n        onErrorCallback: () => {\n          notify({ type: 'error', message: t('common.fileUploader.uploadFromComputerUploadError') })\n          handleUpdateFile({ ...uploadingFile, progress: -1 })\n        },\n      }, !!params.token)\n    }\n  }, [fileStore, notify, t, handleUpdateFile, params])\n\n  const startProgressTimer = useCallback((fileId: string) => {\n    const timer = setInterval(() => {\n      const files = fileStore.getState().files\n      const file = files.find(file => file.id === fileId)\n\n      if (file && file.progress < 80 && file.progress >= 0)\n        handleUpdateFile({ ...file, progress: file.progress + 20 })\n      else\n        clearTimeout(timer)\n    }, 200)\n  }, [fileStore, handleUpdateFile])\n  const handleLoadFileFromLink = useCallback((url: string) => {\n    const allowedFileTypes = fileConfig.allowed_file_types\n\n    const uploadingFile = {\n      id: uuid4(),\n      name: url,\n      type: '',\n      size: 0,\n      progress: 0,\n      transferMethod: TransferMethod.remote_url,\n      supportFileType: '',\n      url,\n      isRemote: true,\n    }\n    handleAddFile(uploadingFile)\n    startProgressTimer(uploadingFile.id)\n\n    uploadRemoteFileInfo(url, !!params.token).then((res) => {\n      const newFile = {\n        ...uploadingFile,\n        type: res.mime_type,\n        size: res.size,\n        progress: 100,\n        supportFileType: getSupportFileType(res.name, res.mime_type, allowedFileTypes?.includes(SupportUploadFileTypes.custom)),\n        uploadedId: res.id,\n        url: res.url,\n      }\n      if (!isAllowedFileExtension(res.name, res.mime_type, fileConfig.allowed_file_types || [], fileConfig.allowed_file_extensions || [])) {\n        notify({ type: 'error', message: t('common.fileUploader.fileExtensionNotSupport') })\n        handleRemoveFile(uploadingFile.id)\n      }\n      if (!checkSizeLimit(newFile.supportFileType, newFile.size))\n        handleRemoveFile(uploadingFile.id)\n      else\n        handleUpdateFile(newFile)\n    }).catch(() => {\n      notify({ type: 'error', message: t('common.fileUploader.pasteFileLinkInvalid') })\n      handleRemoveFile(uploadingFile.id)\n    })\n  }, [checkSizeLimit, handleAddFile, handleUpdateFile, notify, t, handleRemoveFile, fileConfig?.allowed_file_types, fileConfig.allowed_file_extensions, startProgressTimer, params.token])\n\n  const handleLoadFileFromLinkSuccess = useCallback(noop, [])\n\n  const handleLoadFileFromLinkError = useCallback(noop, [])\n\n  const handleClearFiles = useCallback(() => {\n    const {\n      setFiles,\n    } = fileStore.getState()\n    setFiles([])\n  }, [fileStore])\n\n  const handleLocalFileUpload = useCallback((file: File) => {\n    if (!isAllowedFileExtension(file.name, file.type, fileConfig.allowed_file_types || [], fileConfig.allowed_file_extensions || [])) {\n      notify({ type: 'error', message: t('common.fileUploader.fileExtensionNotSupport') })\n      return\n    }\n    const allowedFileTypes = fileConfig.allowed_file_types\n    const fileType = getSupportFileType(file.name, file.type, allowedFileTypes?.includes(SupportUploadFileTypes.custom))\n    if (!checkSizeLimit(fileType, file.size))\n      return\n\n    const reader = new FileReader()\n    const isImage = file.type.startsWith('image')\n\n    reader.addEventListener(\n      'load',\n      () => {\n        const uploadingFile = {\n          id: uuid4(),\n          name: file.name,\n          type: file.type,\n          size: file.size,\n          progress: 0,\n          transferMethod: TransferMethod.local_file,\n          supportFileType: getSupportFileType(file.name, file.type, allowedFileTypes?.includes(SupportUploadFileTypes.custom)),\n          originalFile: file,\n          base64Url: isImage ? reader.result as string : '',\n        }\n        handleAddFile(uploadingFile)\n        fileUpload({\n          file: uploadingFile.originalFile,\n          onProgressCallback: (progress) => {\n            handleUpdateFile({ ...uploadingFile, progress })\n          },\n          onSuccessCallback: (res) => {\n            handleUpdateFile({ ...uploadingFile, uploadedId: res.id, progress: 100 })\n          },\n          onErrorCallback: () => {\n            notify({ type: 'error', message: t('common.fileUploader.uploadFromComputerUploadError') })\n            handleUpdateFile({ ...uploadingFile, progress: -1 })\n          },\n        }, !!params.token)\n      },\n      false,\n    )\n    reader.addEventListener(\n      'error',\n      () => {\n        notify({ type: 'error', message: t('common.fileUploader.uploadFromComputerReadError') })\n      },\n      false,\n    )\n    reader.readAsDataURL(file)\n  }, [checkSizeLimit, notify, t, handleAddFile, handleUpdateFile, params.token, fileConfig?.allowed_file_types, fileConfig?.allowed_file_extensions])\n\n  const handleClipboardPasteFile = useCallback((e: ClipboardEvent<HTMLTextAreaElement>) => {\n    const file = e.clipboardData?.files[0]\n    const text = e.clipboardData?.getData('text/plain')\n    if (file && !text) {\n      e.preventDefault()\n      handleLocalFileUpload(file)\n    }\n  }, [handleLocalFileUpload])\n\n  const [isDragActive, setIsDragActive] = useState(false)\n  const handleDragFileEnter = useCallback((e: React.DragEvent<HTMLElement>) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setIsDragActive(true)\n  }, [])\n\n  const handleDragFileOver = useCallback((e: React.DragEvent<HTMLElement>) => {\n    e.preventDefault()\n    e.stopPropagation()\n  }, [])\n\n  const handleDragFileLeave = useCallback((e: React.DragEvent<HTMLElement>) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setIsDragActive(false)\n  }, [])\n\n  const handleDropFile = useCallback((e: React.DragEvent<HTMLElement>) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setIsDragActive(false)\n\n    const file = e.dataTransfer.files[0]\n\n    if (file)\n      handleLocalFileUpload(file)\n  }, [handleLocalFileUpload])\n\n  return {\n    handleAddFile,\n    handleUpdateFile,\n    handleRemoveFile,\n    handleReUploadFile,\n    handleLoadFileFromLink,\n    handleLoadFileFromLinkSuccess,\n    handleLoadFileFromLinkError,\n    handleClearFiles,\n    handleLocalFileUpload,\n    handleClipboardPasteFile,\n    isDragActive,\n    handleDragFileEnter,\n    handleDragFileOver,\n    handleDragFileLeave,\n    handleDropFile,\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;AAIA;AACA;;;;;;AAEA;AAAA;AAEA;AACA;AAKA;AAOA;;;;;;;;;;;;;;;;;;;;;AAOA;;;;;;;;;;;;;;;;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,eAAe,OAAO,kBAAkB,yBAAyB,OAAO,QAAQ,6JAAA,CAAA,iBAAc;IACpG,MAAM,eAAe,OAAO,kBAAkB,mBAAmB,OAAO,QAAQ,6JAAA,CAAA,kBAAe;IAC/F,MAAM,iBAAiB,OAAO,kBAAkB,yBAAyB,OAAO,QAAQ,6JAAA,CAAA,mBAAgB;IACxG,MAAM,iBAAiB,OAAO,kBAAkB,yBAAyB,OAAO,QAAQ,6JAAA,CAAA,mBAAgB;IACxG,MAAM,qBAAqB,OAAO,kBAAkB,+BAA+B,6JAAA,CAAA,wBAAqB;IAExG,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,MAAM,UAAU,CAAC;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD;IACjC,MAAM,YAAY,CAAA,GAAA,0JAAA,CAAA,eAAY,AAAD;IAC7B,MAAM,SAAS,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,iBAAiB,WAAW,gBAAgB;IAEnH,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;+CAAE,CAAC,UAAkB;YACpD,OAAQ;gBACN,KAAK,uBAAuB,KAAK;oBAAE;wBACjC,IAAI,WAAW,cAAc;4BAC3B,OAAO;gCACL,MAAM;gCACN,SAAS,EAAE,+CAA+C;oCACxD,MAAM,uBAAuB,KAAK;oCAClC,MAAM,eAAe;gCACvB;4BACF;4BACA,OAAO;wBACT;wBACA,OAAO;oBACT;gBACA,KAAK,uBAAuB,QAAQ;oBAAE;wBACpC,IAAI,WAAW,cAAc;4BAC3B,OAAO;gCACL,MAAM;gCACN,SAAS,EAAE,+CAA+C;oCACxD,MAAM,uBAAuB,QAAQ;oCACrC,MAAM,eAAe;gCACvB;4BACF;4BACA,OAAO;wBACT;wBACA,OAAO;oBACT;gBACA,KAAK,uBAAuB,KAAK;oBAAE;wBACjC,IAAI,WAAW,gBAAgB;4BAC7B,OAAO;gCACL,MAAM;gCACN,SAAS,EAAE,+CAA+C;oCACxD,MAAM,uBAAuB,KAAK;oCAClC,MAAM,eAAe;gCACvB;4BACF;4BACA,OAAO;wBACT;wBACA,OAAO;oBACT;gBACA,KAAK,uBAAuB,KAAK;oBAAE;wBACjC,IAAI,WAAW,gBAAgB;4BAC7B,OAAO;gCACL,MAAM;gCACN,SAAS,EAAE,+CAA+C;oCACxD,MAAM,uBAAuB,KAAK;oCAClC,MAAM,eAAe;gCACvB;4BACF;4BACA,OAAO;wBACT;wBACA,OAAO;oBACT;gBACA,KAAK,uBAAuB,MAAM;oBAAE;wBAClC,IAAI,WAAW,cAAc;4BAC3B,OAAO;gCACL,MAAM;gCACN,SAAS,EAAE,+CAA+C;oCACxD,MAAM,uBAAuB,QAAQ;oCACrC,MAAM,eAAe;gCACvB;4BACF;4BACA,OAAO;wBACT;wBACA,OAAO;oBACT;gBACA;oBAAS;wBACP,OAAO;oBACT;YACF;QACF;8CAAG;QAAC;QAAgB;QAAc;QAAc;QAAQ;QAAG;KAAe;IAE1E,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;8CAAE,CAAC;YACjC,MAAM,EACJ,KAAK,EACL,QAAQ,EACT,GAAG,UAAU,QAAQ;YAEtB,MAAM,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAO,AAAD,EAAE;+DAAO,CAAC;oBAC/B,MAAM,IAAI,CAAC;gBACb;;YACA,SAAS;QACX;6CAAG;QAAC;KAAU;IAEd,MAAM,mBAAmB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YACpC,MAAM,EACJ,KAAK,EACL,QAAQ,EACT,GAAG,UAAU,QAAQ;YAEtB,MAAM,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAO,AAAD,EAAE;kEAAO,CAAC;oBAC/B,MAAM,QAAQ,MAAM,SAAS;gFAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,QAAQ,EAAE;;oBAE5D,IAAI,QAAQ,CAAC,GACX,KAAK,CAAC,MAAM,GAAG;gBACnB;;YACA,SAAS;QACX;gDAAG;QAAC;KAAU;IAEd,MAAM,mBAAmB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YACpC,MAAM,EACJ,KAAK,EACL,QAAQ,EACT,GAAG,UAAU,QAAQ;YAEtB,MAAM,WAAW,MAAM,MAAM;kEAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;YAClD,SAAS;QACX;gDAAG;QAAC;KAAU;IAEd,MAAM,qBAAqB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACtC,MAAM,EACJ,KAAK,EACL,QAAQ,EACT,GAAG,UAAU,QAAQ;YACtB,MAAM,QAAQ,MAAM,SAAS;iEAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;YAElD,IAAI,QAAQ,CAAC,GAAG;gBACd,MAAM,gBAAgB,KAAK,CAAC,MAAM;gBAClC,MAAM,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAO,AAAD,EAAE;wEAAO,CAAC;wBAC/B,KAAK,CAAC,MAAM,CAAC,QAAQ,GAAG;oBAC1B;;gBACA,SAAS;gBACT,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD,EAAE;oBACT,MAAM,cAAc,YAAY;oBAChC,kBAAkB;mEAAE,CAAC;4BACnB,iBAAiB;gCAAE,GAAG,aAAa;gCAAE;4BAAS;wBAChD;;oBACA,iBAAiB;mEAAE,CAAC;4BAClB,iBAAiB;gCAAE,GAAG,aAAa;gCAAE,YAAY,IAAI,EAAE;gCAAE,UAAU;4BAAI;wBACzE;;oBACA,eAAe;mEAAE;4BACf,OAAO;gCAAE,MAAM;gCAAS,SAAS,EAAE;4BAAqD;4BACxF,iBAAiB;gCAAE,GAAG,aAAa;gCAAE,UAAU,CAAC;4BAAE;wBACpD;;gBACF,GAAG,CAAC,CAAC,OAAO,KAAK;YACnB;QACF;kDAAG;QAAC;QAAW;QAAQ;QAAG;QAAkB;KAAO;IAEnD,MAAM,qBAAqB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACtC,MAAM,QAAQ;iEAAY;oBACxB,MAAM,QAAQ,UAAU,QAAQ,GAAG,KAAK;oBACxC,MAAM,OAAO,MAAM,IAAI;8EAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;oBAE5C,IAAI,QAAQ,KAAK,QAAQ,GAAG,MAAM,KAAK,QAAQ,IAAI,GACjD,iBAAiB;wBAAE,GAAG,IAAI;wBAAE,UAAU,KAAK,QAAQ,GAAG;oBAAG;yBAEzD,aAAa;gBACjB;gEAAG;QACL;kDAAG;QAAC;QAAW;KAAiB;IAChC,MAAM,yBAAyB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YAC1C,MAAM,mBAAmB,WAAW,kBAAkB;YAEtD,MAAM,gBAAgB;gBACpB,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,gBAAgB,eAAe,UAAU;gBACzC,iBAAiB;gBACjB;gBACA,UAAU;YACZ;YACA,cAAc;YACd,mBAAmB,cAAc,EAAE;YAEnC,qBAAqB,KAAK,CAAC,CAAC,OAAO,KAAK,EAAE,IAAI;+DAAC,CAAC;oBAC9C,MAAM,UAAU;wBACd,GAAG,aAAa;wBAChB,MAAM,IAAI,SAAS;wBACnB,MAAM,IAAI,IAAI;wBACd,UAAU;wBACV,iBAAiB,CAAA,GAAA,yJAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,IAAI,EAAE,IAAI,SAAS,EAAE,kBAAkB,SAAS,uBAAuB,MAAM;wBACrH,YAAY,IAAI,EAAE;wBAClB,KAAK,IAAI,GAAG;oBACd;oBACA,IAAI,CAAC,CAAA,GAAA,yJAAA,CAAA,yBAAsB,AAAD,EAAE,IAAI,IAAI,EAAE,IAAI,SAAS,EAAE,WAAW,kBAAkB,IAAI,EAAE,EAAE,WAAW,uBAAuB,IAAI,EAAE,GAAG;wBACnI,OAAO;4BAAE,MAAM;4BAAS,SAAS,EAAE;wBAA+C;wBAClF,iBAAiB,cAAc,EAAE;oBACnC;oBACA,IAAI,CAAC,eAAe,QAAQ,eAAe,EAAE,QAAQ,IAAI,GACvD,iBAAiB,cAAc,EAAE;yBAEjC,iBAAiB;gBACrB;8DAAG,KAAK;+DAAC;oBACP,OAAO;wBAAE,MAAM;wBAAS,SAAS,EAAE;oBAA4C;oBAC/E,iBAAiB,cAAc,EAAE;gBACnC;;QACF;sDAAG;QAAC;QAAgB;QAAe;QAAkB;QAAQ;QAAG;QAAkB,YAAY;QAAoB,WAAW,uBAAuB;QAAE;QAAoB,OAAO,KAAK;KAAC;IAEvL,MAAM,gCAAgC,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,qOAAA,CAAA,OAAI,EAAE,EAAE;IAE1D,MAAM,8BAA8B,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,qOAAA,CAAA,OAAI,EAAE,EAAE;IAExD,MAAM,mBAAmB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;iDAAE;YACnC,MAAM,EACJ,QAAQ,EACT,GAAG,UAAU,QAAQ;YACtB,SAAS,EAAE;QACb;gDAAG;QAAC;KAAU;IAEd,MAAM,wBAAwB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACzC,IAAI,CAAC,CAAA,GAAA,yJAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,WAAW,kBAAkB,IAAI,EAAE,EAAE,WAAW,uBAAuB,IAAI,EAAE,GAAG;gBAChI,OAAO;oBAAE,MAAM;oBAAS,SAAS,EAAE;gBAA+C;gBAClF;YACF;YACA,MAAM,mBAAmB,WAAW,kBAAkB;YACtD,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,kBAAkB,SAAS,uBAAuB,MAAM;YAClH,IAAI,CAAC,eAAe,UAAU,KAAK,IAAI,GACrC;YAEF,MAAM,SAAS,IAAI;YACnB,MAAM,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC;YAErC,OAAO,gBAAgB,CACrB;8DACA;oBACE,MAAM,gBAAgB;wBACpB,IAAI;wBACJ,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;wBACf,UAAU;wBACV,gBAAgB,eAAe,UAAU;wBACzC,iBAAiB,CAAA,GAAA,yJAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,kBAAkB,SAAS,uBAAuB,MAAM;wBAClH,cAAc;wBACd,WAAW,UAAU,OAAO,MAAM,GAAa;oBACjD;oBACA,cAAc;oBACd,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD,EAAE;wBACT,MAAM,cAAc,YAAY;wBAChC,kBAAkB;0EAAE,CAAC;gCACnB,iBAAiB;oCAAE,GAAG,aAAa;oCAAE;gCAAS;4BAChD;;wBACA,iBAAiB;0EAAE,CAAC;gCAClB,iBAAiB;oCAAE,GAAG,aAAa;oCAAE,YAAY,IAAI,EAAE;oCAAE,UAAU;gCAAI;4BACzE;;wBACA,eAAe;0EAAE;gCACf,OAAO;oCAAE,MAAM;oCAAS,SAAS,EAAE;gCAAqD;gCACxF,iBAAiB;oCAAE,GAAG,aAAa;oCAAE,UAAU,CAAC;gCAAE;4BACpD;;oBACF,GAAG,CAAC,CAAC,OAAO,KAAK;gBACnB;6DACA;YAEF,OAAO,gBAAgB,CACrB;8DACA;oBACE,OAAO;wBAAE,MAAM;wBAAS,SAAS,EAAE;oBAAmD;gBACxF;6DACA;YAEF,OAAO,aAAa,CAAC;QACvB;qDAAG;QAAC;QAAgB;QAAQ;QAAG;QAAe;QAAkB,OAAO,KAAK;QAAE,YAAY;QAAoB,YAAY;KAAwB;IAElJ,MAAM,2BAA2B,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YAC5C,MAAM,OAAO,EAAE,aAAa,EAAE,KAAK,CAAC,EAAE;YACtC,MAAM,OAAO,EAAE,aAAa,EAAE,QAAQ;YACtC,IAAI,QAAQ,CAAC,MAAM;gBACjB,EAAE,cAAc;gBAChB,sBAAsB;YACxB;QACF;wDAAG;QAAC;KAAsB;IAE1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,sBAAsB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YACvC,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,gBAAgB;QAClB;mDAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACtC,EAAE,cAAc;YAChB,EAAE,eAAe;QACnB;kDAAG,EAAE;IAEL,MAAM,sBAAsB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YACvC,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,gBAAgB;QAClB;mDAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;+CAAE,CAAC;YAClC,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,gBAAgB;YAEhB,MAAM,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;YAEpC,IAAI,MACF,sBAAsB;QAC1B;8CAAG;QAAC;KAAsB;IAE1B,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA9Ta;;QACG,8XAAA,CAAA,iBAAc;QACT,+IAAA,CAAA,kBAAe;QAChB,0JAAA,CAAA,eAAY;QACf,4SAAA,CAAA,YAAS;QAC+C", "debugId": null}}, {"offset": {"line": 5141, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/file-input.tsx"], "sourcesContent": ["import { useFile } from './hooks'\nimport { useStore } from './store'\nimport type { FileUpload } from '@/components/base/features/types'\nimport { FILE_EXTS } from '@/components/base/prompt-editor/constants'\nimport { SupportUploadFileTypes } from '@/components/workflow/types'\n\ntype FileInputProps = {\n  fileConfig: FileUpload\n}\nconst FileInput = ({\n  fileConfig,\n}: FileInputProps) => {\n  const files = useStore(s => s.files)\n  const { handleLocalFileUpload } = useFile(fileConfig)\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const targetFiles = e.target.files\n\n    if (targetFiles) {\n      if (fileConfig.number_limits) {\n        for (let i = 0; i < targetFiles.length; i++) {\n          if (i + 1 + files.length <= fileConfig.number_limits)\n            handleLocalFileUpload(targetFiles[i])\n        }\n      }\n      else {\n        handleLocalFileUpload(targetFiles[0])\n      }\n    }\n  }\n\n  const allowedFileTypes = fileConfig.allowed_file_types\n  const isCustom = allowedFileTypes?.includes(SupportUploadFileTypes.custom)\n  const exts = isCustom ? (fileConfig.allowed_file_extensions || []) : (allowedFileTypes?.map(type => FILE_EXTS[type]) || []).flat().map(item => `.${item}`)\n  const accept = exts.join(',')\n\n  return (\n    <input\n      className='absolute inset-0 block w-full cursor-pointer text-[0] opacity-0 disabled:cursor-not-allowed'\n      onClick={e => ((e.target as HTMLInputElement).value = '')}\n      type='file'\n      onChange={handleChange}\n      accept={accept}\n      disabled={!!(fileConfig.number_limits && files.length >= fileConfig?.number_limits)}\n      multiple={!!fileConfig.number_limits && fileConfig.number_limits > 1}\n    />\n  )\n}\n\nexport default FileInput\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;;;;;;;;AAMA,MAAM,YAAY,CAAC,EACjB,UAAU,EACK;;IACf,MAAM,QAAQ,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD;qCAAE,CAAA,IAAK,EAAE,KAAK;;IACnC,MAAM,EAAE,qBAAqB,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE;IAC1C,MAAM,eAAe,CAAC;QACpB,MAAM,cAAc,EAAE,MAAM,CAAC,KAAK;QAElC,IAAI,aAAa;YACf,IAAI,WAAW,aAAa,EAAE;gBAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;oBAC3C,IAAI,IAAI,IAAI,MAAM,MAAM,IAAI,WAAW,aAAa,EAClD,sBAAsB,WAAW,CAAC,EAAE;gBACxC;YACF,OACK;gBACH,sBAAsB,WAAW,CAAC,EAAE;YACtC;QACF;IACF;IAEA,MAAM,mBAAmB,WAAW,kBAAkB;IACtD,MAAM,WAAW,kBAAkB,SAAS,uBAAuB,MAAM;IACzE,MAAM,OAAO,WAAY,WAAW,uBAAuB,IAAI,EAAE,GAAI,CAAC,kBAAkB,IAAI,CAAA,OAAQ,8JAAA,CAAA,YAAS,CAAC,KAAK,KAAK,EAAE,EAAE,IAAI,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,MAAM;IACzJ,MAAM,SAAS,KAAK,IAAI,CAAC;IAEzB,qBACE,oWAAC;QACC,WAAU;QACV,SAAS,CAAA,IAAM,AAAC,EAAE,MAAM,CAAsB,KAAK,GAAG;QACtD,MAAK;QACL,UAAU;QACV,QAAQ;QACR,UAAU,CAAC,CAAC,CAAC,WAAW,aAAa,IAAI,MAAM,MAAM,IAAI,YAAY,aAAa;QAClF,UAAU,CAAC,CAAC,WAAW,aAAa,IAAI,WAAW,aAAa,GAAG;;;;;;AAGzE;GArCM;;QAGU,0JAAA,CAAA,WAAQ;QACY,yJAAA,CAAA,UAAO;;;KAJrC;uCAuCS", "debugId": null}}, {"offset": {"line": 5214, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/file-from-link-or-local/index.tsx"], "sourcesContent": ["import {\n  memo,\n  useState,\n} from 'react'\nimport { useTranslation } from 'react-i18next'\nimport { RiUploadCloud2Line } from '@remixicon/react'\nimport FileInput from '../file-input'\nimport { useFile } from '../hooks'\nimport { useStore } from '../store'\nimport { FILE_URL_REGEX } from '../constants'\nimport {\n  PortalToFollowElem,\n  PortalToFollowElemContent,\n  PortalToFollowElemTrigger,\n} from '@/components/base/portal-to-follow-elem'\nimport Button from '@/components/base/button'\nimport type { FileUpload } from '@/components/base/features/types'\nimport cn from '@/utils/classnames'\n\ntype FileFromLinkOrLocalProps = {\n  showFromLink?: boolean\n  showFromLocal?: boolean\n  trigger: (open: boolean) => React.ReactNode\n  fileConfig: FileUpload\n}\nconst FileFromLinkOrLocal = ({\n  showFromLink = true,\n  showFromLocal = true,\n  trigger,\n  fileConfig,\n}: FileFromLinkOrLocalProps) => {\n  const { t } = useTranslation()\n  const files = useStore(s => s.files)\n  const [open, setOpen] = useState(false)\n  const [url, setUrl] = useState('')\n  const [showError, setShowError] = useState(false)\n  const { handleLoadFileFromLink } = useFile(fileConfig)\n  const disabled = !!fileConfig.number_limits && files.length >= fileConfig.number_limits\n\n  const handleSaveUrl = () => {\n    if (!url)\n      return\n\n    if (!FILE_URL_REGEX.test(url)) {\n      setShowError(true)\n      return\n    }\n    handleLoadFileFromLink(url)\n    setUrl('')\n  }\n\n  return (\n    <PortalToFollowElem\n      placement='top'\n      offset={4}\n      open={open}\n      onOpenChange={setOpen}\n    >\n      <PortalToFollowElemTrigger onClick={() => setOpen(v => !v)} asChild>\n        {trigger(open)}\n      </PortalToFollowElemTrigger>\n      <PortalToFollowElemContent className='z-[1001]'>\n        <div className='w-[280px] rounded-xl border-[0.5px] border-components-panel-border bg-components-panel-bg-blur p-3 shadow-lg'>\n          {\n            showFromLink && (\n              <>\n                <div className={cn(\n                  'flex h-8 items-center rounded-lg border border-components-input-border-active bg-components-input-bg-active p-1 shadow-xs',\n                  showError && 'border-components-input-border-destructive',\n                )}>\n                  <input\n                    className='system-sm-regular mr-0.5 block grow appearance-none bg-transparent px-1 outline-none'\n                    placeholder={t('common.fileUploader.pasteFileLinkInputPlaceholder') || ''}\n                    value={url}\n                    onChange={(e) => {\n                      setShowError(false)\n                      setUrl(e.target.value.trim())\n                    }}\n                    disabled={disabled}\n                  />\n                  <Button\n                    className='shrink-0'\n                    size='small'\n                    variant='primary'\n                    disabled={!url || disabled}\n                    onClick={handleSaveUrl}\n                  >\n                    {t('common.operation.ok')}\n                  </Button>\n                </div>\n                {\n                  showError && (\n                    <div className='body-xs-regular mt-0.5 text-text-destructive'>\n                      {t('common.fileUploader.pasteFileLinkInvalid')}\n                    </div>\n                  )\n                }\n              </>\n            )\n          }\n          {\n            showFromLink && showFromLocal && (\n              <div className='system-2xs-medium-uppercase flex h-7 items-center p-2 text-text-quaternary'>\n                <div className='mr-2 h-[1px] w-[93px] bg-gradient-to-l from-[rgba(16,24,40,0.08)]' />\n                OR\n                <div className='ml-2 h-[1px] w-[93px] bg-gradient-to-r from-[rgba(16,24,40,0.08)]' />\n              </div>\n            )\n          }\n          {\n            showFromLocal && (\n              <Button\n                className='relative w-full'\n                variant='secondary-accent'\n                disabled={disabled}\n              >\n                <RiUploadCloud2Line className='mr-1 h-4 w-4' />\n                {t('common.fileUploader.uploadFromComputer')}\n                <FileInput fileConfig={fileConfig} />\n              </Button>\n            )\n          }\n        </div>\n      </PortalToFollowElemContent>\n    </PortalToFollowElem>\n  )\n}\n\nexport default memo(FileFromLinkOrLocal)\n"], "names": [], "mappings": ";;;;AAAA;AAIA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAEA;;;;;;;;;;;;;AAQA,MAAM,sBAAsB,CAAC,EAC3B,eAAe,IAAI,EACnB,gBAAgB,IAAI,EACpB,OAAO,EACP,UAAU,EACe;;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,QAAQ,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD;+CAAE,CAAA,IAAK,EAAE,KAAK;;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAC,CAAC,WAAW,aAAa,IAAI,MAAM,MAAM,IAAI,WAAW,aAAa;IAEvF,MAAM,gBAAgB;QACpB,IAAI,CAAC,KACH;QAEF,IAAI,CAAC,6JAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,MAAM;YAC7B,aAAa;YACb;QACF;QACA,uBAAuB;QACvB,OAAO;IACT;IAEA,qBACE,oWAAC,wKAAA,CAAA,qBAAkB;QACjB,WAAU;QACV,QAAQ;QACR,MAAM;QACN,cAAc;;0BAEd,oWAAC,wKAAA,CAAA,4BAAyB;gBAAC,SAAS,IAAM,QAAQ,CAAA,IAAK,CAAC;gBAAI,OAAO;0BAChE,QAAQ;;;;;;0BAEX,oWAAC,wKAAA,CAAA,4BAAyB;gBAAC,WAAU;0BACnC,cAAA,oWAAC;oBAAI,WAAU;;wBAEX,8BACE;;8CACE,oWAAC;oCAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EACf,6HACA,aAAa;;sDAEb,oWAAC;4CACC,WAAU;4CACV,aAAa,EAAE,wDAAwD;4CACvE,OAAO;4CACP,UAAU,CAAC;gDACT,aAAa;gDACb,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;4CAC5B;4CACA,UAAU;;;;;;sDAEZ,oWAAC,gJAAA,CAAA,UAAM;4CACL,WAAU;4CACV,MAAK;4CACL,SAAQ;4CACR,UAAU,CAAC,OAAO;4CAClB,SAAS;sDAER,EAAE;;;;;;;;;;;;gCAIL,2BACE,oWAAC;oCAAI,WAAU;8CACZ,EAAE;;;;;;;;wBAQb,gBAAgB,+BACd,oWAAC;4BAAI,WAAU;;8CACb,oWAAC;oCAAI,WAAU;;;;;;gCAAsE;8CAErF,oWAAC;oCAAI,WAAU;;;;;;;;;;;;wBAKnB,+BACE,oWAAC,gJAAA,CAAA,UAAM;4BACL,WAAU;4BACV,SAAQ;4BACR,UAAU;;8CAEV,oWAAC,wOAAA,CAAA,qBAAkB;oCAAC,WAAU;;;;;;gCAC7B,EAAE;8CACH,oWAAC,kKAAA,CAAA,UAAS;oCAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvC;GArGM;;QAMU,8XAAA,CAAA,iBAAc;QACd,0JAAA,CAAA,WAAQ;QAIa,yJAAA,CAAA,UAAO;;;KAXtC;2DAuGS,CAAA,GAAA,oUAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 5417, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/file-type-icon.tsx"], "sourcesContent": ["import { memo } from 'react'\nimport {\n  Ri<PERSON><PERSON>3Fill,\n  Ri<PERSON>ileCodeFill,\n  RiFileExcelFill,\n  R<PERSON>FileGifFill,\n  RiFileImageFill,\n  RiFileMusicFill,\n  RiFilePdf2Fill,\n  RiFilePpt2Fill,\n  RiFileTextFill,\n  RiFileVideoFill,\n  RiFileWordFill,\n  RiMarkdownFill,\n} from '@remixicon/react'\nimport { FileAppearanceTypeEnum } from './types'\nimport type { FileAppearanceType } from './types'\nimport cn from '@/utils/classnames'\n\nconst FILE_TYPE_ICON_MAP = {\n  [FileAppearanceTypeEnum.pdf]: {\n    component: RiFilePdf2Fill,\n    color: 'text-[#EA3434]',\n  },\n  [FileAppearanceTypeEnum.image]: {\n    component: RiFileImageFill,\n    color: 'text-[#00B2EA]',\n  },\n  [FileAppearanceTypeEnum.video]: {\n    component: RiFileVideoFill,\n    color: 'text-[#844FDA]',\n  },\n  [FileAppearanceTypeEnum.audio]: {\n    component: RiFileMusicFill,\n    color: 'text-[#FF3093]',\n  },\n  [FileAppearanceTypeEnum.document]: {\n    component: RiFileTextFill,\n    color: 'text-[#6F8BB5]',\n  },\n  [FileAppearanceTypeEnum.code]: {\n    component: RiFileCodeFill,\n    color: 'text-[#BCC0D1]',\n  },\n  [FileAppearanceTypeEnum.markdown]: {\n    component: RiMarkdownFill,\n    color: 'text-[#309BEC]',\n  },\n  [FileAppearanceTypeEnum.custom]: {\n    component: RiFile3Fill,\n    color: 'text-[#BCC0D1]',\n  },\n  [FileAppearanceTypeEnum.excel]: {\n    component: RiFileExcelFill,\n    color: 'text-[#01AC49]',\n  },\n  [FileAppearanceTypeEnum.word]: {\n    component: RiFileWordFill,\n    color: 'text-[#2684FF]',\n  },\n  [FileAppearanceTypeEnum.ppt]: {\n    component: RiFilePpt2Fill,\n    color: 'text-[#FF650F]',\n  },\n  [FileAppearanceTypeEnum.gif]: {\n    component: RiFileGifFill,\n    color: 'text-[#00B2EA]',\n  },\n}\ntype FileTypeIconProps = {\n  type: FileAppearanceType\n  size?: 'sm' | 'lg' | 'md'\n  className?: string\n}\nconst SizeMap = {\n  sm: 'w-4 h-4',\n  md: 'w-5 h-5',\n  lg: 'w-6 h-6',\n}\nconst FileTypeIcon = ({\n  type,\n  size = 'sm',\n  className,\n}: FileTypeIconProps) => {\n  const Icon = FILE_TYPE_ICON_MAP[type]?.component || FILE_TYPE_ICON_MAP[FileAppearanceTypeEnum.document].component\n  const color = FILE_TYPE_ICON_MAP[type]?.color || FILE_TYPE_ICON_MAP[FileAppearanceTypeEnum.document].color\n\n  return <Icon className={cn('shrink-0', SizeMap[size], color, className)} />\n}\n\nexport default memo(FileTypeIcon)\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAcA;AAEA;;;;;;AAEA,MAAM,qBAAqB;IACzB,CAAC,yJAAA,CAAA,yBAAsB,CAAC,GAAG,CAAC,EAAE;QAC5B,WAAW,wOAAA,CAAA,iBAAc;QACzB,OAAO;IACT;IACA,CAAC,yJAAA,CAAA,yBAAsB,CAAC,KAAK,CAAC,EAAE;QAC9B,WAAW,wOAAA,CAAA,kBAAe;QAC1B,OAAO;IACT;IACA,CAAC,yJAAA,CAAA,yBAAsB,CAAC,KAAK,CAAC,EAAE;QAC9B,WAAW,wOAAA,CAAA,kBAAe;QAC1B,OAAO;IACT;IACA,CAAC,yJAAA,CAAA,yBAAsB,CAAC,KAAK,CAAC,EAAE;QAC9B,WAAW,wOAAA,CAAA,kBAAe;QAC1B,OAAO;IACT;IACA,CAAC,yJAAA,CAAA,yBAAsB,CAAC,QAAQ,CAAC,EAAE;QACjC,WAAW,wOAAA,CAAA,iBAAc;QACzB,OAAO;IACT;IACA,CAAC,yJAAA,CAAA,yBAAsB,CAAC,IAAI,CAAC,EAAE;QAC7B,WAAW,wOAAA,CAAA,iBAAc;QACzB,OAAO;IACT;IACA,CAAC,yJAAA,CAAA,yBAAsB,CAAC,QAAQ,CAAC,EAAE;QACjC,WAAW,wOAAA,CAAA,iBAAc;QACzB,OAAO;IACT;IACA,CAAC,yJAAA,CAAA,yBAAsB,CAAC,MAAM,CAAC,EAAE;QAC/B,WAAW,wOAAA,CAAA,cAAW;QACtB,OAAO;IACT;IACA,CAAC,yJAAA,CAAA,yBAAsB,CAAC,KAAK,CAAC,EAAE;QAC9B,WAAW,wOAAA,CAAA,kBAAe;QAC1B,OAAO;IACT;IACA,CAAC,yJAAA,CAAA,yBAAsB,CAAC,IAAI,CAAC,EAAE;QAC7B,WAAW,wOAAA,CAAA,iBAAc;QACzB,OAAO;IACT;IACA,CAAC,yJAAA,CAAA,yBAAsB,CAAC,GAAG,CAAC,EAAE;QAC5B,WAAW,wOAAA,CAAA,iBAAc;QACzB,OAAO;IACT;IACA,CAAC,yJAAA,CAAA,yBAAsB,CAAC,GAAG,CAAC,EAAE;QAC5B,WAAW,wOAAA,CAAA,gBAAa;QACxB,OAAO;IACT;AACF;AAMA,MAAM,UAAU;IACd,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AACA,MAAM,eAAe,CAAC,EACpB,IAAI,EACJ,OAAO,IAAI,EACX,SAAS,EACS;IAClB,MAAM,OAAO,kBAAkB,CAAC,KAAK,EAAE,aAAa,kBAAkB,CAAC,yJAAA,CAAA,yBAAsB,CAAC,QAAQ,CAAC,CAAC,SAAS;IACjH,MAAM,QAAQ,kBAAkB,CAAC,KAAK,EAAE,SAAS,kBAAkB,CAAC,yJAAA,CAAA,yBAAsB,CAAC,QAAQ,CAAC,CAAC,KAAK;IAE1G,qBAAO,oWAAC;QAAK,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,YAAY,OAAO,CAAC,KAAK,EAAE,OAAO;;;;;;AAC/D;KATM;2DAWS,CAAA,GAAA,oUAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 5510, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/file-image-render.tsx"], "sourcesContent": ["import cn from '@/utils/classnames'\n\ntype FileImageRenderProps = {\n  imageUrl: string\n  className?: string\n  alt?: string\n  onLoad?: () => void\n  onError?: () => void\n  showDownloadAction?: boolean\n}\nconst FileImageRender = ({\n  imageUrl,\n  className,\n  alt,\n  onLoad,\n  onError,\n  showDownloadAction,\n}: FileImageRenderProps) => {\n  return (\n    <div className={cn('border-[2px] border-effects-image-frame shadow-xs', className)}>\n      <img\n        className={cn('h-full w-full object-cover', showDownloadAction && 'cursor-pointer')}\n        alt={alt || 'Preview'}\n        onLoad={onLoad}\n        onError={onError}\n        src={imageUrl}\n      />\n    </div>\n  )\n}\n\nexport default FileImageRender\n"], "names": [], "mappings": ";;;;AAAA;;;AAUA,MAAM,kBAAkB,CAAC,EACvB,QAAQ,EACR,SAAS,EACT,GAAG,EACH,MAAM,EACN,OAAO,EACP,kBAAkB,EACG;IACrB,qBACE,oWAAC;QAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,qDAAqD;kBACtE,cAAA,oWAAC;YACC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,8BAA8B,sBAAsB;YAClE,KAAK,OAAO;YACZ,QAAQ;YACR,SAAS;YACT,KAAK;;;;;;;;;;;AAIb;KAnBM;uCAqBS", "debugId": null}}, {"offset": {"line": 5550, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/progress-bar/progress-circle.tsx"], "sourcesContent": ["import { memo } from 'react'\nimport cn from '@/utils/classnames'\n\ntype ProgressCircleProps = {\n  className?: string\n  percentage?: number\n  size?: number\n  circleStrokeWidth?: number\n  circleStrokeColor?: string\n  circleFillColor?: string\n  sectorFillColor?: string\n}\n\nconst ProgressCircle: React.FC<ProgressCircleProps> = ({\n  className,\n  percentage = 0,\n  size = 12,\n  circleStrokeWidth = 1,\n  circleStrokeColor = 'stroke-components-progress-brand-border',\n  circleFillColor = 'fill-components-progress-brand-bg',\n  sectorFillColor = 'fill-components-progress-brand-progress',\n}) => {\n  const radius = size / 2\n  const center = size / 2\n  const angle = (percentage / 101) * 360\n  const radians = (angle * Math.PI) / 180\n  const x = center + radius * Math.cos(radians - Math.PI / 2)\n  const y = center + radius * Math.sin(radians - Math.PI / 2)\n  const largeArcFlag = percentage > 50 ? 1 : 0\n\n  const pathData = `\n    M ${center},${center}\n    L ${center},${center - radius}\n    A ${radius},${radius} 0 ${largeArcFlag} 1 ${x},${y}\n    Z\n  `\n\n  return (\n    <svg\n      width={size + circleStrokeWidth}\n      height={size + circleStrokeWidth}\n      viewBox={`0 0 ${size + circleStrokeWidth} ${size + circleStrokeWidth}`}\n      className={className}\n    >\n      <circle\n        className={cn(\n          circleFillColor,\n          circleStrokeColor,\n        )}\n        cx={center + circleStrokeWidth / 2}\n        cy={center + circleStrokeWidth / 2}\n        r={radius}\n        strokeWidth={circleStrokeWidth}\n      />\n      <path\n        className={cn(sectorFillColor)}\n        d={pathData}\n        transform={`translate(${circleStrokeWidth / 2}, ${circleStrokeWidth / 2})`}\n      />\n    </svg>\n  )\n}\n\nexport default memo(ProgressCircle)\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAYA,MAAM,iBAAgD,CAAC,EACrD,SAAS,EACT,aAAa,CAAC,EACd,OAAO,EAAE,EACT,oBAAoB,CAAC,EACrB,oBAAoB,yCAAyC,EAC7D,kBAAkB,mCAAmC,EACrD,kBAAkB,yCAAyC,EAC5D;IACC,MAAM,SAAS,OAAO;IACtB,MAAM,SAAS,OAAO;IACtB,MAAM,QAAQ,AAAC,aAAa,MAAO;IACnC,MAAM,UAAU,AAAC,QAAQ,KAAK,EAAE,GAAI;IACpC,MAAM,IAAI,SAAS,SAAS,KAAK,GAAG,CAAC,UAAU,KAAK,EAAE,GAAG;IACzD,MAAM,IAAI,SAAS,SAAS,KAAK,GAAG,CAAC,UAAU,KAAK,EAAE,GAAG;IACzD,MAAM,eAAe,aAAa,KAAK,IAAI;IAE3C,MAAM,WAAW,CAAC;MACd,EAAE,OAAO,CAAC,EAAE,OAAO;MACnB,EAAE,OAAO,CAAC,EAAE,SAAS,OAAO;MAC5B,EAAE,OAAO,CAAC,EAAE,OAAO,GAAG,EAAE,aAAa,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;;EAErD,CAAC;IAED,qBACE,oWAAC;QACC,OAAO,OAAO;QACd,QAAQ,OAAO;QACf,SAAS,CAAC,IAAI,EAAE,OAAO,kBAAkB,CAAC,EAAE,OAAO,mBAAmB;QACtE,WAAW;;0BAEX,oWAAC;gBACC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EACV,iBACA;gBAEF,IAAI,SAAS,oBAAoB;gBACjC,IAAI,SAAS,oBAAoB;gBACjC,GAAG;gBACH,aAAa;;;;;;0BAEf,oWAAC;gBACC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE;gBACd,GAAG;gBACH,WAAW,CAAC,UAAU,EAAE,oBAAoB,EAAE,EAAE,EAAE,oBAAoB,EAAE,CAAC,CAAC;;;;;;;;;;;;AAIlF;KAhDM;2DAkDS,CAAA,GAAA,oUAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 5625, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/other/AnthropicText.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './AnthropicText.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'AnthropicText'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;AACA;;;;AAGA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,oWAAC,kJAAA,CAAA,UAAQ;QAAE,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,qJAAA,CAAA,UAAI;;;;;;KAPxC;AASN,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 5664, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/other/Generator.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './Generator.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'Generator'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;AACA;;;;AAGA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,oWAAC,kJAAA,CAAA,UAAQ;QAAE,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,iJAAA,CAAA,UAAI;;;;;;KAPxC;AASN,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 5703, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/other/Group.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './Group.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'Group'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;AACA;;;;AAGA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,oWAAC,kJAAA,CAAA,UAAQ;QAAE,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,6IAAA,CAAA,UAAI;;;;;;KAPxC;AASN,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 5742, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/other/Openai.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './Openai.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'Openai'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;AACA;;;;AAGA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,oWAAC,kJAAA,CAAA,UAAQ;QAAE,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,8IAAA,CAAA,UAAI;;;;;;KAPxC;AASN,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 5781, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/other/ReplayLine.tsx"], "sourcesContent": ["// GENERATE BY script\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react'\nimport data from './ReplayLine.json'\nimport IconBase from '@/components/base/icons/IconBase'\nimport type { IconData } from '@/components/base/icons/IconBase'\n\nconst Icon = (\n  {\n    ref,\n    ...props\n  }: React.SVGProps<SVGSVGElement> & {\n    ref?: React.RefObject<React.MutableRefObject<HTMLOrSVGElement>>;\n  },\n) => <IconBase {...props} ref={ref} data={data as IconData} />\n\nIcon.displayName = 'ReplayLine'\n\nexport default Icon\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,2BAA2B;;;;;AAG3B;AACA;;;;AAGA,MAAM,OAAO,CACX,EACE,GAAG,EACH,GAAG,OAGJ,iBACE,oWAAC,kJAAA,CAAA,UAAQ;QAAE,GAAG,KAAK;QAAE,KAAK;QAAK,MAAM,kJAAA,CAAA,UAAI;;;;;;KAPxC;AASN,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 5815, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/icons/src/vender/other/index.ts"], "sourcesContent": ["export { default as AnthropicText } from './AnthropicText'\nexport { default as Generator } from './Generator'\nexport { default as Group } from './Group'\nexport { default as Openai } from './Openai'\nexport { default as ReplayLine } from './ReplayLine'\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 5858, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/image-uploader/image-preview.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport React, { useCallback, useEffect, useRef, useState } from 'react'\nimport { t } from 'i18next'\nimport { createPortal } from 'react-dom'\nimport { RiAddBoxLine, RiCloseLine, RiDownloadCloud2Line, RiFileCopyLine, RiZoomInLine, RiZoomOutLine } from '@remixicon/react'\nimport { useHotkeys } from 'react-hotkeys-hook'\nimport Tooltip from '@/components/base/tooltip'\nimport Toast from '@/components/base/toast'\nimport { noop } from 'lodash-es'\n\ntype ImagePreviewProps = {\n  url: string\n  title: string\n  onCancel: () => void\n  onPrev?: () => void\n  onNext?: () => void\n}\n\nconst isBase64 = (str: string): boolean => {\n  try {\n    return btoa(atob(str)) === str\n  }\n  catch (err) {\n    return false\n  }\n}\n\nconst ImagePreview: FC<ImagePreviewProps> = ({\n  url,\n  title,\n  onCancel,\n  onPrev,\n  onNext,\n}) => {\n  const [scale, setScale] = useState(1)\n  const [position, setPosition] = useState({ x: 0, y: 0 })\n  const [isDragging, setIsDragging] = useState(false)\n  const imgRef = useRef<HTMLImageElement>(null)\n  const dragStartRef = useRef({ x: 0, y: 0 })\n  const [isCopied, setIsCopied] = useState(false)\n\n  const openInNewTab = () => {\n    // Open in a new window, considering the case when the page is inside an iframe\n    if (url.startsWith('http') || url.startsWith('https')) {\n      window.open(url, '_blank')\n    }\n    else if (url.startsWith('data:image')) {\n      // Base64 image\n      const win = window.open()\n      win?.document.write(`<img src=\"${url}\" alt=\"${title}\" />`)\n    }\n    else {\n      Toast.notify({\n        type: 'error',\n        message: `Unable to open image: ${url}`,\n      })\n    }\n  }\n\n  const downloadImage = () => {\n    // Open in a new window, considering the case when the page is inside an iframe\n    if (url.startsWith('http') || url.startsWith('https')) {\n      const a = document.createElement('a')\n      a.href = url\n      a.target = '_blank'\n      a.download = title\n      a.click()\n    }\n    else if (url.startsWith('data:image')) {\n      // Base64 image\n      const a = document.createElement('a')\n      a.href = url\n      a.target = '_blank'\n      a.download = title\n      a.click()\n    }\n    else {\n      Toast.notify({\n        type: 'error',\n        message: `Unable to open image: ${url}`,\n      })\n    }\n  }\n\n  const zoomIn = () => {\n    setScale(prevScale => Math.min(prevScale * 1.2, 15))\n  }\n\n  const zoomOut = () => {\n    setScale((prevScale) => {\n      const newScale = Math.max(prevScale / 1.2, 0.5)\n      if (newScale === 1)\n        setPosition({ x: 0, y: 0 }) // Reset position when fully zoomed out\n\n      return newScale\n    })\n  }\n\n  const imageBase64ToBlob = (base64: string, type = 'image/png'): Blob => {\n    const byteCharacters = atob(base64)\n    const byteArrays = []\n\n    for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n      const slice = byteCharacters.slice(offset, offset + 512)\n      const byteNumbers = Array.from({ length: slice.length })\n      for (let i = 0; i < slice.length; i++)\n        byteNumbers[i] = slice.charCodeAt(i)\n\n      const byteArray = new Uint8Array(byteNumbers as any)\n      byteArrays.push(byteArray)\n    }\n\n    return new Blob(byteArrays, { type })\n  }\n\n  const imageCopy = useCallback(() => {\n    const shareImage = async () => {\n      try {\n        const base64Data = url.split(',')[1]\n        const blob = imageBase64ToBlob(base64Data, 'image/png')\n\n        await navigator.clipboard.write([\n          new ClipboardItem({\n            [blob.type]: blob,\n          }),\n        ])\n        setIsCopied(true)\n\n        Toast.notify({\n          type: 'success',\n          message: t('common.operation.imageCopied'),\n        })\n      }\n      catch (err) {\n        console.error('Failed to copy image:', err)\n\n        const link = document.createElement('a')\n        link.href = url\n        link.download = `${title}.png`\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n\n        Toast.notify({\n          type: 'info',\n          message: t('common.operation.imageDownloaded'),\n        })\n      }\n    }\n    shareImage()\n  }, [title, url])\n\n  const handleWheel = useCallback((e: React.WheelEvent<HTMLDivElement>) => {\n    if (e.deltaY < 0)\n      zoomIn()\n    else\n      zoomOut()\n  }, [])\n\n  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => {\n    if (scale > 1) {\n      setIsDragging(true)\n      dragStartRef.current = { x: e.clientX - position.x, y: e.clientY - position.y }\n    }\n  }, [scale, position])\n\n  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {\n    if (isDragging && scale > 1) {\n      const deltaX = e.clientX - dragStartRef.current.x\n      const deltaY = e.clientY - dragStartRef.current.y\n\n      // Calculate boundaries\n      const imgRect = imgRef.current?.getBoundingClientRect()\n      const containerRect = imgRef.current?.parentElement?.getBoundingClientRect()\n\n      if (imgRect && containerRect) {\n        const maxX = (imgRect.width * scale - containerRect.width) / 2\n        const maxY = (imgRect.height * scale - containerRect.height) / 2\n\n        setPosition({\n          x: Math.max(-maxX, Math.min(maxX, deltaX)),\n          y: Math.max(-maxY, Math.min(maxY, deltaY)),\n        })\n      }\n    }\n  }, [isDragging, scale])\n\n  const handleMouseUp = useCallback(() => {\n    setIsDragging(false)\n  }, [])\n\n  useEffect(() => {\n    document.addEventListener('mouseup', handleMouseUp)\n    return () => {\n      document.removeEventListener('mouseup', handleMouseUp)\n    }\n  }, [handleMouseUp])\n\n  useHotkeys('esc', onCancel)\n  useHotkeys('up', zoomIn)\n  useHotkeys('down', zoomOut)\n  useHotkeys('left', onPrev || noop)\n  useHotkeys('right', onNext || noop)\n\n  return createPortal(\n    <div className='image-preview-container fixed inset-0 z-[1000] flex items-center justify-center bg-black/80 p-8'\n      onClick={e => e.stopPropagation()}\n      onWheel={handleWheel}\n      onMouseDown={handleMouseDown}\n      onMouseMove={handleMouseMove}\n      onMouseUp={handleMouseUp}\n      style={{ cursor: scale > 1 ? 'move' : 'default' }}\n      tabIndex={-1}>\n      { }\n      <img\n        ref={imgRef}\n        alt={title}\n        src={isBase64(url) ? `data:image/png;base64,${url}` : url}\n        className='max-h-full max-w-full'\n        style={{\n          transform: `scale(${scale}) translate(${position.x}px, ${position.y}px)`,\n          transition: isDragging ? 'none' : 'transform 0.2s ease-in-out',\n        }}\n      />\n      <Tooltip popupContent={t('common.operation.copyImage')}>\n        <div className='absolute right-48 top-6 flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg'\n          onClick={imageCopy}>\n          {isCopied\n            ? <RiFileCopyLine className='h-4 w-4 text-green-500' />\n            : <RiFileCopyLine className='h-4 w-4 text-gray-500' />}\n        </div>\n      </Tooltip>\n      <Tooltip popupContent={t('common.operation.zoomOut')}>\n        <div className='absolute right-40 top-6 flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg'\n          onClick={zoomOut}>\n          <RiZoomOutLine className='h-4 w-4 text-gray-500' />\n        </div>\n      </Tooltip>\n      <Tooltip popupContent={t('common.operation.zoomIn')}>\n        <div className='absolute right-32 top-6 flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg'\n          onClick={zoomIn}>\n          <RiZoomInLine className='h-4 w-4 text-gray-500' />\n        </div>\n      </Tooltip>\n      <Tooltip popupContent={t('common.operation.download')}>\n        <div className='absolute right-24 top-6 flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg'\n          onClick={downloadImage}>\n          <RiDownloadCloud2Line className='h-4 w-4 text-gray-500' />\n        </div>\n      </Tooltip>\n      <Tooltip popupContent={t('common.operation.openInNewTab')}>\n        <div className='absolute right-16 top-6 flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg'\n          onClick={openInNewTab}>\n          <RiAddBoxLine className='h-4 w-4 text-gray-500' />\n        </div>\n      </Tooltip>\n      <Tooltip popupContent={t('common.operation.cancel')}>\n        <div\n          className='absolute right-6 top-6 flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg bg-white/8 backdrop-blur-[2px]'\n          onClick={onCancel}>\n          <RiCloseLine className='h-4 w-4 text-gray-500' />\n        </div>\n      </Tooltip>\n    </div>,\n    document.body,\n  )\n}\n\nexport default ImagePreview\n"], "names": [], "mappings": ";;;;AACA;;;;;;AAEA;AACA;;;;;;AAEA;AACA;AACA;;;;;;;;;;;AAUA,MAAM,WAAW,CAAC;IAChB,IAAI;QACF,OAAO,KAAK,KAAK,UAAU;IAC7B,EACA,OAAO,KAAK;QACV,OAAO;IACT;AACF;AAEA,MAAM,eAAsC,CAAC,EAC3C,GAAG,EACH,KAAK,EACL,QAAQ,EACR,MAAM,EACN,MAAM,EACP;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAoB;IACxC,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,eAAe;QACnB,+EAA+E;QAC/E,IAAI,IAAI,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,UAAU;YACrD,OAAO,IAAI,CAAC,KAAK;QACnB,OACK,IAAI,IAAI,UAAU,CAAC,eAAe;YACrC,eAAe;YACf,MAAM,MAAM,OAAO,IAAI;YACvB,KAAK,SAAS,MAAM,CAAC,UAAU,EAAE,IAAI,OAAO,EAAE,MAAM,IAAI,CAAC;QAC3D,OACK;YACH,+IAAA,CAAA,UAAK,CAAC,MAAM,CAAC;gBACX,MAAM;gBACN,SAAS,CAAC,sBAAsB,EAAE,KAAK;YACzC;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,+EAA+E;QAC/E,IAAI,IAAI,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,UAAU;YACrD,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,MAAM,GAAG;YACX,EAAE,QAAQ,GAAG;YACb,EAAE,KAAK;QACT,OACK,IAAI,IAAI,UAAU,CAAC,eAAe;YACrC,eAAe;YACf,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,MAAM,GAAG;YACX,EAAE,QAAQ,GAAG;YACb,EAAE,KAAK;QACT,OACK;YACH,+IAAA,CAAA,UAAK,CAAC,MAAM,CAAC;gBACX,MAAM;gBACN,SAAS,CAAC,sBAAsB,EAAE,KAAK;YACzC;QACF;IACF;IAEA,MAAM,SAAS;QACb,SAAS,CAAA,YAAa,KAAK,GAAG,CAAC,YAAY,KAAK;IAClD;IAEA,MAAM,UAAU;QACd,SAAS,CAAC;YACR,MAAM,WAAW,KAAK,GAAG,CAAC,YAAY,KAAK;YAC3C,IAAI,aAAa,GACf,YAAY;gBAAE,GAAG;gBAAG,GAAG;YAAE,GAAG,uCAAuC;;YAErE,OAAO;QACT;IACF;IAEA,MAAM,oBAAoB,CAAC,QAAgB,OAAO,WAAW;QAC3D,MAAM,iBAAiB,KAAK;QAC5B,MAAM,aAAa,EAAE;QAErB,IAAK,IAAI,SAAS,GAAG,SAAS,eAAe,MAAM,EAAE,UAAU,IAAK;YAClE,MAAM,QAAQ,eAAe,KAAK,CAAC,QAAQ,SAAS;YACpD,MAAM,cAAc,MAAM,IAAI,CAAC;gBAAE,QAAQ,MAAM,MAAM;YAAC;YACtD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAChC,WAAW,CAAC,EAAE,GAAG,MAAM,UAAU,CAAC;YAEpC,MAAM,YAAY,IAAI,WAAW;YACjC,WAAW,IAAI,CAAC;QAClB;QAEA,OAAO,IAAI,KAAK,YAAY;YAAE;QAAK;IACrC;IAEA,MAAM,YAAY,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;+CAAE;YAC5B,MAAM;kEAAa;oBACjB,IAAI;wBACF,MAAM,aAAa,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;wBACpC,MAAM,OAAO,kBAAkB,YAAY;wBAE3C,MAAM,UAAU,SAAS,CAAC,KAAK,CAAC;4BAC9B,IAAI,cAAc;gCAChB,CAAC,KAAK,IAAI,CAAC,EAAE;4BACf;yBACD;wBACD,YAAY;wBAEZ,+IAAA,CAAA,UAAK,CAAC,MAAM,CAAC;4BACX,MAAM;4BACN,SAAS,EAAE;wBACb;oBACF,EACA,OAAO,KAAK;wBACV,QAAQ,KAAK,CAAC,yBAAyB;wBAEvC,MAAM,OAAO,SAAS,aAAa,CAAC;wBACpC,KAAK,IAAI,GAAG;wBACZ,KAAK,QAAQ,GAAG,GAAG,MAAM,IAAI,CAAC;wBAC9B,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B,KAAK,KAAK;wBACV,SAAS,IAAI,CAAC,WAAW,CAAC;wBAE1B,+IAAA,CAAA,UAAK,CAAC,MAAM,CAAC;4BACX,MAAM;4BACN,SAAS,EAAE;wBACb;oBACF;gBACF;;YACA;QACF;8CAAG;QAAC;QAAO;KAAI;IAEf,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC/B,IAAI,EAAE,MAAM,GAAG,GACb;iBAEA;QACJ;gDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACnC,IAAI,QAAQ,GAAG;gBACb,cAAc;gBACd,aAAa,OAAO,GAAG;oBAAE,GAAG,EAAE,OAAO,GAAG,SAAS,CAAC;oBAAE,GAAG,EAAE,OAAO,GAAG,SAAS,CAAC;gBAAC;YAChF;QACF;oDAAG;QAAC;QAAO;KAAS;IAEpB,MAAM,kBAAkB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACnC,IAAI,cAAc,QAAQ,GAAG;gBAC3B,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,OAAO,CAAC,CAAC;gBACjD,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,OAAO,CAAC,CAAC;gBAEjD,uBAAuB;gBACvB,MAAM,UAAU,OAAO,OAAO,EAAE;gBAChC,MAAM,gBAAgB,OAAO,OAAO,EAAE,eAAe;gBAErD,IAAI,WAAW,eAAe;oBAC5B,MAAM,OAAO,CAAC,QAAQ,KAAK,GAAG,QAAQ,cAAc,KAAK,IAAI;oBAC7D,MAAM,OAAO,CAAC,QAAQ,MAAM,GAAG,QAAQ,cAAc,MAAM,IAAI;oBAE/D,YAAY;wBACV,GAAG,KAAK,GAAG,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM;wBAClC,GAAG,KAAK,GAAG,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM;oBACpC;gBACF;YACF;QACF;oDAAG;QAAC;QAAY;KAAM;IAEtB,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;mDAAE;YAChC,cAAc;QAChB;kDAAG,EAAE;IAEL,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD;kCAAE;YACR,SAAS,gBAAgB,CAAC,WAAW;YACrC;0CAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;gBAC1C;;QACF;iCAAG;QAAC;KAAc;IAElB,WAAW,OAAO;IAClB,WAAW,MAAM;IACjB,WAAW,QAAQ;IACnB,WAAW,QAAQ,UAAU,qOAAA,CAAA,OAAI;IACjC,WAAW,SAAS,UAAU,qOAAA,CAAA,OAAI;IAElC,qBAAO,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,gBAChB,oWAAC;QAAI,WAAU;QACb,SAAS,CAAA,IAAK,EAAE,eAAe;QAC/B,SAAS;QACT,aAAa;QACb,aAAa;QACb,WAAW;QACX,OAAO;YAAE,QAAQ,QAAQ,IAAI,SAAS;QAAU;QAChD,UAAU,CAAC;;0BAEX,oWAAC;gBACC,KAAK;gBACL,KAAK;gBACL,KAAK,SAAS,OAAO,CAAC,sBAAsB,EAAE,KAAK,GAAG;gBACtD,WAAU;gBACV,OAAO;oBACL,WAAW,CAAC,MAAM,EAAE,MAAM,YAAY,EAAE,SAAS,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC;oBACxE,YAAY,aAAa,SAAS;gBACpC;;;;;;0BAEF,oWAAC,iJAAA,CAAA,UAAO;gBAAC,cAAc,EAAE;0BACvB,cAAA,oWAAC;oBAAI,WAAU;oBACb,SAAS;8BACR,yBACG,oWAAC,wOAAA,CAAA,iBAAc;wBAAC,WAAU;;;;;6CAC1B,oWAAC,wOAAA,CAAA,iBAAc;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGlC,oWAAC,iJAAA,CAAA,UAAO;gBAAC,cAAc,EAAE;0BACvB,cAAA,oWAAC;oBAAI,WAAU;oBACb,SAAS;8BACT,cAAA,oWAAC,wOAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAG7B,oWAAC,iJAAA,CAAA,UAAO;gBAAC,cAAc,EAAE;0BACvB,cAAA,oWAAC;oBAAI,WAAU;oBACb,SAAS;8BACT,cAAA,oWAAC,wOAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAG5B,oWAAC,iJAAA,CAAA,UAAO;gBAAC,cAAc,EAAE;0BACvB,cAAA,oWAAC;oBAAI,WAAU;oBACb,SAAS;8BACT,cAAA,oWAAC,wOAAA,CAAA,uBAAoB;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGpC,oWAAC,iJAAA,CAAA,UAAO;gBAAC,cAAc,EAAE;0BACvB,cAAA,oWAAC;oBAAI,WAAU;oBACb,SAAS;8BACT,cAAA,oWAAC,wOAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAG5B,oWAAC,iJAAA,CAAA,UAAO;gBAAC,cAAc,EAAE;0BACvB,cAAA,oWAAC;oBACC,WAAU;oBACV,SAAS;8BACT,cAAA,oWAAC,wOAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;cAI7B,SAAS,IAAI;AAEjB;GA/OM;;QA2KJ;QACA;QACA;QACA;QACA;;;KA/KI;uCAiPS", "debugId": null}}, {"offset": {"line": 6271, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/file-uploader-in-attachment/file-item.tsx"], "sourcesContent": ["import {\n  memo,\n  useState,\n} from 'react'\nimport {\n  RiDeleteBinLine,\n  RiDownloadLine,\n  RiEyeLine,\n} from '@remixicon/react'\nimport FileTypeIcon from '../file-type-icon'\nimport {\n  downloadFile,\n  fileIsUploaded,\n  getFileAppearanceType,\n  getFileExtension,\n} from '../utils'\nimport FileImageRender from '../file-image-render'\nimport type { FileEntity } from '../types'\nimport ActionButton from '@/components/base/action-button'\nimport ProgressCircle from '@/components/base/progress-bar/progress-circle'\nimport { formatFileSize } from '@/utils/format'\nimport cn from '@/utils/classnames'\nimport { ReplayLine } from '@/components/base/icons/src/vender/other'\nimport { SupportUploadFileTypes } from '@/components/workflow/types'\nimport ImagePreview from '@/components/base/image-uploader/image-preview'\n\ntype FileInAttachmentItemProps = {\n  file: FileEntity\n  showDeleteAction?: boolean\n  showDownloadAction?: boolean\n  onRemove?: (fileId: string) => void\n  onReUpload?: (fileId: string) => void\n  canPreview?: boolean\n}\nconst FileInAttachmentItem = ({\n  file,\n  showDeleteAction,\n  showDownloadAction = true,\n  onRemove,\n  onReUpload,\n  canPreview,\n}: FileInAttachmentItemProps) => {\n  const { id, name, type, progress, supportFileType, base64Url, url, isRemote } = file\n  const ext = getFileExtension(name, type, isRemote)\n  const isImageFile = supportFileType === SupportUploadFileTypes.image\n  const [imagePreviewUrl, setImagePreviewUrl] = useState('')\n  return (\n    <>\n      <div className={cn(\n        'flex h-12 items-center rounded-lg border-[0.5px] border-components-panel-border bg-components-panel-on-panel-item-bg pr-3 shadow-xs',\n        progress === -1 && 'border-state-destructive-border bg-state-destructive-hover',\n      )}>\n        <div className='flex h-12 w-12 items-center justify-center'>\n          {\n            isImageFile && (\n              <FileImageRender\n                className='h-8 w-8'\n                imageUrl={base64Url || url || ''}\n              />\n            )\n          }\n          {\n            !isImageFile && (\n              <FileTypeIcon\n                type={getFileAppearanceType(name, type)}\n                size='lg'\n              />\n            )\n          }\n        </div>\n        <div className='mr-1 w-0 grow'>\n          <div\n            className='system-xs-medium mb-0.5 flex items-center truncate text-text-secondary'\n            title={file.name}\n          >\n            <div className='truncate'>{name}</div>\n          </div>\n          <div className='system-2xs-medium-uppercase flex items-center text-text-tertiary'>\n            {\n              ext && (\n                <span>{ext.toLowerCase()}</span>\n              )\n            }\n            {\n              ext && (\n                <span className='system-2xs-medium mx-1'>•</span>\n              )\n            }\n            {\n              !!file.size && (\n                <span>{formatFileSize(file.size)}</span>\n              )\n            }\n          </div>\n        </div>\n        <div className='flex shrink-0 items-center'>\n          {\n            progress >= 0 && !fileIsUploaded(file) && (\n              <ProgressCircle\n                className='mr-2.5'\n                percentage={progress}\n              />\n            )\n          }\n          {\n            progress === -1 && (\n              <ActionButton\n                className='mr-1'\n                onClick={() => onReUpload?.(id)}\n              >\n                <ReplayLine className='h-4 w-4 text-text-tertiary' />\n              </ActionButton>\n            )\n          }\n          {\n            showDeleteAction && (\n              <ActionButton onClick={() => onRemove?.(id)}>\n                <RiDeleteBinLine className='h-4 w-4' />\n              </ActionButton>\n            )\n          }\n          {\n            canPreview && isImageFile && (\n              <ActionButton className='mr-1' onClick={() => setImagePreviewUrl(url || '')}>\n                <RiEyeLine className='h-4 w-4' />\n              </ActionButton>\n            )\n          }\n          {\n            showDownloadAction && (\n              <ActionButton onClick={(e) => {\n                e.stopPropagation()\n                downloadFile(url || base64Url || '', name)\n              }}>\n                <RiDownloadLine className='h-4 w-4' />\n              </ActionButton>\n            )\n          }\n        </div>\n      </div>\n      {\n        imagePreviewUrl && canPreview && (\n          <ImagePreview\n            title={name}\n            url={imagePreviewUrl}\n            onCancel={() => setImagePreviewUrl('')}\n          />\n        )\n      }\n    </>\n  )\n}\n\nexport default memo(FileInAttachmentItem)\n"], "names": [], "mappings": ";;;;AAAA;AAIA;AAKA;AACA;AAMA;AAEA;AACA;;;;;;AAEA;AACA;AAAA;;;;;;AAEA;;;;;;;;;;;;;;;AAUA,MAAM,uBAAuB,CAAC,EAC5B,IAAI,EACJ,gBAAgB,EAChB,qBAAqB,IAAI,EACzB,QAAQ,EACR,UAAU,EACV,UAAU,EACgB;;IAC1B,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG;IAChF,MAAM,MAAM,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,MAAM;IACzC,MAAM,cAAc,oBAAoB,uBAAuB,KAAK;IACpE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,qBACE;;0BACE,oWAAC;gBAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EACf,uIACA,aAAa,CAAC,KAAK;;kCAEnB,oWAAC;wBAAI,WAAU;;4BAEX,6BACE,oWAAC,4KAAA,CAAA,UAAe;gCACd,WAAU;gCACV,UAAU,aAAa,OAAO;;;;;;4BAKlC,CAAC,6BACC,oWAAC,yKAAA,CAAA,UAAY;gCACX,MAAM,CAAA,GAAA,yJAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM;gCAClC,MAAK;;;;;;;;;;;;kCAKb,oWAAC;wBAAI,WAAU;;0CACb,oWAAC;gCACC,WAAU;gCACV,OAAO,KAAK,IAAI;0CAEhB,cAAA,oWAAC;oCAAI,WAAU;8CAAY;;;;;;;;;;;0CAE7B,oWAAC;gCAAI,WAAU;;oCAEX,qBACE,oWAAC;kDAAM,IAAI,WAAW;;;;;;oCAIxB,qBACE,oWAAC;wCAAK,WAAU;kDAAyB;;;;;;oCAI3C,CAAC,CAAC,KAAK,IAAI,kBACT,oWAAC;kDAAM,eAAe,KAAK,IAAI;;;;;;;;;;;;;;;;;;kCAKvC,oWAAC;wBAAI,WAAU;;4BAEX,YAAY,KAAK,CAAC,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,uBAC/B,oWAAC,sKAAA,CAAA,UAAc;gCACb,WAAU;gCACV,YAAY;;;;;;4BAKhB,aAAa,CAAC,mBACZ,oWAAC,0JAAA,CAAA,UAAY;gCACX,WAAU;gCACV,SAAS,IAAM,aAAa;0CAE5B,cAAA,oWAAC,uNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;4BAK1B,kCACE,oWAAC,0JAAA,CAAA,UAAY;gCAAC,SAAS,IAAM,WAAW;0CACtC,cAAA,oWAAC,wOAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;;;;;;4BAK/B,cAAc,6BACZ,oWAAC,0JAAA,CAAA,UAAY;gCAAC,WAAU;gCAAO,SAAS,IAAM,mBAAmB,OAAO;0CACtE,cAAA,oWAAC,wOAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;4BAKzB,oCACE,oWAAC,0JAAA,CAAA,UAAY;gCAAC,SAAS,CAAC;oCACtB,EAAE,eAAe;oCACjB,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE,OAAO,aAAa,IAAI;gCACvC;0CACE,cAAA,oWAAC,wOAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAOlC,mBAAmB,4BACjB,oWAAC,sKAAA,CAAA,UAAY;gBACX,OAAO;gBACP,KAAK;gBACL,UAAU,IAAM,mBAAmB;;;;;;;;AAM/C;GArHM;KAAA;2DAuHS,CAAA,GAAA,oUAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 6513, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/file-uploader-in-attachment/index.tsx"], "sourcesContent": ["import {\n  useCallback,\n} from 'react'\nimport {\n  RiLink,\n  RiUploadCloud2Line,\n} from '@remixicon/react'\nimport { useTranslation } from 'react-i18next'\nimport FileFromLinkOrLocal from '../file-from-link-or-local'\nimport {\n  FileContextProvider,\n  useStore,\n} from '../store'\nimport type { FileEntity } from '../types'\nimport FileInput from '../file-input'\nimport { useFile } from '../hooks'\nimport FileItem from './file-item'\nimport Button from '@/components/base/button'\nimport cn from '@/utils/classnames'\nimport type { FileUpload } from '@/components/base/features/types'\nimport { TransferMethod } from '@/types/app'\n\ntype Option = {\n  value: string\n  label: string\n  icon: React.JSX.Element\n}\ntype FileUploaderInAttachmentProps = {\n  fileConfig: FileUpload\n}\nconst FileUploaderInAttachment = ({\n  fileConfig,\n}: FileUploaderInAttachmentProps) => {\n  const { t } = useTranslation()\n  const files = useStore(s => s.files)\n  const {\n    handleRemoveFile,\n    handleReUploadFile,\n  } = useFile(fileConfig)\n  const options = [\n    {\n      value: TransferMethod.local_file,\n      label: t('common.fileUploader.uploadFromComputer'),\n      icon: <RiUploadCloud2Line className='h-4 w-4' />,\n    },\n    {\n      value: TransferMethod.remote_url,\n      label: t('common.fileUploader.pasteFileLink'),\n      icon: <RiLink className='h-4 w-4' />,\n    },\n  ]\n\n  const renderButton = useCallback((option: Option, open?: boolean) => {\n    return (\n      <Button\n        key={option.value}\n        variant='tertiary'\n        className={cn('relative grow', open && 'bg-components-button-tertiary-bg-hover')}\n        disabled={!!(fileConfig.number_limits && files.length >= fileConfig.number_limits)}\n      >\n        {option.icon}\n        <span className='ml-1'>{option.label}</span>\n        {\n          option.value === TransferMethod.local_file && (\n            <FileInput fileConfig={fileConfig} />\n          )\n        }\n      </Button>\n    )\n  }, [fileConfig, files.length])\n  const renderTrigger = useCallback((option: Option) => {\n    return (open: boolean) => renderButton(option, open)\n  }, [renderButton])\n  const renderOption = useCallback((option: Option) => {\n    if (option.value === TransferMethod.local_file && fileConfig?.allowed_file_upload_methods?.includes(TransferMethod.local_file))\n      return renderButton(option)\n\n    if (option.value === TransferMethod.remote_url && fileConfig?.allowed_file_upload_methods?.includes(TransferMethod.remote_url)) {\n      return (\n        <FileFromLinkOrLocal\n          key={option.value}\n          showFromLocal={false}\n          trigger={renderTrigger(option)}\n          fileConfig={fileConfig}\n        />\n      )\n    }\n  }, [renderButton, renderTrigger, fileConfig])\n\n  return (\n    <div>\n      <div className='flex items-center space-x-1'>\n        {options.map(renderOption)}\n      </div>\n      <div className='mt-1 space-y-1'>\n        {\n          files.map(file => (\n            <FileItem\n              key={file.id}\n              file={file}\n              showDeleteAction\n              showDownloadAction={false}\n              onRemove={() => handleRemoveFile(file.id)}\n              onReUpload={() => handleReUploadFile(file.id)}\n            />\n          ))\n        }\n      </div>\n    </div>\n  )\n}\n\ntype FileUploaderInAttachmentWrapperProps = {\n  value?: FileEntity[]\n  onChange: (files: FileEntity[]) => void\n  fileConfig: FileUpload\n}\nconst FileUploaderInAttachmentWrapper = ({\n  value,\n  onChange,\n  fileConfig,\n}: FileUploaderInAttachmentWrapperProps) => {\n  return (\n    <FileContextProvider\n      value={value}\n      onChange={onChange}\n    >\n      <FileUploaderInAttachment fileConfig={fileConfig} />\n    </FileContextProvider>\n  )\n}\n\nexport default FileUploaderInAttachmentWrapper\n"], "names": [], "mappings": ";;;;AAAA;AAGA;AAIA;AAAA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAYA,MAAM,2BAA2B,CAAC,EAChC,UAAU,EACoB;;IAC9B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,QAAQ,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD;oDAAE,CAAA,IAAK,EAAE,KAAK;;IACnC,MAAM,EACJ,gBAAgB,EAChB,kBAAkB,EACnB,GAAG,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE;IACZ,MAAM,UAAU;QACd;YACE,OAAO,eAAe,UAAU;YAChC,OAAO,EAAE;YACT,oBAAM,oWAAC,wOAAA,CAAA,qBAAkB;gBAAC,WAAU;;;;;;QACtC;QACA;YACE,OAAO,eAAe,UAAU;YAChC,OAAO,EAAE;YACT,oBAAM,oWAAC,wOAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC1B;KACD;IAED,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;8DAAE,CAAC,QAAgB;YAChD,qBACE,oWAAC,gJAAA,CAAA,UAAM;gBAEL,SAAQ;gBACR,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,iBAAiB,QAAQ;gBACvC,UAAU,CAAC,CAAC,CAAC,WAAW,aAAa,IAAI,MAAM,MAAM,IAAI,WAAW,aAAa;;oBAEhF,OAAO,IAAI;kCACZ,oWAAC;wBAAK,WAAU;kCAAQ,OAAO,KAAK;;;;;;oBAElC,OAAO,KAAK,KAAK,eAAe,UAAU,kBACxC,oWAAC,kKAAA,CAAA,UAAS;wBAAC,YAAY;;;;;;;eATtB,OAAO,KAAK;;;;;QAcvB;6DAAG;QAAC;QAAY,MAAM,MAAM;KAAC;IAC7B,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;+DAAE,CAAC;YACjC;uEAAO,CAAC,OAAkB,aAAa,QAAQ;;QACjD;8DAAG;QAAC;KAAa;IACjB,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YAChC,IAAI,OAAO,KAAK,KAAK,eAAe,UAAU,IAAI,YAAY,6BAA6B,SAAS,eAAe,UAAU,GAC3H,OAAO,aAAa;YAEtB,IAAI,OAAO,KAAK,KAAK,eAAe,UAAU,IAAI,YAAY,6BAA6B,SAAS,eAAe,UAAU,GAAG;gBAC9H,qBACE,oWAAC,iMAAA,CAAA,UAAmB;oBAElB,eAAe;oBACf,SAAS,cAAc;oBACvB,YAAY;mBAHP,OAAO,KAAK;;;;;YAMvB;QACF;6DAAG;QAAC;QAAc;QAAe;KAAW;IAE5C,qBACE,oWAAC;;0BACC,oWAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC;;;;;;0BAEf,oWAAC;gBAAI,WAAU;0BAEX,MAAM,GAAG,CAAC,CAAA,qBACR,oWAAC,yMAAA,CAAA,UAAQ;wBAEP,MAAM;wBACN,gBAAgB;wBAChB,oBAAoB;wBACpB,UAAU,IAAM,iBAAiB,KAAK,EAAE;wBACxC,YAAY,IAAM,mBAAmB,KAAK,EAAE;uBALvC,KAAK,EAAE;;;;;;;;;;;;;;;;AAY1B;GAhFM;;QAGU,8XAAA,CAAA,iBAAc;QACd,0JAAA,CAAA,WAAQ;QAIlB,yJAAA,CAAA,UAAO;;;KARP;AAuFN,MAAM,kCAAkC,CAAC,EACvC,KAAK,EACL,QAAQ,EACR,UAAU,EAC2B;IACrC,qBACE,oWAAC,0JAAA,CAAA,sBAAmB;QAClB,OAAO;QACP,UAAU;kBAEV,cAAA,oWAAC;YAAyB,YAAY;;;;;;;;;;;AAG5C;MAbM;uCAeS", "debugId": null}}, {"offset": {"line": 6714, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/file-uploader-in-chat-input/index.tsx"], "sourcesContent": ["import {\n  memo,\n  useCallback,\n} from 'react'\nimport {\n  RiAttachmentLine,\n} from '@remixicon/react'\nimport FileFromLinkOrLocal from '../file-from-link-or-local'\nimport ActionButton from '@/components/base/action-button'\nimport cn from '@/utils/classnames'\nimport type { FileUpload } from '@/components/base/features/types'\nimport { TransferMethod } from '@/types/app'\n\ntype FileUploaderInChatInputProps = {\n  fileConfig: FileUpload\n}\nconst FileUploaderInChatInput = ({\n  fileConfig,\n}: FileUploaderInChatInputProps) => {\n  const renderTrigger = useCallback((open: boolean) => {\n    return (\n      <ActionButton\n        size='l'\n        className={cn(open && 'bg-state-base-hover')}\n      >\n        <RiAttachmentLine className='h-5 w-5' />\n      </ActionButton>\n    )\n  }, [])\n\n  return (\n    <FileFromLinkOrLocal\n      trigger={renderTrigger}\n      fileConfig={fileConfig}\n      showFromLocal={fileConfig?.allowed_file_upload_methods?.includes(TransferMethod.local_file)}\n      showFromLink={fileConfig?.allowed_file_upload_methods?.includes(TransferMethod.remote_url)}\n    />\n  )\n}\n\nexport default memo(FileUploaderInChatInput)\n"], "names": [], "mappings": ";;;;AAAA;AAIA;AAGA;AACA;AACA;;;;;;;;;;;;;;AAOA,MAAM,0BAA0B,CAAC,EAC/B,UAAU,EACmB;;IAC7B,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YACjC,qBACE,oWAAC,0JAAA,CAAA,UAAY;gBACX,MAAK;gBACL,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,QAAQ;0BAEtB,cAAA,oWAAC,wOAAA,CAAA,mBAAgB;oBAAC,WAAU;;;;;;;;;;;QAGlC;6DAAG,EAAE;IAEL,qBACE,oWAAC,iMAAA,CAAA,UAAmB;QAClB,SAAS;QACT,YAAY;QACZ,eAAe,YAAY,6BAA6B,SAAS,eAAe,UAAU;QAC1F,cAAc,YAAY,6BAA6B,SAAS,eAAe,UAAU;;;;;;AAG/F;GAtBM;KAAA;2DAwBS,CAAA,GAAA,oUAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 6783, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/file-uploader-in-chat-input/file-image-item.tsx"], "sourcesContent": ["import { useState } from 'react'\nimport {\n  RiCloseLine,\n  RiDownloadLine,\n} from '@remixicon/react'\nimport FileImageRender from '../file-image-render'\nimport type { FileEntity } from '../types'\nimport {\n  downloadFile,\n  fileIsUploaded,\n} from '../utils'\nimport Button from '@/components/base/button'\nimport ProgressCircle from '@/components/base/progress-bar/progress-circle'\nimport { ReplayLine } from '@/components/base/icons/src/vender/other'\nimport ImagePreview from '@/components/base/image-uploader/image-preview'\n\ntype FileImageItemProps = {\n  file: FileEntity\n  showDeleteAction?: boolean\n  showDownloadAction?: boolean\n  canPreview?: boolean\n  onRemove?: (fileId: string) => void\n  onReUpload?: (fileId: string) => void\n}\nconst FileImageItem = ({\n  file,\n  showDeleteAction,\n  showDownloadAction,\n  canPreview,\n  onRemove,\n  onReUpload,\n}: FileImageItemProps) => {\n  const { id, progress, base64Url, url, name } = file\n  const [imagePreviewUrl, setImagePreviewUrl] = useState('')\n\n  return (\n    <>\n      <div\n        className='group/file-image relative cursor-pointer'\n        onClick={() => canPreview && setImagePreviewUrl(base64Url || url || '')}\n      >\n        {\n          showDeleteAction && (\n            <Button\n              className='absolute -right-1.5 -top-1.5 z-[11] hidden h-5 w-5 rounded-full p-0 group-hover/file-image:flex'\n              onClick={() => onRemove?.(id)}\n            >\n              <RiCloseLine className='h-4 w-4 text-components-button-secondary-text' />\n            </Button>\n          )\n        }\n        <FileImageRender\n          className='h-[68px] w-[68px] shadow-md'\n          imageUrl={base64Url || url || ''}\n          showDownloadAction={showDownloadAction}\n        />\n        {\n          progress >= 0 && !fileIsUploaded(file) && (\n            <div className='absolute inset-0 z-10 flex items-center justify-center border-[2px] border-effects-image-frame bg-background-overlay-alt'>\n              <ProgressCircle\n                percentage={progress}\n                size={12}\n                circleStrokeColor='stroke-components-progress-white-border'\n                circleFillColor='fill-transparent'\n                sectorFillColor='fill-components-progress-white-progress'\n              />\n            </div>\n          )\n        }\n        {\n          progress === -1 && (\n            <div className='absolute inset-0 z-10 flex items-center justify-center border-[2px] border-state-destructive-border bg-background-overlay-destructive'>\n              <ReplayLine\n                className='h-5 w-5'\n                onClick={() => onReUpload?.(id)}\n              />\n            </div>\n          )\n        }\n        {\n          showDownloadAction && (\n            <div className='absolute inset-0.5 z-10 hidden bg-background-overlay-alt bg-opacity-[0.3] group-hover/file-image:block'>\n              <div\n                className='absolute bottom-0.5 right-0.5  flex h-6 w-6 items-center justify-center rounded-lg bg-components-actionbar-bg shadow-md'\n                onClick={(e) => {\n                  e.stopPropagation()\n                  downloadFile(url || base64Url || '', name)\n                }}\n              >\n                <RiDownloadLine className='h-4 w-4 text-text-tertiary' />\n              </div>\n            </div>\n          )\n        }\n      </div>\n      {\n        imagePreviewUrl && canPreview && (\n          <ImagePreview\n            title={name}\n            url={imagePreviewUrl}\n            onCancel={() => setImagePreviewUrl('')}\n          />\n        )\n      }\n    </>\n  )\n}\n\nexport default FileImageItem\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAIA;AAEA;AAIA;AACA;AACA;AAAA;AACA;;;;;;;;;;;AAUA,MAAM,gBAAgB,CAAC,EACrB,IAAI,EACJ,gBAAgB,EAChB,kBAAkB,EAClB,UAAU,EACV,QAAQ,EACR,UAAU,EACS;;IACnB,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,qBACE;;0BACE,oWAAC;gBACC,WAAU;gBACV,SAAS,IAAM,cAAc,mBAAmB,aAAa,OAAO;;oBAGlE,kCACE,oWAAC,gJAAA,CAAA,UAAM;wBACL,WAAU;wBACV,SAAS,IAAM,WAAW;kCAE1B,cAAA,oWAAC,wOAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAI7B,oWAAC,4KAAA,CAAA,UAAe;wBACd,WAAU;wBACV,UAAU,aAAa,OAAO;wBAC9B,oBAAoB;;;;;;oBAGpB,YAAY,KAAK,CAAC,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,uBAC/B,oWAAC;wBAAI,WAAU;kCACb,cAAA,oWAAC,sKAAA,CAAA,UAAc;4BACb,YAAY;4BACZ,MAAM;4BACN,mBAAkB;4BAClB,iBAAgB;4BAChB,iBAAgB;;;;;;;;;;;oBAMtB,aAAa,CAAC,mBACZ,oWAAC;wBAAI,WAAU;kCACb,cAAA,oWAAC,uNAAA,CAAA,aAAU;4BACT,WAAU;4BACV,SAAS,IAAM,aAAa;;;;;;;;;;;oBAMlC,oCACE,oWAAC;wBAAI,WAAU;kCACb,cAAA,oWAAC;4BACC,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE,OAAO,aAAa,IAAI;4BACvC;sCAEA,cAAA,oWAAC,wOAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAOlC,mBAAmB,4BACjB,oWAAC,sKAAA,CAAA,UAAY;gBACX,OAAO;gBACP,KAAK;gBACL,UAAU,IAAM,mBAAmB;;;;;;;;AAM/C;GAlFM;KAAA;uCAoFS", "debugId": null}}, {"offset": {"line": 6930, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/dynamic-pdf-preview.tsx"], "sourcesContent": ["'use client'\n\nimport dynamic from 'next/dynamic'\n\ntype DynamicPdfPreviewProps = {\n  url: string\n  onCancel: () => void\n}\nconst DynamicPdfPreview = dynamic<DynamicPdfPreviewProps>(\n  (() => {\n    if (typeof window !== 'undefined')\n      return import('./pdf-preview')\n  }) as any,\n  { ssr: false }, // This will prevent the module from being loaded on the server-side\n)\n\nexport default DynamicPdfPreview\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAQA,MAAM,oBAAoB,CAAA,GAAA,yUAAA,CAAA,UAAO,AAAD,EAC7B;IACC,wCACE;AACJ;;;;;;IACE,KAAK;;uCAGM", "debugId": null}}, {"offset": {"line": 6957, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/audio-preview.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport { createPortal } from 'react-dom'\nimport { RiCloseLine } from '@remixicon/react'\nimport React from 'react'\n\nimport { useHotkeys } from 'react-hotkeys-hook'\n\ntype AudioPreviewProps = {\n  url: string\n  title: string\n  onCancel: () => void\n}\nconst AudioPreview: FC<AudioPreviewProps> = ({\n  url,\n  title,\n  onCancel,\n}) => {\n  useHotkeys('esc', onCancel)\n\n  return createPortal(\n    <div\n      className='fixed inset-0 z-[1000] flex items-center justify-center bg-black/80 p-8'\n      onClick={e => e.stopPropagation()}\n      tabIndex={-1}\n    >\n      <div>\n        <audio controls title={title} autoPlay={false} preload=\"metadata\">\n          <source\n            type=\"audio/mpeg\"\n            src={url}\n            className='max-h-full max-w-full'\n          />\n        </audio>\n      </div>\n      <div\n        className='absolute right-6 top-6 flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg bg-white/[0.08] backdrop-blur-[2px]'\n        onClick={onCancel}\n      >\n        <RiCloseLine className='h-4 w-4 text-gray-500'/>\n      </div>\n    </div>,\n    document.body,\n  )\n}\n\nexport default AudioPreview\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;;;;;;;;AAUA,MAAM,eAAsC,CAAC,EAC3C,GAAG,EACH,KAAK,EACL,QAAQ,EACT;;IACC,WAAW,OAAO;IAElB,qBAAO,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,gBAChB,oWAAC;QACC,WAAU;QACV,SAAS,CAAA,IAAK,EAAE,eAAe;QAC/B,UAAU,CAAC;;0BAEX,oWAAC;0BACC,cAAA,oWAAC;oBAAM,QAAQ;oBAAC,OAAO;oBAAO,UAAU;oBAAO,SAAQ;8BACrD,cAAA,oWAAC;wBACC,MAAK;wBACL,KAAK;wBACL,WAAU;;;;;;;;;;;;;;;;0BAIhB,oWAAC;gBACC,WAAU;gBACV,SAAS;0BAET,cAAA,oWAAC,wOAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;cAG3B,SAAS,IAAI;AAEjB;GA/BM;;QAKJ;;;KALI;uCAiCS", "debugId": null}}, {"offset": {"line": 7046, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/video-preview.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport { createPortal } from 'react-dom'\nimport { RiCloseLine } from '@remixicon/react'\nimport React from 'react'\nimport { useHotkeys } from 'react-hotkeys-hook'\n\ntype VideoPreviewProps = {\n  url: string\n  title: string\n  onCancel: () => void\n}\nconst VideoPreview: FC<VideoPreviewProps> = ({\n  url,\n  title,\n  onCancel,\n}) => {\n  useHotkeys('esc', onCancel)\n\n  return createPortal(\n    <div\n      className='fixed inset-0 z-[1000] flex items-center justify-center bg-black/80 p-8'\n      onClick={e => e.stopPropagation()}\n      tabIndex={-1}\n    >\n      <div>\n        <video controls title={title} autoPlay={false} preload=\"metadata\">\n          <source\n            type=\"video/mp4\"\n            src={url}\n            className='max-h-full max-w-full'\n          />\n        </video>\n      </div>\n      <div\n        className='absolute right-6 top-6 flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg bg-white/[0.08] backdrop-blur-[2px]'\n        onClick={onCancel}\n      >\n        <RiCloseLine className='h-4 w-4 text-gray-500'/>\n      </div>\n    </div>,\n    document.body,\n  )\n}\n\nexport default VideoPreview\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;;;;;;;;AASA,MAAM,eAAsC,CAAC,EAC3C,GAAG,EACH,KAAK,EACL,QAAQ,EACT;;IACC,WAAW,OAAO;IAElB,qBAAO,CAAA,GAAA,2UAAA,CAAA,eAAY,AAAD,gBAChB,oWAAC;QACC,WAAU;QACV,SAAS,CAAA,IAAK,EAAE,eAAe;QAC/B,UAAU,CAAC;;0BAEX,oWAAC;0BACC,cAAA,oWAAC;oBAAM,QAAQ;oBAAC,OAAO;oBAAO,UAAU;oBAAO,SAAQ;8BACrD,cAAA,oWAAC;wBACC,MAAK;wBACL,KAAK;wBACL,WAAU;;;;;;;;;;;;;;;;0BAIhB,oWAAC;gBACC,WAAU;gBACV,SAAS;0BAET,cAAA,oWAAC,wOAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;cAG3B,SAAS,IAAI;AAEjB;GA/BM;;QAKJ;;;KALI;uCAiCS", "debugId": null}}, {"offset": {"line": 7135, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/file-uploader-in-chat-input/file-item.tsx"], "sourcesContent": ["import {\n  RiCloseLine,\n  RiDownloadLine,\n} from '@remixicon/react'\nimport { useState } from 'react'\nimport {\n  downloadFile,\n  fileIsUploaded,\n  getFileAppearanceType,\n  getFileExtension,\n} from '../utils'\nimport FileTypeIcon from '../file-type-icon'\nimport type { FileEntity } from '../types'\nimport cn from '@/utils/classnames'\nimport { formatFileSize } from '@/utils/format'\nimport ProgressCircle from '@/components/base/progress-bar/progress-circle'\nimport { ReplayLine } from '@/components/base/icons/src/vender/other'\nimport ActionButton from '@/components/base/action-button'\nimport Button from '@/components/base/button'\nimport PdfPreview from '@/components/base/file-uploader/dynamic-pdf-preview'\nimport AudioPreview from '@/components/base/file-uploader/audio-preview'\nimport VideoPreview from '@/components/base/file-uploader/video-preview'\n\ntype FileItemProps = {\n  file: FileEntity\n  showDeleteAction?: boolean\n  showDownloadAction?: boolean\n  canPreview?: boolean\n  onRemove?: (fileId: string) => void\n  onReUpload?: (fileId: string) => void\n}\nconst FileItem = ({\n  file,\n  showDeleteAction,\n  showDownloadAction = true,\n  onRemove,\n  onReUpload,\n  canPreview,\n}: FileItemProps) => {\n  const { id, name, type, progress, url, base64Url, isRemote } = file\n  const [previewUrl, setPreviewUrl] = useState('')\n  const ext = getFileExtension(name, type, isRemote)\n  const uploadError = progress === -1\n\n  let tmp_preview_url = url || base64Url\n  if (!tmp_preview_url && file?.originalFile)\n    tmp_preview_url = URL.createObjectURL(file.originalFile.slice()).toString()\n\n  return (\n    <>\n      <div\n        className={cn(\n          'group/file-item relative h-[68px] w-[144px] rounded-lg border-[0.5px] border-components-panel-border bg-components-card-bg p-2 shadow-xs',\n          !uploadError && 'hover:bg-components-card-bg-alt',\n          uploadError && 'border border-state-destructive-border bg-state-destructive-hover',\n          uploadError && 'bg-state-destructive-hover-alt hover:border-[0.5px] hover:border-state-destructive-border',\n        )}\n      >\n        {\n          showDeleteAction && (\n            <Button\n              className='absolute -right-1.5 -top-1.5 z-[11] hidden h-5 w-5 rounded-full p-0 group-hover/file-item:flex'\n              onClick={() => onRemove?.(id)}\n            >\n              <RiCloseLine className='h-4 w-4 text-components-button-secondary-text' />\n            </Button>\n          )\n        }\n        <div\n          className='system-xs-medium mb-1 line-clamp-2 h-8 cursor-pointer break-all text-text-tertiary'\n          title={name}\n          onClick={() => canPreview && setPreviewUrl(tmp_preview_url || '')}\n        >\n          {name}\n        </div>\n        <div className='relative flex items-center justify-between'>\n          <div className='system-2xs-medium-uppercase flex items-center text-text-tertiary'>\n            <FileTypeIcon\n              size='sm'\n              type={getFileAppearanceType(name, type)}\n              className='mr-1'\n            />\n            {\n              ext && (\n                <>\n                  {ext}\n                  <div className='mx-1'>·</div>\n                </>\n              )\n            }\n            {\n              !!file.size && formatFileSize(file.size)\n            }\n          </div>\n          {\n            showDownloadAction && tmp_preview_url && (\n              <ActionButton\n                size='m'\n                className='absolute -right-1 -top-1 hidden group-hover/file-item:flex'\n                onClick={(e) => {\n                  e.stopPropagation()\n                  downloadFile(tmp_preview_url || '', name)\n                }}\n              >\n                <RiDownloadLine className='h-3.5 w-3.5 text-text-tertiary' />\n              </ActionButton>\n            )\n          }\n          {\n            progress >= 0 && !fileIsUploaded(file) && (\n              <ProgressCircle\n                percentage={progress}\n                size={12}\n                className='shrink-0'\n              />\n            )\n          }\n          {\n            uploadError && (\n              <ReplayLine\n                className='h-4 w-4 text-text-tertiary'\n                onClick={() => onReUpload?.(id)}\n              />\n            )\n          }\n        </div>\n      </div>\n      {\n        type.split('/')[0] === 'audio' && canPreview && previewUrl && (\n          <AudioPreview\n            title={name}\n            url={previewUrl}\n            onCancel={() => setPreviewUrl('')}\n          />\n        )\n      }\n      {\n        type.split('/')[0] === 'video' && canPreview && previewUrl && (\n          <VideoPreview\n            title={name}\n            url={previewUrl}\n            onCancel={() => setPreviewUrl('')}\n          />\n        )\n      }\n      {\n        type.split('/')[1] === 'pdf' && canPreview && previewUrl && (\n          <PdfPreview url={previewUrl} onCancel={() => { setPreviewUrl('') }} />\n        )\n      }\n    </>\n  )\n}\n\nexport default FileItem\n"], "names": [], "mappings": ";;;;AAAA;AAIA;AACA;AAMA;AAEA;;;;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAUA,MAAM,WAAW,CAAC,EAChB,IAAI,EACJ,gBAAgB,EAChB,qBAAqB,IAAI,EACzB,QAAQ,EACR,UAAU,EACV,UAAU,EACI;;IACd,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,MAAM,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,MAAM;IACzC,MAAM,cAAc,aAAa,CAAC;IAElC,IAAI,kBAAkB,OAAO;IAC7B,IAAI,CAAC,mBAAmB,MAAM,cAC5B,kBAAkB,IAAI,eAAe,CAAC,KAAK,YAAY,CAAC,KAAK,IAAI,QAAQ;IAE3E,qBACE;;0BACE,oWAAC;gBACC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EACV,4IACA,CAAC,eAAe,mCAChB,eAAe,qEACf,eAAe;;oBAIf,kCACE,oWAAC,gJAAA,CAAA,UAAM;wBACL,WAAU;wBACV,SAAS,IAAM,WAAW;kCAE1B,cAAA,oWAAC,wOAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAI7B,oWAAC;wBACC,WAAU;wBACV,OAAO;wBACP,SAAS,IAAM,cAAc,cAAc,mBAAmB;kCAE7D;;;;;;kCAEH,oWAAC;wBAAI,WAAU;;0CACb,oWAAC;gCAAI,WAAU;;kDACb,oWAAC,yKAAA,CAAA,UAAY;wCACX,MAAK;wCACL,MAAM,CAAA,GAAA,yJAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM;wCAClC,WAAU;;;;;;oCAGV,qBACE;;4CACG;0DACD,oWAAC;gDAAI,WAAU;0DAAO;;;;;;;;oCAK1B,CAAC,CAAC,KAAK,IAAI,IAAI,eAAe,KAAK,IAAI;;;;;;;4BAIzC,sBAAsB,iCACpB,oWAAC,0JAAA,CAAA,UAAY;gCACX,MAAK;gCACL,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB,IAAI;gCACtC;0CAEA,cAAA,oWAAC,wOAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;4BAK9B,YAAY,KAAK,CAAC,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,uBAC/B,oWAAC,sKAAA,CAAA,UAAc;gCACb,YAAY;gCACZ,MAAM;gCACN,WAAU;;;;;;4BAKd,6BACE,oWAAC,uNAAA,CAAA,aAAU;gCACT,WAAU;gCACV,SAAS,IAAM,aAAa;;;;;;;;;;;;;;;;;;YAOpC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,WAAW,cAAc,4BAC9C,oWAAC,qKAAA,CAAA,UAAY;gBACX,OAAO;gBACP,KAAK;gBACL,UAAU,IAAM,cAAc;;;;;;YAKlC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,WAAW,cAAc,4BAC9C,oWAAC,qKAAA,CAAA,UAAY;gBACX,OAAO;gBACP,KAAK;gBACL,UAAU,IAAM,cAAc;;;;;;YAKlC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS,cAAc,4BAC5C,oWAAC,8KAAA,CAAA,UAAU;gBAAC,KAAK;gBAAY,UAAU;oBAAQ,cAAc;gBAAI;;;;;;;;AAK3E;GAzHM;KAAA;uCA2HS", "debugId": null}}, {"offset": {"line": 7338, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/file-uploader-in-chat-input/file-list.tsx"], "sourcesContent": ["import { useFile } from '../hooks'\nimport { useStore } from '../store'\nimport type { FileEntity } from '../types'\nimport FileImageItem from './file-image-item'\nimport FileItem from './file-item'\nimport type { FileUpload } from '@/components/base/features/types'\nimport { SupportUploadFileTypes } from '@/components/workflow/types'\nimport cn from '@/utils/classnames'\n\ntype FileListProps = {\n  className?: string\n  files: FileEntity[]\n  onRemove?: (fileId: string) => void\n  onReUpload?: (fileId: string) => void\n  showDeleteAction?: boolean\n  showDownloadAction?: boolean\n  canPreview?: boolean\n}\nexport const FileList = ({\n  className,\n  files,\n  onReUpload,\n  onRemove,\n  showDeleteAction = true,\n  showDownloadAction = false,\n  canPreview = true,\n}: FileListProps) => {\n  return (\n    <div className={cn('flex flex-wrap gap-2', className)}>\n      {\n        files.map((file) => {\n          if (file.supportFileType === SupportUploadFileTypes.image) {\n            return (\n              <FileImageItem\n                key={file.id}\n                file={file}\n                showDeleteAction={showDeleteAction}\n                showDownloadAction={showDownloadAction}\n                onRemove={onRemove}\n                onReUpload={onReUpload}\n                canPreview={canPreview}\n              />\n            )\n          }\n\n          return (\n            <FileItem\n              key={file.id}\n              file={file}\n              showDeleteAction={showDeleteAction}\n              showDownloadAction={showDownloadAction}\n              onRemove={onRemove}\n              onReUpload={onReUpload}\n              canPreview={canPreview}\n            />\n          )\n        })\n      }\n    </div>\n  )\n}\n\ntype FileListInChatInputProps = {\n  fileConfig: FileUpload\n}\nexport const FileListInChatInput = ({\n  fileConfig,\n}: FileListInChatInputProps) => {\n  const files = useStore(s => s.files)\n  const {\n    handleRemoveFile,\n    handleReUploadFile,\n  } = useFile(fileConfig)\n\n  return (\n    <FileList\n      files={files}\n      onReUpload={handleReUploadFile}\n      onRemove={handleRemoveFile}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;AACA;;;;;;AAGA;;;;;;;;;AAWO,MAAM,WAAW,CAAC,EACvB,SAAS,EACT,KAAK,EACL,UAAU,EACV,QAAQ,EACR,mBAAmB,IAAI,EACvB,qBAAqB,KAAK,EAC1B,aAAa,IAAI,EACH;IACd,qBACE,oWAAC;QAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,wBAAwB;kBAEvC,MAAM,GAAG,CAAC,CAAC;YACT,IAAI,KAAK,eAAe,KAAK,uBAAuB,KAAK,EAAE;gBACzD,qBACE,oWAAC,qNAAA,CAAA,UAAa;oBAEZ,MAAM;oBACN,kBAAkB;oBAClB,oBAAoB;oBACpB,UAAU;oBACV,YAAY;oBACZ,YAAY;mBANP,KAAK,EAAE;;;;;YASlB;YAEA,qBACE,oWAAC,4MAAA,CAAA,UAAQ;gBAEP,MAAM;gBACN,kBAAkB;gBAClB,oBAAoB;gBACpB,UAAU;gBACV,YAAY;gBACZ,YAAY;eANP,KAAK,EAAE;;;;;QASlB;;;;;;AAIR;KA1Ca;AA+CN,MAAM,sBAAsB,CAAC,EAClC,UAAU,EACe;;IACzB,MAAM,QAAQ,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD;+CAAE,CAAA,IAAK,EAAE,KAAK;;IACnC,MAAM,EACJ,gBAAgB,EAChB,kBAAkB,EACnB,GAAG,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE;IAEZ,qBACE,oWAAC;QACC,OAAO;QACP,YAAY;QACZ,UAAU;;;;;;AAGhB;GAhBa;;QAGG,0JAAA,CAAA,WAAQ;QAIlB,yJAAA,CAAA,UAAO;;;MAPA", "debugId": null}}, {"offset": {"line": 7434, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/file-uploader/index.ts"], "sourcesContent": ["export { default as <PERSON><PERSON>ploaderInAttachmentWrapper } from './file-uploader-in-attachment'\nexport { default as File<PERSON>temInAttachment } from './file-uploader-in-attachment/file-item'\nexport { default as FileUploaderInChatInput } from './file-uploader-in-chat-input'\nexport { default as FileTypeIcon } from './file-type-icon'\nexport { FileListInChatInput } from './file-uploader-in-chat-input/file-list'\nexport { FileList } from './file-uploader-in-chat-input/file-list'\nexport { default as FileItem } from './file-uploader-in-chat-input/file-item'\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AAEA", "debugId": null}}, {"offset": {"line": 7481, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/inputs-form/content.tsx"], "sourcesContent": ["import React, { useCallback } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport { useChatWithHistoryContext } from '../context'\nimport Input from '@/components/base/input'\nimport Textarea from '@/components/base/textarea'\nimport { PortalSelect } from '@/components/base/select'\nimport { FileUploaderInAttachmentWrapper } from '@/components/base/file-uploader'\nimport { InputVarType } from '@/components/workflow/types'\n\ntype Props = {\n  showTip?: boolean\n}\n\nconst InputsFormContent = ({ showTip }: Props) => {\n  const { t } = useTranslation()\n  const {\n    appParams,\n    inputsForms,\n    currentConversationId,\n    currentConversationInputs,\n    setCurrentConversationInputs,\n    newConversationInputs,\n    newConversationInputsRef,\n    handleNewConversationInputsChange,\n  } = useChatWithHistoryContext()\n  const inputsFormValue = currentConversationId ? currentConversationInputs : newConversationInputs\n\n  const handleFormChange = useCallback((variable: string, value: any) => {\n    setCurrentConversationInputs({\n      ...currentConversationInputs,\n      [variable]: value,\n    })\n    handleNewConversationInputsChange({\n      ...newConversationInputsRef.current,\n      [variable]: value,\n    })\n  }, [newConversationInputsRef, handleNewConversationInputsChange, currentConversationInputs, setCurrentConversationInputs])\n\n  return (\n    <div className='space-y-4'>\n      {inputsForms.map(form => (\n        <div key={form.variable} className='space-y-1'>\n          <div className='flex h-6 items-center gap-1'>\n            <div className='system-md-semibold text-text-secondary'>{form.label}</div>\n            {!form.required && (\n              <div className='system-xs-regular text-text-tertiary'>{t('appDebug.variableTable.optional')}</div>\n            )}\n          </div>\n          {form.type === InputVarType.textInput && (\n            <Input\n              value={inputsFormValue?.[form.variable] || ''}\n              onChange={e => handleFormChange(form.variable, e.target.value)}\n              placeholder={form.label}\n            />\n          )}\n          {form.type === InputVarType.number && (\n            <Input\n              type='number'\n              value={inputsFormValue?.[form.variable] || ''}\n              onChange={e => handleFormChange(form.variable, e.target.value)}\n              placeholder={form.label}\n            />\n          )}\n          {form.type === InputVarType.paragraph && (\n            <Textarea\n              value={inputsFormValue?.[form.variable] || ''}\n              onChange={e => handleFormChange(form.variable, e.target.value)}\n              placeholder={form.label}\n            />\n          )}\n          {form.type === InputVarType.select && (\n            <PortalSelect\n              popupClassName='w-[200px]'\n              value={inputsFormValue?.[form.variable]}\n              items={form.options.map((option: string) => ({ value: option, name: option }))}\n              onSelect={item => handleFormChange(form.variable, item.value as string)}\n              placeholder={form.label}\n            />\n          )}\n          {form.type === InputVarType.singleFile && (\n            <FileUploaderInAttachmentWrapper\n              value={inputsFormValue?.[form.variable] ? [inputsFormValue?.[form.variable]] : []}\n              onChange={files => handleFormChange(form.variable, files[0])}\n              fileConfig={{\n                allowed_file_types: form.allowed_file_types,\n                allowed_file_extensions: form.allowed_file_extensions,\n                allowed_file_upload_methods: form.allowed_file_upload_methods,\n                number_limits: 1,\n                fileUploadConfig: (appParams as any).system_parameters,\n              }}\n            />\n          )}\n          {form.type === InputVarType.multiFiles && (\n            <FileUploaderInAttachmentWrapper\n              value={inputsFormValue?.[form.variable] || []}\n              onChange={files => handleFormChange(form.variable, files)}\n              fileConfig={{\n                allowed_file_types: form.allowed_file_types,\n                allowed_file_extensions: form.allowed_file_extensions,\n                allowed_file_upload_methods: form.allowed_file_upload_methods,\n                number_limits: form.max_length,\n                fileUploadConfig: (appParams as any).system_parameters,\n              }}\n            />\n          )}\n        </div>\n      ))}\n      {showTip && (\n        <div className='system-xs-regular text-text-tertiary'>{t('share.chat.chatFormTip')}</div>\n      )}\n    </div>\n  )\n}\n\nexport default InputsFormContent\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;AAOA,MAAM,oBAAoB,CAAC,EAAE,OAAO,EAAS;;IAC3C,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,EACJ,SAAS,EACT,WAAW,EACX,qBAAqB,EACrB,yBAAyB,EACzB,4BAA4B,EAC5B,qBAAqB,EACrB,wBAAwB,EACxB,iCAAiC,EAClC,GAAG,CAAA,GAAA,2KAAA,CAAA,4BAAyB,AAAD;IAC5B,MAAM,kBAAkB,wBAAwB,4BAA4B;IAE5E,MAAM,mBAAmB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;2DAAE,CAAC,UAAkB;YACtD,6BAA6B;gBAC3B,GAAG,yBAAyB;gBAC5B,CAAC,SAAS,EAAE;YACd;YACA,kCAAkC;gBAChC,GAAG,yBAAyB,OAAO;gBACnC,CAAC,SAAS,EAAE;YACd;QACF;0DAAG;QAAC;QAA0B;QAAmC;QAA2B;KAA6B;IAEzH,qBACE,oWAAC;QAAI,WAAU;;YACZ,YAAY,GAAG,CAAC,CAAA,qBACf,oWAAC;oBAAwB,WAAU;;sCACjC,oWAAC;4BAAI,WAAU;;8CACb,oWAAC;oCAAI,WAAU;8CAA0C,KAAK,KAAK;;;;;;gCAClE,CAAC,KAAK,QAAQ,kBACb,oWAAC;oCAAI,WAAU;8CAAwC,EAAE;;;;;;;;;;;;wBAG5D,KAAK,IAAI,KAAK,aAAa,SAAS,kBACnC,oWAAC,+IAAA,CAAA,UAAK;4BACJ,OAAO,iBAAiB,CAAC,KAAK,QAAQ,CAAC,IAAI;4BAC3C,UAAU,CAAA,IAAK,iBAAiB,KAAK,QAAQ,EAAE,EAAE,MAAM,CAAC,KAAK;4BAC7D,aAAa,KAAK,KAAK;;;;;;wBAG1B,KAAK,IAAI,KAAK,aAAa,MAAM,kBAChC,oWAAC,+IAAA,CAAA,UAAK;4BACJ,MAAK;4BACL,OAAO,iBAAiB,CAAC,KAAK,QAAQ,CAAC,IAAI;4BAC3C,UAAU,CAAA,IAAK,iBAAiB,KAAK,QAAQ,EAAE,EAAE,MAAM,CAAC,KAAK;4BAC7D,aAAa,KAAK,KAAK;;;;;;wBAG1B,KAAK,IAAI,KAAK,aAAa,SAAS,kBACnC,oWAAC,kJAAA,CAAA,UAAQ;4BACP,OAAO,iBAAiB,CAAC,KAAK,QAAQ,CAAC,IAAI;4BAC3C,UAAU,CAAA,IAAK,iBAAiB,KAAK,QAAQ,EAAE,EAAE,MAAM,CAAC,KAAK;4BAC7D,aAAa,KAAK,KAAK;;;;;;wBAG1B,KAAK,IAAI,KAAK,aAAa,MAAM,kBAChC,oWAAC,gJAAA,CAAA,eAAY;4BACX,gBAAe;4BACf,OAAO,iBAAiB,CAAC,KAAK,QAAQ,CAAC;4BACvC,OAAO,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,SAAmB,CAAC;oCAAE,OAAO;oCAAQ,MAAM;gCAAO,CAAC;4BAC5E,UAAU,CAAA,OAAQ,iBAAiB,KAAK,QAAQ,EAAE,KAAK,KAAK;4BAC5D,aAAa,KAAK,KAAK;;;;;;wBAG1B,KAAK,IAAI,KAAK,aAAa,UAAU,kBACpC,oWAAC,gQAAA,CAAA,kCAA+B;4BAC9B,OAAO,iBAAiB,CAAC,KAAK,QAAQ,CAAC,GAAG;gCAAC,iBAAiB,CAAC,KAAK,QAAQ,CAAC;6BAAC,GAAG,EAAE;4BACjF,UAAU,CAAA,QAAS,iBAAiB,KAAK,QAAQ,EAAE,KAAK,CAAC,EAAE;4BAC3D,YAAY;gCACV,oBAAoB,KAAK,kBAAkB;gCAC3C,yBAAyB,KAAK,uBAAuB;gCACrD,6BAA6B,KAAK,2BAA2B;gCAC7D,eAAe;gCACf,kBAAkB,AAAC,UAAkB,iBAAiB;4BACxD;;;;;;wBAGH,KAAK,IAAI,KAAK,aAAa,UAAU,kBACpC,oWAAC,gQAAA,CAAA,kCAA+B;4BAC9B,OAAO,iBAAiB,CAAC,KAAK,QAAQ,CAAC,IAAI,EAAE;4BAC7C,UAAU,CAAA,QAAS,iBAAiB,KAAK,QAAQ,EAAE;4BACnD,YAAY;gCACV,oBAAoB,KAAK,kBAAkB;gCAC3C,yBAAyB,KAAK,uBAAuB;gCACrD,6BAA6B,KAAK,2BAA2B;gCAC7D,eAAe,KAAK,UAAU;gCAC9B,kBAAkB,AAAC,UAAkB,iBAAiB;4BACxD;;;;;;;mBA7DI,KAAK,QAAQ;;;;;YAkExB,yBACC,oWAAC;gBAAI,WAAU;0BAAwC,EAAE;;;;;;;;;;;;AAIjE;GAnGM;;QACU,8XAAA,CAAA,iBAAc;QAUxB,2KAAA,CAAA,4BAAyB;;;KAXzB;uCAqGS", "debugId": null}}, {"offset": {"line": 7676, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/inputs-form/view-form-dropdown.tsx"], "sourcesContent": ["import { useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport {\n  RiChatSettingsLine,\n} from '@remixicon/react'\nimport { PortalToFollowElem, PortalToFollowElemContent, PortalToFollowElemTrigger } from '@/components/base/portal-to-follow-elem'\nimport ActionButton, { ActionButtonState } from '@/components/base/action-button'\nimport { Message3Fill } from '@/components/base/icons/src/public/other'\nimport InputsFormContent from '@/components/base/chat/chat-with-history/inputs-form/content'\n\nconst ViewFormDropdown = () => {\n  const { t } = useTranslation()\n  const [open, setOpen] = useState(false)\n\n  return (\n    <PortalToFollowElem\n      open={open}\n      onOpenChange={setOpen}\n      placement='bottom-end'\n      offset={{\n        mainAxis: 4,\n        crossAxis: 4,\n      }}\n    >\n      <PortalToFollowElemTrigger\n        onClick={() => setOpen(v => !v)}\n      >\n        <ActionButton size='l' state={open ? ActionButtonState.Hover : ActionButtonState.Default}>\n          <RiChatSettingsLine className='h-[18px] w-[18px]' />\n        </ActionButton>\n      </PortalToFollowElemTrigger>\n      <PortalToFollowElemContent className=\"z-50\">\n        <div className='w-[400px] rounded-2xl border-[0.5px] border-components-panel-border bg-components-panel-bg shadow-lg backdrop-blur-sm'>\n          <div className='flex items-center gap-3 rounded-t-2xl border-b border-divider-subtle px-6 py-4'>\n            <Message3Fill className='h-6 w-6 shrink-0' />\n            <div className='system-xl-semibold grow text-text-secondary'>{t('share.chat.chatSettingsTitle')}</div>\n          </div>\n          <div className='p-6'>\n            <InputsFormContent />\n          </div>\n        </div>\n      </PortalToFollowElemContent>\n    </PortalToFollowElem>\n\n  )\n}\n\nexport default ViewFormDropdown\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAGA;AACA;AACA;AAAA;AACA;;;;;;;;;;AAEA,MAAM,mBAAmB;;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,qBACE,oWAAC,wKAAA,CAAA,qBAAkB;QACjB,MAAM;QACN,cAAc;QACd,WAAU;QACV,QAAQ;YACN,UAAU;YACV,WAAW;QACb;;0BAEA,oWAAC,wKAAA,CAAA,4BAAyB;gBACxB,SAAS,IAAM,QAAQ,CAAA,IAAK,CAAC;0BAE7B,cAAA,oWAAC,0JAAA,CAAA,UAAY;oBAAC,MAAK;oBAAI,OAAO,OAAO,0JAAA,CAAA,oBAAiB,CAAC,KAAK,GAAG,0JAAA,CAAA,oBAAiB,CAAC,OAAO;8BACtF,cAAA,oWAAC,wOAAA,CAAA,qBAAkB;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGlC,oWAAC,wKAAA,CAAA,4BAAyB;gBAAC,WAAU;0BACnC,cAAA,oWAAC;oBAAI,WAAU;;sCACb,oWAAC;4BAAI,WAAU;;8CACb,oWAAC,2NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,oWAAC;oCAAI,WAAU;8CAA+C,EAAE;;;;;;;;;;;;sCAElE,oWAAC;4BAAI,WAAU;sCACb,cAAA,oWAAC,6LAAA,CAAA,UAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9B;GAnCM;;QACU,8XAAA,CAAA,iBAAc;;;KADxB;uCAqCS", "debugId": null}}, {"offset": {"line": 7810, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/header/index.tsx"], "sourcesContent": ["import { useCallback, useState } from 'react'\nimport {\n  RiEditBoxLine,\n  RiLayoutRight2Line,\n  RiResetLeftLine,\n} from '@remixicon/react'\nimport { useTranslation } from 'react-i18next'\nimport {\n  useChatWithHistoryContext,\n} from '../context'\nimport Operation from './operation'\nimport ActionButton, { ActionButtonState } from '@/components/base/action-button'\nimport AppIcon from '@/components/base/app-icon'\nimport Tooltip from '@/components/base/tooltip'\nimport ViewFormDropdown from '@/components/base/chat/chat-with-history/inputs-form/view-form-dropdown'\nimport Confirm from '@/components/base/confirm'\nimport RenameModal from '@/components/base/chat/chat-with-history/sidebar/rename-modal'\nimport type { ConversationItem } from '@/models/share'\nimport cn from '@/utils/classnames'\n\nconst Header = () => {\n  const {\n    appData,\n    currentConversationId,\n    currentConversationItem,\n    inputsForms,\n    pinnedConversationList,\n    handlePinConversation,\n    handleUnpinConversation,\n    conversationRenaming,\n    handleRenameConversation,\n    handleDeleteConversation,\n    handleNewConversation,\n    sidebarCollapseState,\n    handleSidebarCollapse,\n    isResponding,\n  } = useChatWithHistoryContext()\n  const { t } = useTranslation()\n  const isSidebarCollapsed = sidebarCollapseState\n\n  const isPin = pinnedConversationList.some(item => item.id === currentConversationId)\n\n  const [showConfirm, setShowConfirm] = useState<ConversationItem | null>(null)\n  const [showRename, setShowRename] = useState<ConversationItem | null>(null)\n  const handleOperate = useCallback((type: string) => {\n    if (type === 'pin')\n      handlePinConversation(currentConversationId)\n\n    if (type === 'unpin')\n      handleUnpinConversation(currentConversationId)\n\n    if (type === 'delete')\n      setShowConfirm(currentConversationItem as any)\n\n    if (type === 'rename')\n      setShowRename(currentConversationItem as any)\n  }, [currentConversationId, currentConversationItem, handlePinConversation, handleUnpinConversation])\n  const handleCancelConfirm = useCallback(() => {\n    setShowConfirm(null)\n  }, [])\n  const handleDelete = useCallback(() => {\n    if (showConfirm)\n      handleDeleteConversation(showConfirm.id, { onSuccess: handleCancelConfirm })\n  }, [showConfirm, handleDeleteConversation, handleCancelConfirm])\n  const handleCancelRename = useCallback(() => {\n    setShowRename(null)\n  }, [])\n  const handleRename = useCallback((newName: string) => {\n    if (showRename)\n      handleRenameConversation(showRename.id, newName, { onSuccess: handleCancelRename })\n  }, [showRename, handleRenameConversation, handleCancelRename])\n\n  return (\n    <>\n      <div className='flex h-14 shrink-0 items-center justify-between p-3'>\n        <div className={cn('flex items-center gap-1 transition-all duration-200 ease-in-out', !isSidebarCollapsed && 'user-select-none opacity-0')}>\n          <ActionButton className={cn(!isSidebarCollapsed && 'cursor-default')} size='l' onClick={() => handleSidebarCollapse(false)}>\n            <RiLayoutRight2Line className='h-[18px] w-[18px]' />\n          </ActionButton>\n          <div className='mr-1 shrink-0'>\n            <AppIcon\n              size='large'\n              iconType={appData?.site.icon_type}\n              icon={appData?.site.icon}\n              background={appData?.site.icon_background}\n              imageUrl={appData?.site.icon_url}\n            />\n          </div>\n          {!currentConversationId && (\n            <div className={cn('system-md-semibold grow truncate text-text-secondary')}>{appData?.site.title}</div>\n          )}\n          {currentConversationId && currentConversationItem && isSidebarCollapsed && (\n            <>\n              <div className='p-1 text-divider-deep'>/</div>\n              <Operation\n                title={currentConversationItem?.name || ''}\n                isPinned={!!isPin}\n                togglePin={() => handleOperate(isPin ? 'unpin' : 'pin')}\n                isShowDelete\n                isShowRenameConversation\n                onRenameConversation={() => handleOperate('rename')}\n                onDelete={() => handleOperate('delete')}\n              />\n            </>\n          )}\n          <div className='flex items-center px-1'>\n            <div className='h-[14px] w-px bg-divider-regular'></div>\n          </div>\n          {isSidebarCollapsed && (\n            <Tooltip\n              disabled={!!currentConversationId}\n              popupContent={t('share.chat.newChatTip')}\n            >\n              <div>\n                <ActionButton\n                  size='l'\n                  state={(!currentConversationId || isResponding) ? ActionButtonState.Disabled : ActionButtonState.Default}\n                  disabled={!currentConversationId || isResponding}\n                  onClick={handleNewConversation}\n                >\n                  <RiEditBoxLine className='h-[18px] w-[18px]' />\n                </ActionButton>\n              </div>\n            </Tooltip>\n          )}\n        </div>\n        <div className='flex items-center gap-1'>\n          {currentConversationId && (\n            <Tooltip\n              popupContent={t('share.chat.resetChat')}\n            >\n              <ActionButton size='l' onClick={handleNewConversation}>\n                <RiResetLeftLine className='h-[18px] w-[18px]' />\n              </ActionButton>\n            </Tooltip>\n          )}\n          {currentConversationId && inputsForms.length > 0 && (\n            <ViewFormDropdown />\n          )}\n        </div>\n      </div>\n      {!!showConfirm && (\n        <Confirm\n          title={t('share.chat.deleteConversation.title')}\n          content={t('share.chat.deleteConversation.content') || ''}\n          isShow\n          onCancel={handleCancelConfirm}\n          onConfirm={handleDelete}\n        />\n      )}\n      {showRename && (\n        <RenameModal\n          isShow\n          onClose={handleCancelRename}\n          saveLoading={conversationRenaming}\n          name={showRename?.name || ''}\n          onSave={handleRename}\n        />\n      )}\n    </>\n  )\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAKA;AAAA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;AAEA,MAAM,SAAS;;IACb,MAAM,EACJ,OAAO,EACP,qBAAqB,EACrB,uBAAuB,EACvB,WAAW,EACX,sBAAsB,EACtB,qBAAqB,EACrB,uBAAuB,EACvB,oBAAoB,EACpB,wBAAwB,EACxB,wBAAwB,EACxB,qBAAqB,EACrB,oBAAoB,EACpB,qBAAqB,EACrB,YAAY,EACb,GAAG,CAAA,GAAA,2KAAA,CAAA,4BAAyB,AAAD;IAC5B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,qBAAqB;IAE3B,MAAM,QAAQ,uBAAuB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAE9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA2B;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA2B;IACtE,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;6CAAE,CAAC;YACjC,IAAI,SAAS,OACX,sBAAsB;YAExB,IAAI,SAAS,SACX,wBAAwB;YAE1B,IAAI,SAAS,UACX,eAAe;YAEjB,IAAI,SAAS,UACX,cAAc;QAClB;4CAAG;QAAC;QAAuB;QAAyB;QAAuB;KAAwB;IACnG,MAAM,sBAAsB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;mDAAE;YACtC,eAAe;QACjB;kDAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;4CAAE;YAC/B,IAAI,aACF,yBAAyB,YAAY,EAAE,EAAE;gBAAE,WAAW;YAAoB;QAC9E;2CAAG;QAAC;QAAa;QAA0B;KAAoB;IAC/D,MAAM,qBAAqB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;kDAAE;YACrC,cAAc;QAChB;iDAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;4CAAE,CAAC;YAChC,IAAI,YACF,yBAAyB,WAAW,EAAE,EAAE,SAAS;gBAAE,WAAW;YAAmB;QACrF;2CAAG;QAAC;QAAY;QAA0B;KAAmB;IAE7D,qBACE;;0BACE,oWAAC;gBAAI,WAAU;;kCACb,oWAAC;wBAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,mEAAmE,CAAC,sBAAsB;;0CAC3G,oWAAC,0JAAA,CAAA,UAAY;gCAAC,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE,CAAC,sBAAsB;gCAAmB,MAAK;gCAAI,SAAS,IAAM,sBAAsB;0CAClH,cAAA,oWAAC,wOAAA,CAAA,qBAAkB;oCAAC,WAAU;;;;;;;;;;;0CAEhC,oWAAC;gCAAI,WAAU;0CACb,cAAA,oWAAC,qJAAA,CAAA,UAAO;oCACN,MAAK;oCACL,UAAU,SAAS,KAAK;oCACxB,MAAM,SAAS,KAAK;oCACpB,YAAY,SAAS,KAAK;oCAC1B,UAAU,SAAS,KAAK;;;;;;;;;;;4BAG3B,CAAC,uCACA,oWAAC;gCAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,UAAE,AAAD,EAAE;0CAA0D,SAAS,KAAK;;;;;;4BAE5F,yBAAyB,2BAA2B,oCACnD;;kDACE,oWAAC;wCAAI,WAAU;kDAAwB;;;;;;kDACvC,oWAAC,uLAAA,CAAA,UAAS;wCACR,OAAO,yBAAyB,QAAQ;wCACxC,UAAU,CAAC,CAAC;wCACZ,WAAW,IAAM,cAAc,QAAQ,UAAU;wCACjD,YAAY;wCACZ,wBAAwB;wCACxB,sBAAsB,IAAM,cAAc;wCAC1C,UAAU,IAAM,cAAc;;;;;;;;0CAIpC,oWAAC;gCAAI,WAAU;0CACb,cAAA,oWAAC;oCAAI,WAAU;;;;;;;;;;;4BAEhB,oCACC,oWAAC,iJAAA,CAAA,UAAO;gCACN,UAAU,CAAC,CAAC;gCACZ,cAAc,EAAE;0CAEhB,cAAA,oWAAC;8CACC,cAAA,oWAAC,0JAAA,CAAA,UAAY;wCACX,MAAK;wCACL,OAAO,AAAC,CAAC,yBAAyB,eAAgB,0JAAA,CAAA,oBAAiB,CAAC,QAAQ,GAAG,0JAAA,CAAA,oBAAiB,CAAC,OAAO;wCACxG,UAAU,CAAC,yBAAyB;wCACpC,SAAS;kDAET,cAAA,oWAAC,wOAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnC,oWAAC;wBAAI,WAAU;;4BACZ,uCACC,oWAAC,iJAAA,CAAA,UAAO;gCACN,cAAc,EAAE;0CAEhB,cAAA,oWAAC,0JAAA,CAAA,UAAY;oCAAC,MAAK;oCAAI,SAAS;8CAC9B,cAAA,oWAAC,wOAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;;;;;;;;;;;4BAIhC,yBAAyB,YAAY,MAAM,GAAG,mBAC7C,oWAAC,8MAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;;YAItB,CAAC,CAAC,6BACD,oWAAC,iJAAA,CAAA,UAAO;gBACN,OAAO,EAAE;gBACT,SAAS,EAAE,4CAA4C;gBACvD,MAAM;gBACN,UAAU;gBACV,WAAW;;;;;;YAGd,4BACC,oWAAC,8LAAA,CAAA,UAAW;gBACV,MAAM;gBACN,SAAS;gBACT,aAAa;gBACb,MAAM,YAAY,QAAQ;gBAC1B,QAAQ;;;;;;;;AAKlB;GA7IM;;QAgBA,2KAAA,CAAA,4BAAyB;QACf,8XAAA,CAAA,iBAAc;;;KAjBxB;uCA+IS", "debugId": null}}, {"offset": {"line": 8106, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/header/mobile-operation-dropdown.tsx"], "sourcesContent": ["import { useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport {\n  RiMoreFill,\n} from '@remixicon/react'\nimport { PortalToFollowElem, PortalToFollowElemContent, PortalToFollowElemTrigger } from '@/components/base/portal-to-follow-elem'\nimport ActionButton, { ActionButtonState } from '@/components/base/action-button'\n\ntype Props = {\n  handleResetChat: () => void\n  handleViewChatSettings: () => void\n}\n\nconst MobileOperationDropdown = ({\n  handleResetChat,\n  handleViewChatSettings,\n}: Props) => {\n  const { t } = useTranslation()\n  const [open, setOpen] = useState(false)\n\n  return (\n    <PortalToFollowElem\n      open={open}\n      onOpenChange={setOpen}\n      placement='bottom-end'\n      offset={{\n        mainAxis: 4,\n        crossAxis: -4,\n      }}\n    >\n      <PortalToFollowElemTrigger\n        onClick={() => setOpen(v => !v)}\n      >\n        <ActionButton size='l' state={open ? ActionButtonState.Hover : ActionButtonState.Default}>\n          <RiMoreFill className='h-[18px] w-[18px]' />\n        </ActionButton>\n      </PortalToFollowElemTrigger>\n      <PortalToFollowElemContent className=\"z-40\">\n        <div\n          className={'min-w-[160px] rounded-xl border-[0.5px] border-components-panel-border bg-components-panel-bg-blur p-1 shadow-lg backdrop-blur-sm'}\n        >\n          <div className='system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-base-hover' onClick={handleResetChat}>\n            <span className='grow'>{t('share.chat.resetChat')}</span>\n          </div>\n          <div className='system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-3 py-1.5 text-text-secondary hover:bg-state-base-hover' onClick={handleViewChatSettings}>\n            <span className='grow'>{t('share.chat.viewChatSettings')}</span>\n          </div>\n        </div>\n      </PortalToFollowElemContent>\n    </PortalToFollowElem>\n\n  )\n}\n\nexport default MobileOperationDropdown\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAGA;AACA;;;;;;;;AAOA,MAAM,0BAA0B,CAAC,EAC/B,eAAe,EACf,sBAAsB,EAChB;;IACN,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,qBACE,oWAAC,wKAAA,CAAA,qBAAkB;QACjB,MAAM;QACN,cAAc;QACd,WAAU;QACV,QAAQ;YACN,UAAU;YACV,WAAW,CAAC;QACd;;0BAEA,oWAAC,wKAAA,CAAA,4BAAyB;gBACxB,SAAS,IAAM,QAAQ,CAAA,IAAK,CAAC;0BAE7B,cAAA,oWAAC,0JAAA,CAAA,UAAY;oBAAC,MAAK;oBAAI,OAAO,OAAO,0JAAA,CAAA,oBAAiB,CAAC,KAAK,GAAG,0JAAA,CAAA,oBAAiB,CAAC,OAAO;8BACtF,cAAA,oWAAC,wOAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAG1B,oWAAC,wKAAA,CAAA,4BAAyB;gBAAC,WAAU;0BACnC,cAAA,oWAAC;oBACC,WAAW;;sCAEX,oWAAC;4BAAI,WAAU;4BAAoI,SAAS;sCAC1J,cAAA,oWAAC;gCAAK,WAAU;0CAAQ,EAAE;;;;;;;;;;;sCAE5B,oWAAC;4BAAI,WAAU;4BAAoI,SAAS;sCAC1J,cAAA,oWAAC;gCAAK,WAAU;0CAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;GAvCM;;QAIU,8XAAA,CAAA,iBAAc;;;KAJxB;uCAyCS", "debugId": null}}, {"offset": {"line": 8231, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/header-in-mobile.tsx"], "sourcesContent": ["import { useCallback, useState } from 'react'\nimport { useTranslation } from 'react-i18next'\nimport {\n  RiMenuLine,\n} from '@remixicon/react'\nimport { useChatWithHistoryContext } from './context'\nimport Operation from './header/operation'\nimport Sidebar from './sidebar'\nimport MobileOperationDropdown from './header/mobile-operation-dropdown'\nimport styles from './index.module.css'\nimport AppIcon from '@/components/base/app-icon'\nimport ActionButton from '@/components/base/action-button'\nimport { Message3Fill } from '@/components/base/icons/src/public/other'\nimport InputsFormContent from '@/components/base/chat/chat-with-history/inputs-form/content'\nimport Confirm from '@/components/base/confirm'\nimport RenameModal from '@/components/base/chat/chat-with-history/sidebar/rename-modal'\nimport type { ConversationItem } from '@/models/share'\n\nconst LarkNewChatIcon = ({ className }: { className: string }) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" className={className}>\n    <path d=\"M10,13.3333333 L10,10 M10,10 L10,6.66666667 M10,10 L6.66666667,10 M10,10 L13.3333333,10 M10.0001111,20 C8.18333333,20 6.47971111,19.5154444 5.01138889,18.6686667 C4.86633333,18.585 4.79371111,18.5431111 4.72547778,18.5243333 C4.66197778,18.5067778 4.60527778,18.5007778 4.53954444,18.5053333 C4.46947778,18.5101111 4.397,18.5342222 4.25287778,18.5823333 L1.68674444,19.4376667 L1.68472222,19.4385556 C1.14324444,19.6191111 0.872,19.7095556 0.691733333,19.6452222 C0.534666667,19.5892222 0.410877778,19.4652222 0.354866667,19.3082222 C0.290633333,19.128 0.380755556,18.8576667 0.560988889,18.317 L0.562066667,18.3136667 L1.41631111,15.7508889 L1.41834444,15.7455556 C1.46595556,15.6026667 1.49006667,15.5303333 1.49483333,15.4606667 C1.49933333,15.3948889 1.49334444,15.3377778 1.47578889,15.2743333 C1.45717778,15.207 1.41608889,15.1356667 1.3346,14.9944444 L1.33132222,14.9887778 C0.484488889,13.5204444 0,11.8167778 0,10 C0,4.47715556 4.47715556,0 10,0 C15.5228889,0 20,4.47715556 20,10 C20,15.5228889 15.523,20 10.0001111,20 Z\" transform=\"translate(2 2)\" stroke=\"#434B5B\" strokeWidth=\"2\" fill=\"none\" fillRule=\"evenodd\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n  </svg>\n)\n\nconst HeaderInMobile = () => {\n  const {\n    appData,\n    currentConversationId,\n    currentConversationItem,\n    pinnedConversationList,\n    handleNewConversation,\n    handlePinConversation,\n    handleUnpinConversation,\n    handleDeleteConversation,\n    handleRenameConversation,\n    conversationRenaming,\n    embedSource,\n  } = useChatWithHistoryContext()\n  const { t } = useTranslation()\n  const isPin = pinnedConversationList.some(item => item.id === currentConversationId)\n  const [showConfirm, setShowConfirm] = useState<ConversationItem | null>(null)\n  const [showRename, setShowRename] = useState<ConversationItem | null>(null)\n  const handleOperate = useCallback((type: string) => {\n    if (type === 'pin')\n      handlePinConversation(currentConversationId)\n\n    if (type === 'unpin')\n      handleUnpinConversation(currentConversationId)\n\n    if (type === 'delete')\n      setShowConfirm(currentConversationItem as any)\n\n    if (type === 'rename')\n      setShowRename(currentConversationItem as any)\n  }, [currentConversationId, currentConversationItem, handlePinConversation, handleUnpinConversation])\n  const handleCancelConfirm = useCallback(() => {\n    setShowConfirm(null)\n  }, [])\n  const handleDelete = useCallback(() => {\n    if (showConfirm)\n      handleDeleteConversation(showConfirm.id, { onSuccess: handleCancelConfirm })\n  }, [showConfirm, handleDeleteConversation, handleCancelConfirm])\n  const handleCancelRename = useCallback(() => {\n    setShowRename(null)\n  }, [])\n  const handleRename = useCallback((newName: string) => {\n    if (showRename)\n      handleRenameConversation(showRename.id, newName, { onSuccess: handleCancelRename })\n  }, [showRename, handleRenameConversation, handleCancelRename])\n  const [showSidebar, setShowSidebar] = useState(false)\n  const [showChatSettings, setShowChatSettings] = useState(false)\n\n  return (\n    <>\n      <div className={`flex shrink-0 items-center gap-1 bg-mask-top2bottom-gray-50-to-transparent px-2 py-3 ${embedSource && 'h-[48px] border-none pt-[8px]'}`}>\n        <ActionButton size='l' className='shrink-0' onClick={() => setShowSidebar(true)}>\n          <RiMenuLine className='h-[18px] w-[18px]' />\n        </ActionButton>\n        <div className='flex grow items-center justify-center'>\n          {!currentConversationId && (\n            <>\n              {!embedSource && (\n                <AppIcon\n                  className='mr-2'\n                  size='tiny'\n                  icon={appData?.site.icon}\n                  iconType={appData?.site.icon_type}\n                  imageUrl={appData?.site.icon_url}\n                  background={appData?.site.icon_background}\n                />\n              )}\n              <div className={`system-md-semibold truncate text-text-secondary ${embedSource && 'text-xl !font-medium'} ${embedSource && styles.mobileNavTitle}`}>\n                {appData?.site.title}\n              </div>\n            </>\n          )}\n          {currentConversationId && (\n            <Operation\n              title={currentConversationItem?.name || ''}\n              isPinned={!!isPin}\n              togglePin={() => handleOperate(isPin ? 'unpin' : 'pin')}\n              isShowDelete\n              isShowRenameConversation\n              onRenameConversation={() => handleOperate('rename')}\n              onDelete={() => handleOperate('delete')}\n            />\n          )}\n        </div>\n        {embedSource\n          ? (\n            <div\n              className='flex h-8 w-8 shrink-0 items-center justify-center rounded-lg'\n              onClick={handleNewConversation}\n            >\n              <LarkNewChatIcon className=\"w-[22px]\" />\n            </div>\n          ) : (\n            <MobileOperationDropdown\n            handleResetChat={handleNewConversation}\n            handleViewChatSettings={() => setShowChatSettings(true)}\n          />\n        )}\n      </div>\n      {showSidebar && (\n        <div className='fixed inset-0 z-50 flex bg-background-overlay p-1'\n          style={{ backgroundColor: embedSource ? 'rgba(0,0,0,.7)' : '' }}\n          onClick={() => setShowSidebar(false)}\n        >\n          <div className='flex h-full w-[calc(100vw_-_40px)] rounded-xl bg-components-panel-bg shadow-lg backdrop-blur-sm' onClick={e => e.stopPropagation()}>\n            <Sidebar />\n          </div>\n        </div>\n      )}\n      {showChatSettings && (\n        <div className='fixed inset-0 z-50 flex justify-end bg-background-overlay p-1'\n          onClick={() => setShowChatSettings(false)}\n        >\n          <div className='flex h-full w-[calc(100vw_-_40px)] flex-col rounded-xl bg-components-panel-bg shadow-lg backdrop-blur-sm' onClick={e => e.stopPropagation()}>\n            <div className='flex items-center gap-3 rounded-t-2xl border-b border-divider-subtle px-4 py-3'>\n              <Message3Fill className='h-6 w-6 shrink-0' />\n              <div className='system-xl-semibold grow text-text-secondary'>{t('share.chat.chatSettingsTitle')}</div>\n            </div>\n            <div className='p-4'>\n              <InputsFormContent />\n            </div>\n          </div>\n        </div>\n      )}\n      {!!showConfirm && (\n        <Confirm\n          title={t('share.chat.deleteConversation.title')}\n          content={t('share.chat.deleteConversation.content') || ''}\n          isShow\n          onCancel={handleCancelConfirm}\n          onConfirm={handleDelete}\n        />\n      )}\n      {showRename && (\n        <RenameModal\n          isShow\n          onClose={handleCancelRename}\n          saveLoading={conversationRenaming}\n          name={showRename?.name || ''}\n          onSave={handleRename}\n        />\n      )}\n    </>\n  )\n}\n\nexport default HeaderInMobile\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAGA,MAAM,kBAAkB,CAAC,EAAE,SAAS,EAAyB,iBAC3D,oWAAC;QAAI,OAAM;QAA6B,SAAQ;QAAY,WAAW;kBACrE,cAAA,oWAAC;YAAK,GAAE;YAAwgC,WAAU;YAAiB,QAAO;YAAU,aAAY;YAAI,MAAK;YAAO,UAAS;YAAU,eAAc;YAAQ,gBAAe;;;;;;;;;;;KAF9oC;AAMN,MAAM,iBAAiB;;IACrB,MAAM,EACJ,OAAO,EACP,qBAAqB,EACrB,uBAAuB,EACvB,sBAAsB,EACtB,qBAAqB,EACrB,qBAAqB,EACrB,uBAAuB,EACvB,wBAAwB,EACxB,wBAAwB,EACxB,oBAAoB,EACpB,WAAW,EACZ,GAAG,CAAA,GAAA,2KAAA,CAAA,4BAAyB,AAAD;IAC5B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8XAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,QAAQ,uBAAuB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA2B;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA2B;IACtE,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACjC,IAAI,SAAS,OACX,sBAAsB;YAExB,IAAI,SAAS,SACX,wBAAwB;YAE1B,IAAI,SAAS,UACX,eAAe;YAEjB,IAAI,SAAS,UACX,cAAc;QAClB;oDAAG;QAAC;QAAuB;QAAyB;QAAuB;KAAwB;IACnG,MAAM,sBAAsB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;2DAAE;YACtC,eAAe;QACjB;0DAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;oDAAE;YAC/B,IAAI,aACF,yBAAyB,YAAY,EAAE,EAAE;gBAAE,WAAW;YAAoB;QAC9E;mDAAG;QAAC;QAAa;QAA0B;KAAoB;IAC/D,MAAM,qBAAqB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,cAAc;QAChB;yDAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YAChC,IAAI,YACF,yBAAyB,WAAW,EAAE,EAAE,SAAS;gBAAE,WAAW;YAAmB;QACrF;mDAAG;QAAC;QAAY;QAA0B;KAAmB;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE;;0BACE,oWAAC;gBAAI,WAAW,CAAC,qFAAqF,EAAE,eAAe,iCAAiC;;kCACtJ,oWAAC,0JAAA,CAAA,UAAY;wBAAC,MAAK;wBAAI,WAAU;wBAAW,SAAS,IAAM,eAAe;kCACxE,cAAA,oWAAC,wOAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;;;;;;kCAExB,oWAAC;wBAAI,WAAU;;4BACZ,CAAC,uCACA;;oCACG,CAAC,6BACA,oWAAC,qJAAA,CAAA,UAAO;wCACN,WAAU;wCACV,MAAK;wCACL,MAAM,SAAS,KAAK;wCACpB,UAAU,SAAS,KAAK;wCACxB,UAAU,SAAS,KAAK;wCACxB,YAAY,SAAS,KAAK;;;;;;kDAG9B,oWAAC;wCAAI,WAAW,CAAC,gDAAgD,EAAE,eAAe,uBAAuB,CAAC,EAAE,eAAe,oLAAA,CAAA,UAAM,CAAC,cAAc,EAAE;kDAC/I,SAAS,KAAK;;;;;;;;4BAIpB,uCACC,oWAAC,uLAAA,CAAA,UAAS;gCACR,OAAO,yBAAyB,QAAQ;gCACxC,UAAU,CAAC,CAAC;gCACZ,WAAW,IAAM,cAAc,QAAQ,UAAU;gCACjD,YAAY;gCACZ,wBAAwB;gCACxB,sBAAsB,IAAM,cAAc;gCAC1C,UAAU,IAAM,cAAc;;;;;;;;;;;;oBAInC,4BAEG,oWAAC;wBACC,WAAU;wBACV,SAAS;kCAET,cAAA,oWAAC;4BAAgB,WAAU;;;;;;;;;;6CAG7B,oWAAC,6MAAA,CAAA,UAAuB;wBACxB,iBAAiB;wBACjB,wBAAwB,IAAM,oBAAoB;;;;;;;;;;;;YAIvD,6BACC,oWAAC;gBAAI,WAAU;gBACb,OAAO;oBAAE,iBAAiB,cAAc,mBAAmB;gBAAG;gBAC9D,SAAS,IAAM,eAAe;0BAE9B,cAAA,oWAAC;oBAAI,WAAU;oBAAkG,SAAS,CAAA,IAAK,EAAE,eAAe;8BAC9I,cAAA,oWAAC,oLAAA,CAAA,UAAO;;;;;;;;;;;;;;;YAIb,kCACC,oWAAC;gBAAI,WAAU;gBACb,SAAS,IAAM,oBAAoB;0BAEnC,cAAA,oWAAC;oBAAI,WAAU;oBAA2G,SAAS,CAAA,IAAK,EAAE,eAAe;;sCACvJ,oWAAC;4BAAI,WAAU;;8CACb,oWAAC,2NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,oWAAC;oCAAI,WAAU;8CAA+C,EAAE;;;;;;;;;;;;sCAElE,oWAAC;4BAAI,WAAU;sCACb,cAAA,oWAAC,6LAAA,CAAA,UAAiB;;;;;;;;;;;;;;;;;;;;;YAKzB,CAAC,CAAC,6BACD,oWAAC,iJAAA,CAAA,UAAO;gBACN,OAAO,EAAE;gBACT,SAAS,EAAE,4CAA4C;gBACvD,MAAM;gBACN,UAAU;gBACV,WAAW;;;;;;YAGd,4BACC,oWAAC,8LAAA,CAAA,UAAW;gBACV,MAAM;gBACN,SAAS;gBACT,aAAa;gBACb,MAAM,YAAY,QAAQ;gBAC1B,QAAQ;;;;;;;;AAKlB;GAhJM;;QAaA,2KAAA,CAAA,4BAAyB;QACf,8XAAA,CAAA,iBAAc;;;MAdxB;uCAkJS", "debugId": null}}, {"offset": {"line": 8563, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/index.tsx"], "sourcesContent": ["import type { FC } from 'react'\nimport {\n  useState,\n} from 'react'\nimport Sidebar from './sidebar'\nimport Header from './header'\nimport HeaderInMobile from './header-in-mobile'\n// import ChatWrapper from './chat-wrapper'\nimport styles from './index.module.css'\n// import Loading from '@/components/base/loading'\nimport cn from 'classnames'\n// import RightSidebar from './right-sidebar'\n\ntype ChatWithHistoryProps = {\n  className?: string\n}\n\nconst ChatWithHistory: FC<ChatWithHistoryProps> = ({\n  className,\n}) => {\n  const [showSidePanel, setShowSidePanel] = useState(false)\n  const isMobile = false\n  const embedSource = true\n  const isSidebarCollapsed = false\n  const isFold = false\n  const appChatListDataLoading = false\n  const chatShouldReloadKey = false\n  const refreshRenderKey = false\n  const rightSideInfo = false\n\n  return (\n    <div className={cn(\n      'flex h-full bg-background-default-burn',\n      isMobile && 'flex-col',\n      className,\n      embedSource && isMobile && '!bg-[#f5f6f8]',\n      embedSource && isMobile && styles.bg,\n    )}>\n      {(!isMobile && !isFold) && (\n        <div className={cn(\n          'flex w-[236px] flex-col pr-0 transition-all duration-200 ease-in-out',\n          isSidebarCollapsed && 'w-0 overflow-hidden !p-0',\n          embedSource && !isMobile && '!bg-white',\n        )}>\n          <Sidebar />\n        </div>\n      )}\n      {isMobile && (\n        <HeaderInMobile />\n      )}\n      <div className={cn('relative grow p-2', embedSource && 'p-0', isMobile && 'h-[calc(100%_-_56px)] p-0')}>\n        {isSidebarCollapsed && (\n          <div\n            className={cn(\n              'absolute top-0 z-20 flex h-full w-[256px] flex-col p-2 transition-all duration-500 ease-in-out',\n              showSidePanel ? 'left-0' : 'left-[-248px]',\n            )}\n            onMouseEnter={() => setShowSidePanel(true)}\n            onMouseLeave={() => setShowSidePanel(false)}\n          >\n            {/* <Sidebar isPanel /> */}\n          </div>\n        )}\n        <div className={cn('flex h-full flex-col overflow-hidden border-[0,5px] border-components-panel-border-subtle bg-chatbot-bg', isMobile ? 'rounded-t-2xl' : 'rounded-2xl', embedSource && 'rounded-none')}>\n          {!isMobile && !embedSource && <Header />}\n          {/* {appChatListDataLoading && (\n            <Loading />\n          )} */}\n          {/* {!appChatListDataLoading && (\n            <ChatWrapper key={chatShouldReloadKey || refreshRenderKey} />\n          )} */}\n        </div>\n      </div>\n      {/* <RightSidebar isMobile={isMobile} visible={Boolean(rightSideInfo)}/> */}\n    </div>\n  )\n}\n\nexport default ChatWithHistory\n"], "names": [], "mappings": ";;;;AACA;AAGA;AACA;AACA;AACA,2CAA2C;AAC3C;AACA,kDAAkD;AAClD;;;;;;;;;AAOA,MAAM,kBAA4C,CAAC,EACjD,SAAS,EACV;;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW;IACjB,MAAM,cAAc;IACpB,MAAM,qBAAqB;IAC3B,MAAM,SAAS;IACf,MAAM,yBAAyB;IAC/B,MAAM,sBAAsB;IAC5B,MAAM,mBAAmB;IACzB,MAAM,gBAAgB;IAEtB,qBACE,oWAAC;QAAI,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAE,AAAD,EACf,0CACA,YAAY,YACZ,WACA,eAAe,YAAY,iBAC3B,eAAe,YAAY,oLAAA,CAAA,UAAM,CAAC,EAAE;;YAElC,CAAC,YAAY,CAAC,wBACd,oWAAC;gBAAI,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAE,AAAD,EACf,wEACA,sBAAsB,4BACtB,eAAe,CAAC,YAAY;0BAE5B,cAAA,oWAAC,oLAAA,CAAA,UAAO;;;;;;;;;;YAGX,0BACC,oWAAC,0LAAA,CAAA,UAAc;;;;;0BAEjB,oWAAC;gBAAI,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAE,AAAD,EAAE,qBAAqB,eAAe,OAAO,YAAY;;oBACvE,oCACC,oWAAC;wBACC,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAE,AAAD,EACV,kGACA,gBAAgB,WAAW;wBAE7B,cAAc,IAAM,iBAAiB;wBACrC,cAAc,IAAM,iBAAiB;;;;;;kCAKzC,oWAAC;wBAAI,WAAW,CAAA,GAAA,6LAAA,CAAA,UAAE,AAAD,EAAE,2GAA2G,6EAA6B,eAAe,eAAe;kCACtL,CAAC,YAAY,CAAC,6BAAe,oWAAC,mLAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;;;;;;AAY/C;GA3DM;KAAA;uCA6DS", "debugId": null}}, {"offset": {"line": 8665, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/app/chat/page.tsx"], "sourcesContent": ["'use client'\nimport React from 'react'\n// import Chat<PERSON>ithHistoryWrap from '@/components/base/chat/chat-with-history'\nimport ChatWithHistoryWrap from '@/components/base/chat/chat-with-history/index'\n\nconst Chat = () => {\n  return (\n    <>\n      <ChatWithHistoryWrap />\n    </>\n  )\n}\n\nexport default React.memo(Chat)\n"], "names": [], "mappings": ";;;;AACA;AACA,6EAA6E;AAC7E;AAHA;;;;AAKA,MAAM,OAAO;IACX,qBACE;kBACE,cAAA,oWAAC,yKAAA,CAAA,UAAmB;;;;;;AAG1B;KANM;2DAQS,oUAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}]}