(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/base/file-uploader/pdf-preview.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_components_base_69934e4e._.js",
  "static/chunks/src_components_base_file-uploader_pdf-preview_tsx_2092f636._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/base/file-uploader/pdf-preview.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);