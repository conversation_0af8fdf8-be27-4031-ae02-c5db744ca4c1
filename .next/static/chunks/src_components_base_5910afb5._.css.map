{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/zh/coli-ai-agi-web/src/components/base/chat/chat-with-history/index.module.css"], "sourcesContent": [".bg {\n    background-image: url('~@/assets/lark-chat-top.jpeg');\n    background-position: top;\n    background-size: contain;\n    background-repeat: no-repeat;\n}\n\n.mobileNavTitle {\n    background: linear-gradient(to bottom, #562DF1, #2381FF); \n    -webkit-background-clip: text; \n    color: transparent; \n}\n\n.navBarBg::after {\n    content: '';\n    height: 100vh;\n    background: linear-gradient(var(--start-color), var(--end-color) 20%);\n    position: absolute;\n    width: 100vw;\n    z-index: -1;\n    top: 0;\n    left: 0;\n}\n.errorPng {\n    background-image: url('~@/assets/404.png');\n    background-position: top;\n    background-size: contain;\n    background-repeat: no-repeat;\n}"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;AAUA", "debugId": null}}]}