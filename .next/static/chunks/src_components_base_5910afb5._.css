/* [project]/src/components/base/chat/chat-with-history/index.module.css [app-client] (css) */
.index-module__guMYKq__bg {
  background-image: url("../media/lark-chat-top.9091725b.jpeg");
  background-position: top;
  background-repeat: no-repeat;
  background-size: contain;
}

.index-module__guMYKq__mobileNavTitle {
  background: linear-gradient(#562df1, #2381ff);
  color: #0000;
  -webkit-background-clip: text;
}

.index-module__guMYKq__navBarBg:after {
  content: "";
  background: linear-gradient(var(--start-color), var(--end-color) 20%);
  z-index: -1;
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
}

.index-module__guMYKq__errorPng {
  background-image: url("../media/404.7de504b4.png");
  background-position: top;
  background-repeat: no-repeat;
  background-size: contain;
}


/* [project]/src/components/base/badge/index.css [app-client] (css) */
/* unparseable [project]/src/components/base/badge/index.css [app-client] (css) */

/*# sourceMappingURL=src_components_base_5910afb5._.css.map*/