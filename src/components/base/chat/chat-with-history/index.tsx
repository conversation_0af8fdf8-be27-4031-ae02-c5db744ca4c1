import type { FC } from 'react'
import {
  useState,
} from 'react'
import Sidebar from './sidebar'
import Header from './header'
import HeaderInMobile from './header-in-mobile'
// import ChatWrapper from './chat-wrapper'
import styles from './index.module.css'
// import Loading from '@/components/base/loading'
import cn from 'classnames'
// import RightSidebar from './right-sidebar'

type ChatWithHistoryProps = {
  className?: string
}

const ChatWithHistory: FC<ChatWithHistoryProps> = ({
  className,
}) => {
  const [showSidePanel, setShowSidePanel] = useState(false)
  const isMobile = false
  const embedSource = true
  const isSidebarCollapsed = false
  const isFold = false
  const appChatListDataLoading = false
  const chatShouldReloadKey = false
  const refreshRenderKey = false
  const rightSideInfo = false

  return (
    <div className={cn(
      'flex h-full bg-background-default-burn',
      isMobile && 'flex-col',
      className,
      embedSource && isMobile && '!bg-[#f5f6f8]',
      embedSource && isMobile && styles.bg,
    )}>
      {(!isMobile && !isFold) && (
        <div className={cn(
          'flex w-[236px] flex-col pr-0 transition-all duration-200 ease-in-out',
          isSidebarCollapsed && 'w-0 overflow-hidden !p-0',
          embedSource && !isMobile && '!bg-white',
        )}>
          <Sidebar />
        </div>
      )}
      {isMobile && (
        <HeaderInMobile />
      )}
      <div className={cn('relative grow p-2', embedSource && 'p-0', isMobile && 'h-[calc(100%_-_56px)] p-0')}>
        {isSidebarCollapsed && (
          <div
            className={cn(
              'absolute top-0 z-20 flex h-full w-[256px] flex-col p-2 transition-all duration-500 ease-in-out',
              showSidePanel ? 'left-0' : 'left-[-248px]',
            )}
            onMouseEnter={() => setShowSidePanel(true)}
            onMouseLeave={() => setShowSidePanel(false)}
          >
            {/* <Sidebar isPanel /> */}
          </div>
        )}
        <div className={cn('flex h-full flex-col overflow-hidden border-[0,5px] border-components-panel-border-subtle bg-chatbot-bg', isMobile ? 'rounded-t-2xl' : 'rounded-2xl', embedSource && 'rounded-none')}>
          {!isMobile && !embedSource && <Header />}
          {/* {appChatListDataLoading && (
            <Loading />
          )} */}
          {/* {!appChatListDataLoading && (
            <ChatWrapper key={chatShouldReloadKey || refreshRenderKey} />
          )} */}
        </div>
      </div>
      {/* <RightSidebar isMobile={isMobile} visible={Boolean(rightSideInfo)}/> */}
    </div>
  )
}

export default ChatWithHistory
