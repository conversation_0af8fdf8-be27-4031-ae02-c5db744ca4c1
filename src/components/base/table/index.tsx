'use client'

import type { FC } from 'react'
import Pagination from '@/components/base/pagination'
import Loading from '@/components/base/loading'

type IColumns = {
  title: string
  dataIndex: string
  width?: number
  render?: (value: any, row: { [x: string]: string }, index: number) => JSX.Element
}

type TableProps = {
  data: { [x: string]: string }[]
  columns: IColumns[]
  loading?: boolean
  pagination?: {
    current: number
    total: number
    limit: number
  } | false
  onChange?: (page: number) => void
}

const Table: FC<TableProps> = ({ data = [], columns = [], loading, pagination, onChange = () => {} }) => {
  return (
    <div className='relative w-full h-full overflow-x-auto min-h-[100px]'>
      <table className={'max-w-full w-full border-collapse border-0 text-sm mt-3'}>
        <thead className="h-8 leading-8 border-b border-gray-200 text-gray-500 font-medium text-xs uppercase">
          <tr>
            {columns.map((item, index) => (
              <td className={`w-[${item.width}px]`} key={index}>{item.title}</td>
            ))}
          </tr>
        </thead>
        <tbody className="relative text-gray-700">
          {
            data.map((rowItem, rowIndex: number) => (
              <tr
                key={rowIndex}
                className={'border-b border-gray-200 h-8 hover:bg-gray-50 cursor-pointer'}>
                {columns.map((colItem, colIndex) => (
                  <td key={colIndex} className='text-left align-middle text-gray-500 text-xs'>{colItem?.render ? colItem.render?.(rowItem[colItem.dataIndex], rowItem, rowIndex) : rowItem[colItem.dataIndex]}</td>
                ))}
              </tr>
            ))
          }
        </tbody>
      </table>
      {loading && <div className="absolute bottom-0 left-0 h-full w-full min-h-[140px]">
        <Loading type="app"/>
      </div>
      }

      {pagination && <Pagination current={pagination.current} onChange={onChange} total={pagination.total} limit={pagination.limit} />}
    </div>
  )
}

export default Table
