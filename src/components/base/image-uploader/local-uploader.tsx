import { type FC, useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import Uploader from './uploader'
import { useImageFiles } from './hooks'
import ImageList from './image-list'
import { ImagePlus } from '@/components/base/icons/src/vender/line/images'
import { type ImageFile } from '@/types/app'

type LocalUploaderProps = {
  onFilesChange: (imageFile: ImageFile[]) => void
  disabled?: boolean
  limit?: number
  fileExtensions?: string[]
}
const LocalUploader: FC<LocalUploaderProps> = ({ onFilesChange, disabled, limit, fileExtensions }) => {
  const { t } = useTranslation()

  const {
    files,
    onUpload,
    onRemove,
    onImageLinkLoadError,
    onImageLinkLoadSuccess,
    onReUpload,
  } = useImageFiles()

  useEffect(() => {
    onFilesChange(files)
  }, [files])

  return <div>
    <ImageList
      readonly
      list={files}
      onRemove={onRemove}
      onReUpload={onReUpload}
      onImageLinkLoadError={onImageLinkLoadError}
      onImageLinkLoadSuccess={onImageLinkLoadSuccess}
    />

    <Uploader onUpload={onUpload} disabled={disabled} limit={limit} fileExtensions={fileExtensions}>
      {hovering => (
        <div
          className={`
            flex items-center justify-center px-3 h-8 bg-gray-100
            text-xs text-gray-500 rounded-lg cursor-pointer
            ${hovering && 'bg-gray-200'}
          `}
        >
          <ImagePlus className="w-4 h-4 text-gray-500" />
          {t('common.imageUploader.uploadFromComputer')}
        </div>
      )}
    </Uploader>
  </div>
}

export default LocalUploader
