import type { FC } from 'react'
import React, { useMemo, useRef, useState } from 'react'
import { useDebounceFn, useGetState, useInfiniteScroll, useMount } from 'ahooks'
import { RiArrowDownSLine } from '@remixicon/react'
import type { Tenant } from './store-tenant'
import { useStore as useTenantStore } from './store-tenant'
import cn from '@/utils/classnames'
import {
  PortalToFollowElem,
  PortalToFollowElemContent,
  PortalToFollowElemTrigger,
} from '@/components/base/portal-to-follow-elem'
import Input from '@/components/base/input'
import { Users01 } from '@/components/base/icons/src/vender/solid/users'
import { Check } from '@/components/base/icons/src/vender/line/general'
import { XCircle } from '@/components/base/icons/src/vender/solid/general'
import { useAppContext } from '@/context/app-context'
import { fetchTenantList } from '@/service/tag'

type TenantFilterProps = {
  type: 'tenant'
  value: any[]
  disabled?: boolean
  isPermission?: boolean
  onChange: (v: any[]) => void
}
const TenantFilter: FC<TenantFilterProps> = ({
  value,
  disabled,
  isPermission,
  onChange,
}) => {
  const { userProfile } = useAppContext()
  const [open, setOpen] = useState(false)

  const tenantList = useTenantStore(s => s.tenantList)
  const setTenantList = useTenantStore(s => s.setTenantList)

  const listRef = useRef<HTMLDivElement>(null)
  const [page, setPage] = useGetState(1)
  const [isNoMore, setIsNoMore] = useState(false)
  const [keywords, setKeywords] = useState('')
  // 获取数据集列表
  const getList = async () => {
    const { data, has_more } = await fetchTenantList({
      keyword: keywords,
      page,
      limit: 30,
    }) as { data: any[]; has_more: boolean }
    setPage(page + 1)
    setIsNoMore(!has_more)
    return data
  }

  const { run: handleSearch } = useDebounceFn(async () => {
    const data = await getList()
    setTenantList(data)
  }, { wait: 500 })
  const handleKeywordsChange = (value: string) => {
    setPage(1)
    setKeywords(value)
    handleSearch()
  }

  const currentTag = useMemo(() => {
    if (!isPermission) {
      return tenantList.find(tag => tag.tenant_id === value[0])
    }
    else {
      const firstItem = value[0]
      const id = firstItem?.id
      const tid = firstItem?.tenant_id
      const obj = tenantList.find((tag) => {
        if (id !== undefined)
          return tag.id === id
        if (tid !== undefined)
          return tag.tenant_id === tid
        return false
      })
      return obj
    }
  }, [value, tenantList, isPermission])

  const selectTag = (tag: Tenant) => {
    if (!isPermission) {
      if (value.includes(tag.tenant_id))
        onChange(value.filter(v => v !== tag.tenant_id))
      else
        onChange([...value, tag.tenant_id])
    }
    else {
      const index = value.findIndex((v) => {
        const id = v?.id
        const tid = v?.tenant_id
        return (id !== undefined && tag.id === id) || (tid !== undefined && tag.tenant_id === tid)
      })
      if (index !== -1) {
        const newValue = [...value]
        newValue.splice(index, 1)
        onChange(newValue)
      }
      else {
        onChange([...value, tag])
      }
    }
  }

  // 滚动加载数据
  useInfiniteScroll(
    async () => {
      if (!isNoMore && listRef.current) {
        const data = await getList()
        const newList = [...tenantList, ...data]
        setTenantList(newList)
      }
      return { list: [] }
    },
    {
      target: listRef,
      isNoMore: () => {
        return isNoMore
      },
      reloadDeps: [isNoMore, open],
    },
  )

  useMount(() => {
    isPermission && handleKeywordsChange('')
  })

  return (
    <PortalToFollowElem
      open={open}
      onOpenChange={setOpen}
      placement='bottom-start'
      offset={4}
    >
      <div className='relative'>
        <PortalToFollowElemTrigger
          onClick={() => !disabled && setOpen(v => !v)}
          className='block'
        >
          {isPermission && (
            <div className={cn('flex items-center px-3 py-[6px] rounded-lg bg-gray-100 cursor-pointer hover:bg-gray-200', open && 'bg-gray-200')}>
              <div className='mr-2 flex items-center justify-center w-6 h-6 rounded-lg bg-[#EEF4FF]'>
                <Users01 className='w-3.5 h-3.5 text-[#444CE7]' />
              </div>
              <div className='flex grow mr-2 text-gray-900 text-sm leading-5 truncate'>
                {!value.length && '请选择租户'}
                {!!value.length && currentTag?.company}
                {value.length > 1 && (
                  <div className='ml-2 text-sm font-medium leading-[18px] text-text-tertiary'>{`+${value.length - 1}`}</div>
                )}
              </div>
              {!disabled && <RiArrowDownSLine className='shrink-0 w-4 h-4 text-gray-700' />}
            </div>
          )}
          {
            !isPermission && <div className={cn(
              'flex items-center gap-1 px-2 h-8 rounded-lg border-[0.5px] border-transparent bg-components-input-bg-normal cursor-pointer',
              !open && !!value.length && 'shadow-xs',
              open && !!value.length && 'shadow-xs',
            )}>
              <div className='p-[1px]'>
                <Users01 className='w-3.5 h-3.5 text-text-tertiary' />
              </div>
              <div className='text-[13px] leading-[18px] text-text-secondary'>
                {!value.length && '全部租户'}
                {!!value.length && currentTag?.company}
              </div>
              {value.length > 1 && (
                <div className='text-xs font-medium leading-[18px] text-text-tertiary'>{`+${value.length - 1}`}</div>
              )}
              {!value.length && (
                <div className='p-[1px]'>
                  <RiArrowDownSLine className='h-3.5 w-3.5 text-text-tertiary' />
                </div>
              )}
              {!!value.length && (
                <div className='p-[1px] cursor-pointer group/clear' onClick={(e) => {
                  e.stopPropagation()
                  onChange([])
                }}>
                  <XCircle className='h-3.5 w-3.5 text-text-tertiary group-hover/clear:text-text-secondary' />
                </div>
              )}
            </div>
          }
        </PortalToFollowElemTrigger>
        <PortalToFollowElemContent className='z-[1002]'>
          <div className={`relative ${isPermission ? 'w-[480px]' : 'w-[240px]'} bg-components-panel-bg-blur backdrop-blur-[5px] rounded-lg border-[0.5px] border-components-panel-border shadow-lg`}>
            <div className='p-2'>
              <Input
                showLeftIcon
                showClearIcon
                value={keywords}
                onChange={e => handleKeywordsChange(e.target.value)}
                onClear={() => handleKeywordsChange('')}
              />
            </div>
            <div ref={listRef} className='p-1 max-h-72 overflow-y-auto'>
              {tenantList.map(tag => (
                <div
                  key={tag.id}
                  className='flex items-center gap-2 pl-3 py-[6px] pr-2 rounded-lg cursor-pointer hover:bg-state-base-hover'
                  onClick={() => selectTag(tag)}
                >
                  <div title={tag.company} className='grow text-sm text-text-tertiary leading-5 truncate'>
                    {tag.company}
                    {userProfile.id === tag.id && '(你)'}
                  </div>
                  { !isPermission && value.includes(tag.tenant_id) && <Check className={'shrink-0 w-4 h-4 text-text-secondary'} />}
                  { isPermission && value.some(item => item.id === tag.id || item.tenant_id === tag.id) && <Check className={'shrink-0 w-4 h-4 text-primary-600'} />}
                </div>
              ))}
              {!tenantList.length && (
                <div className='p-3 flex flex-col items-center gap-1'>
                  <Users01 className='w-6 h-6 text-text-tertiary' />
                  <div className='text-text-tertiary text-xs leading-[14px]'>没有租户</div>
                </div>
              )}
            </div>
          </div>
        </PortalToFollowElemContent>
      </div>
    </PortalToFollowElem>

  )
}

export default TenantFilter
