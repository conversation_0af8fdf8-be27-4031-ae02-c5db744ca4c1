import { CONVERSATION_ID_INFO } from '../base/chat/constants'
import { getProcessedSystemVariablesFromUrlParams } from '../base/chat/utils'
import { getQueryParam } from '@/utils'
import { fetchPublicAccessToken } from '@/service/share'
import { EMBED_SOURCE_TYPE } from '@/config'

export const isTokenV1 = (token: Record<string, any>) => {
  return !token.version
}

export const getInitialTokenV2 = (): Record<string, any> => ({
  version: 2,
})

const getParamsInEmbed = () => {
  const urlParams = getQueryParam()
  let state = urlParams?.get('state') || ''
  const code = urlParams?.get('code') || ''
  const token = urlParams?.get('token') || ''

  if (process.env.NEXT_PUBLIC_OAUTH_NAME === 'CSCEC4A' && !state)
    state = EMBED_SOURCE_TYPE.ZJ4A

  return { state, code, token }
}

export const getAccessToken = () => {
    const sharedToken = globalThis.location.pathname.split('/').slice(-1)[0]
    const accessToken
        = localStorage.getItem('token') || JSON.stringify(getInitialTokenV2())
    let accessTokenJson = getInitialTokenV2()

    try {
        accessTokenJson = JSON.parse(accessToken)
    }
    catch (e) {}

    return accessTokenJson
}

export const checkOrSetAccessToken = async () => {
  const sharedToken = globalThis.location.pathname.split('/').slice(-1)[0]
  const userId = (await getProcessedSystemVariablesFromUrlParams()).user_id
  const accessToken = getAccessToken() || JSON.stringify(getInitialTokenV2())

  // 飞书、中建通新皮肤 其他的默认皮肤
  const includeList = [EMBED_SOURCE_TYPE.FS, EMBED_SOURCE_TYPE.ZJT, EMBED_SOURCE_TYPE.TOKEN]

  // 获取嵌入来源的url参数
  const embedParams = getParamsInEmbed()
  const state = embedParams.state

  // 读取缓存的用户信息
  const userInfo = localStorage.getItem('app_user_info')
  if (userInfo && state === accessToken[sharedToken].source)
     return { embedApp: includeList.includes(state) ? state : '', userInfo: JSON.parse(userInfo) }

  if (!accessToken[sharedToken]?.[userId || 'DEFAULT'] || embedParams.state) {
    try {
      const res = await fetchPublicAccessToken(sharedToken, state, embedParams?.code || embedParams?.token)
      accessToken[sharedToken] = {
        ...accessToken[sharedToken],
        [userId || 'DEFAULT']: res?.access_token || '',
        source: state || '',
      }
      localStorage.setItem('token', JSON.stringify(accessToken))
      state && localStorage.setItem('app_user_info', JSON.stringify(res?.user_info || {}))

      if(state)
        location.href = `${location.origin + location.pathname}?state=${state}`

      // return state ? { embedApp: includeList.includes(state) ? state : '', userInfo: res?.user_info || {} } : null
    }
    catch(err) {
      console.error('Error fetching access token:', err)
      throw err
    }
  }
}

export const setAccessToken = async (sharedToken: string, token: string, user_id?: string) => {
  const accessToken = localStorage.getItem('token') || JSON.stringify(getInitialTokenV2())
  let accessTokenJson = getInitialTokenV2()
  try {
    accessTokenJson = JSON.parse(accessToken)
    if (isTokenV1(accessTokenJson))
      accessTokenJson = getInitialTokenV2()
  }
  catch {

  }

  localStorage.removeItem(CONVERSATION_ID_INFO)

  accessTokenJson[sharedToken] = {
    ...accessTokenJson[sharedToken],
    [user_id || 'DEFAULT']: token,
  }
  localStorage.setItem('token', JSON.stringify(accessTokenJson))
}

export const removeAccessToken = () => {
  const sharedToken = globalThis.location.pathname.split('/').slice(-1)[0]

  const accessToken = localStorage.getItem('token') || JSON.stringify(getInitialTokenV2())
  let accessTokenJson = getInitialTokenV2()
  try {
    accessTokenJson = JSON.parse(accessToken)
    if (isTokenV1(accessTokenJson))
      accessTokenJson = getInitialTokenV2()
  }
  catch {

  }

  localStorage.removeItem(CONVERSATION_ID_INFO)
  localStorage.removeItem('app_user_info')

  delete accessTokenJson[sharedToken]
  localStorage.setItem('token', JSON.stringify(accessTokenJson))
}
